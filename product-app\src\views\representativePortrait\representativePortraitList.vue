<template>
  <div class="representativePortraitList">
    <!-- 时光轴入口+人像+二维码 -->
    <div class="user_box flex_box flex_align_center">
      <div class="timeAxis_box"
           @click="openTimeAxis">
        <img src="../../assets/img/g_shiguang.png"
             alt="">
      </div>
      <img :src="headImg"
           alt=""
           class="user_img">
      <div class="flex_box flex_align_center user_name_codeImg">
        <div class="user_name">{{userName}}</div>
        <img src="../../assets/img/g_code.png"
             alt=""
             class="user_codeImg"
             @click="clickCodeImg">
      </div>
    </div>
    <!-- 履职动态 -->
    <div class="performanceUpdates">
      <div class="title-box">
        <p></p>
        <div class="title">履职动态</div>
      </div>
      <div class="performanceUpdates_box flex_box flex_align_center">
        <div class="performanceUpdates_relative"
             @click="switchTabClick(1)">
          <img src="../../assets/img/g_release.png"
               alt=""
               class="performanceUpdates_bgImg">
          <div class="performanceUpdates_text_box">
            <div class="performanceUpdates_text_num"
                 style="color: #4566C3;">{{postPerformanceCircleNum}}</div>
            <div class="performanceUpdates_text">发布履职圈</div>
          </div>
        </div>
        <div class="performanceUpdates_relative"
             @click="switchTabClick(2)">
          <img src="../../assets/img/g_release.png"
               alt=""
               class="performanceUpdates_bgImg">
          <div class="performanceUpdates_text_box">
            <div class="performanceUpdates_text_num"
                 style="color: #45BEA7;">{{participateActivitiesNum}}</div>
            <div class="performanceUpdates_text">参与活动</div>
          </div>
        </div>
      </div>
      <template v-if="(dataList&&dataList.length!==0)">
        <div v-for="(item,index) in dataList"
             :key="index"
             style="margin: 10px;"
             class="listData_item">
          <div class="listData_item_name"
               v-html="item.name"></div>
          <div class="listData_item_time">{{ item.time }}</div>
          <div class="listData_item_line"></div>
        </div>
      </template>
      <template v-else>
        <div class="nodata">暂无数据</div>
      </template>
    </div>
    <!-- 代表建议 -->
    <div class="suggestion">
      <div class="title-box">
        <p></p>
        <div class="title">代表建议</div>
      </div>
      <div class="suggestion_box flex_box flex_align_center">
        <div class="suggestion_relative">
          <img src="../../assets/img/g_hui.png"
               alt=""
               class="suggestion_img">
          <div class="flex_box flex_align_center suggestion_text_box">
            <div class="suggestion_text">会字建议</div>
            <div class="suggestion_text_num">{{huiDataNum}}<span>件</span></div>
          </div>
        </div>
        <div class="suggestion_relative">
          <img src="../../assets/img/g_ping.png"
               alt=""
               class="suggestion_img">
          <div class="flex_box flex_align_center suggestion_text_box">
            <div class="suggestion_text">平字建议</div>
            <div class="suggestion_text_num">{{pingDataNum}}<span>件</span></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 参会情况 -->
    <div class="attendees">
      <div class="title-box">
        <p></p>
        <div class="title">参会情况</div>
      </div>
      <div class="attendees_box flex_box flex_align_center">
        <div class="attendees_relative">
          <img src="../../assets/img/g_anAssembly.png"
               alt=""
               class="attendees_bgImg">
          <div class="attendees_text_box">
            <div class="attendees_text_num"
                 style="color: #4566C3;">{{duringConferenceNum}}<span>次</span></div>
            <div class="attendees_text">大会期间参会</div>
          </div>
        </div>
        <div class="attendees_relative">
          <img src="../../assets/img/g_adjournment.png"
               alt=""
               class="attendees_bgImg">
          <div class="attendees_text_box">
            <div class="attendees_text_num"
                 style="color: #45BEA7;">{{notInSessionNum}}<span>次</span></div>
            <div class="attendees_text">闭会期间参会</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 能力评估 -->
    <div class="capabilityAssessment"
         v-if="radarBoxShow">
      <div class="title-box">
        <p></p>
        <div class="title">能力评估</div>
      </div>
      <radar v-if="radarShow"
             id="radar"
             :listData="radarListData"
             style="width:100%;height:250px;margin-top:5px;"></radar>
    </div>
    <!-- 履职足迹 -->
    <div class="footprints">
      <div class="title-box">
        <p></p>
        <div class="title">履职足迹</div>
      </div>
      <div class="footprints_box">
        <div class="line05"></div>
        <div class="flex_box"
             v-for="(item,key,index) in dutyListData"
             :key="index">
          <div class="duty_time">
            <div class="flex_box">
              <div class="m"
                   v-if="item[0].show">{{key.split("-")[1]}}月</div>
              <div class="m_no"
                   v-else></div>
              <div class="c"></div>
              <div class="d">{{key.split("-")[2]}}日</div>
            </div>
            <div class="line_shu">1</div>
          </div>
          <div class="duty_item_content">
            <div v-for="(nItem,nIndex) in item"
                 :key="nIndex"
                 class="duty_item_content_item"
                 @click=openDetails(nItem)>
              <div class="mark_right_span">
                <span class="label">{{nItem.typeNmae}}</span>
                <span class="label"
                      v-if="nItem.type == '211'"
                      :style="'padding: 1px 10px;background: #fff;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;color:'+appTheme+';border:1px solid '+appTheme">履职补录</span>
              </div>
              <div class="mark_right_title">{{nItem.title}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 打开二维码 -->
    <van-overlay :show="codeImgShow">
      <div class="codeImgShow_box">
        <img class="codeImgShow_img"
             :src="qrcodeUrl" />
        <img src="../../assets/img/gpx_close.png"
             class="codeImgShow_close_img"
             alt=""
             @click="closespak(2)" />
      </div>
    </van-overlay>
    <van-overlay :show="isShowLoading">
      <div style="text-align: center; padding-top: 100%">
        <van-loading size="24px"
                     vertical
                     text-color="#0094ff"
                     color="#0094ff">加载中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Loading, Overlay, Dialog } from 'vant'
import radar from './component/radar.vue'
export default {
  name: 'representativePortraitList',
  components: {
    radar,
    [Dialog.Component.name]: Dialog.Component,
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    // const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: '人大代表画像',
      user: JSON.parse(sessionStorage.getItem('user')),
      userName: '',
      headImg: '',
      isShowLoading: false,
      postPerformanceCircleNum: 0, // 发布履职圈数量
      participateActivitiesNum: 0, // 参与活动数量
      // 发布履职圈、参与活动的数据
      dataList: [],
      huiDataNum: 0, // 会字建议数量
      pingDataNum: 0, // 平字建议数量
      duringConferenceNum: 0, // 大会期间参会数量
      notInSessionNum: 0, // 闭会期间参会数量
      newDate: '',
      dutyListData: {},
      radarShow: false,
      radarListData: [],
      radarBoxShow: false,
      codeImgShow: false,
      qrcodeUrl: '',
      assessmentNum: {
        wmfw: '',
        lzzs: '',
        xxnl: '',
        jdnl: '',
        dcyj: ''
      }
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      console.log('app进入代表画像')
      data.isShowLoading = true
      const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token')
      if (!token) {
        setTimeout(() => {
          sessionStorage.setItem('Sys_token', JSON.stringify(api.getPrefs({ sync: true, key: 'Sys_token' }))) // eslint-disable-line
          sessionStorage.setItem('areaId', JSON.stringify(api.getPrefs({ sync: true, key: 'SYS_SiteID' }))) // eslint-disable-line
          api.setInterfaceStyle({ style: 'light' }) // eslint-disable-line
          const user = {
            userName: api.getPrefs({ sync: true, key: 'Sys_UserName' }), // eslint-disable-line
            position: api.getPrefs({ sync: true, key: 'Sys_Position' }), // eslint-disable-line
            headImg: window.localStorage.getItem('Sys_AppPhoto')
          }
          data.userName = user.userName
          sessionStorage.setItem('user', JSON.stringify(user))
          onRefresh()
        }, 300)
      } else {
        setTimeout(() => {
          console.log('走到else了')
          data.userName = JSON.parse(sessionStorage.getItem('user')).userName || ''
          data.headImg = JSON.parse(sessionStorage.getItem('user')).headImg || ''
          onRefresh()
          data.isShowLoading = false
        }, 1800)
      }
    })
    const onRefresh = () => {
      memberPortraitDutyData()
      circleList()
      getHuiPingData()
      getAppDutyDetail()
    }
    const onLoad = () => {
    }
    // 进入时光轴
    const openTimeAxis = (rows) => {
      router.push({ path: 'timeAxis', query: { id: rows.id } })
    }
    // 打开二维码
    const clickCodeImg = () => {
      data.codeImgShow = true
      getQrCode()
    }
    // 关闭二维码
    const closespak = async () => {
      data.codeImgShow = false
    }
    // 获取二维码
    const getQrCode = async () => {
      const res = await $api.representativePortrait.memberPortraitQrCode({ id: data.user.id })
      // data.qrcodeUrl = 'http://www.cszysoft.com:9090/utils/qr?text=' + res.data
      console.log('获取二维码===>', res)
      data.qrcodeUrl = res.data
    }
    // 获取数据
    const memberPortraitDutyData = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyData()
      data.postPerformanceCircleNum = res.data.committeeSayCount
      data.participateActivitiesNum = res.data.activityCount
      data.duringConferenceNum = res.data.dhqjch
      data.notInSessionNum = res.data.bhqjch
      data.assessmentNum.wmfw = res.data.wmfw
      data.assessmentNum.lzzs = res.data.lzzs
      data.assessmentNum.xxnl = res.data.xxnl
      data.assessmentNum.jdnl = res.data.jdnl
      data.assessmentNum.dcyj = res.data.dcyj
      getCapabilityAssessment()
      console.log('代表画像=====>>>>>>', res)
    }
    // 获取履职圈列表
    const circleList = async () => {
      const res = await $api.beingCounted.committeesayAppList({
        pageNo: 1,
        pageSize: 999,
        publishBy: data.user.id
      })
      console.log('获取履职圈列表==>', res)
      res.data.forEach(item => {
        item.name = item.content
        item.time = item.publishDate
      })
      data.dataList = res.data.splice(0, 3)
    }
    // 获取履职圈、参与活动
    const getData = () => {
      data.dataList = [
        { id: '1', name: '11对今年以来国民经济和社会发展计划执行情对今年以来国民经济和社会发展计划执行情', time: '2023-07-27 15:11:34' },
        { id: '2', name: '22对今年以来国民经济和社会发展', time: '2023-07-27 15:11:34' },
        { id: '3', name: '33对今年以来国民经济和社会发展计划执行情对今年以来国民经济和社会发展计划执行情', time: '2023-07-27 15:11:34' },
        { id: '4', name: '44对今年以来国民经济和社', time: '2023-07-27 15:11:34' },
        { id: '5', name: '33对今年以来国民经济和社会发展计划执行情对今年以来国民经济和社会发展计划执行情', time: '2023-07-27 15:11:34' },
        { id: '6', name: '33对今年以来国民经济和社会发展计划执行情对今年以来国民经济和社会发展计划执行情', time: '2023-07-27 15:11:34' },
        { id: '7', name: '33对今年以来国民经济和社会发展计划执行情对今年以来国民经济和社会发展计划执行情', time: '2023-07-27 15:11:34' },
        { id: '8', name: '33对今年以来国民经济和社会发展计划执行情对今年以来国民经济和社会发展计划执行情', time: '2023-07-27 15:11:34' }
      ].splice(0, 5)
    }
    // 切换履职圈、参与活动
    const switchTabClick = (_num) => {
      data.dataList = []
      if (_num === 2) {
        getData()
      } else {
        circleList()
      }
    }
    // 获取会、平
    const getHuiPingData = async () => {
      const res = await $api.representativePortrait.getPersonSubmitInfo({ personCode: data.user.id })
      console.log('获取会、平件数===>>', res)
      const list = res.result
      data.huiDataNum = list.submitCountMeeting
      data.pingDataNum = list.submitCountFlat
    }
    // 获取能力评估
    const getCapabilityAssessment = async () => {
      data.radarShow = true
      data.radarListData = [
        { name: '履职指数', max: 10, num: data.assessmentNum.lzzs },
        { name: '为民服务', max: 10, num: data.assessmentNum.wmfw },
        { name: '学习能力', max: 10, num: data.assessmentNum.xxnl },
        { name: '调查研究', max: 10, num: data.assessmentNum.dcyj },
        { name: '监督能力', max: 10, num: data.assessmentNum.jdnl }
      ]
      var allNumZero = data.radarListData.every(function (item) {
        return item.num === 0
      })
      if (allNumZero) {
        data.radarBoxShow = false
      } else {
        data.radarBoxShow = true
      }
    }
    // 获取履职足迹
    const getAppDutyDetail = async () => {
      const param = {
        pageNo: 1,
        pageSize: 99,
        year: 2023,
        userId: data.userId,
        areaId: sessionStorage.getItem('areaId')
      }
      const res = await $api.performanceFiles.getAppDutyDetail(param)
      console.log('获取履职足迹===>', res)
      var list = res.data
      var dataLength = list.length
      for (var i = 0; i < dataLength; i++) {
        var time1 = new Date().getTime()
        var time2 = new Date(list[i].date.replace(/-/g, '/')).getTime()
        if (time1 < time2) {
          continue
        }
        var date = list[i].date.split(' ')[0]
        var nItem = {}
        nItem.id = list[i].id
        nItem.title = list[i].title
        nItem.type = list[i].type
        switch (list[i].type) {
          case 'suggest': // 建议
            nItem.typeNmae = '建议'
            break
          case 'proposal': // 提案
            nItem.typeNmae = '提案'
            break
          case 'officeOnline': // 委员值班
            nItem.typeNmae = '委员值班'
            break
          case 'social': // 社情民意
            nItem.typeNmae = '社情民意'
            break
          case 'activity': // 活动
          case '49': // 活动
            nItem.typeNmae = '活动'
            break
          case 'survey': // 意见征集
            nItem.typeNmae = '意见征集'
            break
          case 'learning': // 考试
            nItem.typeNmae = '学习培训'
            break
          case 'meet': // 会议
            nItem.typeNmae = '会议'
            break
          case 'bill': // 议案
            nItem.typeNmae = '议案'
            break
          case '211': // 履职补录
            nItem.typeNmae = '活动'
            break
        }
        if (data.newDate !== date) {
          data.dutyListData[date] = []
          data.newDate = date
        }
        if (i !== 0) {
          if (date.split('-')[1] === list[i - 1].date.split(' ')[0].split('-')[1]) {
            nItem.show = false
          } else {
            nItem.show = true
          }
        } else {
          nItem.show = true
        }
        data.dutyListData[date].push(nItem)
      }
    }
    // 进履职足迹详情
    const openDetails = (rows) => {
      console.log(rows)
      var keyList = { suggest: '7', social: '12', proposal: '11', officeOnline: '50', activity: '49', 49: '49', survey: '4', learning: '35', meet: '26', bill: '8' }
      rows.relateType = keyList[rows.type]
      if (rows.type === 211) { rows.relateType = '49.1' }
      if (rows.relateType === '12') {
        router.push({ path: 'socialDetails', query: { id: rows.id, title: rows.title } })
      } else if (rows.relateType === '11') {
        router.push({ name: 'proposalDetails', query: { id: rows.id } })
      } else if (rows.relateType === '49') {
        router.push({ path: 'activityDetails', query: { id: rows.id, title: rows.title } })
      } else if (rows.relateType === '50') {
        router.push({ path: 'committeeLivingRoomDetails', query: { id: rows.id, title: rows.title } })
      } else {
        console.log('暂时无法看详情')
        Dialog.alert({
          message: '暂时无法看详情'
        }).then(function () {
        })
      }
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, switchTabClick, openDetails, clickCodeImg, closespak, openTimeAxis }
  }
}
</script>
<style lang="less" scoped>
.representativePortraitList {
  width: 100%;
  min-height: 100%;
  background: #f8f8f8;
  padding-top: 15px;
  .user_box {
    background: #fff;
    margin: 0 15px;
    border-radius: 10px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    position: relative;
    .timeAxis_box {
      position: absolute;
      top: 0;
      right: 0;
      background: #5a9cff;
      // background: radial-gradient(
      //     circle at right top,
      //     transparent 60px,
      //     #5a9cff 0
      //   )
      //   right top;
      padding: 8px 10px 8px 25px;
      border-top-left-radius: 0px;
      border-top-right-radius: 10px;
      border-bottom-left-radius: 300px;
      img {
        width: 24px;
        height: 24px;
      }
    }
    .user_img {
      width: 106px;
      height: 143px;
      border-radius: 5px;
    }
    .user_name_codeImg {
      margin-top: 12px;
    }
    .user_name {
      font-size: 17px;
      font-weight: 600;
      color: #333333;
      line-height: 20px;
    }
    .user_codeImg {
      margin-left: 10px;
      width: 16px;
      height: 16px;
    }
  }
  .performanceUpdates {
    background: #fff;
    margin: 15px;
    border-radius: 10px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    .performanceUpdates_box {
      justify-content: space-between;
      .performanceUpdates_relative {
        position: relative;
        margin-top: 7px;
        .performanceUpdates_bgImg {
          width: 150px;
          height: 75px;
        }
        .performanceUpdates_text_box {
          position: absolute;
          top: 20%;
          left: 38%;
          .performanceUpdates_text_num {
            font-size: 16px;
            font-weight: 800;
            margin-left: 10px;
          }
          .performanceUpdates_text {
            font-size: 14px;
            color: #333333;
            line-height: 16px;
            margin-top: 1px;
          }
        }
      }
    }
    .listData_item {
      .listData_item_name {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
        word-break: break-all;
      }
      .listData_item_time {
        font-size: 14px;
        font-weight: 500;
        color: #999999;
        margin-top: 8px;
      }
      .listData_item_line {
        border-bottom: 1px solid #ebebeb;
        margin-top: 8px;
      }
    }
  }
  .suggestion {
    background: #fff;
    margin: 15px;
    border-radius: 10px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    .suggestion_box {
      margin-top: 14px;
      justify-content: space-between;
      .suggestion_relative {
        position: relative;
        .suggestion_img {
          width: 150px;
          height: 130px;
        }
        .suggestion_text_box {
          flex-direction: column;
          position: absolute;
          top: 55%;
          left: 28%;
          .suggestion_text {
            font-size: 16px;
            color: #ffffff;
          }
          .suggestion_text_num {
            font-size: 22px;
            font-weight: 600;
            color: #fff851;
            letter-spacing: 1px;
            margin-top: 4px;
            span {
              color: #fff;
              font-size: 16px;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
  .attendees {
    background: #fff;
    margin: 15px;
    border-radius: 10px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    .attendees_box {
      justify-content: space-between;
      .attendees_relative {
        position: relative;
        margin-top: 7px;
        .attendees_bgImg {
          width: 150px;
          height: 75px;
        }
        .attendees_text_box {
          position: absolute;
          top: 20%;
          left: 38%;
          .attendees_text_num {
            font-size: 18px;
            font-weight: 800;
            margin-left: 10px;
            span {
              font-size: 14px;
              margin-left: 5px;
            }
          }
          .attendees_text {
            font-size: 14px;
            color: #333333;
            line-height: 16px;
            margin-top: 3px;
          }
        }
      }
    }
  }
  .capabilityAssessment {
    background: #fff;
    margin: 15px;
    border-radius: 10px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
  }
  .footprints {
    background: #fff;
    margin: 15px;
    border-radius: 10px;
    padding: 16px;
    flex-direction: column;
    justify-content: center;
    .footprints_box {
      .line05 {
        width: 94%;
        left: 0;
        right: 0;
        margin: auto;
        margin-top: 10px;
      }
      .duty_time {
        margin: 10px 10px;
        .m {
          white-space: nowrap;
          width: 38px;
          height: 16px;
          background: #3894ff;
          border-radius: 9px;
          font-size: 12px;
          font-weight: 600;
          line-height: 16px;
          text-align: center;
          color: #ffffff;
          opacity: 1;
        }
        .m_no {
          width: 38px;
          height: 16px;
        }
        .c {
          white-space: nowrap;
          width: 6px;
          height: 6px;
          background: #3894ff;
          border: 1px solid #3894ff;
          box-shadow: 0px 0px 6px rgba(235, 77, 77, 0.5);
          border-radius: 50%;
          opacity: 1;
          margin-top: 5px;
          margin: 5px 5px;
        }
        .d {
          white-space: nowrap;
          width: 38px;
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          color: #222222;
          opacity: 1;
        }
        .line_shu {
          width: 1px;
          background: #eeeeee;
          height: 150%;
          left: 0;
          right: 0;
          margin: auto;
          overflow: hidden;
          margin-top: -10px;
        }
      }

      .duty_item_content {
        margin-top: 35px;
        margin-left: -50px;
        .duty_item_content_item {
          width: 250px;
          background: #ffffff;
          box-shadow: 0px 3px 12px rgba(34, 85, 172, 0.12);
          border-radius: 2px;
          padding: 10px;
          margin-bottom: 10px;
          .mark_right_span {
            margin-bottom: 5px;
            .label {
              font-size: 14px;
              padding: 2px 10px;
              margin-right: 10px;
              border-radius: 2px;
              background-color: #3894ff;
              color: #fff;
            }
          }
          .mark_right_title {
            font-size: 16px;
          }
        }
      }
    }
  }
  .codeImgShow_box {
    background: #fff;
    border-radius: 10px;
    margin: 60vw 20vw;
    position: relative;
    .codeImgShow_img {
      width: 40vw;
      object-fit: cover;
      margin: 8vw 10vw;
      border-radius: 0.1rem;
    }
    .codeImgShow_close_img {
      width: 30px;
      height: 32px;
      position: absolute;
      bottom: -15vw;
      left: 26vw;
    }
  }
  .title-box {
    display: flex;
    align-items: center;
    padding-bottom: 10px;

    p {
      width: 4px;
      height: 16px;
      background: linear-gradient(180deg, #51a1fe 0%, #51a1fe 100%);
      border-radius: 2px 2px 2px 2px;
    }

    .title {
      color: #333333;
      font-size: 19px;
      padding-left: 12px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 800;
      line-height: 22px;
      letter-spacing: 1px;
    }
  }
  .nodata {
    text-align: center;
    color: #ccc;
    margin: 20px;
  }
}
</style>

<template>
  <div class="surveyComentInfo">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">
        <!-- <template #right>{{playText}}
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="'font-size:20px; '"
           v-html="title"> </div>
      <div class="n_details_more_box flex_box">
        <!--来源-->
        <div class="n_details_name flex_placeholder"
             :style="'font-size:12px;'">{{userName}}</div>
        <div class=" flex_box flex_align_center flex_justify-content_end">
          <div class="n_details_time">{{ createDate }}</div>
        </div>
      </div>
      <div class="n_details_content"
           v-html="contents"></div>
    </div>
    <div class="survey_comment">
      <div class="survey_comment_title">
        <div class="survey_comment_bg"></div>
        <div class="survey_comment_fw">
          评论
        </div>
      </div>
      <div class="survey_comment_list"
           v-for="item in commentList"
           :key="item.id"
           @click="skipDetails('surveyComment', item)">
        <div class="survey_comment_list_top">
          <div class="survey_comment_list_top_img">
            <img :src="item.url"
                 alt="">
          </div>
          <div class="survey_comment_list_top_cen">
            <div class="survey_comment_list_top_name">{{ item.name }}</div>
            <div class="survey_comment_list_top_time">
              <div class="survey_comment_list_top_time_left">{{ item.createDate }}</div>
              <div class="survey_comment_list_top_time_right">
                <div :class="{survey_comment_list_top_time_right_like:true}"
                     @click.stop="downLike(item)">
                  <van-icon name="good-job-o"
                            :class="{bg:item.fabulousCount>0}" />
                  {{ item.fabulousCount }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="survey_comment_list_but">
          <div class="survey_comment_list_but_detail">
            {{ item.content }}
          </div>
        </div>
      </div>
    </div>
    <!-- <surveyInput ref="inputBox"
                 :inputData="inputData"
                 :surveyDetailsTitle="title"
                 @addCommentEvent="addCommentEvent"
                 :pageType="pageType"
                 :type="type"
                 :id="id" /> -->
  </div>
  <!--展示评论-->
  <!-- <commentList ref="commentOldAllList"
               @openInputBoxEvent="openInputBoxEvent"
               @freshState="freshState"
               :commentData="commentData"
               :type="type"
               :pageType="pageType"
               :id="id" />
  <div style="height:60px;"></div>
  <footer class="footerBox">
    <inputBox ref="inputBox"
              :inputData="inputData"
              @addCommentEvent="addCommentEvent"
              :pageType="pageType"
              :type="type"
              :id="id" />
  </footer> -->
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, computed, watch } from 'vue'
import { NavBar, Sticky, ImagePreview, Image as VanImage, Dialog, Toast } from 'vant'
import { useStore } from 'vuex'
// import surveyInput from './components/surveyInput.vue'
export default {
  name: 'surveyComentInfo',
  components: {
    // surveyInput,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const store = useStore()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      ifIike: route.query.ifIike || '',
      id: route.query.id,
      isFollow: route.query.isFollow || '',
      publishBy: route.query.publishBy || '',
      pageType: route.query.type || '',
      userName: route.query.userName || '',
      contents: route.query.content || '',
      createDate: route.query.createDate || '',
      type: 53,
      commentCount: 0,
      fabulousCount: route.query.fabulousCount || 0,
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] }, // 附件对象
      playText: '播放',
      inputData: {
        showTitleInput: 1,
        input_placeholder: '写评论...', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: false, // 显示评论功能
        showLike: false, // 显示点赞功能
        showAttach: false // 显示添加附件(图片)
      },
      commentData: {},
      commentOldAllList: null,
      inputBox: null,
      commentList: []
    })
    const getShow = computed(() => {
      // 返回的是ref对象
      return store.state.speechShow
    })
    const getStatus = computed(() => {
      // 返回的是ref对象
      return store.state.speechStauts
    })
    watch(getShow, (newName, oldName) => {
      data.show = newName
      if (!data.show) {
        data.playText = '播放'
      }
    })
    watch(getStatus, (newName, oldName) => {
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '继续'
        } else {
          data.playText = '暂停'
        }
      }
    })
    onMounted(() => {
      newsInfo()
    })
    const onRefresh = () => {
      newsInfo()
    }
    // 详情
    const newsInfo = async () => {
      getDetails()
      getFabulousList()
      if (data.pageType !== 'community') {
        getCommentList()
      }
    }
    // 获取详情
    const getDetails = async () => {
      var pageArr = ['countryside', 'community', 'representative', 'numberApp', 'favorite53']
      if (data.pageType === 'resumption') {
        // resumption 履职圈
        const res = await $api.news.getRepresentativeDetails({
          id: data.id
        })
        var { data: list } = await $api.general.getCommentStats({
          keyId: data.id,
          type: '25',
          areaId: data.user.areaId
        })
        data.conmmentList = list
        data.representativeDetails = res.data
      } else if (data.pageType === 'favorite5' || data.pageType === 'political' || data.pageType === 'twoInstitutes' || data.pageType === 'ZT' || data.pageType === 'columnList' || data.pageType === 'module6') {
        // political 政情快递
        // twoInstitutes 两院咨询
        const res = await $api.news.getNewsDetail({
          id: data.id
        })
        var interlinkage = sessionStorage.getItem('interlinkage') || ''
        if (interlinkage === res.data.externalLinks) {
          router.push({ path: '/newsList5', query: { title: '资讯' } })
          return
        }
        if (res.data.externalLinks !== null) {
          sessionStorage.setItem('interlinkage', res.data.externalLinks)
          window.location.href = res.data.externalLinks
          return
        }
        data.politicalData = res.data
      } else if (pageArr.includes(data.pageType)) {
        // countryside 乡村振兴
        // community 青岛社区民意
        // representative 代表风采
        // numberApp 数字应用
        const res = await $api.news.getSpecialsubjectnewsInfo({
          id: data.id
        })
        data.politicalData = res.data
      } else if (data.pageType === 'announcement') { // 公告栏
        const res = await $api.news.getAnnouncement({ id: data.id })
        data.politicalData = res.data
      } else if (data.pageType === 'photograpr') { // 随手拍详情
        const res = await $api.news.photograprDetails({ id: data.id })
        data.representativeDetails = res.data
      } else if (data.pageType === 'listType') { // 首页专题列表详情
        const res = await $api.news.getSpecialsubjectRelateinfo({ id: data.id, relateType: '53' })
        data.politicalData = res.data
      }
    }
    // 获取评论列表
    const getCommentList = async () => {
      const res = await $api.general.getCommentList({
        pageNo: 1,
        pageSize: 99,
        keyId: data.id,
        type: '',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      console.log('获取评论列表===>>', res)
      var list = res ? res.data || [] : []
      data.commentCount = res ? res.total : 0
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          var item = {}
          var itemData = _eItem
          item.hasDelete = itemData.createBy === data.user.id
          item.createBy = itemData.createBy
          item.id = itemData.id || ''
          item.extend = itemData.extend || ''
          item.url = itemData.userHeadImg || '../../../images/icon_default_user.png'
          item.name = itemData.userName || '匿名用户'
          item.createDate = itemData.createDate || ''
          item.content = itemData.content
          item.fabulousCount = itemData.fabulousCount
          item.commentCount = itemData.commentCount
          item.isFabulous = itemData.isFabulous === '1'
          // 评论中的图片
          var resultBatchAttach = itemData.filePathList || []
          var resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
          for (var p = 0; p < resultBatchAttachLength; p++) {
            var attachpath = resultBatchAttach[p].fullUrl || ''
            var resultBatchAttachItem = {
              url: attachpath
            }
            item.nAttach.push(resultBatchAttachItem)
          }
          item.commentList = []
          var Commentlist = itemData.children || []
          var CommentlistLength = Commentlist ? Commentlist.length : 0
          for (var j = 0; j < CommentlistLength; j++) {
            var item2 = {}
            var itemData2 = Commentlist[j]
            item2.hasDelete = itemData2.createBy === data.user.id
            item.createBy = itemData2.createBy
            // id
            item2.id = itemData2.id || ''
            // id
            item2.extend = itemData2.extend || ''
            // 来源
            item2.url = itemData2.userHeadImg || ''
            // 用户头像
            item2.name = itemData2.userName || '匿名用户'
            // 用户名
            item2.createTime = itemData2.dateDetail || ''
            item2.content = itemData.content
            // 是否审核,0待审核，1审核通过，2审核不通过
            item2.nAttach = []
            // 评论中的图片
            const resultBatchAttach = itemData2.filePathList || []
            const resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
            for (var k = 0; k < resultBatchAttachLength; k++) {
              const attachpaths = resultBatchAttach[k].fullUrl || ''
              const resultBatchAttachItem = {
                url: attachpaths
              }
              item2.nAttach.push(resultBatchAttachItem)
            }
            item.commentList.push(item2)
          }
          data.commentList.push(item)
        })
      }
    }
    // 获取点赞列表
    const getFabulousList = async () => {
      const res = await $api.general.getFabulousList({
        pageNo: 1,
        pageSize: 100,
        keyId: data.id,
        type: '25',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      if (res) {
        var list = res ? res.data || [] : []
        var dataLength = list ? list.length : 0
        if (list) {
          data.menuList = []
          for (var i = 0; i < dataLength; i++) {
            var item = {}
            var itemData = list[i]
            item.id = itemData.createBy // id
            item.name = itemData.userName // 标题
            item.url = itemData.headImg // 图标地址
            data.menuList.push(item)
          }
        }
      }
      console.log('获取点赞列表===>>', res)
    }
    // 点击评论中的评论框
    const openMoreComment = (_item, _index) => {
      data.pingData = _item || ''
      data.pingIndex = _index || ''
      if (_item) {
        data.inputInfo = { value: '', placeholder: '回复' + _item.name, name: '评论', btn: '回复' }
      } else {
        data.inputInfo = { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送' }
      }
      // that.inputFocus = true
    }
    // 发送
    const mgcHandle = async () => {
      if (!data.inputInfo.value) {
        return
      }
      var url = 'comment/save'
      var params = {
        content: data.inputInfo.value,
        createBy: data.user.id,
        commentPid: data.pingData ? data.pingData.id : '0',
        keyId: data.id,
        attach: '',
        extend: '1',
        type: '25',
        areaId: '370200'
      }
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        data.commentList = []
        getCommentList()
        data.inputInfo.value = ''
      } else {
        Toast('请求失败。')
      }
    }
    // 点赞与取消点赞
    const downLike = async (item) => {
      console.log('item===========', item)
      item.isFabulous = !item.isFabulous
      if (item.isFabulous) {
        item.fabulousCount++
        var saveres = await $api.general.saveFabulous({
          keyId: item.id,
          type: '101',
          areaId: data.user.areaId
        })
        if (saveres) {
          Toast('点赞成功')
        }
      } else {
        item.fabulousCount--
        var delres = await $api.general.delFabulous({
          keyId: item.id,
          type: '101',
          areaId: data.user.areaId
        })
        if (delres) {
          Toast('取消点赞成功')
        }
      }
    }
    const addCommentEvent = (value) => {
      data.commentOldAllList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      data.inputBox.getStats()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), $general, openMoreComment, route, dayjs, onRefresh, downLike, onClickLeft, addCommentEvent, openInputBoxEvent, freshState, annexClick, mgcHandle }
  }
}
</script>
<style lang="less">
.surveyComentInfo {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }
  .n_details_survey {
    width: 100%;
    .n_details_survey_bg {
      width: 100%;
      height: 350px;
      background: #000;
      background-size: 100% 100% !important;
      overflow: hidden;
      position: relative;
      .n_details_survey_bgooo {
        overflow: hidden;
        width: 100%;
        height: 100%;
        background: #0000005b;
      }
      .n_details_survey_content {
        width: 100%;
        height: 30%;
        background: #fff;
        border-radius: 20px 20px 0 0;
        position: absolute;
        bottom: 0;
        padding: 10px;
        box-sizing: border-box;
      }
      .n_details_survey_top {
        width: 100%;
        height: 30px;
        color: #a8a8a8;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .n_details_survey_top_time {
          font-size: 14px;
        }
        .n_details_survey_top_text {
          font-size: 14px;
        }
      }
      .n_details_survey_title {
        width: 100%;
        height: 30px;
        font-size: 20px;
        font-weight: 700;
        color: #fff;
        margin: 40px 10px 0;
      }
    }
  }
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }
  .n_details_content {
    margin: 10px;
    img {
      width: 100%;
    }
    > p {
      margin: 15px 0;
      font-size: 16px !important;
      line-height: 28px !important;
      span {
        font-size: 16px !important;
        line-height: 28px !important;
      }
    }
  }
  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }
  .n_details_more_box > .flex_box {
    width: 40%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    text-align: right !important;
    align-items: right;
  }
  .n_details_time {
    font-size: 12px;
    color: #666;
    width: 100%;
    margin-top: 10px;
  }
  .n_details_name {
    color: #666;
    width: 100%;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .survey_comment {
    width: 100%;
    background: #fff;
    margin-bottom: 80px;
    .survey_comment_title {
      width: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
    }
    .survey_comment_fw {
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-weight: 700;
    }
    .survey_comment_bg {
      width: 5px;
      height: 16px;
      background: #3894ff;
      margin: 0 10px;
    }
    .survey_comment_list {
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      border-bottom: 1px solid #e8e8e8;
      .survey_comment_list_but {
        box-sizing: border-box;
        width: 100%;
        .survey_comment_list_but_title {
          font-weight: 700;
          margin: 10px 0;
          font-size: 18px;
        }
        .survey_comment_list_but_detail {
          font-size: 14px;
          text-indent: 2em;
          margin-top: 10px;
        }
      }
      .survey_comment_list_top {
        display: flex;
        align-items: center;
        width: 100%;
        height: 50px;
        .survey_comment_list_top_img {
          width: 40px;
          height: 90%;
          border-radius: 3px;
          overflow: hidden;
          margin: 0 5px;
          > img {
            width: 100%;
            height: 100%;
          }
        }
        .survey_comment_list_top_cen {
          height: 100%;
          width: calc(100% - 30px);
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .survey_comment_list_top_name {
          }
          .survey_comment_list_top_time {
            width: 100%;
            color: #c2c2c2;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .survey_comment_list_top_time_left {
              font-size: 14px;
            }
            .survey_comment_list_top_time_right {
              width: 20%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin: 0 10px;
              .survey_comment_list_top_time_right_like {
                font-size: 14px;
                .bg {
                  color: #3894ff;
                }
              }
              .survey_comment_list_top_time_right_commentNumber {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>

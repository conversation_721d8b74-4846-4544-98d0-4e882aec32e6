<template>
  <div class="pie_page">
    <div class="pie"
         :id="id"></div>
  </div>

</template>
<script>
// import { Toast } from 'vant'
// import { useRoute } from 'vue-router'
import { onMounted, reactive, toRefs, nextTick } from 'vue'
import * as echarts from 'echarts'
export default {
  name: 'radarChart1',
  props: ['id', 'listData'],
  setup (props) {
    const data = reactive({})
    var myChart = null
    onMounted(() => {
      nextTick(() => {
        myChart = echarts.init(document.getElementById(props.id))
        pieBtn()
      })
      // 让图表跟随屏幕自动的去适应
      window.addEventListener('resize', function () {
        myChart.resize()
        pieBtn()
      })
    })
    const pieBtn = () => {
      var radarListData = props.listData
      // const radarListData = listData.sort((a, b) => {
      //   return b.num - a.num
      // })
      var textVal = ''
      radarListData.forEach(_item => {
        if (_item.num === 10) {
          if (_item.name === '履职指数') {
            textVal = '发圈型代表\n'
          } else if (_item.name === '监督能力') {
            textVal += '监督型代表\n'
          } else if (_item.name === '调查研究') {
            textVal += '调研型代表\n'
          } else if (_item.name === '学习能力') {
            textVal += '学习型代表\n'
          } else if (_item.name === '为民服务') {
            textVal += '为民型代表\n'
          }
        }
      })
      const allNumTen = radarListData.every(function (item) {
        return item.num === 10
      })
      if (allNumTen) {
        textVal = '全能达人'
      }
      console.log('textVal------', textVal)
      var option = {
        title: {
          text: textVal,
          top: 'center',
          left: 'center',
          // x: '37%',
          // y: '40%',
          textStyle: {
            padding: 20,
            fontSize: 16,
            color: '#3894FF'
          }
        },
        radar: [{
          indicator: radarListData,
          center: ['50%', '55%'], // 控制雷达图的位置
          radius: 80, // 控制雷达图的大小
          startAngle: 90,
          splitNumber: 4,
          shape: 'circle',
          name: {
            formatter: function (value, indicator) {
              // var npercent = indicator.num;
              // var percent = npercent / nsum * 100;
              // return '{a|' + value + '}{b|\n' + percent +'}{c|%}'
              return '{a|' + indicator.num + '}\n{b|' + value + '}'
            },
            rich: {
              a: {
                color: '#3894FF',
                fontSize: 12,
                align: 'center'
              },
              b: {
                fontSize: 14,
                fontWidth: 'bold',
                color: '#666666',
                padding: 5
              },
              c: {
                fontSize: 16,
                color: '#000'
              }
            },
            textStyle: {
              color: '#fff'
            }
          },
          splitArea: {
            areaStyle: {
              color: '#F7F8F9',
              shadowBlur: 0
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D7E0E7'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#D7E0E7',
              width: 1 // 分隔线线宽
            }
          }
        }
        ],
        series: [{
          name: '雷达图',
          type: 'radar',
          symbolSize: 8,
          symbol: 'circle',
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#3894FF'
            }, {
              offset: 1,
              color: '#3894FF'
            }], false),
            width: 1
          },
          itemStyle: {
            color: '#fff',
            borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#3894FF'
            }, {
              offset: 1,
              color: '#3894FF'
            }], false),
            borderWidth: 2,
            opacity: 1
          },
          areaStyle: {
            normal: {
              color: 'rgba(56,148,255,0.5)'
            },
            emphasis: {
              color: 'rgba(56,148,255,0.5)'
            }
          },
          // lineStyle: {
          //   normal: {
          //     color: '#f9d400',
          //     type: 'solid',
          //     width: 0
          //   },
          //   emphasis: {}
          // },
          data: [{
            value: radarListData.map(item => item.num),
            label: {
              show: false
            }
          }]
        }]
      }
      nextTick(() => {
        myChart.setOption(option)
      })
    }
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less">
.pie_page {
  text-align: center;
  .pie_mark {
    font-size: 20px;
    font-weight: bold;
  }
}
.pie {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
</style>

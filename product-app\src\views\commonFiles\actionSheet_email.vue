<template>
  <div class="actionSheetEmail">
    <div class="box_warp T-flexbox-vertical">
      <div class="header_box flex_box flex_align_center">
        <div @click="cancelActionSheetEmail" class="header_cancel" :style="$general.loadConfiguration(-3)">取消</div>
        <div class="header_hint flex_placeholder" :style="$general.loadConfiguration(-1)">{{ title }}</div>
        <div @click="itemSelect(false)" class="header_ok" :style="$general.loadConfiguration(-3) + 'color:' + appTheme">确定
        </div>
      </div>
      <div style="margin: 0 15px;" class="clouddisk_after van-hairline--top"></div>
      <div class="main_box flex_placeholder">
        <template v-for="(item, index) in listData" :key="index">
          <ul class="add_box" v-if="!item.hide">
            <li class="add_item">
              <!--标题提示-->
              <div class="add_title_box flex_box">
                <div v-if="!item.noRequired" class="add_required">*</div>
                <div :style="$general.loadConfiguration(-1)" class="add_title">{{ item.label }}</div>
              </div>
              <!--输入框-->
              <div class="add_content_box flex_box" :style="$general.loadConfiguration(-3)">
                <template v-if="item.type == 'text'">
                  <input v-model="item.value" :ref="item.key" class="inherit add_content_input flex_placeholder"
                    type="text" :placeholder="item.hint" />
                </template>
                <template v-if="item.type == 'textarea'">
                  <textarea v-model="item.value" :ref="item.key"
                    class="inherit add_content_input add_content_textarea flex_placeholder" type="text"
                    :placeholder="item.hint"></textarea>
                </template>
              </div>
            </li>
          </ul>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Toast } from 'vant'
export default {
  name: 'actionSheetEmail',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: {
    nBaseType: {
      type: String,
      default: ''
    },
    operateIds: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    baseTitle: {
      type: String,
      default: ''
    },
    baseType: {
      type: String,
      default: ''
    },
    baseData: {
      type: Array,
      default: () => []
    }
  },
  emits: ['cancelActionSheetEmail'],
  setup (props, { emit }) {
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: props.title || route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      show: false,
      nBaseType: props.nBaseType, // 是在个人还是处室
      operateIds: props.operateIds, // 当前操作数据ids
      listData: [
        // eslint-disable-next-line no-useless-escape
        { type: 'text', value: '', regex: '^[A-Za-z0-9]+([_\.][A-Za-z0-9]+)*@([A-Za-z0-9\-]+\.)+[A-Za-z]{2,6}$', key: 'toMailAddr', label: '邮箱地址', hint: '请输入地址', noRequired: false },
        { type: 'text', value: '', key: 'subject', label: '邮箱主题', hint: '请输入主题', noRequired: false },
        { type: 'textarea', value: '', key: 'message', label: '邮箱内容', hint: '请输入内容', noRequired: false }
      ] // 列表数据

    })
    onMounted(() => {
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    const cancelActionSheetEmail = () => {
      emit('cancelActionSheetEmail', false)
    }

    // 点击 item
    const itemSelect = async (_submit) => {
      for (var i = 0; i < data.listData.length; i++) {
        var nItem = data.listData[i]
        if (!nItem.noRequired && !nItem.hide) { // 没有不必填 就是必填并且没有隐藏
          if (!$general.trim(nItem.value)) {
            Toast((nItem.hint || nItem.label))
            return
          }
          if (nItem.regex) {
            var regex = new RegExp(nItem.regex)
            if (!regex.test(nItem.value)) {
              Toast('请输入正确的' + nItem.label)
              return
            }
          }
        }
      }
      // 先设置 接口和默认参数
      var postUrl = window.location.origin + '/filestore' + '/' + (data.nBaseType) + '/sendFileMail?'
      var postData = {
        fileId: data.operateIds
      }
      // 再添加 输入的参数
      // eslint-disable-next-line no-redeclare
      for (var i = 0; i < data.listData.length; i++) {
        const nItem = data.listData[i]
        if (nItem.notUpdate || nItem.hide) { // 不增加这个字段或者隐藏的
          continue
        }
        postData[nItem.key] = nItem.value
      }
      // eslint-disable-next-line no-undef
      if (!_submit) {
        // T.nPrompt({
        //   onlyName: 'up_dialog_send_email',
        //   msg: '确定发送吗？'
        // }, function (ret, err) {
        //   if (ret.buttonIndex == 1) {
        //     that.itemSelect(true)
        //   }
        // })
        Dialog.confirm({
          message: '确定发送吗？',
          confirmButtonColor: data.appTheme
        }).then(() => {
          itemSelect(true)
          // on confirm
        }).catch(() => {
          // on cancel
        })
        console.error(JSON.stringify(postData))
        return
      }
      const ret = await $api.cloudDisk.generalPost(postUrl, postData)
      if (ret && ret.code === 1) {
        Toast('操作成功')
        setTimeout(() => {
          cancelActionSheetEmail()
        }, 800)
      } else {
        Toast(ret ? (ret.message || ret.prompt || '操作失败') : ('网络错误'))
      }
    }

    // 点击 取消
    const itemCancel = () => {

    }

    return { ...toRefs(data), $general, confirm, cancelActionSheetEmail, itemCancel, itemSelect }
  }
}
</script>
<style lang="less" scoped>
.actionSheetEmail {
  #app {
    height: 100%;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  #app .van-overlay {
    background-color: rgba(0, 0, 0, 0);
  }

  #app .van-action-sheet {
    height: 80%;
  }

  .box_warp {
    background: #fff;
    border-radius: 10px 10px 0px 0px;
    width: 100%;
    height: 84%;
    height: 700px;
  }

  .header_box {
    background-color: #fff;
    min-height: 45px;
  }

  .header_cancel {
    padding: 10px 15px;
    color: #666666;
  }

  .header_hint {
    font-weight: 600;
    color: #333333;
    text-align: center;
  }

  .header_ok {
    padding: 10px 15px;
    font-weight: 600;
  }

  .add_item {
    padding: 14px 15px 0;
  }

  .add_title_box {
    position: relative;
  }

  .add_required {
    position: absolute;
    color: #ff0000;
    left: -7px;
    top: 0;
    font-size: 14px;
    line-height: 1.26;
  }

  .add_title {
    font-weight: 400;
    color: #333333;
    line-height: 1.26;
  }

  .add_content_box {
    margin-top: 10px;
  }

  .add_content_input {
    background: #f8f9fa;
    border-radius: 2px;
    padding: 6px 10px;
    color: #333;
  }

  .add_content_input::-webkit-input-placeholder {
    color: #999;
  }

  .add_content_textarea {
    min-height: 200px;
  }
}
</style>

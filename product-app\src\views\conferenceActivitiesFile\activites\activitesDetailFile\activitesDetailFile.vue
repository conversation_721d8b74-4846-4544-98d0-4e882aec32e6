<template>
  <div class="activitesDetailFile">
    <template v-if="firstAjax">
      <div v-for="(item,index) in msgs"
           :key="index">
        <div v-if="msgMore.has?!msgMore.is?index<msgMore.num:true:true"
             class="msgs_item flex_box van-hairline--bottom">
          <div :style="general.loadConfiguration(-1)"
               class="msgs_hint"
               v-html="item.hint"></div>
          <div :style="general.loadConfiguration(-1)"
               class="flex_placeholder msgs_value"
               v-html="item.value"></div>
        </div>
      </div>
      <div v-if="msgMore.has"
           :style="general.loadConfiguration(-2)"
           @click="msgMore.is = !msgMore.is;"
           class="flex_box flex_align_center flex_justify_content"
           style="padding: 11px 0;color: #999">
        <van-icon :class="msgMore.is?'arrowClose':'arrowOpen'"
                  :size="((appFontSize-3)*0.01)+'rem'"
                  :color="'#999'"
                  name="arrow"
                  style="margin-right:5px;"></van-icon>{{msgMore.is?'收起':'展开'}}
      </div>

      <div class="n_details_content"
           :style="general.loadConfiguration(-1)+'border-top:0.1rem solid #F4F4F4;margin-left: 20px;'">
        <div class="inherit"
             style="font-weight: bold;">活动内容:</div>
        <div class="inherit"
             style="color: #333333;line-height: 2.2;"
             v-html="content"></div>
      </div>
    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
    <!--返回顶部-->
    <ul v-if="footerBtnsShow&& !dialogShow"
        class="footer_btn_box"
        :style="'bottom:'+(44+30)+'px;'">
      <transition name="van-slide-right">
        <div v-if="footerMore.show">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((general.appFontSize+25)*0.01)+'rem'"
                        name="upgrade"></van-icon>
            </div>
            <div v-if="item.type == 'btn'"
                 class="van-button-box"
                 :style="general.loadConfiguration(-3)">
              <van-button loading-type="spinner"
                          :loading-size="((general.appFontSize)*0.01)+'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)"
                          :icon="item.icon">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </transition>
      <div v-if="title && footerBtns.length != 0"
           @click="footerMore.show = !footerMore.show;"
           :style="general.loadConfigurationSize(15)+'border-radius:50%;background:'+appTheme"
           class="footer_item flex_box flex_align_center flex_justify_content">
        <van-icon :size="((general.appFontSize+2)*0.01)+'rem'"
                  color="#FFF"
                  :name="footerMore.show?'arrow-down':'ellipsis'"></van-icon>
      </div>
    </ul>
    <!--不为一级页面时 适配底部条-->
    <footer :style="{paddingBottom:(44)+'px'}"></footer>
    <van-action-sheet v-model:show="showSignIn"
                      :description="description"
                      :actions="actions"
                      @select="onSelect"
                      cancel-text="取消" />
    <van-overlay :show="dialogShow"
                 @click="showSignIn = false">
      <van-dialog v-model:show="dialogShow"
                  :title="dialogTitle"
                  :width="'288px'"
                  :overlay="false"
                  @confirm="confirm"
                  show-cancel-button>
        <div class="inherit"
             style="padding: 20px 0 20px 0;">
          <!-- 密码输入框 -->
          <van-password-input :value="value"
                              :mask="false"
                              :length="signlength"
                              :focused="showKeyboard"
                              @focus="showKeyboard = true" />
        </div>

      </van-dialog>
      <!-- 数字键盘 -->
      <van-number-keyboard v-model="value"
                           :show="showKeyboard"
                           :z-index="'99'"
                           @blur="showKeyboard = false" />
    </van-overlay>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Skeleton, NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, Toast, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'

export default {
  name: 'activitesDetailFile',
  components: {
    [Skeleton.name]: Skeleton,
    [Dialog.Component.name]: Dialog.Component,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const general = inject('$general')
    const appTheme = inject('$appTheme')
    const isShowHead = inject('$isShowHead')
    const route = useRoute()
    const data = reactive({
      appTheme: appTheme,
      isShowHead: isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      total: 0,
      showSkeleton: false,
      firstAjax: true,
      scrollTop: 0, // 页面划动距离
      msgs: [
        // { hint: '活动名称:', value: '政协第十二届第十五次常委会议-开 幕式' },
        // { hint: '活动类型:', value: '视察' },
        // { hint: '组织部门:', value: '市城管执法局' },
        // { hint: '报名截止:', value: '2021-12-10 16:00' },
        // { hint: '签到时间:', value: '2021-12-10 16:00<br />至2021-12-10 16:00' },
        // { hint: '活动时间:', value: '2021-12-10 16:00<br />至2021-12-10 16:00' },
        // { hint: '活动地点:', value: '青岛王朝大酒店' }
      ],
      msgMore: { has: true, is: false, num: 4 }, // has是否展示 展开收起  is是否展开  num多少条收起
      title: '',
      content: '',
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部   btn为按钮
      footerMore: { show: true }, // 展开附加按钮
      showSignIn: false, // 签到弹框
      actions: [{ name: '签到口令', color: '#3088fe' }], // 签到选择二维码还是口令
      description: '签到方式',
      dialogShow: false,
      dialogTitle: '',
      showKeyboard: true,
      value: '',
      signlength: 4
    })

    onMounted(() => {
      onRefresh()
    })
    watch(() => data.value, (newVal) => {
      console.log(newVal)
      if (newVal.length > data.signlength) {
        Toast(`签到口令为${data.signlength}位`)
        data.value = data.value.slice(0, 4)
      }
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getDetails()
    }
    const getDetails = async () => {
      var res = []
      res = await $api.conferenceActivitiesFile.activityupgradeetActivityDetail({
        id: data.id
      }) // 详情
      var { data: list } = res
      console.log('🚀 ~ file: activitesDetailFile.vue ~ line 53 ~ getDetails ~ res', list)
      data.showSkeleton = false
      data.firstAjax = true
      var info = list || {}
      data.title = info.meetName || ''// 标题
      data.content = info.content || ''
      data.id = info.id || ''
      data.msgs = []
      data.msgs.push({ hint: '活动名称:', value: data.title })
      data.msgs.push({ hint: '活动类型:', value: info.meetTypeName || '' })
      data.msgs.push({ hint: '活动时间:', value: (info.meetStartTime ? dayjs(info.meetStartTime).format('YYYY-MM-DD HH:mm') : '') + '<br/>至' + (info.meetEndTime ? dayjs(info.meetEndTime).format('YYYY-MM-DD HH:mm') : '') })
      data.msgs.push({ hint: '活动地点:', value: info.address || '' })
      data.msgs.push({ hint: '组织部门:', value: info.organizerName || '' })
      data.msgs.push({ hint: '报名截止:', value: info.signEndTime ? dayjs(info.signEndTime).format('YYYY-MM-DD HH:mm') : '' })
      data.msgs.push({ hint: '签到时间:', value: (info.meetSignBeginTime ? dayjs(info.meetSignBeginTime).format('YYYY-MM-DD HH:mm') : '') + '<br/>至' + (info.meetSignEndTime ? dayjs(info.meetSignEndTime).format('YYYY-MM-DD HH:mm') : '') })

      var collection = info.collection// 是否补录 补录的数据不需要报名签到和请假
      var signUpItem = { name: '报名', type: 'btn', click: 'signUp', icon: 'edit', color: '', loading: false, disabled: false }

      var signInItem = { name: '签到', type: 'btn', click: 'signIn', icon: 'sign', color: '', loading: false, disabled: false }
      var leaveItem = { name: '请假', type: 'btn', click: 'leave', icon: 'tosend', color: '', loading: false, disabled: false }
      var materaItem = { name: '资料', type: 'btn', click: 'matera', icon: 'newspaper-o', color: '', loading: false, disabled: false }
      var signUpBtn = general.getItemForKey(signUpItem.click, data.footerBtns, 'click')// 报名
      var signInBtn = general.getItemForKey(signInItem.click, data.footerBtns, 'click')// 签到
      var leaveBtn = general.getItemForKey(leaveItem.click, data.footerBtns, 'click')// 请假
      var materaItemBtn = general.getItemForKey(materaItem.click, data.footerBtns, 'click')// 资料
      general.delItemForKey(signUpBtn, data.footerBtns, 'click')
      general.delItemForKey(signInBtn, data.footerBtns, 'click')
      general.delItemForKey(leaveBtn, data.footerBtns, 'click')
      general.delItemForKey(materaItemBtn, data.footerBtns, 'click')
      if (!collection) { // 不是补录 就有操作
        var signUp = info.signUp// 是否可以报名
        var signUpList = info.signUpList || []// 所有报名人
        var signUpIn = general.getItemForKey(data.user.id, signUpList, 'userId')// 当前人是否已报名
        if (signUp || signUpIn) {
          if (signUpIn) {
            signUpItem.name = '已报名'
            signUpItem.color = '#ccc'
            signUpItem.disabled = true
          }
          data.footerBtns.push(signUpItem)
        }
        var signIn = info.signIn// 是否可以签到
        var signInList = info.signInList || []// 所有签到人
        var signInIn = general.getItemForKey(data.user.id, signInList, 'userId')// 当前人是否已签到
        if (signIn || signInIn) {
          if (signInIn) {
            signInItem.name = '已签到'
            signInItem.color = '#ccc'
            signInItem.disabled = true
          }
          data.footerBtns.push(signInItem)
        }
        var leave = info.leave// 是否可以请假
        var leaveState = info.leaveState || {}
        if (leave) {
          // status 0审核中  1通过
          var leaveName = [{ name: '请假审核中' }, { name: '请假已通过' }, { name: '请假不通过' }]
          leaveItem.name = general.isParameters(leaveState.status) ? leaveName[leaveState.status].name : '请假'
          leaveItem.leaveId = leaveState.id || ''
          data.footerBtns.push(leaveItem)
        }
      }
      if (info.materiainfos && info.materiainfos.length !== 0) {
        data.footerBtns.push(materaItem)
      }
    }

    // 底部按钮事件
    const footerBtnClick = (_item) => {
      console.error(JSON.stringify(_item))
      switch (_item.click) {
        case 'signUp':// 报名
          $api.conferenceActivitiesFile.activityaddMySignUp({
            activityId: data.id
          }).then(res => {
            _item.loading = false
            if (!res) {
              Toast('失败,请重试!')
            } else {
              var code = res.errcode || 0
              if (code === 200) {
                Toast('参会成功')
                getDetails()
              } else {
                Toast('失败,请重试!')
              }
            }
          })
          break
        case 'signIn':// 签到
          data.showSignIn = true
          break
        case 'leave':// 请假
          router.push({ name: 'activitesLeave', query: { title: '请假', id: data.id, paramType: 'addLeave', leaveId: _item.leaveId } })
          break
        case 'matera':// 会议资料
          router.push({
            name: 'activitesfileList', query: { relateType: _item.click, id: data.id, title: _item.name }
          })
          break
      }
    }

    const onSelect = (item) => {
      data.showSignIn = false
      switch (item.name) {
        case '签到口令':
          data.dialogTitle = '会议签到码'
          data.value = ''
          data.dialogShow = true
          data.showKeyboard = true
          break
      }
    }

    const confirm = async () => {
      console.log(data.value)
      if (!data.value || data.value.length !== 4) {
        Toast('签到口令不正确')
        return
      }
      const res = await $api.conferenceActivitiesFile.activityaddMySignIn({
        activityId: data.id,
        signInType: 'signInCommand',
        command: data.value
      })
      var code = res.errcode || 0
      if (code === 200) {
        Toast('签到成功')
        onRefresh()
      } else {
        Toast(res.errmsg || res.data)
      }
    }

    return { ...toRefs(data), dayjs, general, appTheme, isShowHead, footerBtnClick, onSelect, confirm }
  }
}
</script>

<style lang="less" >
.activitesDetailFile {
  width: 100%;
  height: 100vh;
  background: #fff;

  .msgs_item {
    padding: 16px 14px;
  }
  .msgs_hint {
    margin-right: 20px;
    font-weight: 500;
    color: #333333;
    line-height: 1.5;
  }
  .msgs_value {
    font-weight: bold;
    color: #333333;
    line-height: 1.5;
  }
}
</style>

<template>
  <div class="actionSheetPerson">
    <div class="T-flexbox-vertical flex_justify-content_end">
      <transition name="van-slide-up">
        <template v-if="show">
          <div class="box_warp T-flexbox-vertical">
            <div class="header_box flex_box flex_align_center">
              <div @click="cancelActionSheetPerson" class="header_cancel" :style="$general.loadConfiguration(-3)">取消
              </div>
              <div class="header_hint flex_placeholder" :style="$general.loadConfiguration(-1)">{{ title }}</div>
              <div @click="itemSelect(false)" class="header_ok"
                :style="$general.loadConfiguration(-3) + 'color:' + appTheme">确定</div>
            </div>
            <div style="margin: 0 15px;" class="clouddisk_after van-hairline--top"></div>
            <!--搜索-->
            <div id="search" class="search_box" :style="$general.loadConfiguration()">
              <div class="search_warp flex_box" style="height: auto;background: transparent;padding-left: 0;">
                <form class="flex_placeholder flex_box flex_align_center search_input" style="padding-left: 10px;"
                  action="javascript:return true;"> <input id="searchInput" class="flex_placeholder"
                    :style="$general.loadConfiguration(-1)" :placeholder="seachPlaceholder" maxlength="100"
                    type="search" @keyup.enter="btnSearch()" v-model="seachText" />
                  <div v-if="seachText" @click="seachText = ''; btnSearch();"
                    class="search_btn flex_box flex_align_center flex_justify_content">
                    <van-icon :size="((appFontSize)) + 'px'" :color="'#ccc'" :name="'clear'"></van-icon>
                  </div>
                </form>
                <div @click="btnSearch()" class="search_btn flex_box flex_align_center flex_justify_content"
                  :style="'background:' + appTheme">
                  <van-icon :size="((appFontSize - 2)) + 'px'" color="#FFF" name="search"></van-icon>
                </div>
              </div>
            </div>
            <!--有选择的人员-->
            <template v-if="selectUser.length">
              <div :style="$general.loadConfiguration(-2) + 'padding:4px 15px;font-weight: 600;'">
                已选{{ selectUser.length ? '（' + selectUser.length + '人）' : '' }}</div>
              <div style="padding: 5px 14px;">
                <div class="component_selectuser_warp flex_box flex_align_center">
                  <div class="flex_placeholder component_selectuser_box">
                    <div v-for="(item, index) in selectUser" :key="index" class="component_selectuser_item">
                      <van-icon v-if="!item.notDel" @click.stop="$general.delItemForKey(index, selectUser)"
                        class="component_selectuser_del" :size="((appFontSize)) + 'px'" color="#7F7F7F"
                        name="clear"></van-icon>
                      <img :style="$general.loadConfigurationSize(22) + 'margin-right:10px;'" :src="item.url" alt=""
                        srcset="">
                      <div class="component_selectuser_name"
                        :style="$general.loadConfiguration(-2) + '-webkit-line-clamp: ' + (maxrow || 3) + ';'">{{ item.name }}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <zy-selectuser :data="selectUser"
                               maxrow="2"
                               @click="clickUser"></zy-selectuser> -->
              </div>
            </template>
            <!--级别选择 -->
            <div v-if="level.data.length != 0" class="flex-wrap flex_box">
              <div id="navigation" class="flex-con flex_placeholder">
                <div class="header_css">
                  <div v-for="(item, index) in level.data" :key="index" @click="clickLevel(item)"
                    :style="$general.loadConfiguration(-3)"
                    v-html="item.name + (index != level.data.length - 1 ? '&nbsp;&nbsp;>&nbsp;&nbsp;' : '')"></div>
                </div>
              </div>
              <div v-if="ifReturn && userList.length" class="checkAllBtn" @click="checkAll"
                :style="$general.loadConfiguration(-2) + 'color:' + appTheme">{{ hasCancelFlag &&
                  selectUser.length!=0?'取消':''}}全选</div>
            </div>
            <div class="flex_placeholder main_box">
              <div class="content">
                <template v-if="!ifSearch">
                  <div v-for="(item, index) in groupList" :key="index" class="group_item flex_box flex_align_center"
                    @click="clickGroup(item)">
                    <div class="group_name flex_placeholder" :style="$general.loadConfiguration(-1) + 'color: #333333;'">
                      {{ item.name }}</div>
                    <van-icon class="group_img" color="#666666" :size="((appFontSize - 2)) + 'px'" name="arrow"></van-icon>
                  </div>
                </template>
                <van-checkbox-group ref="checkboxGroup" v-model="selectIds">
                  <div v-for="(item, index) in userList" :key="index" class="user_item flex_box flex_align_center"
                    @click="clickUser(item)">
                    <img :style="$general.loadConfigurationSize(4) + 'margin-right:10px;'" :src="item.url" alt=""
                      srcset="">
                    <!-- <zy-photo :style="$general.loadConfigurationSize(4)+'margin-right:10px;'"
                              :key="item.refresh"
                              :data="item"></zy-photo> -->
                    <div class="flex_placeholder flex_box">
                      <div class="user_name" :style="$general.loadConfiguration(-2)" v-html="item.name"></div>
                      <div class="user_phone" :style="$general.loadConfiguration(-2)" v-html="item.mobile"></div>
                    </div>
                    <div style="background:rgba(0,0,0,0);width:30px;height:30px;margin-right:-20px;z-index: 99999;">
                    </div>
                    <van-checkbox v-if="ifReturn" :name="item.id" :icon-size="((appFontSize + 1)) + 'px'"
                      :checked-color="appTheme" shape="round"></van-checkbox>
                  </div>
                </van-checkbox-group>
              </div>
              <!--加载中提示 首次为骨架屏-->
              <div v-if="showSkeleton" class="notText">
              </div>
            </div>
          </div>
        </template>
      </transition>
    </div>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Toast } from 'vant'
export default {
  name: 'actionSheetPerson',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: {
    nBaseType: {
      type: String,
      default: ''
    },
    operateIds: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    baseTitle: {
      type: String,
      default: ''
    },
    baseType: {
      type: String,
      default: ''
    },
    baseData: {
      type: Array,
      default: () => []
    }
  },
  emits: ['cancelActionSheetPerson'],
  setup (props, { emit }) {
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: props.title || route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      show: false,
      userId: '', // 当前人id
      ifSearch: false, // 是否搜索中

      level: { value: 0, data: [{ name: '全部', id: 0 }] },

      groupSource: [], // 分组数据源
      groupList: [
        // {name:"湖南省",id:0}
      ],
      userList: [
        // {id:1,name:"超级管理员",url:"https://www.apicloud.com/image/png/be/03/be034b9b1daf1e992c801b59cc66b336.180x180.png",notDel:true},
      ],
      selectIds: [],
      // 选择的用户集合 id name url notDel[是否可以删除选择]
      selectUser: [
        // {id:1,name:"超级管理员",url:"../../../images/btn_home_sectors_h.png",notDel:true},
      ],
      ifReturn: false, // 是否返回人员
      ifYourself: true, // 是否不能选择当前用户
      ifReturnNull: false, // 是否能选择返回空 默认不能
      selectMax: 0, // 是否有选择限制为0不限制
      nBaseType: props.nBaseType, // 是在个人还是处室
      operateIds: props.operateIds, // 当前操作数据ids
      hasCancelFlag: true
    })
    onMounted(() => {
      data.show = true
      data.seachPlaceholder = '搜索人员名字'
      data.ifReturn = true// 是否返回用户
      data.selectUser = props.selectUser || []// 接收页面数据否则为空
      data.selectMax = props.selectMax || 0// 是否有选择限制为0不限制
      data.ifYourself = props.ifYourself// 是否不能选择当前用户
      data.level.data = [{ name: '全部', id: 0 }]
      // setTimeout(function () {
      //   navigation = new IScroll('#navigation', { scrollX: true, tap: true })
      // }, 0)
      getData()
    })
    // 获取列表数据
    const getData = async (_type) => {
      if (!_type) {
        data.pageNo = 1
      }
      data.ifSearch = !!data.seachText
      if (data.ifSearch) {
        data.level.data = [{ name: '全部', id: 0 }]
      }
      // 分组时只用获取一次并且不是搜索的时候
      if (data.level.data.length <= 1 && !data.ifSearch) {
        const ret = await $api.cloudDisk.generalGet(window.location.origin + '/filestore' + '/' + (data.nBaseType) + '/userGroupSearch?', {})
        data.showSkeleton = false
        var code = ret ? ret.code : ''
        var datas = code === 1 ? ret.attribute.result || [] : []
        if (!_type) { // 有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
          data.groupList = []
          data.userList = []
        }
        if (datas.length !== 0) {
          data.groupSource = datas
          data.groupList = []
          console.log(data.groupSource)
          showGroup(data.groupSource)
        }
      } else {
        var url = window.location.origin + '/filestore' + '/' + (data.nBaseType) + '/userSearch?'
        var postData = {
          pageNo: 1,
          pageSize: 9999,
          groupId: data.level.data[data.level.data.length - 1].id,
          provinceAreaId: sessionStorage.getItem('areaId') || ''
        }
        url = url + 'pageNo=1&pageSize=999&groupId=' + data.level.data[data.level.data.length - 1].id + '&provinceAreaId' + sessionStorage.getItem('areaId') || ''
        if (data.ifSearch) {
          postData = {
            search: data.seachText,
            provinceAreaId: sessionStorage.getItem('areaId') || ''
          }
          url = url + '&search=' + data.seachText
        }
        const ret = await $api.cloudDisk.generalGet(url, postData)
        data.showSkeleton = false
        const code = ret ? ret.code : ''
        const datas = code === 1 ? ret.attribute.result || [] : []
        if (!_type) { // 有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
          data.userList = []
          data.groupList = []
          if (!data.ifSearch) showGroup(data.groupSource)
        }
        if ($general.isArray(datas) && datas.length !== 0) {
          // 循环添加数据
          for (var i = 0; i < datas.length; i++) {
            var item = {}; var itemData = datas[i]
            item.id = itemData.id || ''// id
            item.name = itemData.userName || ''// 名字
            item.mobile = itemData.mobile || ''
            // if ($general.trim(data.seachText)) {
            //   item.name = item.name.replace(new RegExp(data.seachText, 'g'), '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>')
            // }
            item.areaId = itemData.areaId || ''
            item.url = itemData.headImg || '../../assets/img/icon_default_user.png'// 头像
            data.userList.push(item)
          }
        }
      }
    }
    // 展示分组
    const showGroup = (_list) => {
      var nowId = data.level.data[data.level.data.length - 1].id
      for (var i = 0; i < _list.length; i++) {
        var item = {}; var itemData = _list[i]
        item.id = itemData.id || ''// id
        item.sort = itemData.sort || ''// sort
        item.name = itemData.name || ''// 名字
        item.total = itemData.total || '0'// 人数
        var parentId = itemData.parentId || '0'
        var children = itemData.children || []
        // eslint-disable-next-line eqeqeq
        if (parentId == nowId) { // 是当前一级就添加到组里面
          data.groupList.push(item)
        } else {
          showGroup(children)
        }
      }
      data.groupList.sort(function (a, b) {
        return a.sort - b.sort
      })
    }
    // 点击级别
    const clickLevel = (_item) => {
      var nLevel = []
      for (var i = 0; i < data.level.data.length; i++) {
        var nItem = data.level.data[i]
        nLevel.push(nItem)
        if (nItem.id === _item.id) {
          data.level.data = nLevel
          // setTimeout(function () {
          //   navigation.refresh();
          //   navigation.scrollToElement($api.domAll("#navigation .header_css div")[that.level.data.length - 1], 200, true);
          // }, 100);
          getData()
          return
        }
      }
    }
    // 点击组
    const clickGroup = (_item) => {
      var nItem = { id: _item.id, name: _item.name }
      data.level.data.push(nItem)
      // setTimeout(function () {
      //   navigation.refresh();
      //   navigation.scrollToElement($api.domAll("#navigation .header_css div")[that.level.data.length - 1], 200, true);
      // }, 100);
      getData()
    }
    // 点击人
    const clickUser = (_item, _details) => {
      if (data.ifReturn && !_details) {
        if (data.ifYourself && data.user.id === _item.id) {
          Toast('不能选择自己！')
          return
        }
        var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
        // 在就删除这个
        if (nItem) {
          if (!nItem.notDel) { // 为非不可删除时才能删除 否则不能动
            $general.delItemForKey(nItem, data.selectUser, 'id')
          }
        } else {
          if (data.selectMax !== 0 && data.selectMax === data.selectUser.length) {
            Toast('最多只能选择' + data.selectMax + '人')
            return
          }
          data.selectUser.push(_item)
          // data.selectUser.push({ id: _item.id, name: $general.delHtmlTag(_item.name), url: _item.url })
        }
      } else {
        // $o.openPersonalDataDetails(_item);
      }
    }
    // 设置选中状态
    const isSelect = (_item) => {
      return $general.getItemForKey(_item.id, data.selectUser, 'id')
    }
    // 设置不可选的状态
    const isDisabled = (_item) => {
      if (data.ifYourself && data.user.id === _item.id) {
        return true
      }
      return $general.getItemForKey(_item.id, data.selectUser, 'id').notDel
    }
    // 是否是取消全选
    const checkAllHasCancel = () => {
      var hasCancel = true
      data.userList.forEach((_item, index) => {
        var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
        if (!nItem && !isDisabled(_item)) {
          hasCancel = false
        }
      })
      data.hasCancelFlag = hasCancel
      return hasCancel
    }
    watch(() => data.selectUser, (newName, oldName) => {
      data.selectIds = []
      data.selectUser.forEach(element => {
        data.selectIds.push(element.id)
      })
      checkAllHasCancel()
    }, { immediate: true, deep: true })
    // 点选全选
    const checkAll = () => {
      if (checkAllHasCancel()) {
        data.userList.forEach(function (_item, index) {
          var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
          if (nItem && !nItem.notDel) { // 为非不可删除时才能删除 否则不能动
            $general.delItemForKey(nItem, data.selectUser, 'id')
          }
        })
      } else {
        data.userList.forEach(function (_item, index) {
          var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
          if (!nItem) {
            if (data.selectMax !== 0 && data.selectMax >= data.selectUser.length) {
              Toast('最多只能选择' + data.selectMax + '人')
              return
            }
            if (!data.ifYourself || data.user.id !== _item.id) {
              data.selectUser.push(_item)
              // data.selectUser.push({ id: _item.id, name: $general.delHtmlTag(_item.name), url: _item.url })
            }
          }
        })
      }
    }
    // 搜索
    const btnSearch = () => {
      // this.$refs["btnSearch"].blur();
      getData(0)
    }
    // 点击 确定
    const itemSelect = async (_submit) => {
      if (!data.selectUser.length) {
        Toast('请先选择发送的用户')
        return
      }
      var receiveUserId = ''; var receiveUserName = ''; var receiveUserAccount = ''
      data.selectUser.forEach(function (_eItem) {
        receiveUserId += (receiveUserId ? ',,' : '') + _eItem.id
        receiveUserAccount += (receiveUserAccount ? ',,' : '') + _eItem.id
        receiveUserName += (receiveUserName ? ',,' : '') + _eItem.name
      })
      // 先设置 接口和默认参数
      var postUrl = window.location.origin + '/filestore' + '/' + (data.nBaseType) + '/sendFile?'
      var postData = {
        fileId: data.operateIds,
        type: 1,
        receiveUserId: receiveUserId,
        receiveUserName: receiveUserName,
        receiveUserAccount: receiveUserAccount
      }
      if (!_submit) {
        Dialog.confirm({
          message: '确定发送吗？',
          confirmButtonColor: data.appTheme
        }).then(() => {
          itemSelect(true)
          // on confirm
        }).catch(() => {
          // on cancel
        })
        console.error(JSON.stringify(postData))
        return
      }
      const ret = await $api.cloudDisk.generalPost(postUrl, postData)
      if (ret && ret.code === 1) {
        Toast('操作成功')
        setTimeout(() => {
          cancelActionSheetPerson()
          // T.sendEvent({ name: that.pageParam.onlyCallback, extra: { buttonIndex: 1 } });
          // that.show = false;
        }, 800)
      } else {
        Toast(ret ? (ret.message || ret.prompt) : ('操作失败'))
      }
      //
    }
    const cancelActionSheetPerson = () => {
      emit('cancelActionSheetPerson', false)
    }
    return { ...toRefs(data), $general, clickLevel, clickGroup, clickUser, isSelect, isDisabled, checkAll, btnSearch, itemSelect, cancelActionSheetPerson }
  }
}
</script>
<style lang="less" scoped>
.actionSheetPerson {
  #app {
    height: 100%;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .box_warp {
    background: #fff;
    border-radius: 10px 10px 0px 0px;
    width: 100%;
    height: 84%;
    height: 700px;
  }

  .main_box {
    overflow: hidden;
    overflow-y: auto;
  }

  .header_box {
    background-color: #fff;
    min-height: 45px;
  }

  .header_cancel {
    padding: 10px 15px;
    color: #666666;
  }

  .header_hint {
    font-weight: 600;
    color: #333333;
    text-align: center;
  }

  .header_ok {
    padding: 10px 15px;
    font-weight: 600;
  }

  .search_input {
    min-height: 31px;
    background: #f8f8f8;
    border-radius: 4px;
  }

  .search_btn {
    margin-left: 10px;
    min-height: 31px;
    width: 44px;
    border-radius: 4px;
  }

  .add_warp_n {
    background: #fff;
    box-sizing: border-box;
    padding: 5px 14px;
  }

  .user_box_n {
    white-space: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .user_item_n {
    text-align: center;
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    margin-top: 5px;
    max-width: 60px;
  }

  .user_item_n+.user_item_n {
    margin-left: 30px;
  }

  .user_name_n {
    color: #333;
    margin-top: 5px;
    line-height: 1.1;
  }

  .user_del {
    position: absolute;
    top: -5px;
    right: -1px;
    z-index: 99;
  }

  .switch_box {
    padding: 5px 15px;
  }

  .switch_item {
    color: #666666;
    line-height: 1.4;
    position: relative;
  }

  .switch_item_active {
    font-weight: 600;
    color: #333333;
  }

  .switch_item_active::after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    bottom: -4px;
    left: 22%;
    width: 56%;
    border-bottom-width: 3px;
    border-bottom-style: solid;
    border-bottom-color: inherit;
  }

  .switch_item+.switch_item {
    margin-left: 19px;
  }

  .flex-wrap {
    background: #fff;
    margin-top: 15px;
  }

  .flex-con {
    width: 0;
    overflow: hidden;
    position: relative;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
    height: 30px;
  }

  .header_css {
    white-space: nowrap;
    position: absolute;
    padding: 0 15px;
  }

  .header_css div {
    color: #999999;
    display: inline-block;
  }

  .header_css div:last-child {
    color: #333;
  }

  .header_css div:first-child {
    color: #999;
  }

  .checkAllBtn {
    margin: 0 22px 0 10px;
  }

  .user_item,
  .group_item {
    border-bottom: 1px solid rgba(63, 63, 63, 0.1);
    background: #fff;
    width: calc(100% - 32px);
    margin-left: 16px;
    padding: 12px 0;
    position: relative;
    box-sizing: border-box;
    color: #333333;
  }

  .user_name {
    color: #333333;
  }

  .user_phone {
    color: #666;
    margin-left: 19px;
  }
}
</style>

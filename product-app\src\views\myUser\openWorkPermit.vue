<template>
  <div class="openWorkPermit">
    <div class="nav">
      <div class="info-box">
        <div class="info-arae">
          {{Sys_isMember?detail.simpleName:'电子工作证'}}
          <!-- {{ '电子工作证' }} -->
        </div>
        <div v-if="showCode"
             class="qrcode">
          <van-image :src="qrcodeurl"
                     fit="contain"
                     width="4rem">
            <template v-slot:loading>
              <van-loading type="spinner"
                           size="0.25rem"></van-loading>
            </template>
          </van-image>
        </div>
        <div class="info-item"
             v-else>
          <div class="head-img">
            <img :src="detail.headImg"
                 alt="">
          </div>
          <div class="card-item">
            <div class="row-item">
              <div class="row-name">姓名</div>
              <div class="row-value">{{detail.userName}}</div>
            </div>
            <div style="display: flex;">
              <div class="row-item"
                   style="margin-right: 24px;">
                <div class="row-name"
                     style="width: 40px;">性别</div>
                <div class="row-value">{{detail.sex}}</div>
              </div>
              <div class="row-item"
                   v-if="Sys_isMember">
                <div class="row-name"
                     style="width: 40px;">民族</div>
                <div class="row-value">{{detail.nation}}</div>
              </div>
            </div>
            <div class="row-item"
                 v-if="Sys_isMember">
              <div class="row-name">代表团</div>
              <div class="row-value">{{detail.representerTeam}}</div>
            </div>
            <div class="row-item"
                 v-if="!Sys_isMember">
              <div class="row-name">部门</div>
              <div class="row-value">{{detail.officeName}}</div>
            </div>
            <div class="row-item">
              <div class="row-name">{{Sys_isMember?'单位及职务':'职务'}}</div>
              <div class="row-value">{{detail.position}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-button"
         @click="showCode=!showCode">
      <div class="qr-code"
           v-if="!showCode"><img src="../../assets/img/qr_code_two-dimensional-code-two-1.png"
             alt="">
        <div style="white-space:nowrap;">亮 码</div>
      </div>
      <div class="qr-code"
           v-else><img src="../../assets/img/id-card-h-1.png"
             alt="">
        <div style="white-space:nowrap;">亮 证</div>
      </div>
    </div>
  </div>
</template>
<script>

import { useRoute } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, Grid, GridItem, Image as VanImage, ActionSheet } from 'vant'
export default {
  name: 'openWorkPermit',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem
  },
  setup () {
    // const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id || JSON.parse(sessionStorage.getItem('user').id) || '',
      detail: {},
      Sys_isMember: '',
      showCode: false,
      qrcodeurl: ''
    })
    onMounted(() => {
      data.Sys_isMember = data.user.otherInfo.userOtherInfo.isRepresenter || data.user.otherInfo.userOtherInfo.isMember // 是否代表委员
      // http://www.cszysoft.com:9090/utils/qr?text=product4_rd%7Ccard%7C1&logo=http://**************:22508/lzt/images/******************.jpg
      data.qrcodeurl = sessionStorage.getItem('tomcatAddress') + 'utils/qr?text=' + encodeURIComponent(sessionStorage.getItem('rongCloudIdPrefix') + '|card|' + data.user.id) + '&logo=' + data.user.headImg
      console.log(data.qrcodeurl)
      // data.qrcodeurl = zyUrl.getTomcatAddress() + 'utils/qr?text=' + encodeURIComponent(zyUrl.getChatHeader() + '|card|' + T.getPrefs("Sys_UserID")) + '&logo=' + $api.getStorage("Sys_AppPhoto")
      getUserInfo()
    })
    const getUserInfo = async () => {
      var res = await $api.general.QrCodeInfo({
        id: data.id
      })
      var { data: info } = res
      console.log('info===>', info)
      data.detail = info
    }
    return { ...toRefs(data), $general }
  }
}
</script>
<style lang="less">
.openWorkPermit {
  width: 100%;
  padding-bottom: 50px;
  background: #f6f6f6;
  height: 100vh;
  background: #fff;
  background: url("../../assets/img/Rectangle-558.png") no-repeat center top;
  background-size: contain;

  .nav {
    padding-top: 40px;
    margin-right: 14px;
    margin-left: 14px;
    .info-box {
      padding: 16px 16px 25px 16px;
      background-color: #fff;
      border-radius: 0px 0px 10px 10px;
      .info-arae {
        font-size: 20px;
        font-family: SimSun;
        font-weight: bold;
        color: #333333;
        line-height: 24px;
        letter-spacing: 1px;
        -webkit-background-clip: text;
        text-align: center;
      }
      .qrcode {
        display: flex;
        justify-content: center;
        margin-top: 34px;
        margin-bottom: 10px;
      }
      .info-item {
        display: flex;
        background-color: #fff;
        margin-top: 20px;
        .head-img {
          flex-shrink: 0;
          img {
            width: 80px;
            height: 96px;
            object-fit: cover;
          }
        }
        .card-item {
          margin-left: 11px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          .row-item {
            display: flex;
            margin-bottom: 10px;
            .row-name {
              font-size: 14px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #999999;
              line-height: 13px;
              margin-right: 5px;
              flex-shrink: 0;
              display: flex;
              align-items: center;
              width: 50px;
              line-height: 1.5;
            }
            .row-value {
              font-size: 12px;
              font-family: PingFang SC-Medium, PingFang SC;
              font-weight: 500;
              color: #333333;
              line-height: 13px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
  .bottom-button {
    margin-top: 20px;
    margin-right: 14px;
    margin-left: 14px;
    height: 40px;
    background: #ffffff;
    border-radius: 24px;
    opacity: 1;
    border: 1px solid #3894ff;
    display: flex;
    align-items: center;
    justify-content: center;

    .qr-code {
      display: flex;
      font-size: 14px;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: bold;
      color: #3894ff;
      align-items: center;
      img {
        height: 18px;
        display: flex;
        margin-right: 4px;
      }
    }
  }
}
</style>

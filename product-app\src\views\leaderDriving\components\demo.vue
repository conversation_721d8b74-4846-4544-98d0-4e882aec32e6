<template>
  <div>
    <!-- <input type="file" @change="handleImageUpload" />
    <div v-if="recognizedText">{{ recognizedText }}</div> -->
  </div>
</template>

<script>
// import { createWorker } from 'tesseract.js'

// export default {
//   data () {
//     return {
//       recognizedText: ''
//     }
//   },
//   methods: {
//     async handleImageUpload (event) {
//       const worker = createWorker({
//         logger: (m) => console.log(m) // 可选，用于记录Tesseract日志
//       })
//       await worker.load()
//       await worker.loadLanguage('chi_sim') // 选择中文简体语言包
//       await worker.initialize('chi_sim')
//       const {
//         data: { text }
//       } = await worker.recognize(event.target.files[0])
//       this.recognizedText = text
//       await worker.terminate()
//     }
//   }
// }
</script>

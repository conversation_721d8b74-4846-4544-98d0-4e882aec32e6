import {
  HTTP
} from '../http.js'
class activity extends HTTP {
  general (url, params) {
    return this.request({
      url: url,
      data: params
    })
  }

  // 活动列表
  activityList (params) {
    return this.request({
      url: '/app/activity/getActivityList',
      data: params
    })
  }

  // 活动列表
  myActivityList (params) {
    return this.request({
      url: '/activity/findMyActivitys',
      data: params
    })
  }

  // 活动详情
  activityInfo (params) {
    return this.request({
      url: `/activity/info/${params}`
    })
  }

  // 报名签到
  addSignInUser (params) {
    return this.request({
      url: '/app/activity/addSignInUser',
      data: params
    })
  }

  // 请假
  addLeave (params) {
    return this.request({
      url: 'app/activity/addLeave',
      data: params
    })
  }

  // 活动资料
  getMaterialList (params) {
    return this.request({
      url: '/app/activity/getMaterialList',
      data: params
    })
  }

  // 活动资料详情
  getMaterialInfo (params) {
    return this.request({
      url: '/app/activity/getMaterialInfo/' + params,
      data: params
    })
  }

  // 日程行程
  getScheduleList (params) {
    return this.request({
      url: '/app/activity/getScheduleList',
      data: params
    })
  }

  // 日程行程详情
  getScheduleInfo (params) {
    return this.request({
      url: '/app/activity/getScheduleInfo/' + params,
      data: params
    })
  }

  // 活动报告
  getReportList (params) {
    return this.request({
      url: '/app/activity/getReportList',
      data: params
    })
  }

  // 活动报告详情
  getReportInfo (params) {
    return this.request({
      url: '/app/activity/getReportInfo/' + params,
      data: params
    })
  }

  treelist (params) { // 查询树列表
    return this.request({
      url: '/tree/list',
      data: params
    })
  }

  dictionaryPubkvs (params) { // 活动标签
    return this.request({
      url: '/dictionary/pubkvs',
      data: params
    })
  }

  activitySaveActivity (params) { // 发起会议
    return this.request({
      url: '/activity/saveActivity',
      data: params
    })
  }

  // 获取全会数据
  findAppConferenceParents (params) {
    return this.request({
      url: '/conferenceparent/findAppConferenceParents?',
      data: params
    })
  }

  // 会议通知数据列表
  findAppConferences (params) {
    return this.request({
      url: '/conference/findAppConferences?',
      data: params
    })
  }

  // 会议通知详情
  conferencenfo (params) {
    return this.request({
      url: `/conference/info/${params}`
    })
  }

  // 通知详情附件
  conferencematerialList (params) {
    return this.request({
      url: '/conferencematerial/list?',
      data: params
    })
  }

  // 通知详情附件2
  conferencematerialGetFileVos (params) {
    return this.request({
      url: '/conferencematerial/getFileVos?',
      data: params
    })
  }

  // 活动通知列表
  findAppActivitys (params) {
    return this.request({
      url: '/activityupgrade/findAppActivitys?',
      data: params
    })
  }

  // 活动通知详情
  activityupgradeetActivityDetail (params) {
    return this.request({
      url: '/activityupgrade/getActivityDetail?',
      data: params
    })
  }

  // 报名数据统计
  conferenceGetAttendanceNum (params) {
    return this.request({
      url: '/conference/getAttendanceNum?',
      data: params
    })
  }

  // 出勤率统计
  conferenceGetSignUpNum (params) {
    return this.request({
      url: '/conference/getSignUpNum?',
      data: params
    })
  }
}
export {
  activity
}

import { HTTP } from '../http.js'
class scan extends HTTP {
  // 第三方 - 获取token
  gettoken (params) {
    return this.request({
      url: 'http://59.206.205.195:9080/cgi-bin/gettoken?corpid=ww2389532372dc6805&corpsecret=cJ80CzpuSXrSsHl4DySAZTNUJx3Ar-EncxJ5YKlSZ6Y',
      data: params,
      method: 'GET'
    })
  }

  // 获取签名信息
  signature (params) {
    return this.request({
      url: '/shandongAccess/signature',
      data: params
    })
  }

  // 获取签名所需数据
  // getJsapiTicket (params) {
  //   return this.request({
  //     url: `http://59.206.205.195:9080/cgi-bin/get_jsapi_ticket?access_token=${params}`,
  //     data: params,
  //     method: 'GET'
  //   })
  // }
}
export {
  scan
}

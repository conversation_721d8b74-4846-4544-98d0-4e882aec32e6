import AES from 'crypto-js/aes'
import pad from 'crypto-js/pad-pkcs7'
import mode from 'crypto-js/mode-ecb'
import enc from 'crypto-js/enc-utf8'
export default {
  // 加密
  encrypt (word, keyStr) {
    var key = enc.parse(keyStr)
    var srcs = enc.parse(word)
    var encrypted = AES.encrypt(srcs, key, { mode: mode, padding: pad })
    return encrypted.toString()
  },
  // 解密
  decrypt (word, keyStr) {
    var key = enc.parse(keyStr)
    var decrypt = AES.decrypt(word, key, { mode: mode, padding: pad })
    return enc.stringify(decrypt).toString()
  }
}

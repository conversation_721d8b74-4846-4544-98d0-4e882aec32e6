<template>
  <div class="Pie1">
    <div class="container_swipe">
      <div class="pages1">
        <img src="../../../assets/img/timeAxis/g_bg01.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item1">
          <div class="pages_item1_text1"
               :class="{ 'fade-in1': showText1 }">尊敬的<span>{{userName}}</span>代表，</div>
          <div class="pages_item1_text2"
               :class="{ 'fade-in1': showText1 }">您好，感谢您登陆青岛人大APP</div>
          <div class="pages_item1_text4"
               :class="{ 'fade-in1': showText1 }">2023<span>年</span></div>
          <div class="pages_item1_text5"
               :class="{ 'fade-in1': showText1 }">是青岛人大APP正式上线运行的第一年</div>
          <div class="pages_item1_text6"
               :class="{ 'fade-in1': showText2 }">这一年</div>
          <div class="pages_item1_text7"
               :class="{ 'fade-in1': showText2 }">青岛人大APP&nbsp;&nbsp;陪伴您度过了</div>
          <div class="pages_item1_text8"
               :class="{ 'fade-in1': showText3 }">每一个履职时光</div>
          <div class="pages_item1_text8"
               :class="{ 'fade-in1': showText3 }">记录下了您的每一次履职实践</div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, onUpdated } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'Page1',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      userName: '',
      showText1: false,
      showText2: false,
      showText3: false
    })
    if (data.title) {
      document.title = data.title
    }
    onUpdated(() => {
      data.showText1 = props.showText1
      data.showText2 = props.showText2
      data.showText3 = props.showText3
    })
    onMounted(() => {
      data.userName = data.user.userName
      preventScroll()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        setTimeout(() => {
          router.push('/Page2')
        }, 500) // 延迟500毫秒，可以根据实际需要调整
      } else {
        // 向下滑动
        console.log('向下滑动')
        setTimeout(() => {
          router.push('/Page0')
        }, 500) // 延迟500毫秒，可以根据实际需要调整
      }
    }
    return { ...toRefs(data), $general, handleTouchMove, handleTouchStart }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
.Pie1 {
  width: 100%;
  min-height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    .pages_item1_text1,
    .pages_item1_text2,
    .pages_item1_text4,
    .pages_item1_text5,
    .pages_item1_text6,
    .pages_item1_text7,
    .pages_item1_text8 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item1_text1.fade-in,
    .pages_item1_text2.fade-in,
    .pages_item1_text4.fade-in,
    .pages_item1_text5.fade-in,
    .pages_item1_text6.fade-in,
    .pages_item1_text7.fade-in,
    .pages_item1_text8.fade-in {
      opacity: 1;
    }
    .pages1 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages_item1 {
        margin-top: -1rem;
        margin-left: -1rem;
        position: absolute;
        top: 6.2rem;
        left: 1.6rem;
        .pages_item1_text1 {
          color: #a9894c;
          margin-top: 0.5rem;
          font-size: 0.62rem;
          font-family: "YouSheBiaoTiHei-2";

          span {
            font-size: 0.72rem;
            margin: 0 3px;
          }
        }
        .pages_item1_text2 {
          color: #336cb5;
          margin-top: 0.1rem;
          font-size: 0.4rem;
        }
        .pages_item1_text4 {
          color: #a9894c;
          margin-top: 0.1rem;
          font-size: 0.6rem;
          font-family: "YouSheBiaoTiHei-2";
          span {
            font-size: 0.6rem;
          }
        }
        .pages_item1_text5 {
          color: #336cb5;
          margin-top: 0.15rem;
          font-size: 0.4rem;
        }
        .pages_item1_text6 {
          color: #336cb5;
          margin-top: 0.15rem;
          font-size: 0.4rem;
        }
        .pages_item1_text7 {
          color: #336cb5;
          margin-top: 0.15rem;
          font-size: 0.4rem;
        }
        .pages_item1_text8 {
          color: #336cb5;
          margin-top: 0.15rem;
          font-size: 0.4rem;
        }
      }
      .pages1_gif1 {
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 100px;
        height: 100px;
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    .more {
      position: absolute;
      bottom: 1rem;
      left: 4.5rem;
      .drop {
        font-size: 30px;
        animation: drop 1s linear infinite;
      }
      // .text {
      //   color: #454545;
      // }
    }
    @keyframes drop {
      0% {
        opacity: 0;
        margin-top: 0px;
      }

      25% {
        opacity: 0.5;
        margin-top: -10px;
      }

      50% {
        opacity: 1;
        margin-top: -20px;
      }

      75% {
        opacity: 0.5;
        margin-top: -30px;
      }

      100% {
        opacity: 0;
        margin-top: -40px;
      }
    }
  }
}
</style>

<template>
  <div class="noticeDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="noticeDetailsBox">
        <div class="noticeTitle">{{details.noticeTitle}}</div>
        <div class="noticeInfo">
          <div>{{details.publishDate}}</div>
          <div>{{details.org}}</div>
        </div>
        <div class="noticeContent"
             @click="setImgBigger"
             v-html="details.content"></div>
      </div>
      <div class="readUserBox"
           v-if="details.attachmentList && details.attachmentList.length != 0">
        <div class="number">附件：</div>
        <div class="readUser"
             style="line-height:30px;">
          <div v-for="(item,index) in details.attachmentList"
               @click="annexClick(item)"
               style="text-decoration:underline;color:#08c"
               :key="index">{{item.fileName}}</div>
        </div>
      </div>
      <div class="readUserBox">
        <div class="number">已读{{readUser.length}}人</div>
        <div class="readUser ellipsisTwo">
          <span v-for="(item,index) in readUser"
                :key="item.id">{{item.userName}}{{index!==readUser.length-1?'，':''}}</span>
        </div>
        <div class="viewMore"
             @click="detailsClick">点击查看
          <van-icon name="arrow" />
        </div>
      </div>
    </van-pull-refresh>
    <div class="receipt"
         v-if="receiptShow"
         @click="show=!show">回执</div>
    <van-popup v-model:show="show"
               round
               closeable
               position="bottom">
      <div class="noticeTitlePopup">通知回执</div>
      <template v-if="details.returnType === '文本'">
        <van-cell-group>
          <van-field v-model="receipt"
                     rows="9"
                     type="textarea"
                     placeholder="请输入内容" />
        </van-cell-group>
      </template>
      <template v-if="details.returnType === '单选'">
        <van-radio-group v-model="optionId">
          <van-radio :name="item.id"
                     v-for="item in options"
                     :key="item.id"
                     icon-size="16px">{{item.content}}</van-radio>
        </van-radio-group>
      </template>
      <template v-if="details.returnType === '多选'">
        <van-checkbox-group v-model="optionIds">
          <van-checkbox :name="item.id"
                        v-for="item in options"
                        :key="item.id"
                        shape="square"
                        icon-size="16px">{{item.content}}</van-checkbox>
        </van-checkbox-group>
      </template>
      <div class="noticeButton">
        <van-button type="primary"
                    @click="submit"
                    :disabled="optionIds.length==0&&optionId==''&&receipt==''"
                    size="small">确定</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { NavBar, Sticky, Toast, ImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'noticeDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false,
      show: false,
      receipt: '',
      receiptShow: false,
      optionId: '',
      optionIds: [],
      options: [],
      readUser: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      browseSave()
      noticeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        noticeInfo()
      }, 520)
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.filePath,
        name: item.fileName
      }
      router.push({ name: 'superFile', query: param })
    }
    const browseSave = async () => {
      const res = await $api.notice.browseSave({
        keyId: data.id,
        type: '27'
      })
      console.log(res)
    }
    // 列表请求
    const noticeInfo = async () => {
      const res = await $api.notice.noticeInfo(data.id)
      var { data: details } = res
      data.details = details

      data.refreshing = false
      if (details.isReturn && details.returnType !== '文本') {
        noticereturnoptionList()
      }
      if (details.isReturn) {
        var date = new Date()
        var returnStartDate = new Date(details.returnStartDate)
        var returnEndDate = new Date(details.returnEndDate)
        if (date.getTime() - returnStartDate.getTime() > 0 && date.getTime() - returnEndDate.getTime() < 0) {
          data.receiptShow = true
        }
      }
      readingDetail()
    }
    const readingDetail = async () => {
      const res = await $api.notice.readingDetail({
        id: data.id
      })
      var { data: { readUser } } = res
      data.readUser = readUser
    }
    const noticereturnoptionList = async () => {
      const res = await $api.notice.noticereturnoptionList({
        pageNo: 1,
        pageSize: 99,
        noticeId: data.id
      })
      var { data: options } = res
      data.options = options
    }
    const submit = () => {
      noticereturnAdd()
    }
    const noticereturnAdd = async () => {
      const res = await $api.notice.noticereturnAdd({
        noticeId: data.id,
        content: data.receipt,
        optionId: data.details.returnType === '单选' ? data.optionId : data.optionIds.join(',')
      })
      console.log(res)
      data.show = false
      data.receipt = ''
      data.optionId = ''
      data.optionIds = []
      Toast('回执成功')
      noticeInfo()
    }
    const detailsClick = () => {
      router.push({ name: 'readUserList', query: { id: data.id } })
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.noticeContent img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview(img, nowIndex)
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, submit, detailsClick, onClickLeft, setImgBigger, annexClick }
  }
}
</script>
<style lang="less">
.noticeDetails {
  width: 100%;
  min-height: 100%;
  background: #eee;
  // background: #f8f8f8;
  .noticeDetailsBox {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 10px;
    .noticeTitle {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .noticeInfo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 0;
      div {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #999999;
      }
    }
    .noticeContent {
      width: 100%;
      img {
        width: 100%;
      }
    }
  }
  .readUserBox {
    padding: 16px;
    padding-bottom: 52px;
    background-color: #fff;
    .number {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 20px;
      color: #333333;
      position: relative;
      padding-left: 6px;
      margin-bottom: 6px;
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #3088fe;
        opacity: 1;
        border-radius: 10px;
      }
    }
    .readUser {
      overflow: hidden;
      span {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 20px;
        color: #666666;
      }
    }
    .viewMore {
      font-size: 10px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 14px;
      color: #3088fe;
      text-align: right;
    }
  }
  .receipt {
    position: fixed;
    top: 68%;
    right: 16px;
    width: 44px;
    height: 44px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 44px;
    color: #ffffff;
    text-align: center;
  }
  .van-popup {
    .van-icon-cross {
      font-size: 16px;
    }
    .noticeTitlePopup {
      height: 44px;
      line-height: 40px;
      padding-top: 4px;
      text-align: center;
      position: relative;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 343px;
        height: 0px;
        border-bottom: 1px solid #999999;
        opacity: 0.2;
      }
    }
    .van-field__body {
      font-size: 14px;
    }
    .van-radio-group {
      padding: 16px;
      .van-radio {
        padding: 6px;
      }
      .van-radio__label {
        font-size: 14px;
      }
    }
    .van-checkbox-group {
      padding: 16px;
      .van-checkbox {
        padding: 6px;
      }
      .van-checkbox__label {
        font-size: 14px;
      }
    }
    .noticeButton {
      width: 100%;
      padding-top: 52px;
      padding-bottom: 82px;
      display: flex;
      justify-content: center;
      .van-button {
        width: 82px;
      }
      .van-button__text {
        font-size: 14px;
      }
    }
  }
}
</style>

const newsList = () => import('@/views/news/newsList')
const legalRegulatoryNews = () => import('@/views/news/legalRegulatoryNews')
const newsZTList = () => import('@/views/news/newsZTList')
const newsZTListTwo = () => import('@/views/news/newsZTListTwo')
const newsDetails = () => import('@/views/news/newsDetails')
const newsMore = () => import('@/views/news/newsMore')
const ZTList = () => import('@/views/news/ZTList')
const ZT = () => import('@/views/news/ZT')
const LiaisonDataDetails = () => import('@/views/news/liaison/LiaisonDataDetails')
const LiaisonDataList = () => import('@/views/news/liaison/LiaisonDataList')
const LiaisonDataAdd = () => import('@/views/news/liaison/LiaisonDataAdd')
const xinwenNewsInfo = () => import('@/views/news/xinwenNewsInfo')

const news = [{
  path: '/newsList',
  name: 'newsList',
  component: newsList,
  meta: {
    title: '列表',
    keepAlive: true
  }
}, {
  path: '/legalRegulatoryNews',
  name: 'legalRegulatoryNews',
  component: legalRegulatoryNews,
  meta: {
    title: '法律法规',
    keepAlive: true
  }
}, {
  path: '/newsZTList',
  name: 'newsZTList',
  component: newsZTList,
  meta: {
    title: '专题列表',
    keepAlive: true
  }
}, {
  path: '/newsMore',
  name: 'newsMore',
  component: newsMore,
  meta: {
    title: '专题列表',
    keepAlive: true
  }
}, {
  path: '/ZTList',
  name: 'ZTList',
  component: ZTList,
  meta: {
    title: '专题列表',
    keepAlive: true
  }
}, {
  path: '/newsZTListTwo',
  name: 'newsZTListTwo',
  component: newsZTListTwo,
  meta: {
    title: '专题列表',
    keepAlive: true
  }
}, {
  path: '/newsDetails',
  name: 'newsDetails',
  component: newsDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/ZT',
  name: 'ZT',
  component: ZT,
  meta: {
    title: '专题',
    keepAlive: true
  }

}, {
  path: '/LiaisonDataDetails',
  name: 'LiaisonDataDetails',
  component: LiaisonDataDetails,
  meta: {
    title: '基层履职动态详情',
    keepAlive: true
  }
}, {
  path: '/LiaisonDataList',
  name: 'LiaisonDataList',
  component: LiaisonDataList,
  meta: {
    title: '基层履职动态列表',
    keepAlive: true
  }
}, {
  path: '/LiaisonDataAdd',
  name: 'LiaisonDataAdd',
  component: LiaisonDataAdd,
  meta: {
    title: '基层履职动态列表',
    keepAlive: true
  }
}, {
  path: '/xinwenNewsInfo',
  name: 'xinwenNewsInfo',
  component: xinwenNewsInfo,
  meta: {
    title: '资讯重构版的详情',
    keepAlive: true
  }
}]
export default news

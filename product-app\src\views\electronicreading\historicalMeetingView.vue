<template>
  <div class="historicalMeetingView">
    <!-- 搜索框 -->
    <div id="search"
         style="border-radius: 10px;"
         class="search_box"
         :style="$general.loadConfiguration() ">
      <div class="search_warp flex_box">
        <div @click="search();"
             class="search_btn flex_box flex_align_center flex_justify_content">
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;">
          <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 placeholder="请输入搜索内容"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="search()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="(($general.appFontSize)*0.01)+'rem'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>
    <div v-for="(item,index) in historicalMeetingList"
         :key="index"
         class="vue_newslist3_warp">
      <van-cell clickable
                class="vue_newslist3_item"
                @click="openDetails(item)">
        <div class="historName"
             v-html="item.name"></div>
        <div class="historTime">{{item.startTime && item.endTime?dayjs(item.startTime).format('YYYY-MM-DD HH:mm')+'至'+dayjs(item.endTime).format('YYYY-MM-DD HH:mm') : ''}}</div>
      </van-cell>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
             showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage } from 'vant'
export default {
  name: 'historicalMeetingView',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      historicalMeetingList: [],
      meetingTypeId: JSON.parse(sessionStorage.getItem('meetingTypeId')),
      seachText: ''
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getHistoricalConference()
    })
    const search = () => {
      data.historicalMeetingList = []
      data.loading = true
      data.finished = false
      getHistoricalConference()
    }
    // 列表
    const getHistoricalConference = async () => {
      const res = await $api.electronicreading.getHistoricalConference({
        keyword: data.seachText,
        meetingType: data.meetingTypeId
      })
      var { data: list } = res
      data.historicalMeetingList = data.historicalMeetingList.concat(list)
    }
    // 详情
    const openDetails = (row) => {
      if (row.bigType === '1' || row.bigType === '2') {
        router.push({ name: 'electronicreadingItemDetails2', query: { id: row.id, Verticalscreen: 1 } })
      } else {
        router.push({ name: 'electronicreadingItemDetails', query: { id: row.id, Verticalscreen: 1 } })
      }
    }
    const onRefresh = () => {
    }
    const onLoad = () => {
    }
    return { ...toRefs(data), search, onRefresh, onLoad, $general, dayjs, openDetails }
  }
}
</script>
<style lang="less" scoped>
.historicalMeetingView {
  width: 100%;
  .historName {
    font-size: 0.42rem;
  }
  .historTime {
    font-size: 0.35rem;
    color: #8d8c8c;
    margin-top: 0.2rem;
  }
}
</style>

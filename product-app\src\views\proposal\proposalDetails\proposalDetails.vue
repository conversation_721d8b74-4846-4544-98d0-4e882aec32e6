<template>
  <div class="mainModuleDetails">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="mainModuleDetailsBox">
        <div class="mainModuleTitle">{{details.title}}</div>
        <div class="mainModuleInfoBox">
          <div class="mainModuleInfo">
            <div>提案者：{{details.mainSubmitUserName}}</div>
            <div v-if="details.submitDate">{{details.submitDate.slice(0,10)}}</div>
          </div>
          <div class="mainModuleState">{{details.processStateView}}</div>
        </div>
        <template v-if="details.joinUserList">
          <div class="joinUserBox"
               v-if="details.joinUserList.length">
            <div class="mainModuleText">联名人{{details.joinUserList.length}}人</div>
            <div class="joinUser ellipsisTwo">
              <span v-for="(item,index) in details.joinUserList"
                    :key="item.userId">{{index != 0?'，':''}}{{item.name}}</span>
            </div>
            <div class="viewMore"
                 @click="detailsClick">点击查看
              <van-icon name="arrow" />
            </div>
          </div>
        </template>
        <div class="mainModuleText">正文</div>
        <div class="mainModuleContent"
             v-html="details.content"></div>
        <template v-if="details.attachmentList">
          <div class="mainModuleText"
               v-if="details.attachmentList.length">附件</div>
          <div class="attachmentBox">
            <div class="attachmentItem"
                 v-for="item in details.attachmentList"
                 :key="item.id">{{item.fileName}}</div>
          </div>
        </template>
        <div class="situationBox">
          <div class="situation">是否调研：{{details.ifInvestigate == '1'?'是':''}}{{details.ifInvestigate == '0'?'否':''}}</div>
          <div class="situation">是否公开：{{details.ifAdvicePublic == '1'?'是':''}}{{details.ifAdvicePublic == '0'?'否':''}}</div>
          <div class="situation">是否涉密：{{details.ifSecret == '1'?'是':''}}{{details.ifSecret == '0'?'否':''}}</div>
        </div>
      </div>
      <div class="mainModuleProcessBox">
        <div class="mainModuleText">提案进程</div>
        <div class="mainModuleProcess">
          <div class="mainModuleProcessItem"
               v-for="(item,index) in process"
               :key="index+'process'">
            <div class="processIcon"
                 :class="{processIcone:item.pointFlag&&details.processState != '200'}"></div>
            <div class="processName">{{item.nodeName}} <span>{{item.submitDate||item.auditDate||item.assignDate||item.evaluateDate}}</span></div>
            <template v-if="item.transactDetailVoList">
              <div class="processText"
                   v-for="unit in item.transactDetailVoList"
                   :key="unit.id">
                <template v-if="unit.transactType =='1'">主办</template>
                <template v-if="unit.transactType =='2'">协办</template>
                <template v-if="unit.transactType =='3'">分办</template>单位：{{unit.groupName}}
                <template v-if="item.showTransactStatusAndDate">（{{unit.transactStatusView}}）</template>
              </div>
            </template>
            <div class="processText"
                 v-if="item.submitUserName">{{item.nodeName}}人：{{item.submitUserName}}</div>
            <div class="processText"
                 v-if="item.auditUserName">{{item.nodeName}}人：{{item.auditUserName}} <span @click="reviewClick(item)">审查情况</span></div>
            <div class="processText"
                 v-if="item.evaluateResultView">{{item.evaluateResultView}}<span @click="satisfactionClick">满意度测评</span></div>
          </div>
        </div>
        <div class="mainModuleButton">
          <!-- <van-button type="primary"
                      @click="communication"
                      v-if="['170','180','190','200'].includes(details.processState)"
                      block>查看单位与委员沟通情况</van-button> -->
          <van-button type="primary"
                      @click="reply"
                      v-if="details.flowAnswerListVoList"
                      block>查看答复信息</van-button>
        </div>
      </div>
      <!-- <proposalReview :id="id"></proposalReview> -->
    </van-pull-refresh>
    <!-- <van-popup v-model:show="show"
               :style="{maxHeight:'66%'}"
               position="top">
      <div class="review"
           v-for="item in reviewList"
           :key="item.id">
        <div class="reviewState"
             v-if="item.statusCode == '115'">待专委会审查</div>
        <div class="reviewState"
             v-if="item.statusCode == '120'">待审查</div>
        <div class="reviewState"
             v-if="item.statusCode == '130'">待复审</div>
        <div class="reviewState"
             v-if="item.statusCode == '140'">待审定</div>
        <div class="reviewResults">审查结果：<span>{{item.acceptResultView}}</span></div>
        <div class="reviewOpinion">审查意见：{{item.acceptOpinion}}</div>
      </div>
    </van-popup> -->
  </div>
</template>
<script>
// import { Toast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
// import proposalReview from './proposalReview/proposalReview'
export default {
  name: 'proposalDetails',
  components: {
    // proposalReview
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const data = reactive({
      id: route.query.id,
      details: {},
      refreshing: false,
      process: [],
      reviewList: [],
      show: false
    })
    onMounted(() => {
      transactProposalDetail()
      nodeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        transactProposalDetail()
      }, 520)
    }
    // 列表请求
    const transactProposalDetail = async () => {
      const res = await $api.proposal.transactProposalDetail({
        proposalId: data.id
      })
      var { data: details } = res
      data.details = details
      data.refreshing = false
    }
    // 列表请求
    const nodeInfo = async () => {
      const res = await $api.proposal.nodeInfo({
        id: data.id
      })
      var { data: process } = res
      var processList = []
      var i = true
      process.forEach(item => {
        if (i) {
          processList.push(item)
        }
        if (item.pointFlag) {
          i = false
        }
      })
      data.process = processList
    }

    const detailsClick = () => {
      router.push({ name: 'readUserList', query: { id: data.id } })
    }
    const reviewClick = (row) => {
      data.reviewList = row.acceptList
      data.show = true
    }
    const communication = () => {
      router.push({ name: 'memberCommunication', query: { id: data.id } })
    }
    const reply = () => {
      router.push({ name: 'proposalReply', query: { id: data.id } })
    }
    const satisfactionClick = () => {
      router.push({ name: 'proposalSatisfactionDetails', query: { id: data.id } })
    }
    return { ...toRefs(data), onRefresh, detailsClick, reviewClick, communication, reply, satisfactionClick }
  }
}
</script>

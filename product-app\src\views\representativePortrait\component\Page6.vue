<template>
  <div class="Pie6">
    <div class="container_swipe">
      <div class="pages6">
        <img src="../../../assets/img/timeAxis/g_bg06.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item2">
          <div class="pages_item2_text0"
               :class="{ 'fade-in1': showText1 }">{{ year6 }}年{{ month6 }}月{{ day6 }}日</div>
          <div class="pages_item2_text1"
               :class="{ 'fade-in1': showText1 }">您第一次参加&nbsp;&nbsp;线上履职学习</div>
          <div class="pages_item2_text2"
               :class="{ 'fade-in1': showText2 }">全年</div>
          <div class="pages_item2_text3"
               :class="{ 'fade-in1': showText3 }">您观看专题讲座<span>{{watchVideoTime}}次</span></div>
          <div class="pages_item2_text4"
               :class="{ 'fade-in1': showText3 }">阅读电子书籍<span>{{readBookTime}}次</span></div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, onUpdated } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'Page6',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean,
    pageData: Object
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      year6: '', // 第一次参加线上履职学习年
      month6: '', // 第一次参加线上履职学习月
      day6: '', // 第一次参加线上履职学习日
      watchVideoTime: 0, // 观看专题讲座时长
      readBookTime: 0, // 阅读电子数据时长
      showText1: false,
      showText2: false,
      showText3: false
    })
    onUpdated(() => {
      data.showText1 = props.showText1
      data.showText2 = props.showText2
      data.showText3 = props.showText3
      data.year6 = props.pageData.firstStudyDuty ? props.pageData.firstStudyDuty.substring(0, 4) : ''
      data.month6 = props.pageData.firstStudyDuty ? props.pageData.firstStudyDuty.substring(5, 7) : ''
      data.day6 = props.pageData.firstStudyDuty ? props.pageData.firstStudyDuty.substring(8, 10) : ''
      data.watchVideoTime = props.pageData.watchVideoTime
      data.readBookTime = props.pageData.readBookTime
    })
    onMounted(() => {
      // setTimeout(() => {
      //   data.showText1 = true
      // }, 300)
      // setTimeout(() => {
      //   data.showText2 = true
      // }, 1200)
      // setTimeout(() => {
      //   data.showText3 = true
      // }, 2200)
      // memberPortraitDutyTime()
      preventScroll()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    // 获取数据
    // const memberPortraitDutyTime = async () => {
    //   const res = await $api.representativePortrait.memberPortraitDutyTime()
    //   console.log('获取数据===>>', res)
    //   data.year6 = res.data.firstStudyDuty.substring(0, 4)
    //   data.month6 = res.data.firstStudyDuty.substring(5, 7)
    //   data.day6 = res.data.firstStudyDuty.substring(8, 10)
    //   data.watchVideoTime = res.data.watchVideoTime
    //   data.readBookTime = res.data.readBookTime
    // }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        setTimeout(() => {
          router.push('/Page7')
        }, 500) // 延迟500毫秒
      } else {
        // 向下滑动
        console.log('向下滑动')
        setTimeout(() => {
          router.push('/Page5')
        }, 500) // 延迟500毫秒
      }
    }
    return { ...toRefs(data), $general, handleTouchStart, handleTouchMove }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
.Pie6 {
  width: 100%;
  min-height: 100%;
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages_item2_text0,
    .pages_item2_text1,
    .pages_item2_text2,
    .pages_item2_text3,
    .pages_item2_text4 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item2_text0.fade-in,
    .pages_item2_text1.fade-in,
    .pages_item2_text2.fade-in,
    .pages_item2_text3.fade-in,
    .pages_item2_text4.fade-in {
      opacity: 1;
    }
    .pages6 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages_item2 {
        position: absolute;
        top: 21%;
        z-index: 1;
        .pages_item2_text0 {
          color: #fff;
          font-size: 0.7rem;
          font-family: "YouSheBiaoTiHei-2";
          text-align: center;
        }
        .pages_item2_text1 {
          color: #fff;
          font-size: 0.4rem;
          margin-top: 0.15rem;
          text-align: center;
        }
        .pages_item2_text2 {
          color: #fff;
          font-size: 0.4rem;
          margin-top: 0.3rem;
          letter-spacing: 3px;
          text-align: center;
        }
        .pages_item2_text3 {
          color: #fff;
          font-size: 0.4rem;
          letter-spacing: 2px;
          margin-top: 0.2rem;
          text-align: center;
          span {
            font-size: 0.75rem;
            color: #fff;
            font-family: "YouSheBiaoTiHei-2";
          }
        }
        .pages_item2_text4 {
          color: #fff;
          font-size: 0.4rem;
          letter-spacing: 2px;
          margin-top: 0.2rem;
          text-align: center;
          span {
            font-size: 0.75rem;
            color: #fff;
            font-family: "YouSheBiaoTiHei-2";
          }
        }
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    .more {
      position: absolute;
      bottom: 1rem;
      left: 4.5rem;
      color: #fff;
      .drop {
        font-size: 30px;
        animation: drop 1s linear infinite;
      }
    }
    @keyframes drop {
      0% {
        opacity: 0;
        margin-top: 0px;
      }

      25% {
        opacity: 0.5;
        margin-top: -10px;
      }

      50% {
        opacity: 1;
        margin-top: -20px;
      }

      75% {
        opacity: 0.5;
        margin-top: -30px;
      }

      100% {
        opacity: 0;
        margin-top: -40px;
      }
    }
  }
}
</style>

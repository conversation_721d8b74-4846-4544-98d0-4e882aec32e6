<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import mapData from '../../../assets/js/mapdata.json'
import { inject, reactive, toRefs, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'maplist',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: <PERSON>rid<PERSON><PERSON>,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: {
    color: String,
    id: String,
    list: Array
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachText: '',
      dataList: [],
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      nextTick(() => {
        var chartDom = document.getElementById(props.id)
        echarts.registerMap('SC', mapData)
        myChart = echarts.init(chartDom)
        // data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      })
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
    })
    const setOptions = () => {
      const areas = [[
        // 市南
        [120.32027, 36.057428],
        [120.622, 35.87]
      ],
      [
        // 市北
        [120.374313, 36.121563],
        [120.901, 35.918]
      ],
      [
        // 黄岛
        [119.950438, 35.924541],
        [120.5, 35.6]
      ],
      [
        [120.625614, 36.262282],
        [120.842932, 36.262282]
        // 崂山区
      ],
      [
        // 李沧
        [120.38671, 36.184057],
        [120.797199, 36.09]
      ],
      [
        // 城阳
        [120.32602, 36.287011],
        [119.75765, 36.287011]
      ],
      [
        // 即墨区
        [120.464457, 36.396843],
        [120.850801, 36.396843]
      ],
      [
        // 胶东
        [119.911901, 36.100927],
        [119.70765, 36.100927]
      ],
      [
        // 平度市
        [119.97843, 36.58],
        [119.69035015625002, 36.58]
      ],
      [
        // 莱西市
        [120.523979, 36.856959],
        [120.862029, 36.856959]
      ]
      ]
      var color = ['#39E01E', '#FFAF6A', '#A6D7FA ', '#FB7A7A', '#3894FF', '#FFEB38 ', '#6DD664', '#C9B4F5', '#71B2FF', '#FFAF6A']
      var mydata = props.list.map((v, i) => {
        return {
          name: v.areaName,
          value: v.studioCount,
          itemStyle: {
            borderColor: color[i],
            color: color[i],
            areaColor: color[i]
          }
        }
      })
      // console.log(mydata)
      var options = {
        tooltip: {
          trigger: 'item',
          showDelay: 0,
          transitionDuration: 0.2,
          formatter: function (params) {
            var value = (params.value + '').split('.')
            value = value[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g, '$1,')
            return params.name + ': ' + (params.value ? value : '-')
          }
        },
        geo: { map: 'SC', zoom: 0.9 },
        series: [
          {
            type: 'map',
            map: 'SC',
            data: mydata,
            zoom: 0.9,
            lineStyle: {
              color: '#eee'
            },
            label: {
              normal: {
                show: false
              },
              emphasis: {
                show: false,
                textStyle: {
                  color: '#333'
                }
              }
            },
            itemStyle: {
              normal: {
                borderColor: '#3894FF',
                borderWidth: 2,
                areaColor: '#E5F2FF'
              },
              emphasis: {
                areaColor: '#FFEFE1',
                borderWidth: 2,
                color: '#FFEFE1',
                borderColor: '#FFAF6A'
              }
            }
          },
          {
            type: 'lines',
            color: '#ff8003',
            opacity: 1,
            label: {
              show: true,
              padding: [10, 5],
              color: '#333',
              // formatter: '{b}:{c}'
              formatter: function (params) {
                return `{name|${params.name}} {value|${params.value}}`
              },
              rich: {
                name: {
                  fontSize: 15,
                  color: '#333333',
                  align: 'center',
                  fontWeight: 'bold'
                },
                value: {
                  fontSize: 15,
                  color: '#3894FF',
                  align: 'center',
                  fontWeight: 'bold'
                }
              }
            },
            lineStyle: {
              type: 'solid',
              opacity: 1,
              color: '#333'
            },
            data: mydata.map((v, i) => {
              v.coords = areas[i]
              return v
            })
          }
        ]
      }
      nextTick(() => {
        myChart.setOption(options)
      })
    }
    return { ...toRefs(data), general }
  }
}
</script>
<style lang="less" scoped>
#maplist {
  background: #fff;
  width: 100%;
  height: 300px;
  margin: 10px 0;
  box-sizing: border-box;
}
</style>

<template>
  <div class="actionSheet_send">
    <div class="box_warp T-flexbox-vertical">
      <div class="header_box flex_box flex_align_center">
        <div class="header_cancel" :style="$general.loadConfiguration(-3)">
          {{ '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' }}</div>
        <div class="header_hint flex_placeholder" :style="$general.loadConfiguration(-1)">{{ '发送' }}</div>
        <div @click="cancelActionSheetSend" class="header_ok" :style="$general.loadConfiguration(-3) + 'color:' + appTheme">
          关闭</div>
      </div>
      <div style="margin: 0 15px;" class="clouddisk_after van-hairline--top"></div>
      <div class="main_box flex_placeholder">
        <div v-if="listDataShow" class="send_box">
          <div class="send_hint" :style="$general.loadConfiguration(-3)">发送到</div>
          <div class="send_warp flex_box T-flex-flow-row-wrap">
            <div v-for="(item, index) in listData" :key="index" class="send_item">
              <div v-if="item.show" @click="itemClick(item)">
                <div class="send_item_img flex_box flex_align_center flex_justify_content"
                  :style="$general.loadConfigurationSize(32)">
                  <van-icon :color="item.disabled ? '#ccc' : (item.color || '#333')" :size="((appFontSize + 5)) + 'px'"
                    :class-prefix="item.prefix" :name="item.iconName"></van-icon>
                </div>
              </div>
              <div class="send_text"
                :style="$general.loadConfiguration(-3) + (item.disabled ? '#ccc' : (item.color || '#333'))">{{ item.name }}</div>
            </div>
          </div>
        </div>
        <div v-if="otherDataShow" class="send_box">
          <div class="send_hint" :style="$general.loadConfiguration(-3)">其它方式</div>
          <div class="send_warp flex_box T-flex-flow-row-wrap">
            <div v-for="(item, index) in otherData" :key="index" class="send_item">
              <div v-if="item.show" @click="itemClick(item)">
                <div class="send_item_img flex_box flex_align_center flex_justify_content"
                  :style="$general.loadConfigurationSize(32)">
                  <van-icon :color="item.disabled ? '#ccc' : (item.color || '#333')" :size="((appFontSize + 5)) + 'px'"
                    :class-prefix="item.prefix" :name="item.iconName"></van-icon>
                </div>
                <div class="send_text"
                  :style="$general.loadConfiguration(-3) + (item.disabled ? '#ccc' : (item.color || '#333'))">{{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="other_add flex_box flex_align_center" :style="$general.loadConfiguration(-1) + 'color:#333;'">
            <div class="inherit">链接有效期：</div>
            <van-radio-group v-model="otherLink.value" direction="horizontal">
              <van-radio v-for="(item, index) in otherLink.data" :key="index" :name="item.value"
                :icon-size="((appFontSize + 1)) + 'px'" :checked-color="appTheme"
                label-position="left">{{ item.name }}</van-radio>
            </van-radio-group>
          </div>
        </div>
      </div>
    </div>
    <van-action-sheet v-model:show="showActionSheetEmail" :round="true" :closeable="false" safe-area-inset-bottom
      :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
      <actionSheetEmail v-if="showActionSheetEmail" @cancelActionSheetEmail="cancelActionSheetEmail" :title="title"
        :nBaseType="nBaseType" :operateIds="operateIds"></actionSheetEmail>
    </van-action-sheet>
    <van-action-sheet v-model:show="showActionSheetPerson" :round="true" :closeable="false" safe-area-inset-bottom
      :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
      <actionSheetPerson v-if="showActionSheetPerson" @cancelActionSheetPerson="cancelActionSheetPerson" :title="title"
        :nBaseType="nBaseType" :operateIds="operateIds"></actionSheetPerson>
    </van-action-sheet>
    <van-action-sheet v-model:show="showActionSheetDepart" :round="true" :closeable="false" safe-area-inset-bottom
      :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
      <actionSheetDepart v-if="showActionSheetDepart" @cancelActionSheetDepart="cancelActionSheetDepart" :title="title"
        :nBaseType="nBaseType" :operateIds="operateIds"></actionSheetDepart>
    </van-action-sheet>
  </div>

</template>
<script>
import actionSheetEmail from './actionSheet_email'
import actionSheetPerson from './actionSheet_person'
import actionSheetDepart from './actionSheet_depart.vue'
import useClipboard from 'vue-clipboard3'
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Toast } from 'vant'
export default {
  name: 'actionSheet_send',
  components: {
    actionSheetEmail,
    actionSheetPerson,
    actionSheetDepart,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: {
    nBaseType: {
      type: String,
      default: ''
    },
    operateIds: {
      type: String,
      default: ''
    },
    baseTitle: {
      type: String,
      default: ''
    },
    baseType: {
      type: String,
      default: ''
    },
    baseData: {
      type: Array,
      default: () => []
    }
  },
  emits: ['cancelActionSheetSend'],
  setup (props, { emit }) {
    const { toClipboard } = useClipboard()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      showActionSheetEmail: false,
      showActionSheetPerson: false,
      showActionSheetDepart: false,
      show: false,
      nBaseType: props.nBaseType, // 是在个人还是处室
      operateIds: props.operateIds, // 当前操作数据ids
      listDataShow: true,
      listData: [
        { show: true, name: '个人', click: 'person', iconName: 'manager-o', prefix: undefined, disabled: false },
        { show: true, name: '处室', click: 'depart', iconName: 'friends-o', prefix: undefined, disabled: false },
        { show: true, name: '邮箱', click: 'email', iconName: 'envelop-o', prefix: undefined, disabled: false }
      ],
      otherDataShow: true,
      otherData: [
        { show: true, name: '复制链接', click: 'link', iconName: 'guide-o', prefix: undefined, disabled: false }
      ],
      otherLink: {
        url: '', // 要复制的链接
        value: '7', // 当前过期天数
        data: [
          { name: '7天', value: '7' }, { name: '3天', value: '3' }, { name: '1天', value: '1' }
        ]
      }

    })
    onMounted(() => {
      open()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    const cancelActionSheetEmail = (msg) => {
      data.showActionSheetEmail = false
    }
    const cancelActionSheetPerson = (msg) => {
      data.showActionSheetPerson = false
    }
    const cancelActionSheetDepart = (msg) => {
      data.showActionSheetDepart = false
    }
    const cancelActionSheetSend = () => {
      emit('cancelActionSheetSend', false)
    }

    // 打开页面
    const open = () => {
      data.show = true
      data.listDataShow = true
      data.otherDataShow = true
      $general.getItemForKey('email', data.listData, 'click').show = true
      if (data.nBaseType === 'share') { // 分享页发送
        data.listDataShow = false
      } else if (data.operateIds.split(',,').length > 1) { // 是多选
        data.otherDataShow = false
        $general.getItemForKey('email', data.listData, 'click').show = false
      }

      data.otherDataShow && savelink()
    }
    // 点击 item
    const itemSelect = () => {
      // var param = {
      //   ids: data.operateIds,
      //   level: data.nowLevel[data.nowLevel.length - 1],
      //   download: data.permissionDownload ? 1 : 0
      // }
      // if (data.type === 'share') {
      //   emit('fileShare', param)
      // } else if (data.type === 'move') {
      //   emit('fileMove', param)
      // } else if (data.type === 'copy') {
      //   emit('fileCopy', param)
      // }
      data.show = false
    }

    // 点击 取消
    const itemCancel = () => {

    }
    // 点击 图标
    const itemClick = (_item) => {
      data.title = '选择发送' + _item.name
      switch (_item.click) {
        case 'person':
          data.showActionSheetPerson = true
          break
        case 'depart':
          data.showActionSheetDepart = true
          break
        case 'email':
          data.showActionSheetEmail = true
          // T.openDialogCallback({
          //   onlyName: "up_openDialogCallback_send_" + _item.click,
          //   title: "选择发送" + _item.name,
          //   operateIds: that.operateIds,
          //   nBaseType: that.nBaseType,
          // }, function (ret, err) {
          //   if (ret.buttonIndex == 1) {
          //     that.itemSelect();
          //   }
          // }, "actionSheet_" + _item.click);
          break
        case 'link':
          if (!data.otherLink.url) {
            Toast('生成链接失败，请稍候重试')
          }
          copy(encodeURI(data.otherLink.url))
          // api.require('clipBoard').set({
          //   value: encodeURI(that.otherLink.url)
          // }, function (ret, err) {
          //   T.toast(ret.status ? '复制成功' : '复制失败');
          // });
          break
      }
    }

    const copy = value => {
      try {
        toClipboard(value)
        Toast('复制成功！')
      } catch (e) {
        Toast('复制失败！')
      }
    }
    // 生成复制链接
    const savelink = async () => {
      var expireTimes = dayjs().add(Number(data.otherLink.value), 'day').valueOf()
      const ret = await $api.cloudDisk.generalPost(window.location.origin + '/filestore' + '/' + (data.nBaseType) + '/savelink?', { id: data.operateIds, expireTimes: expireTimes })
      if (ret && ret.code === 1) {
        data.otherLink.url = window.location.origin + '/filestore' + '/' + (data.nBaseType) + '/shareLink?id=' + ret.attribute.result
      } else {
        data.otherLink.url = ''
      }
    }
    return { ...toRefs(data), $general, confirm, cancelActionSheetSend, cancelActionSheetEmail, cancelActionSheetPerson, cancelActionSheetDepart, itemCancel, itemSelect, itemClick, open }
  }
}
</script>
<style lang="less" scoped>
.actionSheet_send {
  #app {
    height: 100%;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  #app .van-overlay {
    background-color: rgba(0, 0, 0, 0);
  }

  #app .van-action-sheet {
    height: 80%;
  }

  .box_warp {
    background: #fff;
    border-radius: 10px 10px 0px 0px;
    width: 100%;
    height: 500px;
  }

  .header_box {
    background-color: #fff;
    min-height: 45px;
  }

  .header_cancel {
    padding: 10px 15px;
    color: #666666;
  }

  .header_hint {
    font-weight: 600;
    color: #333333;
    text-align: center;
  }

  .header_ok {
    padding: 10px 15px;
    font-weight: 600;
  }

  .send_box {
    padding: 5px 0;
    background-color: #fff;
  }

  .send_hint {
    padding: 14px 15px;
    color: #999;
  }

  .send_item {
    width: 25%;
    padding: 0 5px;
    margin-bottom: 19px;
  }

  .send_item_img {
    border-radius: 50%;
    overflow: hidden;
    margin: auto;
    margin-bottom: 5px;
    background-color: #f8f8f8;
  }

  .send_text {
    text-align: center;
  }

  .other_add {
    padding: 0 15px;
    margin-top: -5px;
    margin-bottom: 19px;
  }

  body .van-radio-group {
    margin-left: 19px;
  }

  body .van-radio--horizontal {
    margin-right: 19px;
  }
}
</style>

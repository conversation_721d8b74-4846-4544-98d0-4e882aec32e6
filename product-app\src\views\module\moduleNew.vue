<template>
  <div class="module-new">
    <!--顶部默认背景-->
    <div class="bg_default T-flexbox-vertical flex_align_center flex_justify_content"
         :style="'padding-top:'+safeAreaTop+'px;background-image: url('+require(SYS_IF_ZX?'../../assets/img/bg_module_top_zx.png':'../../assets/img/bg_module_top_rd.png') ">
      <!--<img class="bg_default_icon" src="../../../images/top_bg1.png" />-->
      <div v-if="pageType=='page'"
           :style="'top:'+safeAreaTop+'px'"
           class="btnLeft_box flex_box">
        <div @click="close()"
             class="img_btn flex_box flex_align_center flex_justify_content"><img :style="$general.loadConfigurationSize(0,'h')"
               src="../../assets/img/icon_back1.png" /></div>
      </div>
      <van-image v-if="SYS_IF_ZX"
                 :style="$general.loadConfigurationSize([21,24])"
                 fit="100%"
                 :src="logoTheme"></van-image>
      <div class="bg_default_text"
           :style="$general.loadConfiguration(8)+(!SYS_IF_ZX?'color:#FFFFFF;text-align: left;width:100%;padding-left:33px;':'')">{{appName}}</div>
      <!-- <div v-if="!SYS_IF_ZX"
           class="bg_default_text_small"
           :style="$general.loadConfiguration(-2)">护航您的履职路</div> -->
    </div>
    <!--<div style="height: 176px;width: 100%;box-sizing: content-box;" :style="'padding-top:'+safeAreaTop+'px;'"></div>-->
    <!--二、菜单栏目-->
    <van-grid clickable
              :column-num="3">
      <van-grid-item v-for="(item,index) in menuList"
                     :key="index"
                     :badge="item.pointNumber>0?(item.pointNumber>99?'99+':item.pointNumber):''"
                     :style="$general.loadConfiguration(-2)"
                     @click="itemClick(item)">
        <template v-slot:default>
          <van-image fit="cover"
                     :style="$general.loadConfigurationSize(18)+'margin-bottom:8px;'"
                     :src="item.url"></van-image>
          <div :style="$general.loadConfiguration(-2)+'font-weight: 500;color: #222222;'"
               v-html="item.name"></div>
          <p v-if="item.pointNumber > 0"
             class="flex_box flex_align_center flex_justify_content text_one"
             :class="item.pointType == 'big'?'footer_item_hot_big':'footer_item_hot'"
             :style="item.pointType == 'big'?$general.loadConfiguration(-4)+$general.loadConfigurationSize(4):$general.loadConfigurationSize(-6)"
             v-html="item.pointType == 'big'?(item.pointNumber>99?'...':item.pointNumber):''"></p>
        </template>
      </van-grid-item>
    </van-grid>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Toast } from 'vant'
export default {
  name: 'moduleNew',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      img_hot: require($ifzx ? '../../assets/img/icon_img_hot_zx.png' : '../../assets/img/icon_img_hot_rd.png'),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: '1',
      carouselIndex: 0,
      carouselClick: true,
      carouselList: [],
      dataList: [],
      appName: '',
      logoTheme: '',
      menuList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      if (route.query.module) {
        data.module = route.query.module
      }
      getList()
    })

    watch(() => data.dataList, (newName, oldName) => {

    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }

    // 列表请求
    const getList = async () => {
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      var res = await $api.general.appList({ parentId: '340390410742398976', areaId: data.user.areaId })

      var { data: list, total } = res
      // list = list[0].children || []
      list = [{
        clickSort: null,
        createDate: '2022-03-24 14:06',
        iconUrl: 'http://123.206.212.39:22404/lzt/sysImages/议案icon-1650871970041.png',
        id: '450149068522389504',
        infoUrl: 'widget://html/module/mo_sug_motion/motion_win.html',
        infoUrl2: '/suggestList',
        isPublic: true,
        moduleColumn: null,
        moduleColumnView: null,
        name: '掌上议案',
        needRecord: false,
        parentId: '340390410742398976',
        remarks: null,
        selectIconUrl: null,
        sort: 2,
        type: '8'
      }, {
        clickSort: null,
        createDate: '2020-07-10 16:47',
        iconUrl: 'http://123.206.212.39:22404/lzt/sysImages/会议活动@3x.png',
        id: '224784406689087488',
        infoUrl: 'widget://html/module/mo_activity/activity_win.html',
        infoUrl2: 'activityList',
        isPublic: true,
        moduleColumn: null,
        moduleColumnView: null,
        name: '会议活动',
        needRecord: false,
        parentId: '340390410742398976',
        remarks: null,
        selectIconUrl: null,
        sort: 4,
        type: '49'
      }, {
        clickSort: null,
        createDate: '2020-07-10 16:53',
        iconUrl: 'http://123.206.212.39:22404/lzt/sysImages/学习培训@3x.png',
        id: '224785778507513856',
        infoUrl: 'widget://html/module/mo_training/training_win.html',
        infoUrl2: 'newsList',
        isPublic: true,
        moduleColumn: null,
        moduleColumnView: null,
        name: '学习培训',
        needRecord: false,
        parentId: '340390410742398976',
        remarks: 'module=3',
        selectIconUrl: 'http://123.206.212.39:22404/lzt/sysImages/def_head_img.jpg',
        sort: 8,
        type: '106'
      }]
      const newData = []
      list.forEach(item => {
        item.url = item.iconUrl// 图标地址
        item.pointType = 'big'// 红点类型
        item.pointNumber = 0// 数量
        if (item.name === '掌上议案') {
          item.name = '建议办理'
        } else if (item.name === '会议活动') {
          item.name = '国家有关机关联系代表'
        } else if (item.name === '学习培训') {
          item.name = '知情知政'
        }
        if (item.remarks === 'module=3') {
          item.remarks = 'module=6'
        }
        if (item.infoUrl2 && item.infoUrl2 !== '#') {
          newData.push(item)
        }
      })
      data.menuList = data.menuList.concat(newData)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }

    // 中间栏目点击
    const itemClick = (_item) => {
      console.log(JSON.stringify(_item))
      if (_item.infoUrl2) {
        var routerStr = _item.infoUrl2 + '?' + _item.remarks
        console.log(routerStr)
        var myParam = { title: _item.name }
        if (_item.remarks) {
          const a = _item.remarks.split('&')
          a.forEach(element => {
            myParam[element.split('=')[0]] = element.split('=')[1]
          })
        }
        router.push({ path: routerStr, query: myParam })
      } else {
        Toast('请配置好H5路由')
      }
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), search, onClickLeft, onRefresh, onLoad, $general, itemClick }
  }
}
</script>
<style lang="less" scoped>
.module-new {
  .van-grid {
    padding: 7px 14px;
    margin-top: -35px;
    border-top: 0;
  }
  .van-grid-item {
    padding: 0 5px;
    margin-top: 14px;
  }
  .van-grid-item__content {
    background: #fff;
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.08);
    border-radius: 4px;
  }
  .van-grid-item__text {
    margin-top: 7px;
  }
  #app .van-grid-item__content {
    padding: 20px 0;
    position: relative;
  }
  .footer_item_hot {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #f92323;
    border-radius: 50%;
  }
  .footer_item_hot_big {
    position: absolute;
    top: 4px;
    right: 4px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
  }
  .bg_default {
    background-position: bottom;
    background-size: cover;
    height: 220px;
  }
  [class*="van-hairline"]::after {
    border: 0 solid #ebedf0;
  }
  .bg_default_text_small {
    color: #ffffff;
    text-align: left;
    width: 100%;
    padding-left: 33px;
    margin-top: 10px;
  }
}
</style>

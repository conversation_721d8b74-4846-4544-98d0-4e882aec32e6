<template>
  <div class="committeeLivingRoom">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <van-tabs v-model:active="switchs.type"
                :color="appTheme"
                swipeable
                sticky
                :offset-top="isShowHead?'46px':'0'"
                :title-active-color="appTheme"
                :ellipsis="false">
        <van-tab v-for="(item,index) in switchs.data"
                 :key="index"
                 :title="item.label"
                 :name="item.value">
          <!--搜索-->
          <van-pull-refresh v-model="refreshing"
                            @refresh="onRefresh">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      offset="52"
                      @load="onLoad">
              <!--数据列表-->
              <ul class="onduty_box">
                <li class="onduty_item"
                    v-for="(item) in dataList"
                    :key="item.id"
                    @click="openDetails(item)">
                  <!--今日值班标签-->
                  <div v-if="switchs.type == 0"
                       style="position: absolute;left: 10px;top:-7px;">
                    <van-icon :size="(($general.appFontSize-100))+'px'"
                              :color="appTheme"
                              name="bookmark"></van-icon>
                  </div>
                  <!--值班标题-->
                  <div class="onduty_main_s text_two ouduty_title"
                       :style="$general.loadConfiguration(-1)">{{item.title}}</div>
                  <template v-if="switchs.type == 0">
                    <!--值班委员信息-->
                    <div class="onduty_user flex_box flex_align_center">
                      <img class="onduty_user_img"
                           :style="$general.loadConfigurationSize(22)"
                           :src="item.url" />
                      <!-- :alt="cacheImg(item)" -->
                      <div class="flex_placeholder">
                        <div class="flex_box flex_align_center">
                          <div class="onduty_main_s"
                               :style="$general.loadConfiguration(-1)">{{item.name}}</div>
                          <div class="onduty_user_dele"
                               :style="$general.loadConfiguration(-4)">{{item.deleName}}</div>
                        </div>
                        <div v-if="item.position"
                             class="onduty_user_position"
                             :style="$general.loadConfiguration(-4)">
                          {{item.position}}</div>
                      </div>
                    </div>
                    <!--今日值班信息附加信息-->
                    <div class="onduty_today_add flex_box flex_align_center">
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'color:#666;'">转发</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:5px;font-family: DIN;color:'+appTheme">
                        {{item.shareCount}}</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:19px;color:#666;'">
                        留言</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:5px;font-family: DIN;color:'+appTheme">
                        {{item.letterCount}}</div>
                      <div class="flex_placeholder"></div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'color:#999;'">点击量</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:5px;font-family: DIN;color:#999'">
                        {{item.clickCount}}</div>
                    </div>
                  </template>
                  <template v-else-if="switchs.type == 2">
                    <div class="flex_box flex_align_center"
                         style="margin-top:5px;">
                      <div class="onduty_main_s"
                           :style="$general.loadConfiguration(-3)+'font-weight: 400;'">值班时间：</div>
                      <div class="onduty_main_s"
                           :style="$general.loadConfiguration(-3)+'font-weight: 400;color: #666666;'">
                        {{dayjs(item.time).format('YYYY-MM-DD')}}</div>
                    </div>
                    <div class="flex_box"
                         style="margin-top:5px;">
                      <div class="onduty_main_s"
                           :style="$general.loadConfiguration(-3)+'font-weight: 400;'">值班委员：</div>
                      <div class="flex_placeholder">
                        <div class="flex_box flex_align_center">
                          <div class="onduty_main_s"
                               :style="$general.loadConfiguration(-3)+'font-weight: 400;color: #666666;'">{{item.name}}
                          </div>
                          <div class="onduty_user_dele"
                               :style="$general.loadConfiguration(-6)">{{item.deleName}}</div>
                        </div>
                        <div class="onduty_main_s text_one2"
                             :style="$general.loadConfiguration(-4)+'font-weight: 400;color: #666666;'">{{item.position}}
                        </div>
                      </div>
                    </div>
                  </template>
                  <template v-else-if="switchs.type == 1">
                    <div class="flex_box flex_align_center"
                         style="margin-top:5px;">
                      <div class="onduty_main_s"
                           :style="$general.loadConfiguration(-4)+'font-weight: 400;color: #666666;'">
                        {{item.name}}</div>
                      <div class="onduty_main_s"
                           :style="$general.loadConfiguration(-4)+'font-weight: 400;color: #666666;margin-left:19px;'">
                        {{dayjs(item.time).format('YYYY-MM-DD')}}</div>
                    </div>
                    <div class="onduty_today_add flex_box flex_align_center">
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'color:#666;'">转发</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:5px;font-family: DIN;color:'+appTheme">
                        {{item.shareCount}}</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:19px;color:#666;'">
                        留言</div>
                      <div class="onduty_today_hint"
                           :style="$general.loadConfiguration(-4)+'margin-left:5px;font-family: DIN;color:'+appTheme">
                        {{item.letterCount}}</div>
                      <div class="flex_placeholder"></div>
                    </div>
                  </template>
                </li>
                <!--今日值班工作人员-->
                <li v-if="switchs.type == 0 && todayWork.show"
                    class="onduty_item">
                  <div class="flex_box flex_align_center">
                    <div class="onduty_main_s"
                         :style="$general.loadConfiguration(-1)">工作人员：</div>
                    <div class="onduty_main_s"
                         :style="$general.loadConfiguration(-1)+'font-weight: 400;'">{{todayWork.name}}
                    </div>
                  </div>
                  <div class="flex_box flex_align_center"
                       style="margin-top:5px;">
                    <div class="onduty_main_s"
                         :style="$general.loadConfiguration(-1)">联系方式：</div>
                    <div class="onduty_main_s"
                         :style="$general.loadConfiguration(-1)+'font-weight: 400;'">{{todayWork.phone}}
                    </div>
                  </div>
                </li>

              </ul>
              <!--加载中提示 首次为骨架屏-->
              <div v-if="showSkeleton"
                   class="notText">
                <van-skeleton v-for="(item,index) in 3"
                              :key="index"
                              title
                              :row="3"></van-skeleton>
              </div>
              <template v-else-if="dataList.length == 0">
                <van-empty :style="$general.loadConfiguration(-2)">
                  <template #description>
                    <div class="van-empty__description_text"
                         :style="$general.loadConfiguration(-1)"
                         v-html="'暂无数据'"></div>
                  </template>
                </van-empty>
              </template>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
    </div>
  </div>
  <van-action-sheet cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
  <!-- v-model:show="show"
                    :actions="actions"
                    :description="description" -->
</template>
<script>
import { useRouter, useRoute } from 'vue-router'

import { inject, reactive, onMounted, toRefs, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Skeleton } from 'vant'
export default {
  name: 'keyWork',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Skeleton.name]: Skeleton
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const data = reactive({
      switchs: { type: '2', data: [{ label: '今日值班', type: '1' }, { label: '值班回顾', type: '2' }, { label: '值班预告', type: '3' }] },
      zbType: 1,
      officeId: '',
      attendDate: '',
      isShowHead: $isShowHead,
      SYS_IF_ZX: $ifzx,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      appTheme: $appTheme,
      showSkeleton: true,
      refreshing: false,
      ifInit: false,
      pageNo: 1,
      pageSize: 10,
      seachText: '',
      loading: false,
      finished: false,
      value: '',
      ifCommitteeWY: false,
      isStaff: false,
      dataList: [],
      todayWork: { show: false, name: '', phone: '' }
    })
    onMounted(() => {
      onRefresh()
    })
    watch(() => data.switchs.type, (newName, oldName) => {
      data.dataList = []
      onRefresh()
    })
    // 列表请求
    const getList = async () => {
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      if (data.pageNo === 1) {
        data.dataList = []
      }

      const param = {
        pageNo: 1,
        pageSize: 100,
        keyword: data.seachText,
        type: (data.switchs.type + 1),
        attendDate: data.attendDate
      }

      const res = await $api.committeeLivingRoom.committeeLivingRoomList(param)
      data.dataList = []
      var { data: list, total } = res
      list.forEach(item => {
        const itemData = item
        item.id = itemData.id || ''// id
        item.title = itemData.title || ''// 标题
        item.url = itemData.userHeadImg || ''
        item.name = itemData.userName || ''
        item.position = itemData.position || ''
        item.deleName = itemData.deleName || ''
        item.time = itemData.attendDate || ''
        item.officeId = itemData.officeId || ''
        item.shareCount = itemData.shareCount
        item.letterCount = itemData.letterCount
        item.clickCount = itemData.clickCount
        // item.relateType = that.pageParam.relateType
        item.userName = itemData.userName
        item.userHeadImg = itemData.userHeadImg
        item.attendDate = itemData.attendDate
        item.staffName = itemData.staffName
        item.staffPhone = itemData.staffPhone
        if (data.switchs.type === '1') {
          item.ifCommitteeWY = true
        }
        if (data.switchs.type === '1') {
          item.isStaff = true
        }
        data.dataList.push(item)
      })
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
      if (data.switchs.type === 0 && data.dataList === true) {
        data.todayWork.show = true
        data.todayWork.name = data.dataList[0].staffName || ''
        data.todayWork.phone = data.dataList[0].staffPhone || ''
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    const onSelect = (item) => {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      data.show = false
      // var touchItem = data.dataList[data.onTouchIndex]
      switch (item.name) {
        case '':
          break
      }
    }

    const openDetails = (rows) => {
      router.push({
        path: 'committeeLivingRoomDetails',
        query: {
          userName: rows.userName,
          userHeadImg: rows.userHeadImg,
          position: rows.position,
          attendDate: rows.attendDate,
          id: rows.id,
          officeId: rows.officeId
        }
      })
    }
    const test = (val) => {
      console.log(val)
    }

    return { ...toRefs(data), test, onRefresh, $general, openDetails, confirm, onSelect, data, getList, dayjs, onLoad }
  }
}
</script>
<style lang="less" scoped>
.committeeLivingRoom {
  .van-tabs {
    background: #fff;
  }
  .search_box {
    background: #ffffff;
  }
  .a_box_warp {
    background: #ffffff;
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
    overflow: hidden;
  }
  .a_search_box {
    padding: 14px 15px 0 15px;
  }
  .a_search_select_box {
    padding: 2px 0 2px 10px;
  }
  .a_search_select_text {
    color: #222222;
    font-weight: 500;
    line-height: 1.46;
  }
  .a_search_box form {
    padding: 0 13px;
  }
  .a_search_btn_box {
    padding: 9px 16px;
  }
  .a_search_select_text_icon {
    position: relative;
    margin-left: 6px;
    width: 13px;
  }
  .a_search_select_text_icon::after {
    position: absolute;
    top: 50%;
    margin-top: -5px;
    border: 3px solid;
    border-color: transparent transparent #222 #222;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    opacity: 0.8;
    content: "";
  }
  .a_select_btn_box {
    background: #666666;
    margin-left: 5px;
    font-weight: 500;
    line-height: 1.5;
    color: #ffffff;
    padding: 7px 11px;
    border-radius: 2px;
  }

  .search-dropdown-menu {
    margin-right: 15px;
    padding: 10px 0;
  }
  .van-dropdown-menu.van-hairline--top-bottom::after {
    border-width: 0 0;
  }
  .onduty_box {
    padding: 10px 15px 0 15px;

    .onduty_item {
      position: relative;
      padding: 14px 12px;
      background: #ffffff;
      box-shadow: 0px 2px 8px rgba(24, 64, 118, 0.12);
      border-radius: 4px;

      .onduty_main_s {
        font-weight: 600;
        color: #333333;
        line-height: 1.46;
      }
      .text_two {
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
      }
      // .ouduty_title {
      //   margin-top: 30px;
      // }
      .onduty_today_add {
        margin-top: 10px;

        .onduty_today_hint {
          line-height: 1.3;
        }
      }
    }
    .onduty_item + .onduty_item {
      margin-top: 10px;
    }
    .onduty_user {
      background: #f8f9fa;
      border-radius: 4px;
      padding: 14px;
      margin-top: 10px;

      .onduty_user_img {
        object-fit: contain;
        background: #ffffff;
        margin-right: 10px;
      }

      .onduty_user_dele {
        color: #999999;
        margin-left: 10px;
        line-height: 1.3;
      }
      .onduty_user_position {
        color: #666666;
        margin-top: 4px;
        line-height: 1.33;
      }
    }
  }
  .list_title {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    opacity: 1;
    padding: 10px;
  }
  .bottom_box {
    padding: 0 10px 10px;
    color: #999999;
    width: 100%;
  }
  .bottom_left {
    width: 70%;
  }
  .list_name {
    margin-right: 10px;
  }
  .bottom_right {
    width: 30%;
  }
  .bottom_right .van-tag {
    float: right; /*margin-right:-10px;width:65px;overflow: hidden;white-space:nowrap;*/
  }
  .list_item {
    width: 100%;
  }
  .right_btn {
    width: 60px;
  }
  .btn_box {
    text-align: center;
    background: #3088fe;
    margin-top: 6px;
    margin-right: 10px;
    padding: 5px 0;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
  }
  .addmsg {
    width: 56px;
    height: 56px;
    text-align: center;
    background: #3088fe;
    box-shadow: 0 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    opacity: 1;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    color: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 50px;
    margin: auto;
  }
  #app .van-dropdown-item {
    margin-top: 10px;
  }
}
</style>

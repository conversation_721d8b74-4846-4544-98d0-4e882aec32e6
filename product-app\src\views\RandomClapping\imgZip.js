export const imgZip = (event, fn) => {
  const file = event.file
  const reader = new FileReader()
  var base64Data = ''
  reader.onload = function (e) {
    const img = new Image()
    img.onload = function () {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      // 设置canvas的尺寸以便容纳压缩后的图片
      const MAX_WIDTH = 800
      const MAX_HEIGHT = 600
      var width = img.width
      var height = img.height
      if (width > height) {
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width
          width = MAX_WIDTH
        }
      } else {
        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height
          height = MAX_HEIGHT
        }
      }
      canvas.width = width
      canvas.height = height
      // 将图片绘制到canvas并压缩
      ctx.drawImage(img, 0, 0, width, height)

      // 压缩后的图片数据
      const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.7) // 第二个参数为压缩质量，0.7表示70%质量
      base64Data = compressedDataUrl
      // 将base64转换为blob
      var dataURLtoBlob = function (dataurl) {
        var arr = dataurl.split(',')
        var mime = arr[0].match(/:(.*?);/)[1]
        var bstr = atob(arr[1])
        var n = bstr.length
        var u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new Blob([u8arr], { type: mime })
      }
      // 将blob转换为file
      var blobToFile = function (theBlob, fileName) {
        theBlob.lastModifiedDate = new Date()
        var FileData = new File([theBlob], fileName)
        return FileData
      }
      // 调用
      var blob = dataURLtoBlob(base64Data)
      var files = blobToFile(blob, file.name)
      fn(files)
    }
    img.src = e.target.result
  }
  reader.readAsDataURL(file)
}

<template>
  <div class="inputBox">
    <template v-if="showType == 1">
      <div class="type1_box flex_box flex_align_center">
        <div class="flex_placeholder"
             :style="$general.loadConfiguration()">
          <input type="text"
                 class="flex_placeholder"
                 @click="!input_not?changeType(2):''"
                 v-model="input_value"
                 readonly="true"
                 :placeholder="input_placeholder" />
        </div>
        <!-- <div v-if="showRead"
             class="comment_box flex_box flex_align_end">
          <van-icon :color="'#000'"
                    :size="21"
                    name="eye-o"></van-icon>
          <div v-html="readInfo.num"
               :style="$general.loadConfiguration(-6)+'color:#000'"></div>
        </div> -->
        <div v-if="showComment"
             class="comment_box flex_box flex_align_end"
             @click="downComment()">
          <van-icon :color="'#000'"
                    :size="21"
                    name="comment-o"></van-icon>
          <div v-html="commentInfo.num"
               :style="$general.loadConfiguration(-6)+'color:#000'"></div>
        </div>
        <div v-if="showLike"
             class="like_box flex_box flex_align_end"
             @click="downLike()">
          <van-icon :color="likeInfo.isLike?appTheme:'#000'"
                    :size="21"
                    :name="likeInfo.isLike?'good-job':'good-job-o'"></van-icon>
          <div :style="$general.loadConfiguration(-6)+'color:'+(likeInfo.isLike?appTheme:'#000')"
               v-html="likeInfo.num"></div>
        </div>
      </div>
    </template>
    <van-overlay v-else-if="showType == 2"
                 z-index="99999999"
                 :show="showType == 2"
                 @click="show = false">
      <div class="type2 T-flexbox-vertical"
           @click="changeType(1)">
        <div class="flex_placeholder"></div>
        <div class="type2_box"
             @click.stop>
          <textarea id="textarea"
                    autofocus
                    v-model="input_value"
                    :style="$general.loadConfiguration()"
                    :placeholder="input_placeholder"></textarea>
          <ul v-if="nImgs.length != 0"
              class="select_img_box flex_box T-flex-flow-row-wrap">
            <li v-for="(item,index) in nImgs"
                :key="index">
              <img v-if="item.state > 0"
                   :src="item.state == 1?require('../../assets/img/icon_doubt.png'):item.state == 2?require('../../assets/img/icon_upload_success.png'):require('../../assets/img/icon_upload_fail.png')"
                   class="select_img_state"
                   :style="$general.loadConfigurationSize()" />
              <img src="../../assets/img/icon_upload_delete.png"
                   class="select_img_del"
                   :style="$general.loadConfigurationSize(4)"
                   @click="deleteSelectImg(item,index)" />
              <div :style="'background-image:url('+item.url+')'"
                   @click="openSelectImg(nImgs,index)"></div>
            </li>
          </ul>
          <div class="select_btn_box flex_box flex_align_center">
            <img v-if="showAttach"
                 src="../../assets/img/icon_upload_photo.png"
                 :style="$general.loadConfigurationSize(4,'h')"
                 class="btn_camera"
                 @click="openPic(1)" />
            <img v-if="showAttach"
                 src="../../assets/img/icon_upload_picture.png"
                 :style="$general.loadConfigurationSize(4,'h')"
                 class="btn_image"
                 @click="openPic(2)" />
            <div class="flex_placeholder"></div>
            <div class="btn_submit"
                 :style="$general.loadConfiguration() + 'color:'+(input_value?appTheme:'#ccc')+';'"
                 @click="mgcHandle()">{{btnok.text}}</div>
          </div>
        </div>
      </div>
    </van-overlay>
    <template v-else-if="showType == 3">
      <div class="type1_box flex_box flex_align_center flex_justify_content">
        <div v-if="showLike"
             class="like_box flex_box flex_align_center flex_justify_content onlyLike"
             @click="downLike()">
          <van-icon :color="likeInfo.isLike?appTheme:'#000'"
                    :size="21"
                    :name="likeInfo.isLike?'good-job':'good-job-o'"></van-icon>
          <div :style="$general.loadConfiguration(-6)+'color:'+(likeInfo.isLike?appTheme:'#000')"
               v-html="likeInfo.num"></div>
        </div>
      </div>
    </template>
  </div>
  <van-uploader style="display: none;"
                v-model="fileList"
                :max-count='9'
                :after-read="afterRead"
                ref="chatImg">
  </van-uploader>
  <van-uploader style="display: none;"
                v-model="fileList"
                :max-count='9'
                capture="camera"
                :after-read="afterReadCamera"
                ref="chatImgCamera">
  </van-uploader>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { Toast, NavBar, Sticky, ActionSheet, Overlay, Uploader, ImagePreview } from 'vant'
import moment from 'moment'
export default {
  name: 'inputBox',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [Uploader.name]: Uploader,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: [
    'inputData',
    'type',
    'id',
    'pageType'
  ],
  setup (props, context) {
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      send: route.query.send || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      showType: 1, // 1平常的框  2为点击放大的框 3只有点赞
      input_value: '', // 输入框中的字
      input_placeholder: '', // 输入框中的提示文字
      input_not: false, // 是否禁止输入
      showComment: true, // 显示评论功能
      showRead: true, // 显示浏览
      showLike: true, // 显示点赞功能
      showAttach: true, // 显示添加附件(图片)
      readInfo: { num: 0, data: ['../../assets/img/X.png'] }, // 评论相关
      commentInfo: { num: 0, data: ['../../assets/img/X.png'] }, // 评论相关
      likeInfo: { isLike: false, num: 0, data: ['../../assets/img/1.png', '../../assets/img/2.png'] }, // 点赞相关  是否点赞  图标  data[0]没点赞   1点赞的

      btnok: { text: '发表' },
      // url本地地址  uploadUrl上传后的地址  state状态【0刚开始1上传中2完成3失败】
      module: 'generalComments',
      nImgs: [
        // { url: 'https://file.tapd.cn/compress/compress_img/700?src=https://file.tapd.cn//tfl/captures/2020-03/tapd_36469468_base64_1583892767_11.png', uploadUrl: '', state: 2 }
      ],
      commentPid: '0',
      id: '',
      type: '',
      chatImg: null,
      chatImgCamera: null,
      fileList: []
    })

    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      const inputData = props.inputData
      data.type = props.type
      data.id = props.id
      data.input_placeholder = inputData.input_placeholder// 输入框中的提示文字
      data.input_not = inputData.input_not// 是否禁止输入
      data.showComment = inputData.showComment// 显示评论功能
      data.showLike = inputData.showLike// 显示点赞功能
      data.showAttach = inputData.showAttach// 显示添加附件(图片)
      getStats()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    const getStats = async () => {
      const res = $api.general.getCommentStats({
        keyId: data.id,
        type: data.type,
        areaId: data.user.areaId
      })
      // console.log(res)
      res.then(ret => {
        // console.log(ret)
        const infos = ret.data
        if (infos) {
          data.readInfo.num = infos.browseCount || 0// 浏览数
          data.commentInfo.num = infos.commentCount || 0// 评论数
          data.likeInfo.num = infos.fabulousCount || 0// 点赞数
          // eslint-disable-next-line eqeqeq
          data.likeInfo.isLike = infos.isFabulous == '1'// 是否点赞
          // shareCount分享数isFavorite是否收藏
        }
      })
    }
    const mgcHandle = () => {
      addComment()
    }
    const addComment = async () => {
      var commentPid = data.commentPid || '0'
      var files = data.nImgs || []// 附件 有2种 格式   files:{key:["",""]}   这里是  files:{key:value,key:value}
      var postFiles = ''
      if (files.length !== 0) {
        for (var i = 0; i < files.length; i++) {
          postFiles += (postFiles ? ',' : '') + files[i].uploadId
        }
      }
      const res = await $api.general.saveComment({
        keyId: data.id,
        type: data.type,
        content: data.input_value,
        createBy: data.user.id,
        commentPid: commentPid === '0' ? '' : commentPid,
        attach: postFiles,
        extend: '1',
        isCheck: '1', // 先默认审核通过 后面去除
        areaId: data.user.areaId
      })
      // console.log(res)
      if (res) {
        console.log('评论', res)
        Toast(data.input_placeholder + (res.errcode === 200 ? '成功' : '失败'))
        if (res.errcode === 200) { // 成功  刷新详情页
          data.input_value = ''
          if (data.showType === 2) { // 评论框 打开状态  关掉
            data.showType = 1
            data.input_placeholder = props.inputData.input_placeholder// 输入框中的提示文字
            data.commentPid = '0'
            getStats()
            data.nImgs = []
            data.fileList = []
            context.emit('addCommentEvent', true)
          }
        }
      }
    }
    const changeType = (type, item) => {
      // console.log(item)
      data.showType = type
      if (item) {
        data.input_placeholder = '回复：' + item.name
        data.commentPid = item.id
      } else {
        data.input_placeholder = props.inputData.input_placeholder// 输入框中的提示文字
        data.commentPid = '0'
      }
    }
    const downLike = async () => {
      if (data.input_not) {
        Toast(data.input_placeholder, '', true)
        return
      }
      var likeInfo = data.likeInfo
      if (likeInfo.isLike) { // 当前是已点赞状态数量-1
        if (likeInfo.num > 0) { likeInfo.num-- }
        await $api.general.delFabulous({
          keyId: data.id,
          type: data.type,
          areaId: data.user.areaId
        })
      } else {
        await $api.general.saveFabulous({
          keyId: data.id,
          type: data.type,
          areaId: data.user.areaId
        })
        likeInfo.num++
      }
      likeInfo.isLike = !likeInfo.isLike
    }
    const openPic = (type) => {
      if (data.nImgs.length >= 9) {
        Toast('最多上传9张图片')
        return
      }
      switch (type) {
        case 1:
          data.chatImgCamera.chooseFile()
          break
        case 2:
          data.chatImg.chooseFile()
          break
      }
    }
    const afterRead = async (file) => {
      const item = { url: file.content, uploadUrl: '', uploadId: '', state: 0, module: 'generalComments' }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'generalComments')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.state = 2
        item.uploadUrl = info.filePath || ''
        item.uploadId = info.id || ''
      } else {
        item.state = 3
        item.error = ret.errmsg || ''
      }
      data.nImgs.push(item)
    }
    const afterReadCamera = async (file) => {
      const item = { url: file.content, uploadUrl: '', uploadId: '', state: 0, module: 'generalComments' }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'generalComments')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.state = 2
        item.uploadUrl = info.filePath || ''
        item.uploadId = info.id || ''
      } else {
        item.state = 3
        item.error = ret.errmsg || ''
      }
      data.nImgs.push(item)
    }
    // 删除选择图片
    const deleteSelectImg = (_item, _index) => {
      $general.delItemForKey(_item, data.nImgs)
      data.fileList.splice(0, 1)
    }
    const openSelectImg = (imgs, _index) => {
      var img = []
      imgs.forEach(element => {
        img.push(element.url)
      })
      ImagePreview(img, _index)
    }
    const downComment = () => {

    }
    const search = () => {

    }
    const onRefresh = () => {

    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), moment, $general, search, onClickLeft, onRefresh, onLoad, changeType, downComment, mgcHandle, downLike, getStats, openPic, afterRead, afterReadCamera, deleteSelectImg, openSelectImg }
  }
}
</script>

<style lang="less" scoped>
.inputBox {
  width: 100%;
  background: #fff;
  padding: 10px 0;
  html,
  body {
    background: rgba(0, 0, 0, 0);
    height: 100%;
  }
  #app {
    height: 100%;
  }
  .type1_box {
    padding: 10px 10px 0;
    box-sizing: border-box;
    height: 100%;
    position: relative;
  }
  .type1_box:after {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    content: "";
    width: 200%;
    height: 2px;
    background-color: #dedede;
    transform: scale(0.5);
    -webkit-transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    box-sizing: border-box;
  }
  .type1_box input {
    border: 1px solid #dedede;
    border-radius: 3px;
    padding: 6px 10px;
    box-sizing: border-box;
    width: 100%;
    font-size: inherit;
    font-family: inherit;
  }

  .comment_box,
  .like_box {
    margin-left: 8px;
    margin-top: -2px;
  }
  .comment_box img {
  }
  .like_box img {
  }
  .comment_box div,
  .like_box div {
    color: #777;
    margin-left: 3px;
  }

  .type2 {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }
  .type2_box {
    padding: 10px 0 5px 0;
    box-sizing: border-box;
    background: #fafafa;
  }
  .type2_box textarea {
    width: 100%;
    height: 120px;
    background: #fff;
    border-radius: 8px;
    padding: 5px;
    box-sizing: border-box;
  }
  .select_btn_box {
    padding: 5px 10px;
    box-sizing: border-box;
  }
  .btn_camera {
    margin-right: 19px;
  }
  .btn_image {
  }

  .select_img_box {
    max-height: 130px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 10px;
    box-sizing: border-box;
  }
  .select_img_box li {
    width: 25%;
    position: relative;
    padding: 0 6px;
    box-sizing: border-box;
  }
  .select_img_box li div {
    width: 100%;
    height: 75px;
    background-size: cover;
    -webkit-background-size: cover;
    background-position: 50%;
  }
  .select_img_box li .select_img_del {
    position: absolute;
    top: 3px;
    right: 10px;
  }
  .select_img_box li .select_img_state {
    position: absolute;
    bottom: 3px;
    right: 10px;
  }

  .onlyLike {
    border-radius: 50%;
    height: 40px;
  }
  .onlyLike img {
    width: 22px;
  }
}
</style>

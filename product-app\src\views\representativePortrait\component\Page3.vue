<template>
  <div class="Pie3">
    <!-- @touchstart="handleTouchStart"
         @touchmove="handleTouchMove" -->
    <div class="container_swipe">
      <div class="pages3">
        <img src="../../../assets/img/timeAxis/g_bg03.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item2">
          <div class="pages_item2_text0"
               :class="{ 'fade-in1': showText1 }">{{ year3 }}年{{ month3 }}月{{ day3 }}日</div>
          <div class="pages_item2_text1"
               :class="{ 'fade-in1': showText1 }">您第一次参与意见征集</div>
          <div class="pages_item2_text2"
               :class="{ 'fade-in1': showText2 }">全年</div>
          <div class="pages_item2_text3"
               :class="{ 'fade-in1': showText3 }">您共参与意见征集<span>{{participateServer}}次</span></div>
          <div class="pages_item2_text4"
               :class="{ 'fade-in1': showText3 }">发表意见<span>{{publishServerTownhallAdvice}}次</span></div>
        </div>
        <div class="top_img">
          <img src="../../../assets/img/timeAxis/g_bg03_top.png"
               alt=""
               style="width:100%;height:100%;">
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, onUpdated } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'Page3',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean,
    pageData: Object
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      year3: '', // 第一次参与意见征集年
      month3: '', // 第一次参与意见征集月
      day3: '', // 第一次参与意见征集日
      participateServer: 0, // 共参与意见征集
      publishServerTownhallAdvice: 0, // 共发表意见
      showText1: false,
      showText2: false,
      showText3: false
    })
    onUpdated(() => {
      data.showText1 = props.showText1
      data.showText2 = props.showText2
      data.showText3 = props.showText3
      data.year3 = props.pageData.firstPublishSurver ? props.pageData.firstPublishSurver.substring(0, 4) : ''
      data.month3 = props.pageData.firstPublishSurver ? props.pageData.firstPublishSurver.substring(5, 7) : ''
      data.day3 = props.pageData.firstPublishSurver ? props.pageData.firstPublishSurver.substring(8, 10) : ''
      data.participateServer = props.pageData.participateServer
      data.publishServerTownhallAdvice = props.pageData.publishServerTownhallAdvice
    })
    onMounted(() => {
      // setTimeout(() => {
      //   data.showText1 = true
      // }, 300)
      // setTimeout(() => {
      //   data.showText2 = true
      // }, 1200)
      // setTimeout(() => {
      //   data.showText3 = true
      // }, 2200)
      // memberPortraitDutyTime()
      preventScroll()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    // 获取数据
    // const memberPortraitDutyTime = async () => {
    //   const res = await $api.representativePortrait.memberPortraitDutyTime()
    //   console.log('获取数据===>>', res)
    //   data.year3 = res.data.firstPublishSurver.substring(0, 4)
    //   data.month3 = res.data.firstPublishSurver.substring(5, 7)
    //   data.day3 = res.data.firstPublishSurver.substring(8, 10)
    //   data.participateServer = res.data.participateServer
    //   data.publishServerTownhallAdvice = res.data.publishServerTownhallAdvice
    // }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        setTimeout(() => {
          router.push('/Page4')
        }, 500) // 延迟500毫秒
      } else {
        // 向下滑动
        console.log('向下滑动')
        setTimeout(() => {
          router.push('/Page2')
        }, 500) // 延迟500毫秒
      }
    }
    return { ...toRefs(data), $general, handleTouchStart, handleTouchMove }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
.Pie3 {
  width: 100%;
  min-height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages_item2_text0,
    .pages_item2_text1,
    .pages_item2_text2,
    .pages_item2_text3,
    .pages_item2_text4 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item2_text0.fade-in,
    .pages_item2_text1.fade-in,
    .pages_item2_text2.fade-in,
    .pages_item2_text3.fade-in,
    .pages_item2_text4.fade-in {
      opacity: 1;
    }
    .pages3 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages_item2 {
        position: absolute;
        top: 10.5rem;
        // left: 2.5rem;
        .pages_item2_text0 {
          color: #527ffd;
          font-size: 0.65rem;
          font-family: "YouSheBiaoTiHei-2";
          text-align: center;
        }
        .pages_item2_text1 {
          color: #2555d0;
          font-size: 0.45rem;
          margin-top: 0.1rem;
          text-align: center;
        }
        .pages_item2_text2 {
          color: #2555d0;
          font-size: 0.45rem;
          margin-top: 0.3rem;
          letter-spacing: 3px;
          text-align: center;
        }
        .pages_item2_text3 {
          color: #2555d0;
          font-size: 0.45rem;
          letter-spacing: 2px;
          margin-top: 0.2rem;
          text-align: center;
          span {
            font-size: 0.8rem;
            color: #527ffd;
            font-family: "YouSheBiaoTiHei-2";
          }
        }
        .pages_item2_text4 {
          color: #2555d0;
          font-size: 0.45rem;
          letter-spacing: 2px;
          margin-top: 0.2rem;
          text-align: center;
          span {
            font-size: 0.8rem;
            color: #527ffd;
            font-family: "YouSheBiaoTiHei-2";
          }
        }
      }
      .top_img {
        position: absolute;
        top: 3.5rem;
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    .more {
      position: absolute;
      bottom: 1rem;
      left: 4.5rem;
      .drop {
        font-size: 30px;
        animation: drop 1s linear infinite;
      }
    }
    @keyframes drop {
      0% {
        opacity: 0;
        margin-top: 0px;
      }

      25% {
        opacity: 0.5;
        margin-top: -10px;
      }

      50% {
        opacity: 1;
        margin-top: -20px;
      }

      75% {
        opacity: 0.5;
        margin-top: -30px;
      }

      100% {
        opacity: 0;
        margin-top: -40px;
      }
    }
  }
}
</style>

<template>
  <div class="representativePortraitHome">
    <div class="representativePortraitHome_box">
      <img src="../../assets/img/timeAxis/g_home_bg001.png"
           alt="">
      <div class="representativePortraitHome_item">
        <img src="../../assets/img/timeAxis/g_home001.png"
             alt=""
             class="representativePortraitHome_item_img1"
             @click="openList(1)">
        <img src="../../assets/img/timeAxis/g_home002.png"
             alt=""
             class="representativePortraitHome_item_img2"
             @click="openList(2)">
      </div>
    </div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { Image as VanImage } from 'vant'
export default {
  name: 'representativePortraitHome',
  components: {
    [VanImage.name]: VanImage
  },
  setup () {
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      console.log('app进入到了入口页')
    })
    const openList = (type) => {
      switch (type) {
        case 1:
          router.push({ path: 'representativePortraitList' })
          break
        case 2:
          router.push({ path: 'timeAxis' })
          break
        default:
          break
      }
    }
    const onRefresh = () => {
    }
    const onLoad = () => {
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, openList }
  }
}
</script>
<style lang="less" scoped>
.representativePortraitHome {
  width: 100%;
  min-height: 100%;
  .representativePortraitHome_box {
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .representativePortraitHome_item {
      position: absolute;
      top: 35%;
      margin: 50px;
      .representativePortraitHome_item_img1 {
        width: 100%;
        height: 100%;
      }
      .representativePortraitHome_item_img2 {
        width: 100%;
        height: 100%;
        margin-top: 10%;
      }
    }
  }
}
</style>

<template>
  <div class="Pie7">
    <div class="container_swipe">
      <div class="pages7">
        <img src="../../../assets/img/timeAxis/g_bg07.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages7_item_topText">履职足迹</div>
        <div class="pages7_item_text_box">
          <div class="pages7_item_text_year">2023年</div>
          <div class="pages7_item_box"
               ref="scrollContainer">
            <vueSeamless :data="dutyListData"
                         :class-option="defaultOption">
              <div v-for="(item,index) in dutyListData"
                   :key="item.title"
                   class="pages7_item_margin_box"
                   :style="{ 'margin-top': index === 0 ? '0.4rem' : '0.6rem' }">
                <div class="flex_box flex_align_center pages7_item_topText_Box">
                  <div class="pages7_item_text1">——{{item.time}}</div>
                  <div class="pages7_item_text2">{{item.typeName}}</div>
                  <!-- <div class="pages7_item_text3"
                       v-if="item.type=='211'">履职补录</div>
                  <div class="flex_item"
                       v-else></div> -->
                </div>
                <div class="pages7_item_title">{{item.title}}</div>
              </div>
            </vueSeamless>
          </div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, computed, onUpdated } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
import vueSeamless from 'vue-seamless-scroll/src'
export default {
  name: 'Page7',
  components: {
    vueSeamless,
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean,
    pageData: Object
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const scrollContainer = ref(null)
    const defaultOption = computed(() => {
      return {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 2, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: false, // 开启数据实时监控刷新dom
        singleHeight: 200, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    })
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      year6: '', // 第一次参加线上履职学习年
      month6: '', // 第一次参加线上履职学习月
      day6: '', // 第一次参加线上履职学习日
      watchVideoTime: 0, // 观看专题讲座时长
      readBookTime: 0, // 阅读电子数据时长
      showText1: false,
      showText2: false,
      showText3: false,
      dutyListData: []
    })
    onUpdated(() => {
      data.showText1 = props.showText1
      data.showText2 = props.showText2
      data.showText3 = props.showText3
      data.dutyListData = props.pageData
    })
    onMounted(() => {
      // setTimeout(() => {
      //   data.showText1 = true
      // }, 300)
      // setTimeout(() => {
      //   data.showText2 = true
      // }, 1200)
      // setTimeout(() => {
      //   data.showText3 = true
      // }, 2200)
      preventScroll()
      // getAppDutyDetail()
    })
    // 获取履职足迹
    // const getAppDutyDetail = async () => {
    //   const param = {
    //     pageNo: 1,
    //     pageSize: 99,
    //     year: 2023,
    //     userId: data.user.id,
    //     areaId: sessionStorage.getItem('areaId')
    //   }
    //   const res = await $api.performanceFiles.getAppDutyDetail(param)
    //   console.log('获取履职足迹===>', res)
    //   const list = res.data
    //   list.forEach(item => {
    //     var date = new Date(item.date)
    //     var month = date.getMonth() + 1
    //     var day = date.getDate()
    //     var formattedDate = month + '月' + day + '日'
    //     item.time = formattedDate
    //     switch (item.type) {
    //       case 'suggest': // 建议
    //         item.typeName = '建议'
    //         break
    //       case 'proposal': // 提案
    //         item.typeName = '提案'
    //         break
    //       case 'officeOnline': // 委员值班
    //         item.typeName = '委员值班'
    //         break
    //       case 'social': // 社情民意
    //         item.typeName = '社情民意'
    //         break
    //       case 'activity': // 活动
    //       case '49': // 活动
    //         item.typeName = '活动'
    //         break
    //       case 'survey': // 意见征集
    //         item.typeName = '意见征集'
    //         break
    //       case 'learning': // 考试
    //         item.typeName = '学习培训'
    //         break
    //       case 'meet': // 会议
    //         item.typeName = '会议'
    //         break
    //       case 'bill': // 议案
    //         item.typeName = '议案'
    //         break
    //       case '211': // 履职补录
    //         item.typeName = '活动'
    //         break
    //     }
    //   })
    //   data.dutyListData = data.dutyListData.concat(list)
    // }
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      const target = event.target
      if (!target.closest('.pages7_item_box')) {
        event.preventDefault()
      }
    }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      const target = event.target
      if (!target.closest('.pages7_item_box')) {
        if (touchMoveY.value < touchStartY.value) {
          // 向上滑动
          console.log('向上滑动')
          setTimeout(() => {
            router.push('/Page8')
          }, 500) // 延迟500毫秒
        } else {
          // 向下滑动
          console.log('向下滑动')
          setTimeout(() => {
            router.push('/Page6')
          }, 500) // 延迟500毫秒
        }
      }
    }
    return { ...toRefs(data), $general, handleTouchStart, handleTouchMove, scrollContainer, defaultOption }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
@font-face {
  font-family: "YouSheBiaoTiYuan-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiYuan-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
body {
  overflow: auto !important;
}
.Pie7 {
  width: 100%;
  min-height: 100%;
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages_item2_text0,
    .pages_item2_text1,
    .pages_item2_text2,
    .pages_item2_text3,
    .pages_item2_text4 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item2_text0.fade-in,
    .pages_item2_text1.fade-in,
    .pages_item2_text2.fade-in,
    .pages_item2_text3.fade-in,
    .pages_item2_text4.fade-in {
      opacity: 1;
    }
    .pages7 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages7_item_topText {
        position: absolute;
        top: 15%;
        font-size: 0.9rem;
        color: #204a86;
        font-family: "YouSheBiaoTiHei-2";
      }
      .pages7_item_text_box {
        position: absolute;
        top: 28%;
        left: 1rem;
        right: 1.2rem;
        height: 500px;
        width: 300px;
        overflow: hidden !important;
        .pages7_item_text_year {
          color: #3488ff;
          font-size: 0.85rem;
          font-family: "YouSheBiaoTiYuan-2";
          text-align: center;
        }
        .pages7_item_box {
          height: 42vh; /* 设置高度 */
          // overflow: scroll; /* 支持滚动 */
          // height: 200px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .pages7_item_topText_Box {
            justify-content: space-between;
            margin-top: 0.3rem;
            .pages7_item_text1 {
              font-size: 0.35rem;
              color: #3488ff;
              flex: 3;
            }
            .pages7_item_text2 {
              font-size: 0.35rem;
              color: #3488ff;
              flex: 1;
            }
            .pages7_item_text3 {
              font-size: 0.35rem;
              color: #3488ff;
              flex: 1.2;
            }
            .flex_item {
              flex: 1.2;
            }
          }
          .pages7_item_title {
            color: #204a86;
            font-size: 0.4rem;
            margin-top: 0.25rem;
          }
        }
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    .more {
      position: absolute;
      bottom: 1rem;
      left: 4.5rem;
      .drop {
        font-size: 30px;
        animation: drop 1s linear infinite;
      }
    }
    @keyframes drop {
      0% {
        opacity: 0;
        margin-top: 0px;
      }

      25% {
        opacity: 0.5;
        margin-top: -10px;
      }

      50% {
        opacity: 1;
        margin-top: -20px;
      }

      75% {
        opacity: 0.5;
        margin-top: -30px;
      }

      100% {
        opacity: 0;
        margin-top: -40px;
      }
    }
  }
}
</style>

<template>
  <div class="module">
    <!--顶部默认背景-->
    <div class="bg_default T-flexbox-vertical flex_align_center flex_justify_content"
      :style="'padding-top:' + safeAreaTop + 'px;background-image: url(' + require(SYS_IF_ZX ? '../../assets/img/bg_module_top_rd.png' : '../../assets/img/bg_module_top_zx.png')">
      <div class="btnLeft_box">
        <van-image v-if="SYS_IF_ZX" :style="$general.loadConfigurationSize([24, 24])" fit="100%"
          :src="logoTheme"></van-image>
        <p class="representative_title">代表履职通</p>
        <div class="bg_default_text"
          :style="$general.loadConfiguration(8) + (!SYS_IF_ZX ? '' : 'color:#FFFFFF;text-align: center;width:100%;')">
          <van-popover v-model:show="showPopover" :actions="actions" @select="onSelect">
            <template #reference>
              {{ appName }} <span class="play"></span>
            </template>
          </van-popover>
        </div>
      </div>
      <div class="search">
        <div class="search_box" @click="router.push('/searchHome')">
          <van-icon name="search" />
          {{ hot }}
        </div>
      </div>
      <div class="four_Bigboxs">
        <div class="four_box" @click="openScan">
          <img :src="require('../../assets/img/saoyisao.png')" alt="">
          <p>扫一扫</p>
        </div>
        <div class="four_box" @click="goSchedule">
          <img :src="require('../../assets/img/richeng.png')" alt="">
          <p>我的日程</p>
        </div>
        <div class="four_box" v-if="lzShow"
          @click="router.push({ path: '/resumption', query: { id: user.id, types: 'myLZ' } })">
          <img :src="require('../../assets/img/lvzhi.png')" alt="">
          <p>我的履职</p>
        </div>
        <div class="four_box" @click="router.push('/myUser')">
          <img :src="require('../../assets/img/avatar.png')" alt="">
          <p>个人中心</p>
        </div>
      </div>
      <div class="card-item-head" @click="router.push('/leaderDriving')">
      </div>
    </div>
    <!-- 工作应用 -->
    <div class="menu">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>工作应用</span> </div>
      </div>
      <div class="grid">
        <van-grid clickable :column-num="4">
          <van-grid-item v-for="(item, index) in menuList" :key="index" :badge="getPointNumber(item)"
            :style="$general.loadConfiguration(-2)" @click="itemClick(item)">
            <template v-slot:default>
              <van-badge :content="item.pointNumber > 0 ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''">
                <van-image fit="cover" :style="$general.loadConfigurationSize(18) + 'margin-bottom:8px;'"
                  :src="item.url"></van-image>
                <div :style="$general.loadConfiguration(-2) + 'font-weight: 500;color: #222222;'" class="grid_name"> {{
                  item.name }}</div>
              </van-badge>
            </template>
          </van-grid-item>
          <!-- <draggable v-model="menuList">
          </draggable> -->
        </van-grid>
      </div>
    </div>
    <!-- 公告栏 -->
    <div class="news_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text">
          <van-badge :content="msgCount != 0 ? msgCount : ''">
            <span class="title_text_size">公告栏</span>
          </van-badge>
          <span class="more" @click="skipMore('announcement')">更多<van-icon name="arrow" /></span>
        </div>
      </div>
      <div v-if="msgboxList.length > 0" class="news_box_list">
        <ul>
          <li v-for="item, index in msgboxList" :key="item.id" @click="skipDetails('announcement', item)"
            v-show="index <= 2"> {{ item.content }}
            <div class="isRead" v-if="item.isRead == 0 && index <= 3"></div>
          </li>
        </ul>
      </div>
      <div v-else class="nodata">暂无数据</div>
    </div>
    <!-- 代表履职圈 -->
    <div class="representativeCircle_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text">
          <!-- <van-badge :content="committeesayUnread != 0 ? committeesayUnread : ''"> -->
          <span class="title_text_size">代表履职圈</span>
          <!-- </van-badge> -->
          <span class="more" v-if="representativeCircleData.length > 0 && representativeCircleData.length > 5"
            @click="skipMore('resumption')">
            更多
            <van-icon name="arrow" />
          </span>
        </div>
      </div>
      <div v-if="representativeCircleData.length > 0" class="representativeCircle_box_content">
        <ul>
          <li class="representativeCircle_box_li" v-for="item, index in representativeCircleData" :key="item.id"
            @click="skipDetails('resumption', item.id, null, item.isFollow, item.publishBy, item.isFabulous, item.fabulousCount)"
            v-show="index <= 2">
            <div class="representativeCircle_box_top">
              <img :src="item.headImg" alt="" class="representativeCircle_box_top_headImg">
              <div class="representativeCircle_box_name">
                <p class="representativeCircle_box_names">{{ item.publishName }}</p>
                <p class="representativeCircle_box_congressStr" v-if="item.congressStr.length > 0"> {{
                  item.congressStr.join(' | ') + '人大代表' }}
                  {{ item.representerTeam ? '(' + item.representerTeam + ')' : '' }}
                </p>
              </div>
            </div>
            <div class="representativeCircle_box_center">
              <div class="representativeCircle_box_center_content">
                <p v-html="item.content"></p>
              </div>
              <div class="representativeCircle_box_center_attachmentList">
                <van-image position="contain" width="2.5rem" fit="cover" height="2rem" v-for="it in item.attachmentList"
                  :key="it.id" :src="it.filePath" @click.stop="previewCalback(item.attachmentList)" />
              </div>
            </div>
            <div class="representativeCircle_box_buttom">
              <div class="representativeCircle_box_buttom_time">{{ item.publishDate }}</div>
              <div class="representativeCircle_box_buttom_cont">
                <div class="representativeCircle_box_buttom_comment" @click.stop="replyClick(item)">
                  <img :src="require('../../assets/img/icon_comments.png')" alt="">
                  <span>{{ item.commentCount }}</span>
                </div>
                <div class="representativeCircle_box_buttom_like" @click.stop="downLike(item)">
                  <img
                    :src="require(!item.isFabulous ? '../../assets/img/icon_likes.png' : '../../assets/img/icon_likes_on.png')"
                    alt="">
                  <span
                    :style="$general.loadConfiguration(-2) + ';color:' + (item.isFabulous ? '#FE7530' : '#333333') + ';'">{{
                      item.fabulousCount }}</span>
                </div>
              </div>
            </div>
            <div class="likeComment_box">
              <div class="like_box" :style="$general.loadConfiguration(-4)"
                v-if="item.fabulousList != null && item.fabulousList.length">
                <van-icon name="like-o" v-if="item.fabulousList != null && item.fabulousList.length" />
                <span :style="$general.loadConfiguration(-4) + 'margin-bottom: 2px;' + 'line-height: 0.2rem;'"
                  @click.stop="openUserList(it.id)" v-for="(it, ind) in item.fabulousList" :key="ind">
                  {{ ind > 0 ? ',' : '' }} {{ it.userName }} </span>
              </div>
              <div class="comment_box" @click.stop="" v-if="item.children.length != 0">
                <div v-for="(items, indexs) in item.children" :key="indexs">
                  <p style="display: flex;align-items: center;">
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                      @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType }}:
                    </span>
                    <span :style="$general.loadConfiguration(-4) + 'flex:1;'"
                      @click.stop="replyClick(item, items, indexs)">
                      {{ items.content }} </span>
                  </p>
                  <p v-for="(ite, inde) in items.children" :key="inde" :style="$general.loadConfiguration(-4)">
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                      @click.stop="openUserList(ite.createBy)">{{ ite.userName }}</span> 回复
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                      @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType }}:
                    </span>
                    <span :style="$general.loadConfiguration(-4)" @click.stop="replyClick(item, items, inde)"> {{
                      ite.content }} </span>
                  </p>
                </div>
              </div>
              <div class="reply_box" @click.stop="" v-show="item.inputObj.replyShow">
                <div class="reply_box_item">
                  <input type="text" v-model="item.commentObj.content" :style="$general.loadConfiguration(-4)"
                    class="reply_box_inp" :placeholder="item.inputObj.replyName">
                  <button :class="item.commentObj.content != '' ? 'reply_box_but' : 'reply_box_buts'"
                    :style="$general.loadConfiguration(-4)"
                    @click="transmitClick(item.commentObj, item.inputObj)">发送</button>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <!-- 基层代表联络站工作动态 -->
    <div class="representativeCircle_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>基层代表联络站工作动态</span> <span class="more"
            v-if="LiaisonData.length > 0 && LiaisonData.length > 3" @click="skipMore('LiaisonData')">更多<van-icon
              name="arrow" /></span></div>
      </div>
      <div v-if="LiaisonData.length > 0">
        <ul>
          <li v-for="item, index in LiaisonData" :key="index"
            @click="representativeWorkStatusDetails('LiaisonData', item)" v-show="index < 3">
            <div class="T-flex-flow-row-wrap-Liaison">
              <van-image position="contain" width="2.5rem" fit="cover" height="2.5rem"
                style="border-radius: .2rem;overflow: hidden;margin-right: 0.1rem;" :src="item.url" />
              <!-- <img :src="item.url"
                   alt=""
                   class="T-flex-flow-row-wrap-Liaison-img"> -->
              <div class="T-flex-flow-row-wrap-Liaison-right">
                <div class="T-flex-flow-row-wrap-Liaison-title">
                  {{ item.title }}
                </div>
                <div class="T-flex-flow-row-wrap-Liaison-but">
                  <div class="T-flex-flow-row-wrap-Liaison-time">
                    {{ dayjs(item.time).format('YYYY-MM-DD') }}
                  </div>
                  <div class="T-flex-flow-row-wrap-Liaison-text">
                    {{ item.studioName }}
                  </div>
                </div>
              </div>
            </div>
            <div class="house_content_bottom">
              <div class="flex_box flex_align_center">
                <div class="flex_placeholder"></div>
                <div class="house_content_bottom_comment flex_box flex_align_center flex_justify-content_end"
                  @click.stop="replyClick(item)">
                  <img :src="require('../../assets/img/icon_comments.png')" alt="" style="width: 15px;">
                  <span :style="'margin-left:5px;' + $general.loadConfiguration(-2)">{{ item.commentCount }}</span>
                </div>
                <div class="house_content_bottom_like flex_box flex_align_center flex_justify-content_end"
                  style="margin-left:18px;" @click.stop="downLikecontact(item)">
                  <img
                    :src="require(!item.ifIike ? '../../assets/img/icon_likes.png' : '../../assets/img/icon_likes_on.png')"
                    alt="" style="width: 15px;">
                  <span
                    :style="'margin-left:5px;' + $general.loadConfiguration(-2) + ';color:' + (item.ifIike ? '#FE7530' : '#333333') + ';'">{{
                      item.fabulousCount }}</span>
                </div>
              </div>
            </div>
            <div class="likeComment_box">
              <div class="like_box" :style="$general.loadConfiguration(-4)"
                v-if="item.fabulousList != null && item.fabulousList.length">
                <van-icon name="like-o" v-if="item.fabulousList != null && item.fabulousList.length" />
                <span :style="$general.loadConfiguration(-4) + 'margin-bottom: 2px;' + 'line-height: 18px;'"
                  v-for="(it, ind) in item.fabulousList" :key="ind">
                  {{ ind > 0 ? ',' : '' }} {{ it.userName }} </span>
              </div>
              <div class="comment_box" v-if="item.comments.length != 0">
                <div v-for="(items, indexs) in item.comments" :key="indexs">
                  <p style="display: flex;align-items: center;">
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'">{{ items.userName ? items.userName
                      :
                      items.userType }}:
                    </span>
                    <span :style="$general.loadConfiguration(-4) + 'flex:1;'"
                      @click.stop="replyClick(item, items, indexs)">
                      {{ items.content }} </span>
                  </p>
                  <p v-for="(ite, inde) in items.children" :key="inde" :style="$general.loadConfiguration(-4)">
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                      @click.stop="openUserList(ite.createBy)">{{ ite.userName }}</span> 回复
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                      @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType }}:
                    </span>
                    <span :style="$general.loadConfiguration(-4)" @click.stop="replyClick(item, items, inde)"> {{
                      ite.content }} </span>
                  </p>
                </div>
              </div>
              <div class="reply_box" @click.stop="" v-show="item.inputObj.replyShow">
                <div class="reply_box_item">
                  <input type="text" v-model="item.commentObj.content" :style="$general.loadConfiguration(-4)"
                    class="reply_box_inp" :placeholder="item.inputObj.replyName">
                  <button :class="item.commentObj.content != '' ? 'reply_box_but' : 'reply_box_buts'"
                    :style="$general.loadConfiguration(-4)"
                    @click="transmitClick(item.commentObj, item.inputObj)">发送</button>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <!-- 政情快递 -->
    <div class="news_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>政情快递</span> <span class="more"
            v-if="highlightsList.length > 0 && highlightsList.length > 5"
            @click="skipMore('political', '政情快递')">更多<van-icon name="arrow" /></span></div>
      </div>
      <div v-if="highlightsList.length > 0" class="situation_box">
        <ul class="situation_ul">
          <li class="situation_li" v-for="item, index in highlightsList" :key="item.id"
            @click="skipDetails('political', item.relateRecordId)" v-show="index <= 4">
            <p class="situation_li_title">{{ item.title }} <span v-if="item.isRead == '0'"
                class="situation_li_title_sp"></span></p>
            <p class="situation_li_time">{{ item.createDate ? item.createDate.slice(0, 10) : '' }}</p>
          </li>
        </ul>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <!-- 两院咨询 -->
    <div class="news_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>两院资讯</span> <span class="more"
            v-if="zySpecialsubjectRelateinfoList.length > 0 && zySpecialsubjectRelateinfoList.length > 5"
            @click="skipMore('twoInstitutes', '两院资讯')">更多<van-icon name="arrow" /></span></div>
      </div>
      <div v-if="zySpecialsubjectRelateinfoList.length > 0" class="situation_box">
        <ul class="situation_ul">
          <li class="situation_li" v-for="item, index in zySpecialsubjectRelateinfoList" :key="item.id"
            @click="skipDetails('twoInstitutes', item.relateRecordId)" v-show="index <= 4">
            <p class="situation_li_title"> {{ item.title }} <span v-if="item.isRead == '0'"
                class="situation_li_title_sp"></span></p>
            <p class="situation_li_time">{{ item.createDate ? item.createDate.slice(0, 10) : '' }}</p>
          </li>
        </ul>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <!-- 青岛社区民意厅 -->
    <div class="news_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>青青岛社区民意厅</span> <span class="more"
            v-if="zySpecialsubjectColumnList.length > 0 && zySpecialsubjectColumnList.length > 3"
            @click="skipMore('community', '青青岛社区民意厅')">更多<van-icon name="arrow" /></span></div>
      </div>
      <div v-if="zySpecialsubjectColumnList.length > 0" class="situation_box">
        <ul class="situation_ul">
          <li class="situation_li" v-for="item, index in zySpecialsubjectColumnList" :key="item.id"
            @click="skipDetails('community', item.id)" v-show="index <= 4">
            <p class="situation_li_title">{{ item.title }} <span v-if="item.isRead == '0'"
                class="situation_li_title_sp"></span></p>
            <p class="situation_li_time">{{ item.createDate ? item.createDate.slice(0, 10) : '' }}</p>
          </li>
        </ul>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <!-- 代表风采 -->
    <div class="news_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>代表风采</span> <span class="more"
            v-if="representativeList.length > 0 && representativeList.length > 3"
            @click="skipMore('representative', '代表风采')">更多<van-icon name="arrow" /></span></div>
      </div>
      <div v-if="representativeList.length > 0" class="representative_box">
        <ul class="representative_box_ul">
          <!-- 'https://mp.weixin.qq.com/s/MFqOZGS3qntb96-ackdZqw' -->
          <li class="representative_box_li" v-for="item, index in representativeList" :key="item.id"
            @click="skipDetails('representative', item.id, item.externalLinks)" v-show="index <= 1">
            <div class="representative_box_right">
              <p class="representative_title">{{ item.title }}</p>
              <p class="representative_time">{{ item.createDate ? item.createDate.slice(0, 10) : '' }}</p>
            </div>
            <div class="representative_box_left">
              <img v-if="item.image != null" class="representative_img" :src="item.imgObj.fullUrl" alt="">
            </div>
          </li>
        </ul>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <!-- 工作专题 -->
    <div class="news_box">
      <div class="title">
        <img :src="require('../../assets/img/blue.png')" alt="">
        <div class="title_text"><span>工作专题</span> </div>
      </div>
      <div v-if="specialsubjectinfoList.length > 0">
        <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white">
          <van-swipe-item v-for="item in specialsubjectinfoList" :key="item.id">
            <img v-if="item.coverImg != null" @click.stop="skipZT(item.id, item.coverImg)" :src="item.coverImg" alt=""
              class="my-swipe-img">
          </van-swipe-item>
        </van-swipe>
      </div>
      <div v-else>
        <div style="color: #a8a8a8;text-align: center;">暂无数据</div>
      </div>
    </div>
    <div class="photograpr_box" v-if="cjhjsspShow === '1'">
      <div class="photograpr" v-if="photograprShow" @click="goPhotograpr">
        <img :src="require('../../assets/img/qingdao/ssp.png')" alt="">
        <span style="color: #000;" class="jumin">村居环境</span>
        <span style="color: #000;" class="suishou">随手拍</span>
      </div>
      <div class="rightbg" @click="photograprShow = !photograprShow">
        <span v-if="photograprShow" style="color: #fff;">></span>
      </div>
    </div>
  </div>
</template>
<script>
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, ImagePreview, Toast, Dialog, Badge } from 'vant'
export default {
  name: 'module',
  components: {
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component,
    [Badge.name]: Badge
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showPopover: false,
      photograprShow: true,
      cjhjsspShow: '',
      pageNo: 1,
      pageSize: 10,
      module: '1',
      appName: JSON.parse(sessionStorage.getItem('areaName')) || '',
      areaId: sessionStorage.getItem('areaId'),
      actions: [],
      actionsId: '370200',
      logoTheme: sessionStorage.getItem('logo').split('#').length > 1 ? sessionStorage.getItem('logo').split('#')[1] : '',
      menuList: [],
      AllCount: 0,
      hot: '请输入搜索内容',
      msgboxList: [], // 公告栏
      representativeCircleData: [], // 代表履职圈
      LiaisonData: [], // 基层代表联络站工作动态
      highlightsList: [], // 政情快递
      zySpecialsubjectRelateinfoList: [], // 两院资讯
      zySpecialsubjectColumnList: [], // 青岛社区民意厅
      representativeList: [], // 代表风采
      specialsubjectinfoList: [], // 工作专题
      topic: [],
      lzShow: false,
      committeesayUnread: '',
      msgCount: 0
    })

    if (data.title) {
      document.title = data.title
    }
    // 监听数据变化
    watch(
      [
        () => data.menuList,
        () => data.msgboxList,
        () => data.representativeCircleData,
        () => data.LiaisonData,
        () => data.highlightsList,
        () => data.zySpecialsubjectRelateinfoList,
        () => data.zySpecialsubjectColumnList,
        () => data.representativeList,
        () => data.specialsubjectinfoList
      ],
      () => {
        setCachedData({
          menuList: data.menuList,
          msgboxList: data.msgboxList,
          representativeCircleData: data.representativeCircleData,
          LiaisonData: data.LiaisonData,
          highlightsList: data.highlightsList,
          zySpecialsubjectRelateinfoList: data.zySpecialsubjectRelateinfoList,
          zySpecialsubjectColumnList: data.zySpecialsubjectColumnList,
          representativeList: data.representativeList,
          specialsubjectinfoList: data.specialsubjectinfoList
        })
      }
    )
    onMounted(() => {
      getCachedData()
      getLzshow()
      getAreasList()
      setTimeout(() => {
        getList()
      }, 10)
      if (window.location.origin === 'http://localhost:8080' || window.location.origin === 'http://**************') {
        data.topic = ['497281301590573056', '497281406292983808', '497911551798280192', '497911588330668032', '583535616667418624', '583535685554667520', '583576120553635840', '583576176543399936', '583581969791582208', '583582056441708544', '583586658427863040', '583586693840371712', '588606723766353920', '588606750186274816']
        // data.topic = route.query.topic ? route.query.topic.split(',') : ['498377074449317888', '498377147954495488', '498631879772078080', '498632215421255680', '583913427601195008', '583913456667721728', '583913561495961600', '583913588691828736', '583913702919503872', '583913728316014592', '583913823384109056', '583913848705122304', '588587230944034816', '588587379388841984']
      } else {
        data.topic = ['497281301590573056', '497281406292983808', '497911551798280192', '497911588330668032', '583535616667418624', '583535685554667520', '583576120553635840', '583576176543399936', '583581969791582208', '583582056441708544', '583586658427863040', '583586693840371712', '588606723766353920', '588606750186274816']
      }
      if (route.query.module) {
        data.module = route.query.modulel
      }
      data.cjhjsspShow = sessionStorage.getItem('cjhjssp')
    })
    // 缓存键名
    const CACHE_KEY = 'module_data_cache'
    // 获取缓存数据
    const getCachedData = () => {
      const cacheData = JSON.parse(localStorage.getItem(CACHE_KEY))
      if (cacheData) {
        data.menuList = cacheData.menuList
        data.msgboxList = cacheData.msgboxList
        data.representativeCircleData = cacheData.representativeCircleData
        data.LiaisonData = cacheData.LiaisonData
        data.highlightsList = cacheData.highlightsList
        data.zySpecialsubjectRelateinfoList = cacheData.zySpecialsubjectRelateinfoList
        data.zySpecialsubjectColumnList = cacheData.zySpecialsubjectColumnList
        data.representativeList = cacheData.representativeList
        data.specialsubjectinfoList = cacheData.specialsubjectinfoList
      }
    }
    // 设置缓存数据
    const setCachedData = (data) => {
      localStorage.setItem(CACHE_KEY, JSON.stringify(data))
    }
    // 列表请求
    const getList = async () => {
      const res = await $api.general.appList({ parentId: sessionStorage.getItem('historyIndex'), areaId: sessionStorage.getItem('areaId') })
      var { data: list } = res
      var lists = []
      if (list && list.length !== 0) {
        lists = list.find(item => item.name === '工作台').children || []
        var dataLength = lists ? lists.length : 0
        var newMenu = []
        var allMenu = []
        for (var i = 0; i < dataLength; i++) {
          var item = {}
          var itemData = lists[i]
          item.id = itemData.id
          item.name = itemData.name
          item.url = itemData.iconUrl
          item.infoUrl2 = itemData.infoUrl2
          item.needRecord = itemData.needRecord
          item.type = itemData.type
          item.remarks = itemData.remarks
          item.pointType = 'big'
          item.pointNumber = 0
          if (item.infoUrl2 && item.infoUrl2 !== '#') {
            allMenu.push(item)
          }
        }
        newMenu = allMenu
        console.log('allMenu===>', allMenu)
        if (newMenu.length > 7) { // 大于7个栏目 就只显示7个 加一个更多
          newMenu = newMenu.slice(0, 7)
        }
        data.menuList = newMenu
        if (allMenu.length > 7) {
          data.menuList.push({
            id: '',
            type: 'other',
            name: '更多',
            url: require('../../assets/img/icon_more.png'),
            pointType: 'big',
            pointNumber: 0,
            infoUrl2: `moduleMorePage?areaId=${data.actionsId}`
          })
        }
      }
      getMsgbox()
      getRepresentativeCircle()
    }
    // 打开扫一扫
    const openScan = () => {
      router.push({ name: 'scan' })
    }
    // 跳日程
    const goSchedule = () => {
      router.push({ name: 'schedule' })
    }
    // 是否展示我的履职
    const getLzshow = async () => {
      const list = sessionStorage.getItem('roleList')
      if (list.includes('青岛市人大代表')) {
        data.lzShow = true
      } else {
        data.lzShow = false
      }
      // getConferenceAgentNumbe()
      // browseNotCount()
      getcommitteesayUnread()
    }
    // 点击地区菜单栏
    const onSelect = ({ text, id }) => {
      data.actionsId = id
      data.appName = text
      sessionStorage.setItem('areaName', JSON.stringify(data.appName))
      sessionStorage.setItem('areaId', id)
      data.areaId = id
      Toast.loading({
        message: '切换中...',
        forbidClick: true
      })
      getList()
    }
    // 点击随手拍
    const goPhotograpr = () => {
      router.push('/photograpr')
    }
    // 获取地区
    const getAreasList = () => {
      const areasList = JSON.parse(sessionStorage.getItem('areas')) || []
      data.actions = areasList.map(item => {
        return { text: item.name, id: item.id }
      })
    }
    // 首页所有红点数
    const getAllNumber = () => {
      var browseNotCount = JSON.parse(sessionStorage.getItem('browseNotCount'))
      var committeesayUnread = sessionStorage.getItem('committeesayUnread')
      var conferenceAgentNumbe = JSON.parse(sessionStorage.getItem('conferenceAgentNumbe')).sum
      var areaId = sessionStorage.getItem('areaId')
      var number = Number(browseNotCount[areaId].notCount4) + Number(conferenceAgentNumbe) + Number(committeesayUnread) + Number(data.msgCount)
      store.commit('setAllNumber', number)
    }
    // 代表履职圈红点
    const getcommitteesayUnread = async () => {
      const res = await $api.general.committeesayUnread({
        isFollow: 1,
        publishBy: 1
      })
      data.committeesayUnread = Object.values(res.data).reduce((accumulator, currentValue) => accumulator + currentValue, 0)
      sessionStorage.setItem('committeesayUnread', data.committeesayUnread)
    }
    // 全局红点
    // const browseNotCount = async () => {
    //   const res = await $api.general.browseNotCount({
    //     areaIds: data.areaId
    //   })
    //   sessionStorage.setItem('browseNotCount', JSON.stringify(res.data))
    // }
    // 会议活动红点
    // const getConferenceAgentNumbe = async () => {
    //   const res = await $api.general.getConferenceAgentNumbe()
    //   sessionStorage.setItem('conferenceAgentNumbe', JSON.stringify(res.data))
    // }

    // 获取公告栏
    const getMsgbox = async () => {
      var { data: list } = await $api.general.msgboxList({ pageNo: data.pageNo, pageSize: 50 })
      for (var i = 0; i < list.length; i++) {
        var dates = new Date(list[i].createDate) // 数据时间转换为date对象格式
        const targetTimestamp = new Date(dates) // 数据的时间
        const curTimestamp = new Date()	// 当前时间
        var time = parseInt((curTimestamp - targetTimestamp) / 1000 / 60 / 60 / 24, 10)	// 当前时间减去数据时间的天数差
        if (time <= 10) {
          data.msgboxList.push(list[i])
        }
        if (list[i].isRead === '0') {
          data.msgCount = data.msgCount + 1
        }
      }
      getAllNumber()
    }
    // 获取履职圈3条数据
    const getRepresentativeCircle = async () => {
      var { data: list } = await $api.general.representativeCircle({ pageNo: data.pageNo, pageSize: 4 })
      list.forEach(item => {
        // 点赞人名称
        item.likes = item.fabulous || ''
        if (item.fabulousUser != null) {
          item.fabulousList = item.fabulousUser.map(items => {
            return {
              userName: items.userName,
              id: items.id
            }
          })
        } else {
          item.fabulousList = []
        }
        item.commentObj = {
          content: '',
          createBy: data.user.id,
          commentPid: '',
          keyId: item.id,
          attach: '',
          extend: '1',
          type: '25',
          areaId: data.actionsId || '370200'
        }
        item.inputObj = {
          replyShow: false,
          replyName: '发送评论'
        }
      })
      data.representativeCircleData = list
      getLiaison()
    }
    // 打开用户列表
    const openUserList = (_item) => {
      router.push({ path: '/committeesayUserList', query: { uId: _item } })
    }
    // 获取基层代表联络站工作动态
    const getLiaison = async () => {
      // data.LiaisonData = []
      var { data: list } = await $api.general.findWygzsWorkDynamic({ pageNo: 1, pageSize: 4 })
      getHighlights()
      if (list && list.length !== 0) {
        for (var i = 0; i < list.length; i++) {
          var item = {}
          var itemData = list[i]
          item.id = itemData.id || ''
          item.isTop = itemData.isTop || '0'
          item.title = itemData.name || ''
          item.url = itemData.imgPath || ''
          item.externalLinks = itemData.externalLinks || ''
          item.time = itemData.createDate
          item.studioName = itemData.studioName
          item.key = 1
          // 点赞数
          item.fabulousCount = itemData.fabulousNumber || 0
          // 点赞人名称
          item.likes = itemData.likes || ''
          if (itemData.fabulousUser != null) {
            item.fabulousList = itemData.fabulousUser.map(items => {
              return {
                userName: items.userName,
                id: items.id
              }
            })
          } else {
            item.fabulousList = []
          }
          // 评论集合
          item.comments = itemData.children || []
          item.commentCount = itemData.commentNumber || 0
          item.ifFollow = itemData.isFollow || 0
          item.ifIike = itemData.isFabulous || false
          item.relateType = 'interfaceLocation'
          item.commentObj = {
            content: '',
            createBy: data.user.id,
            commentPid: '',
            keyId: item.id,
            attach: '',
            extend: '1',
            type: '71',
            areaId: data.actionsId || '370200'
          }
          item.inputObj = {
            replyShow: false,
            replyName: '发送评论'
          }
          data.LiaisonData.push(item)
        }
      }
    }
    // 点击跳详情
    const skipDetails = async (type, _id, url, isFollow, publishBy, isFabulous, fabulousCount) => {
      if (type === 'announcement') {
        _id.isRead = '1'
        await $api.general.updateState({ id: _id.id })
        if (_id.module === 'survey') {
          router.push({ path: '/survey', query: { type: 'survey', id: _id.paramMap.id } })
        } else if (_id.module === 'notice') {
          router.push({ path: '/noticeDetails', query: { type: _id.module, id: _id.paramMap.id } })
        } else {
          Dialog.alert({
            title: _id.moduleView,
            message: _id.content,
            confirmButtonColor: '#39a9ed'
          }).then(() => {
          })
        }
        return
      }
      if (url != null) {
        window.location.href = url
      } else {
        router.push({ name: 'newsDetails', query: { id: _id, type, isFollow, publishBy, ifIike: isFabulous, fabulousCount: fabulousCount } })
      }
    }
    // 进基层代表联络站工作动态详情页面
    const representativeWorkStatusDetails = async (type, _item) => {
      router.push({ name: 'LiaisonDataDetails', query: { id: _item.id, type } })
    }
    // 点击点赞
    const downLike = (_item) => {
      _item.isFabulous = !_item.isFabulous
      if (_item.isFabulous) {
        _item.fabulousCount++
        _item.likes = _item.likes + (_item.likes ? ',' : '') + data.user.userName
        _item.fabulousList.push({ id: data.user.id, userName: data.user.userName })
      } else {
        _item.fabulousCount--
        _item.fabulousList = _item.fabulousList.filter(item => item.id !== data.user.id)
      }
      fabulousInfo(_item.isFabulous, _item.id, '25')
    }
    const downLikecontact = (_item) => {
      if (_item.ifIike) { // 当前是已点赞状态数量-1
        if (_item.fabulousCount > 0) {
          _item.fabulousCount--
          _item.fabulousList = _item.fabulousList.filter(item => item.id !== data.user.id)
        }
      } else {
        _item.fabulousCount++
        _item.likes = _item.likes + (_item.likes ? ',' : '') + data.user.userName
        _item.fabulousList.push({ id: data.user.id, userName: data.user.userName })
      }
      _item.ifIike = !_item.ifIike
      fabulousInfo(_item.ifIike, _item.id, '71')
    }
    // 回复
    const replyClick = (_item, _items) => {
      _item.inputObj.replyShow = true
      _item.inputObj.replyName = '发送评论'
      if (_items) {
        _item.commentObj.commentPid = _items.id
        _item.inputObj.replyName = _items.userName ? '回复' + _items.userName : '回复'
      }
    }
    // 发送
    const transmitClick = async (_item, _items) => {
      if (_item.content === '') {
        return false
      }
      _items.replyShow = false
      var url = 'comment/save'
      var params = _item
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        getRepresentativeCircle()
      } else {
        Toast('请求失败。')
      }
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id, type = '25') => {
      var relateType = type
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: relateType
      }
      await $api.general.fabulous({ url, params })
    }
    // 预览图片
    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    // 获取政情快递
    const getHighlights = async () => {
      var params = {
        pageNo: 1,
        pageSize: 6,
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1,
        columnId: data.topic[5],
        subjectId: data.topic[4]
      }
      var { data: list } = await $api.general.highlights(params)
      getZySpecialsubjectRelateinfo()
      data.highlightsList = list
    }
    // 获取两院咨询
    const getZySpecialsubjectRelateinfo = async () => {
      var params = {
        pageNo: 1,
        pageSize: 6,
        subjectId: data.topic[12],
        columnId: data.topic[13],
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1
      }
      var { data: list } = await $api.general.zySpecialsubjectRelateinfo(params)
      data.zySpecialsubjectRelateinfoList = list
      getZySpecialsubjectColumn()
    }
    // 获取青岛社区民意厅
    const getZySpecialsubjectColumn = async () => {
      var params = {
        pageNo: 1,
        pageSize: 6,
        subjectId: data.topic[6],
        isOpen: 1,
        showDirectional: 2
      }
      var { data: list } = await $api.general.zySpecialsubjectColumn(params)
      if (list.length <= 0) {
        return
      }
      data.zySpecialsubjectColumnList = list[0].specialsubjectNews
      getRepresentative()
    }
    // 获取代表风采
    const getRepresentative = async () => {
      var params = {
        pageNo: 1,
        pageSize: 3,
        subjectId: data.topic[2],
        isOpen: 1,
        showDirectional: 2
      }
      var { data: list } = await $api.general.representative(params)
      getSpecialsubjectinfo()
      if (list.length <= 0) {
        return
      }
      // getCountryside()
      data.representativeList = list[0].specialsubjectNews
    }
    // 获取工作专题
    const getSpecialsubjectinfo = async () => {
      var params = {
        module: 1,
        pageNo: 1,
        pageSize: 10,
        isPublish: '1'
      }
      var { data: list } = await $api.general.specialsubjectinfo(params)
      setTimeout(() => {
        if (sessionStorage.getItem('scrollPosition')) {
          window.scrollTo(0, sessionStorage.getItem('scrollPosition'))
        } else {
          window.scrollTo(0, 0)
        }
      }, 1500)
      Toast.clear()
      data.specialsubjectinfoList = list
    }
    // 跳转专题
    const skipZT = (id, url) => {
      router.push({ path: '/ZT', query: { id, url } })
    }
    // 点击更多
    const skipMore = (type, title) => {
      if (type === 'announcement') {
        router.push({ path: '/bulletinBoard' })
        return
      }
      if (type === 'resumption') {
        router.push({ path: '/beingCountedList' })
        return
      }
      if (type === 'LiaisonData') {
        router.push({ path: '/LiaisonDataList' })
        return
      }
      router.push({ path: '/newsMore', query: { type, title } })
    }
    // 中间栏目点击
    const itemClick = (_item) => {
      // 更多应用
      if (_item === 'all') {
        router.push({ path: '/applications' })
        return false
      }
      switch (_item.name) {
        case '群众留言':
          window.location.href = 'http://**************/qingdaord-meet-app/#/crowdReply?token={{token}}'
          break
        case '议案建议':
          var token = JSON.parse(sessionStorage.getItem('token'))
          if (_item.infoUrl2.indexOf('mobile') !== -1 || _item.infoUrl2.indexOf('mobile') !== -1) {
            window.location.href = _item.infoUrl2 + '?token=' + token
          } else {
            window.location.href = _item.infoUrl2
          }
          break
        case '意见征集':
          if (_item.infoUrl2) {
            var routerStr2 = _item.infoUrl2 + (_item.remarks === null ? '' : '?' + _item.remarks)
            var pageData2 = {}
            if (_item.infoUrl2.indexOf('?')) {
              var pageArr2 = routerStr2.split('?')
              var pageIndex2 = pageArr2.findIndex(item => item.includes('='))
              pageData2[pageArr2[pageIndex2].split('=')[0]] = pageArr2[pageIndex2].split('=')[1]
            }
            if (routerStr2.indexOf('http') === 0) {
              window.location.href = routerStr2
              return false
            }
            router.push({ path: routerStr2, query: pageData2 })
          } else {
            Toast('请配置好H5路由')
          }
          break
        case '圈子':
          if (_item.infoUrl2) {
            var routerStr1 = _item.infoUrl2 + (_item.remarks === null ? '' : '?' + _item.remarks)
            var pageData = {}
            if (_item.infoUrl2.indexOf('?')) {
              var pageArr = routerStr1.split('?')
              console.log(pageArr)
              var pageIndex = pageArr.findIndex(item => item.includes('='))
              pageData[pageArr[pageIndex].split('=')[0]] = pageArr[pageIndex].split('=')[1]
            }
            if (routerStr1.indexOf('http') === 0) {
              window.location.href = routerStr1
              return false
            }
            router.push({ path: routerStr1, query: pageData })
          } else {
            Toast('请配置好H5路由')
          }
          break
        default:
          if (_item.infoUrl2) {
            var routerStr = _item.infoUrl2
            var pageDatas = {}
            if (routerStr.indexOf('http') === 0) {
              window.location.href = routerStr
              return false
            }
            if (_item.infoUrl2.indexOf('?')) {
              console.log(_item.infoUrl2.indexOf('?'))
              var pageArrs = routerStr.split('?')
              console.log(pageArrs)
              var pageIndexs = pageArrs.findIndex(item => item.includes('='))
              if (pageIndexs !== -1) {
                pageDatas[pageArrs[pageIndexs].split('=')[0]] = pageArrs[pageIndexs].split('=')[1]
              }
            }
            router.push({ path: routerStr, query: pageDatas })
          } else {
            Toast('请配置好H5路由')
          }
          break
      }
    }
    // 工作台的红点
    const getPointNumber = (item) => {
      setTimeout(() => {
        var browseNotCount = JSON.parse(sessionStorage.getItem('browseNotCount'))
        var committeesayUnread = sessionStorage.getItem('committeesayUnread')
        var conferenceAgentNumbe = JSON.parse(sessionStorage.getItem('conferenceAgentNumbe')).sum || 0
        var areaId = sessionStorage.getItem('areaId')
        if (item.name === '更多') {
          data.AllCount = Number(browseNotCount[areaId].notCount4) + Number(conferenceAgentNumbe) + Number(committeesayUnread)
          data.menuList.forEach(_item => {
            if (_item.infoUrl2 === 'newsMore?type=survey') {
              data.AllCount = data.AllCount - Number(browseNotCount[areaId].notCount4)
            }
            if (_item.infoUrl2 === 'conferenceActivities') {
              data.AllCount = data.AllCount - Number(conferenceAgentNumbe)
            }
            if (_item.infoUrl2 === 'beingCountedList') {
              data.AllCount = data.AllCount - Number(committeesayUnread)
            }
            if (_item.name === '更多') {
              item.pointNumber = data.AllCount
            }
          })
          return
        }
        if (item.infoUrl2 === 'newsMore?type=survey') { // 意见征集
          item.pointNumber = browseNotCount[areaId].notCount4 || ''
        } else if (item.infoUrl2 === 'conferenceActivities') { // 会议活动
          item.pointNumber = conferenceAgentNumbe || ''
        } else if (item.infoUrl2 === 'beingCountedList') { // 履职圈
          item.pointNumber = committeesayUnread || ''
        } else {
          item.pointNumber = ''
        }
      }, 500)
    }
    return { ...toRefs(data), getPointNumber, dayjs, openScan, representativeWorkStatusDetails, downLikecontact, openUserList, goPhotograpr, router, goSchedule, skipZT, skipMore, $general, itemClick, onSelect, previewCalback, downLike, skipDetails, replyClick, transmitClick }
  }
}
</script>
<style lang="less" scoped>
.module {
  box-sizing: border-box;
  padding-bottom: 50px;

  .bg_default {
    background-position: bottom;
    background-size: cover;
    height: 300px !important;

    .btnLeft_box {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      background: linear-gradient(to bottom, #3a98ff, #3c9ffd);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      z-index: 99;

      .representative_title {
        color: #fff;
        flex: 1;
        font-weight: 700;
        font-size: 24px;
      }

      .bg_default_text {
        font-size: 16px !important;
        width: 35% !important;
        height: 100%;

        .play {
          display: inline-block;
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-top: 8px solid #fff;
        }
      }
    }

    .search {
      background: #00000000;
      padding: 15px 10px 0;
      box-sizing: border-box;
      display: flex;
      height: 50px;
      align-items: center;
      justify-content: space-between;

      .search_box {
        width: 100%;
        border-radius: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        color: #3783c6;
        background: #ffffff3d;
      }
    }

    .four_Bigboxs {
      width: 100%;
      height: 65px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 15px;

      .four_box {
        flex: 1;
        height: 100%;
        color: #fff;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;

        >img {
          width: 40px;
          height: 40px;
        }
      }
    }

    .card-item-head {
      width: 95%;
      height: 100px;
      margin: 15px 10px 0;
      background: url("../../assets/img/ldjsc_head_bg.png");
      background-size: 100% 100%;
    }
  }

  .T-flexbox-vertical {
    padding-top: 50px !important;
    display: block !important;
  }

  .menu {
    width: 95%;
    margin: 15px auto 0;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 10px;

    .grid_name {
      font-size: 14px !important;
    }
  }

  ::v-deep .van-grid-item__content {
    padding: 0 !important;
  }

  .van-grid-item {
    height: 100px !important;

    .van-image {
      width: 40px !important;
      height: 40px !important;
    }
  }

  .news_box {
    width: 95%;
    margin: 15px auto 0;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 10px;

    .news_box_list li {
      width: 100%;
      // height: 35px;
      line-height: 1.6;
      padding-bottom: 5px;
      border-bottom: 1px solid #e5e5e5;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
      position: relative;

      .isRead {
        width: 8px;
        height: 8px;
        background: red;
        border-radius: 20px;
        position: absolute;
        top: 5px;
        right: 0px;
      }
    }
  }

  .representativeCircle_box {
    width: 95%;
    margin: 15px auto 0;
    background: #fff;
    border-radius: 10px;
    box-sizing: border-box;
    padding: 10px;

    .representativeCircle_box_content {
      .representativeCircle_box_li {
        width: 100%;
        padding-bottom: 5px;
        border-bottom: 1px solid #e5e5e5;

        .representativeCircle_box_top {
          width: 100%;
          height: 35px;
          margin: 5px 0;
          display: flex;
          align-items: center;

          .representativeCircle_box_top_headImg {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin: 5px;
            object-fit: contain;
          }

          .representativeCircle_box_name {
            font-size: 16px;

            .representativeCircle_box_congressStr {
              font-size: 14px;
              color: #4c4c4c;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              display: -webkit-box;
              overflow: hidden;
            }
          }
        }

        .representativeCircle_box_center {
          box-sizing: border-box;

          .representativeCircle_box_center_content {
            padding-left: 13px;
            margin: 5px 0;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .representativeCircle_box_center_attachmentList {
            width: 95%;
            margin: auto;
            display: flex;
            flex-wrap: wrap;

            .van-image {
              margin: 5px;
            }
          }
        }

        .representativeCircle_box_buttom {
          width: 100%;
          height: 35px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .representativeCircle_box_buttom_time {
            width: 70%;
            font-size: 14px;
            color: #a8a8a8;
          }

          .representativeCircle_box_buttom_cont {
            width: 25% !important;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .representativeCircle_box_buttom_comment {
              display: flex;
              align-items: center;
              justify-content: space-between;

              >img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
              }
            }

            .representativeCircle_box_buttom_like {
              line-height: 100%;

              >img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
              }
            }
          }
        }
      }
    }

    .T-flex-flow-row-wrap-Liaison {
      width: 100%;
      display: flex;
      align-items: center;
      height: 140px;
      overflow: hidden;

      .T-flex-flow-row-wrap-Liaison-img {
        width: 30%;
        height: 100px;
        border-radius: 10px;
        margin-right: 10px;
      }

      .T-flex-flow-row-wrap-Liaison-right {
        width: 70%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .T-flex-flow-row-wrap-Liaison-title {
          width: 100%;
          margin-bottom: 50px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .T-flex-flow-row-wrap-Liaison-but {
          width: 100%;
          display: flex;
          align-items: center;
          height: 100%;
          justify-content: space-between;
          overflow: hidden;

          .T-flex-flow-row-wrap-Liaison-time {
            color: #a8a8a8;
            font-size: 16px;
            width: 50%;
          }

          .T-flex-flow-row-wrap-Liaison-text {
            font-size: 16px;
            width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .house_content_bottom {
      width: 100%;
      background: #ffffff;
      padding: 8px 14px;
    }

    .likeComment_box {
      background: #f7f7f7;
      margin: 0 0 10px;
      overflow: hidden;
      box-sizing: border-box;
      border-radius: 5px;

      .comment_box {
        margin: 0 5px 0px;
      }

      .like_box {
        color: #6e7fa3;
        margin: 5px 5px;
      }

      .reply_box {
        background: #f7f7f7;
        margin: 5px 5px 0;
        padding: 5px 0 0 0;
        border-top: 1px solid #e8e8e8;
        height: 50px;

        .reply_box_item {
          width: 100%;
          background: #fff;
          height: 100%;
          border-radius: 5px;
          border: 1px solid #3895ff;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          padding: 0 5px;

          .reply_box_but {
            width: 60px;
            border-radius: 5px;
            height: 80%;
            color: #fff;
            background: #3895ff;
          }

          .reply_box_buts {
            color: rgb(112, 112, 112);
            background: #bdbdbd;
            width: 60px;
            border-radius: 5px;
            height: 80%;
          }

          .reply_box_inp {
            height: 80%;
            flex: 1;
          }
        }
      }
    }
  }

  .photograpr_box {
    background: #d5ebfd;
    position: fixed;
    right: 0px;
    top: 50%;
    background: #d5ebfd;
    transform: translate(0, -50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 10px;

    .photograpr {
      width: 60px;
      height: 80px;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      border-radius: 10px;

      .jumin {
        font-size: 14px;
        transform: scale(0.6);
        text-align: center;
      }

      .suishou {
        font-size: 14px;
        transform: scale(0.6);
        margin-top: -8px;
      }

      >img {
        width: 40px;
        height: 40px;
      }
    }

    .rightbg {
      width: 15px;
      height: 80px;
      background: #54befe;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  ::v-deep .van-dialog__confirm:active {
    color: #39a9ed !important;
  }

  .my-swipe {
    border-radius: 15px;
    height: 160px;
    width: 100%;

    .my-swipe-img {
      width: 100%;
      height: 100%;
    }
  }

  .my-swipe .van-swipe-item {
    color: #fff;
    font-size: 20px;
    line-height: 150px;
    text-align: center;
    background-color: #39a9ed;
    border-radius: 15px;
    height: 100%;
    width: 100%;
  }

  [class*="van-hairline"]::after {
    border: 0 solid #ebedf0;
  }

  .title {
    width: 100%;
    height: 30px;
    color: #000;
    position: relative;
    margin-bottom: 15px;

    .title_text {
      font-size: 18px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .title_text_size {
      font-size: 18px;
      font-weight: bold;
    }

    .title_text .more {
      font-size: 16px;
      color: #a8a8a8;
      font-weight: normal;
    }

    >img {
      width: 20px;
      height: 8px;
      position: absolute;
      left: 0px;
      bottom: 4px;
    }
  }

  .situation_box {
    .situation_ul {
      .situation_li {
        width: 100%;
        height: 80px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .situation_li_title {
          margin: 10px 0 0px 0;
          position: relative;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .situation_li_title_sp {
            position: absolute;
            bottom: 0px;
            right: 10px;
            display: inline-block;
            width: 8px;
            height: 8px;
            background: red;
            border-radius: 50%;
          }
        }

        .situation_li_time {
          font-size: 14px;
          color: #a8a8a8;
          margin: 5px 0;
        }
      }
    }
  }

  .representative_box_ul {
    .representative_box_li {
      width: 100%;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e5e5e5;
      margin: 10px 0;

      .representative_box_right {
        width: 65%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .representative_title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .representative_time {
          font-size: 14px;
          color: #a8a8a8;
          margin: 5px 0;
        }
      }

      .representative_box_left {
        width: 35%;
        height: 100%;

        .representative_img {
          width: 90%;
          height: 90%;
          margin: 5px;
        }
      }
    }
  }

  .van-image {
    .van-image_img {
      border-radius: 5px !important;
    }
  }

  .nodata {
    text-align: center;
    margin: 20px 0;
    color: #ccc;
  }
}
</style>

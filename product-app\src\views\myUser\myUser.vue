<template>
  <div class="myUser">
    <div class="myUserHead">
      <div v-if="themeImg.url">
        <img :style="'width:100%;height:' + (190 + safeAreaTop) * 0.011 + 'rem;object-fit: cover;'"
             :src="themeImg.url" />
      </div>
      <div class="myUserHeadUserBox">
        <div class="myUserHeadUser">
          <div class="myUserHeadUserImg">
            <img :src="userInfo.url"
                 alt="">
          </div>
          <div class="myUserHeadUserInfo">
            <div class="myUserHeadUserName">{{ userInfo.name }}</div>
            <!-- <div>共产党</div> -->
            <!-- <div class="ellipsis">{{user.position}}</div> -->
          </div>
        </div>
        <div class="flex_box flex_align_center Modify_big"
             @click="divClick(userInfo)">
          <img src="../../assets/img/editTX.png"
               alt="">
          <div class="text_edit">修改头像</div>
        </div>
      </div>
    </div>

    <!-- 我的应用 -->
    <div v-for="(nItem, nIndex) in gridBtn"
         :key="nIndex"
         class="grid_bg">
      <div class="flex_box flex_align_center"
           style="height: 100%;padding-top: 15px;">
        <div class="yingyong"
             v-html="'我的应用'"></div>
      </div>
      <div class="grid">
        <van-grid clickable
                  :border="false"
                  :column-num="4">
          <van-grid-item v-for="(item, index) in nItem"
                         :key="index"
                         :info="item.pointNumber > 0 ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"
                         :text="item.name"
                         @click="divClick(item)">
            <template v-slot:default>
              <van-image fit="cover"
                         :style="$general.loadConfigurationSize(18) + 'margin-bottom:8px;'"
                         :src="item.url"></van-image>
              <div class="text_name"
                   v-html="item.name">
              </div>
              <p v-if="item.pointNumber > 0"
                 class="flex_box flex_align_center flex_justify_content text_one"
                 :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
                 :style="item.pointType == 'big' ? 'font-size:12px;height:20px' : 'height:12px'"
                 v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '...' : item.pointNumber) : ''"></p>
            </template>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <van-action-sheet v-model:show="editshow"
                      :actions="actions"
                      :description="description"
                      cancel-text="取消"
                      @select="selectProfileAvatar"
                      close-on-click-action />
    <van-action-sheet v-model:show="modifyAvatar"
                      :actions="albumCamera"
                      :description="description"
                      cancel-text="取消"
                      @select="chooseAlbumCamera"
                      close-on-click-action />
    <van-uploader style="display: none;"
                  v-model="fileList"
                  :max-count='1'
                  :after-read="afterRead"
                  ref="chatImg">
    </van-uploader>
    <van-uploader style="display: none;"
                  v-model="fileList"
                  :max-count='1'
                  capture="camera"
                  :after-read="afterReadCamera"
                  ref="chatImgCamera">
    </van-uploader>
    <!-- <van-cell-group>
      <van-cell :title="item.name"
                v-for="item in dataList"
                :key="item.id"
                :icon="item.icon"
                :to="item.to"
                is-link />
    </van-cell-group>
    <van-cell-group>
      <van-cell :title="item.name"
                v-for="item in dataList1"
                :key="item.id"
                :icon="item.icon"
                :to="item.to"
                is-link />
    </van-cell-group>
    <van-cell-group>
      <van-button type="large"
                  @click="loginOut">安全退出</van-button>
    </van-cell-group> -->
  </div>
</template>
<script>

import { useRouter } from 'vue-router'
import userNotice from '../../assets/img/userNotice.png'
import userCollection from '../../assets/img/userCollection.png'
import userRecord from '../../assets/img/userRecord.png'
import userFeedback from '../../assets/img/userFeedback.png'
import userSetting from '../../assets/img/userSetting.png'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, Grid, GridItem, Image as VanImage, ActionSheet, Uploader, Toast } from 'vant'
export default {
  name: 'myUser',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Grid.name]: Grid,
    [Uploader.name]: Uploader,
    [GridItem.name]: GridItem
  },
  setup () {
    const router = useRouter()
    // const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      userInfo: {
        name: '胡烈那',
        url: '',
        position: 'changePhoto',
        click: 'changePhoto'
      },
      // 几个按钮
      gridBtn: [
        [
          { show: true, name: '电子工作证', url: require('../../assets/img/my_2.png'), click: 'openWorkPermit', pointNumber: 0 },
          { show: true, name: '收藏', url: require('../../assets/img/my_3.png'), click: 'collection', pointNumber: 0 },
          { show: true, name: '意见反馈', url: require('../../assets/img/my_3.png'), click: 'feedback', pointNumber: 0 },
          { show: true, name: '信息修改', url: require('../../assets/img/my_7.png'), click: 'useraudit', pointNumber: 0 }
        ]
      ],
      themeImg: { url: '' },
      editshow: false,
      actions: [
        { name: '查看个人资料', color: '#3088fe' },
        { name: '修改头像', color: '#3088fe' }
      ],
      description: '请选择',
      modifyAvatar: false,
      albumCamera: [
        { name: '相册', color: '#3088fe' },
        { name: '相机', color: '#3088fe' }
      ],
      chatImg: null,
      chatImgCamera: null,
      fileList: [],
      dataList: [
        { id: '1', name: '通知公告', icon: userNotice, to: 'noticeList' },
        { id: '2', name: '收藏', icon: userCollection, to: '' },
        { id: '3', name: '浏览记录', icon: userRecord, to: '' }
      ],
      dataList1: [
        { id: '4', name: '使用反馈', icon: userFeedback, to: '' },
        { id: '5', name: '设置', icon: userSetting, to: '' }
      ]
    })
    onMounted(() => {
      data.userInfo.name = data.user.userName
      data.userInfo.url = sessionStorage.getItem('userHeadImg') || data.user.headImg
      getTopImg()
    })
    // 获取我的页面顶部图
    const getTopImg = async () => {
      var areasList = JSON.parse(sessionStorage.getItem('areas')) || []
      var areaIds = ''
      areasList.forEach(function (_eItem, _eIndex, _eArr) {
        areaIds += (areaIds ? ',' : '') + _eItem.id
      })
      var res = await $api.general.findByAreas({ areaIds: areaIds })
      var dataArea = res.data || {}
      var setAreaId = sessionStorage.getItem('areaId')
      var baseItem = dataArea[setAreaId]
      var myBackgroundItems = baseItem.find(item => item.type === 'myBackground')
      data.themeImg.url = myBackgroundItems.isUsing === '1' ? myBackgroundItems.iconFullUrl : ''
    }
    // 点击事件
    const divClick = (_item) => {
      switch (_item.click) {
        case 'changePhoto':
          changePhoto()
          break
        case 'openWorkPermit':
          router.push({ name: 'openWorkPermit', query: { id: data.user.id } })
          break
        case 'collection':
          router.push({ name: 'favorite', query: { id: data.user.id } })
          break
        case 'useraudit':
          router.push({ name: 'informationModification', query: { id: data.user.id } })
          break
        case 'feedback':
          router.push({ name: 'userFeedbackAdd', query: { id: data.user.id } })
          break
        default:
          break
      }
    }
    // 修改头像
    const changePhoto = () => {
      data.editshow = true
    }
    // 选择查看资料还是修改头像
    const selectProfileAvatar = (item) => {
      data.editshow = false
      switch (item.name) {
        case '查看个人资料':
          router.push({ name: 'personalDataView', query: { id: data.user.id } })
          break
        case '修改头像':
          data.modifyAvatar = true
          break
      }
    }
    const chooseAlbumCamera = (_item) => {
      data.modifyAvatar = false
      switch (_item.name) {
        case '相册':
          data.chatImg.chooseFile()
          break
        case '相机':
          data.chatImgCamera.chooseFile()
          break
      }
    }
    // 上传相册图片
    const afterRead = async (file) => {
      const formData = new FormData()
      formData.append('areaId', JSON.parse(sessionStorage.getItem('areaId')))
      formData.append('file', file.file)
      var res = await $api.general.fileMyHeadimg(formData)
      Toast(res.errmsg)
      data.userInfo.url = res.data.fullUrl
      sessionStorage.setItem('userHeadImg', data.userInfo.url)
    }
    // 上传拍照图片
    const afterReadCamera = async (file) => {
      const formData = new FormData()
      formData.append('areaId', JSON.parse(sessionStorage.getItem('areaId')))
      formData.append('file', file.file)
      var res = await $api.general.fileMyHeadimg(formData)
      Toast(res.errmsg)
      data.userInfo.url = res.data.fullUrl
      sessionStorage.setItem('userHeadImg', data.userInfo.url)
    }
    // 退出
    const loginOut = () => {
      Dialog.confirm({
        message:
          '此操作将退出当前系统, 是否继续?'
      })
        .then(() => {
          sessionStorage.clear()
          router.push({ name: 'login' })
        })
        .catch(() => {
          // on cancel
        })
    }
    return { ...toRefs(data), loginOut, $general, divClick, selectProfileAvatar, chooseAlbumCamera, afterRead, afterReadCamera }
  }
}
</script>
<style lang="less">
.myUser {
  width: 100%;
  padding-bottom: 50px;
  background: #f6f6f6;
  height: 100vh;

  .myUserHead {
    width: 100%;

    .myUserHeadUserBox {
      width: 100%;
      padding: 28px 22px 0 36px;
      display: flex;
      align-items: center;
      position: absolute;
      top: 5px;

      .myUserHeadUser {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-bottom: 8px;
        flex: 1;

        .myUserHeadUserImg {
          width: 68px;
          height: 68px;
          border-radius: 50%;
          border: 2px solid #fff;
          overflow: hidden;
          background-color: #ccc;
          position: relative;

          img {
            position: absolute;
            height: 100%;
            left: 0;
            right: 0;
            margin: auto;
          }
        }

        .myUserHeadUserInfo {
          width: calc(100% - 82px);
          display: flex;
          align-items: center;

          .myUserHeadUserName {
            font-size: 18px;
            font-family: PingFang SC;
            font-weight: 600;
            line-height: 22px;
            padding-bottom: 6px;
            color: #ffffff;
          }

          div {
            font-size: 14px;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 22px;
            color: #e1eeff;
          }
        }
      }

      .Modify_big {
        background: #fff;
        height: 30px;
        border-radius: 24px;
        padding: 6px;
        width: 86px;
        margin-left: 40px;

        img {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }

        .text_edit {
          font-size: 14px;
        }
      }
    }
  }

  .grid_bg {
    width: calc(100% - 26px);
    margin: auto;
    box-shadow: 0px 2px 10px rgba(24, 64, 118, 0.08);
    opacity: 1;
    border-radius: 10px;
    position: absolute;
    top: 16%;
    background: #fff;
    margin: 0 13px;
    box-sizing: border-box;

    .yingyong {
      font-weight: bold;
      margin-left: 14px;
      position: relative;
      bottom: 1px;
    }

    .text_name {
      font-size: 14px;
      color: #222;
      font-weight: 600;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      overflow: hidden;
    }
  }

  .van-cell-group {
    margin-top: 10px;

    .van-icon__image {
      transform: translateY(1px);
    }
  }
}
</style>

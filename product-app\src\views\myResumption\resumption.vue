<template>
  <div class="resumption">
    <div class="resumption_top">
      <img :src="headimg"
           alt=""
           class="resumption_top_my">
      <div class="resumption_top_num">
        <p class="num">{{ score }}</p>
        <p>我的得分</p>
      </div>
    </div>
    <div class="resumption_center">
      <div class="resumption_center_con">
        <p class="resumption_center_con_p">{{ areaRanking ? areaRanking : 0 }}</p>
        <p class="resumption_center_con_ps">您在市总排名</p>
      </div>
      <div class="resumption_center_con center_bor">
        <p class="resumption_center_con_p ">{{ deleRanking ? deleRanking : 0 }}</p>
        <p class="resumption_center_con_ps">您在代表团排名</p>
      </div>
      <div class="resumption_center_con">
        <p class="resumption_center_con_p">{{ groupRanking ? groupRanking : 0 }}</p>
        <p class="resumption_center_con_ps">您在代表小组排名</p>
      </div>
    </div>
    <!-- 线下 -->
    <div class="resumption_content">
      <div class="resumption_content_title">
        <p class="title_right">
          <img :src="require('../../assets/img/ldjsc_zzkg_l.png')"
               class="title_right_img"
               alt="">
          <span>线下履职积分</span>
          <span class="title_icon"><van-icon @click="offline"
                      name="question-o" /></span>
        </p>
        <p class="title_left"
           @click="add"
           v-if="types === 'myLZ'">+履职补录</p>
      </div>
      <ul class="resumption_content_list">
        <li class="resumption_content_li"
            v-for="item, index in propose"
            :key="index"
            @click="suggestionsClickInfo(item)">
          <div class="resumption_content_li_title"
               :style="{ background: item.bgcolor }">
            {{ item.name }}
          </div>
          <div class="resumption_content_li_num">
            <span class="resumption_content_piece">{{ item.value }}</span>件
            <span class="resumption_content_minute">{{ item.score }}</span>分
          </div>
        </li>
      </ul>
      <ul class="resumption_content_list">
        <li class="resumption_content_li"
            v-for="item, index in dataList"
            :key="index"
            @click="dataClickInfo(item)">
          <div class="resumption_content_li_title"
               :style="{ background: item.bgcolor }">
            {{ item.name }}
          </div>
          <div class="resumption_content_li_num">
            <span class="resumption_content_piece">{{ item.value }}</span>件
            <span class="resumption_content_minute">{{ item.score }}</span>分
          </div>
        </li>
      </ul>
    </div>
    <!-- 线上 -->
    <div class="resumption_content">
      <div class="resumption_content_title">
        <p class="title_right">
          <img :src="require('../../assets/img/ldjsc_zzkg_l.png')"
               class="title_right_img"
               alt="">
          <span>线上履职积分</span>
          <span class="title_icon"><van-icon @click="show = true"
                      name="question-o" /></span>
        </p>
      </div>
      <ul class="resumption_content_list">
        <li class="resumption_content_li"
            v-for="item, index in onlinePerformancePoints"
            :key="index">
          <div class="resumption_content_li_title"
               :style="{ background: item.bgcolor }">
            {{ item.name }}
          </div>
          <div class="resumption_content_li_num">
            <span class="resumption_content_piece">{{ item.num }}</span>件
            <span class="resumption_content_minute">{{ item.score }}</span>分
          </div>
        </li>
      </ul>
    </div>

    <!-- 议案建议 -->
    <van-popup close-icon="close"
               round
               v-model:show="shows"
               closeable
               :style="{ height: '25%', width: '90%' }">
      <div class="popup_con">
        <div class="popup_con_title">
          议案建议
        </div>
        <div class="info">1.大会期间,提出议案或建议的每件计2分,形成大会议案的领衔人每件计10分;</div>
        <div class="info">2.闭会期间,提出建议的每件计2分;</div>
        <div class="info">3.线下履职积分按照90%比例计入履职积分总分。</div>
      </div>
    </van-popup>
    <!-- 线上 -->
    <van-popup close-icon="close"
               round
               v-model:show="show"
               closeable
               :style="{ height: '48%', width: '90%' }">
      <div class="popup_con">
        <div class="popup_con_title">
          线上履职积分
        </div>
        <div class="info">1.线上履职积分按照10%比例计入履职积分总分;</div>
        <div class="info">2.每日首次登录代表履职通计1分;</div>
        <div class="info">3.在代表履职圈上发布履职信息每发布一条计1分;</div>
        <div class="info">4.参加意见征集活动发表相关意见计2分，相关意见被采纳计5分;</div>
        <div class="info">5.相关事迹在代表风采栏目作为单条发布每次计1分;</div>
        <div class="info">6.通过履职学习板块观看专家讲座视频每满一个小时计2分;</div>
        <div class="info">7.通过履职学习板块阅览电子书每满一个小时计2分;</div>
        <div class="info">8.通过代表履职通报名参加各类活动每次计1分。</div>
      </div>
    </van-popup>
    <!-- 提案那五个弹窗查看履职详情 -->
    <van-popup close-icon="close"
               round
               v-model:show="suggestionsinfoShow"
               closeable
               :style="{ height: '25%', width: '90%' }">
      <div class="popup_con">
        <div class="popup_con_title">
          履职项
        </div>
        <div class="info">1.大会期间,提出议案或建议的每件计2分,形成大会议案的领衔人每件计10分;</div>
        <div class="info">2.闭会期间,提出建议的每件计2分;</div>
        <div class="info">3.线下履职积分按照90%比例计入履职积分总分。</div>
      </div>
    </van-popup>
    <!-- 提案那五个弹窗查看履职详情 -->
    <van-popup close-icon="close"
               round
               v-model:show="suggestionsinfoShow"
               closeable
               :style="{ height: '40%', width: '90%' }">
      <div class="popup_con">
        <div class="popup_con_title">履职项</div>
        <ul>
          <li v-for="(item, index) in dataMessage"
              :key="index">
            {{ item }}
          </li>
        </ul>
      </div>
    </van-popup>
    <!-- 弹窗查看履职详情 -->
    <van-popup close-icon="close"
               round
               v-model:show="datainfoShow"
               closeable
               :style="{ height: '25%', width: '90%' }">
      <div class="popup_con">
        <div class="popup_con_title">履职项</div>
        <div class="info2">
          <ul>
            <li v-for="(item, index) in dataMessage"
                :key="index">
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Popup } from 'vant'
export default ({
  name: 'resumption',
  props: {},
  components: {
    [Popup.name]: Popup
  },
  setup () {
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const router = useRouter()
    const route = useRoute()
    const data = reactive({
      dataList: [],
      headimg: '',
      score: '',
      areaRanking: '',
      deleRanking: '',
      groupRanking: '',
      title: '履职详情',
      shows: false,
      show: false,
      id: route.query.id || '1',
      uid: route.query.uid || '1',
      types: route.query.types,
      // 建议五条数据
      propose: [
        { name: '领衔提交议案数量', value: 0, score: 0, bgcolor: '#fdf3ed' },
        { name: '附议提交议案数量', value: 0, score: 0, bgcolor: '#fdeff1' },
        { name: '领衔提交建议', value: 0, score: 0, bgcolor: '#edeefd' },
        { name: '附议提交建议', value: 0, score: 0, bgcolor: '#edf9fd' },
        { name: '建议被接受数量', value: 0, score: 0, bgcolor: '#fdf3ed' }
      ],
      onlinePerformancePoints: [
        { name: '登录代表履职通', num: 0, score: 0, bgcolor: '#fdf3ed' },
        { name: '在代表履职圈上发布履职信息', num: 0, score: 0, bgcolor: '#fdeff1' },
        { name: '参加意见征集活动发表意见或相关意见被有关部门采纳', num: 0, score: 0, bgcolor: '#edeefd' },
        { name: '相关事迹在代表风采栏目作为单条发布', num: 0, score: 0, bgcolor: '#edf9fd' },
        { name: '通过履职学习板块观看专家讲座视频', num: 0, score: 0, bgcolor: '#edf9fd' },
        { name: '通过履职学习板块阅览电子书', num: 0, score: 0, bgcolor: '#fdf3ed' },
        { name: '通过代表履职通报名参加各类活动', num: 0, score: 0, bgcolor: '#fdf3ed' }
      ],
      suggestionsinfoShow: false,
      datainfoShow: false,
      dutyDetailMap: {},
      entList: [],
      dataMessage: ''
    })
    onMounted(() => {
      if (data.types === 'myLZ') {
        getHoneDuty()
      } else {
        getDuty()
      }
    })
    // 履职详情
    const getHoneDuty = async () => {
      const { data: list } = await $api.myResumption.getResumption({ id: data.id, year: dayjs(new Date()).format('YYYY') })
      data.headimg = list.headimg
      data.score = list.score
      data.areaRanking = list.areaRanking
      data.deleRanking = list.deleRanking
      data.groupRanking = list.groupRanking
      data.propose[2].value = list.suggestlead || 0// 领衔提交建议
      data.propose[2].score = list.suggestleadScore || 0 // 领衔提交建议
      data.propose[3].value = list.suggestjoin || 0 // 附议提交建议
      data.propose[3].score = list.suggestjoinScore || 0 // 附议提交建议
      data.propose[4].value = list.suggestaccept || 0 // 建议被接受数量
      data.propose[4].score = list.suggestacceptScore || 0 // 建议被接受数量
      data.onlinePerformancePoints[0].num = list.loginCount || 0
      data.onlinePerformancePoints[0].score = list.loginScore || 0
      data.onlinePerformancePoints[1].num = list.publishDutyCount || 0
      data.onlinePerformancePoints[1].score = list.publishDutyScore || 0
      data.onlinePerformancePoints[2].num = list.publishSurveyCount || 0
      data.onlinePerformancePoints[2].score = list.publishSurveyScore || 0
      data.onlinePerformancePoints[3].num = list.publishRoutineCount || 0
      data.onlinePerformancePoints[3].score = list.publishRoutineScore || 0
      data.onlinePerformancePoints[4].num = list.watchVideoTime || 0
      data.onlinePerformancePoints[4].score = list.watchVideoScore || 0
      data.onlinePerformancePoints[5].num = list.readBookTime || 0
      data.onlinePerformancePoints[5].score = list.readBookScore || 0
      data.onlinePerformancePoints[6].num = list.signUpActivityCount || 0
      data.onlinePerformancePoints[6].score = list.signUpActivityScore || 0
      var actList = list.dutyList || []
      for (var i in actList) {
        var item = actList[i]
        item.num = actList[i].value
        var nColor = ['#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd']
        item.color = nColor[i]
        item.bgcolor = nColor[i]
        item.maxNum = actList[0].value
      }
      data.dataList = actList
      console.log('data.dataList==>', data.dataList)
    }

    const getDuty = async () => {
      const { data: list } = await $api.myResumption.getResumptionInfo({ id: data.uid, year: dayjs(new Date()).format('YYYY') })
      data.headimg = list.headimg
      data.score = list.score
      data.areaRanking = list.areaRanking
      data.deleRanking = list.deleRanking
      data.groupRanking = list.groupRanking
      data.propose[2].value = list.suggestlead || 0 // 领衔提交建议
      data.propose[2].score = list.suggestleadScore || 0 // 领衔提交建议
      data.propose[3].value = list.suggestjoin || 0 // 附议提交建议
      data.propose[3].score = list.suggestjoinScore || 0 // 附议提交建议
      data.propose[4].value = list.suggestaccept || 0 // 建议被接受数量
      data.propose[4].score = list.suggestacceptScore || 0 // 建议被接受数量
      data.onlinePerformancePoints[0].num = list.loginCount || 0
      data.onlinePerformancePoints[0].score = list.loginScore || 0
      data.onlinePerformancePoints[1].num = list.publishDutyCount || 0
      data.onlinePerformancePoints[1].score = list.publishDutyScore || 0
      data.onlinePerformancePoints[2].num = list.publishSurveyCount || 0
      data.onlinePerformancePoints[2].score = list.publishSurveyScore || 0
      data.onlinePerformancePoints[3].num = list.publishRoutineCount || 0
      data.onlinePerformancePoints[3].score = list.publishRoutineScore || 0
      data.onlinePerformancePoints[4].num = list.watchVideoTime || 0
      data.onlinePerformancePoints[4].score = list.watchVideoScore || 0
      data.onlinePerformancePoints[5].num = list.readBookTime || 0
      data.onlinePerformancePoints[5].score = list.readBookScore || 0
      data.onlinePerformancePoints[6].num = list.signUpActivityCount || 0
      data.onlinePerformancePoints[6].score = list.signUpActivityScore || 0
      data.dutyDetailMap = list.dutyDetailMap || []
      var actList = list.dutyList || []
      for (var i in actList) {
        var item = actList[i]
        item.num = actList[i].value
        var nColor = ['#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdeff1', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdf3ed', '#fdeff1', '#edeefd', '#edf9fd', '#edf9fd', '#fdeff1']
        item.color = nColor[i]
        item.bgcolor = nColor[i]
        item.maxNum = actList[0].value
      }
      data.dataList = actList
    }
    // 查看履职详情
    const suggestionsClickInfo = (bitem) => {
      data.suggestionsinfoShow = true
      if (bitem.name !== '领衔提交议案数量' || bitem.name !== '附议提交议案数量') {
        data.entList = []
        for (const i in data.dutyDetailMap) {
          data.entList.push({
            name: i,
            infoText: data.dutyDetailMap[i]
          })
        }
        data.entList.forEach(_item => {
          if (bitem.name === _item.name) {
            var dataMessage = _item.infoText.map((item, i) => {
              return `${i + 1}、 ${item}`
            })
            data.dataMessage = _item.infoText.length === 0 ? '暂无数据' : dataMessage
          }
        })
      }
    }
    // 查看履职详情
    const dataClickInfo = (item) => {
      data.datainfoShow = true
      data.entList = []
      for (const i in data.dutyDetailMap) {
        data.entList.push({
          name: i,
          infoText: data.dutyDetailMap[i]
        })
      }
      data.entList.map(aitem => {
        if (item.name === aitem.name) {
          var dataMessage = aitem.infoText.map((item, i) => {
            return `${i + 1}、 ${item}`
          })
          console.log('dataMessage===>>', dataMessage)
          data.dataMessage = aitem.infoText.length === 0 ? '暂无数据' : dataMessage
        }
      })
    }

    const offline = () => {
      data.shows = true
    }

    const add = () => {
      router.push({ path: '/add', query: { paramType: 'lzbl' } })
    }
    return { ...toRefs(data), $api, offline, router, route, add, suggestionsClickInfo, dataClickInfo }
  }
})
</script>
<style lang='less' scoped>
.resumption {
  box-sizing: border-box;

  .resumption_top {
    display: flex;
    width: 100%;
    height: 120px;
    background: #e8f1f7;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 10px;

    .resumption_top_my {
      width: 100px;
      height: 100px;
      border-radius: 10px;
    }

    .resumption_top_num {
      color: #3d5a86;
      text-align: center;

      >.num {
        font-size: 55px;
      }
    }
  }

  .resumption_center {
    width: 95%;
    margin: 10px auto;
    height: 80px;
    background: #fff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0px 6px 56px -10px rgba(0, 0, 0, 0.1);

    .resumption_center_con {
      text-align: center;
      flex: 1;

      .resumption_center_con_p {
        font-size: 25px;
        flex-wrap: 700;
        color: #3894ff;
      }

      .resumption_center_con_ps {
        font-size: 14px;
      }
    }

    .center_bor {
      border-right: 1px solid #ebebeb;
      border-left: 1px solid #ebebeb;
    }
  }

  .resumption_content {
    width: 95%;
    margin: 0 auto;
    background: #fff;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;

    .resumption_content_title {
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title_right {
        height: 25px;
        line-height: 25px;
        display: flex;
        align-items: center;
      }

      .title_right_img {
        width: 5px;
        height: 16px;
        margin-right: 5px;
      }

      .title_icon {
        font-size: 20px;
        color: #a8a8a8;
        margin: 0 5px;
      }

      .title_left {
        width: 80px;
        height: 25px;
        color: #fff;
        text-align: center;
        line-height: 25px;
        background: #3894ff;
        border-radius: 5px;
      }
    }
  }

  .resumption_content_list {
    width: 95%;
    margin: 0 auto;
    border-radius: 10px;

    .resumption_content_li {
      width: 100%;
      // height: 30px;
      margin: 10px 0;
      display: flex;
      align-items: center;

      .resumption_content_li_title {
        width: 60%;
        // height: 30px;
        // line-height: 30px;
        // padding-left: 10px;
        padding: 5px;
        background: #3894ff;
        border-radius: 5px;
        font-size: 14px;
        color: #eb5757;
      }

      .resumption_content_li_num {
        width: 40%;
        font-size: 14px;
        display: flex;
        align-items: center;

        .resumption_content_piece {
          display: block;
          color: #eb5757;
          width: 40px;
          text-align: center;
        }

        .resumption_content_minute {
          display: block;
          color: #eb5757;
          width: 40px;
          text-align: center;
        }
      }
    }
  }

  .popup_con {
    margin: 0px 10px;

    .popup_con_title {
      text-align: center;
      font-size: 20px;
      margin: 10px 0;
      font-weight: 700;
    }

    .info {
      font-size: 14px;
      margin: 10px 0;
    }

    .info2 {
      text-align: center;
      margin-top: 30px;
      font-size: 15px;
      margin: 10px 0;
    }
  }
}
</style>

<template>
  <div class="vantEmptyGeneralStyle">
    <div class="imgBoxStyle">
      <img :src="imgAddress" alt="" />
    </div>
    <div class="textStyle">
      <div class="textOne">暂无数据~</div>
      <div class="textTwo">
        {{ messageText ? messageText : '您还没有相关信息哦！' }}~
      </div>
    </div>
  </div>
</template>
<script>

import { reactive, toRefs, onMounted } from 'vue'
export default {
  name: 'vantEmptyGeneral',
  props: ['messageText'], // 接收的提示信息
  components: {
  },
  setup (props) {
    const data = reactive({
      imgAddress: require('@/assets/img/errMsg1.png')
    })
    onMounted(() => {
    })
    return { ...toRefs(data) }
  }
}
</script>

<style lang="less" scoped>
.vantEmptyGeneralStyle {
  height: 100vh;
  display: flex;
  align-items: center;
  flex-direction: column;
  align-content: center;
  .imgBoxStyle {
    width: 100%;
    text-align: center;
    margin-top: 100px;
    img {
      width: 200px;
    }
  }
  .textStyle {
    text-align: center;
    .textOne {
      margin: 10px 0px;
      font-size: 18px;
      color: #66676b;
    }
    .textTwo {
      font-size: 18px;
      color: #9b9ca0;
    }
  }
}
</style>

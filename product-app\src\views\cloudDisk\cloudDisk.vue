<template>
  <div class="cloudDisk">
    <!-- 标题栏------------------------------------------ -->
    <!-- v-if="!pageParam.headerH" -->
    <header id="dblclick" class="header flex_box"
      :style="$general.loadConfiguration(-3) + 'padding-top:' + safeAreaTop + 'px;background: #fff;'">
      <div class="btnLeft_box flex_box">
        <div v-if="isSelect" @click="close()" :style="$general.loadConfiguration(-1) + 'color:#333;margin-left:10px;'"
          class="img_btn flex_box flex_align_center flex_justify_content">取消</div>
        <div v-else @click="close()" :style="$general.loadConfiguration(-1) + 'color:#333;margin-left:10px;'"
          class="img_btn flex_box flex_align_center flex_justify_content">
          <van-icon v-if="nowLevel.length > 1" :color="$general.getHeadThemeRelatively()"
            :size="($general.appFontSize + 3) + 'px'" name="arrow-left"></van-icon>
        </div>
      </div>
      <div class="flex_placeholder flex_box">
        <div class="flex_placeholder flex_box flex_align_center flex_justify_content new_title">
          <span v-if="isSelect" :style="$general.loadConfiguration(3) + 'color:' + $general.getHeadThemeRelatively()"
            class="text_one2">已选中{{ listSelect.length }}个文件</span>
          <span v-else :style="$general.loadConfiguration(3) + 'color:' + $general.getHeadThemeRelatively()"
            class="text_one2" v-html="title"></span>
        </div>
      </div>
      <div id="btnRight_box" class="btnRight_box flex_box">
        <div v-if="isSelect" @click="checkAll"
          :style="$general.loadConfiguration(-1) + 'margin-right:10px;color:' + appTheme"
          class="img_btn flex_box flex_align_center flex_justify_content">{{ checkAllHasCancel() ? '取消' : '' }}全选</div>
        <template v-else>
          <div v-if="showMore()" :style="$general.loadConfiguration(-1) + 'margin-right:10px;color:' + appTheme"
            class="img_btn flex_box flex_align_center flex_justify_content">
            <van-popover get-container="#dblclick" v-model:show="moreType.showPopover" trigger="click"
              placement="bottom-end">
              <div v-for="(item, index) in moreType.data" :key="index" @click="onPopMoreSelect(item)" role="menuitem"
                class="van-popover__action" style="width:130px;height:40px;">
                <div class="van-popover__action-text" style="font-size:15px;">{{ item.text }}</div>
                <div v-if="item.unread && item.unread > 0"
                  class="redDot_box flex_box flex_align_center flex_justify_content" :class="'redDot_big'"
                  :style="$general.loadConfiguration(-4) + $general.loadConfigurationSize(4)"
                  v-html="item.unread > 99 ? '99+' : item.unread"></div>
              </div>
              <template #reference>
                <div style="position: relative;">
                  <van-icon :color="$general.getHeadThemeRelatively()" style="padding: 10px 0;"
                    :size="($general.appFontSize + 3) + 'px'" name="ellipsis"></van-icon>
                  <div v-if="moreType.unreadAll > 0" class="redDot_box flex_box flex_align_center flex_justify_content"
                    :class="'redDot_big'"
                    :style="$general.loadConfiguration(-4) + $general.loadConfigurationSize(4) + 'right:-10px;'"
                    v-html="moreType.unreadAll > 99 ? '99+' : moreType.unreadAll"></div>
                </div>
              </template>
            </van-popover>
          </div>
          <div v-else-if="(nowLevel.length && nowLevel[nowLevel.length - 1].key == 'inboxList')"
            class="flex_placeholder flex_box">
            <div @click="switchInbox()" class="img_btn flex_box flex_align_center flex_justify_content"
              :style="$general.loadConfiguration(-3) + 'color:#333;'">{{ nowLevel[nowLevel.length - 1].type ==
                "1" ? "处室" : "个人" }}收件箱</div>
          </div>
          <div v-else-if="(nowLevel.length && nowLevel[nowLevel.length - 1].key == 'recycleList')"
            @click="footerBtnClick(thoroughDeleteBtn, true)"
            class="img_btn flex_box flex_align_center flex_justify_content"
            :style="$general.loadConfiguration(-3) + 'color:#333;margin-right:10px;'">全部清除</div>
        </template>
      </div>
    </header>
    <div class="headerPlaceholder" :style="'padding-top:' + safeAreaTop + 'px;'"></div>

    <div v-if="(nowLevel.length && nowLevel[nowLevel.length - 1].key == 'recycleList')"
      :style="$general.loadConfiguration(-3) + 'padding:40px 15px 10px;color:#666;'">
      存放期限为30天，时间过后自动删除。
    </div>
    <!-- <van-sticky :offset-top="nowStickyH"> -->
    <!--搜索-------------------------------------------->
    <div v-else id="search" class="search_box" :style="$general.loadConfiguration()">
      <div class="search_warp flex_box">
        <div @click="btnSearch();" class="search_btn flex_box flex_align_center flex_justify_content">
          <van-icon :size="($general.appFontSize - 10) + 'px'" :color="'#666'" class-prefix="icon"
            :name="'sousuo'"></van-icon>
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input" action="javascript:return true;"> <input
            id="searchInput" class="flex_placeholder search_input" :style="$general.loadConfiguration(-1)"
            :placeholder="seachPlaceholder" maxlength="100" type="search" ref="btnSearch" @keyup.enter="btnSearch()"
            v-model="seachText" />
          <div v-if="seachText" @click="seachText = ''; btnSearch();"
            class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="($general.appFontSize) + 'px'" :color="'#ccc'" :name="'clear'"></van-icon>
          </div>
        </form>
        <!-- <van-dropdown-menu v-if="false"
                           class="search-dropdown-menu flex_box flex_align_center"
                           :active-color="appTheme"
                           :style="$general.loadConfiguration(-1)">
          <van-dropdown-item @open="dropdownOpen"
                             @closed="dropdownClosed"
                             title="筛选"
                             ref="filter">
            <van-cell v-for="(item,index) in filters"
                      :key="index"
                      :title="item.title"
                      :style="$general.loadConfiguration()">
              <template v-if="item.show"
                        v-slot:right-icon> -->
        <!--选择-->
        <!-- <van-dropdown-menu v-if="item.type == 'select'"
                                   :active-color="appTheme">
                  <van-dropdown-item v-model="item.value"
                                     get-container="#search"
                                     :options="item.data"></van-dropdown-item>
                </van-dropdown-menu> -->
        <!--开关-->
        <!-- <van-switch v-else-if="item.type == 'switch'"
                            :active-color="appTheme"
                            v-model="item.value"
                            :size="($general.appFontSize+8)+'px'"></van-switch> -->
        <!--其它只展示文字-->
        <!-- <div v-else
                     :style="$general.loadConfiguration()">{{item.value}}</div>
              </template>
            </van-cell>
            <div class="flex_box">
              <van-button block
                          @click="onReset">重置</van-button>
              <van-button block
                          :color="appTheme"
                          @click="onConfirm">确认</van-button>
            </div>
          </van-dropdown-item>
        </van-dropdown-menu> -->
      </div>
    </div>
    <!-- 排序 和展示切换------------------------------------------ -->
    <div class="switchShow_box flex_box flex_align_center" :style="$general.loadConfiguration(-3)">
      <van-popover class="sort" get-container=".switchShow_box" v-model:show="sortType.showPopover" :offset="[15, 8]"
        trigger="click" placement="bottom-start" :actions="sortType.data" @select="onPopSortSelect">
        <template #reference>
          <div class="switchShow_warp click" :style="$general.loadConfiguration(-5)">{{ sortType.value.text }}</div>
        </template>
      </van-popover>
      <div class="flex_placeholder"></div>
      <div @click="switchShowType()" class="switchShow_warp flex_box flex_align_center flex_justify_content">
        <van-icon :color="'#666'" :size="($general.appFontSize + (showType == 0 ? 3 : 4)) + 'px'" class-prefix="icon"
          :name="showType == 0 ? 'gongge' : 'liebiao3'"></van-icon>
      </div>
    </div>
    <div style="margin: 0 15px;" class="clouddisk_after van-hairline--top"></div>
    <!-- </van-sticky> -->

    <!--数据列表------------------------------------------ -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <template v-if="listData.length">
          <template v-if="showType == 0">
            <van-checkbox-group ref="checkboxGroup" v-model="listSelect">
              <ul>
                <li v-for="(item, index) in listData" :key="index" @click="msgImgTap(index)" v-press="() => {
                  msgImgPress(index)
                }
                  " class="flex_box flex_align_center">
                  <img :src="require('../../assets/img/fileicon/' + item.iconInfo.name)"
                    :style="$general.loadConfigurationSize(13)" class="clouddisk_icon" />
                  <div class="flex_placeholder">
                    <div class="clouddisk_warp flex_box flex_align_center">
                      <div class="flex_placeholder">
                        <div :style="$general.loadConfiguration(-1)" class="clouddisk_name text_one2">{{ item.name }}
                        </div>
                        <div :style="$general.loadConfiguration(-4)" class="clouddisk_add flex_box flex_align_center">
                          <template
                            v-if="item.shareObjectList && item.shareObjectList.length && (nowLevel.length && nowLevel[nowLevel.length - 1].id != 'share')">
                            <div class="inherit">已共享</div>
                            <div class="clouddisk_add_line" :style="$general.loadConfigurationSize(-2, 'h')"></div>
                          </template>
                          <template
                            v-if="item.userName && (nowLevel.length && nowLevel[nowLevel.length - 1].id != 'person')">
                            <div class="inherit">{{ item.userName }}</div>
                            <div class="clouddisk_add_line" :style="$general.loadConfigurationSize(-2, 'h')"></div>
                          </template>
                          <div class="inherit">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm') }}</div>
                        </div>
                      </div>
                      <div v-if="isSelect" class="clouddisk_more flex_box flex_align_center flex_justify_content">
                        <div style="background:rgba(0,0,0,0);width:30px;height:30px;margin-right:-20px;z-index: 99999;">
                        </div>
                        <van-checkbox :icon-size="($general.appFontSize + 1) + 'px'" :checked-color="appTheme"
                          :name="item" shape="round"></van-checkbox>
                      </div>
                      <div v-else @click.stop="openListMore(item)"
                        class="clouddisk_more flex_box flex_align_center flex_justify_content">
                        <van-icon :color="'#999'" :size="($general.appFontSize + 1) + 'px'"
                          :name="'ellipsis'"></van-icon>
                      </div>
                    </div>
                    <div style="margin-right: 15px;" class="clouddisk_after van-hairline--top"></div>
                  </div>
                </li>
              </ul>
            </van-checkbox-group>
          </template>
          <template v-else-if="showType == 1">
            <van-checkbox-group ref="checkboxGroup" v-model="listSelect">
              <ul class="clouddisk_grid_box flex_box T-flex-flow-row-wrap">
                <li v-for="(item, index) in listData" :key="index" @click="msgImgTap(index)" v-press="() => {
                  msgImgPress(index)
                }
                  " class="clouddisk_grid_item"
                  :style="'border-color:' + (systemType == 'ios' ? 'rgba(153,153,153,0.1)' : 'rgba(153,153,153,0.2)')">
                  <img :src="require('../../assets/img/fileicon/' + item.iconInfo.name)"
                    :style="$general.loadConfigurationSize(22)" class="clouddisk_grid_icon" />
                  <div :style="$general.loadConfiguration(-1)" class="clouddisk_grid_name text_two">{{ item.name }}
                  </div>

                  <div v-if="isSelect" class="clouddisk_grid_more flex_box flex_align_center flex_justify_content">
                    <div style="background:rgba(0,0,0,0);width:30px;height:30px;margin-right:-20px;z-index: 99999;">
                    </div>
                    <van-checkbox :icon-size="($general.appFontSize + 1) + 'px'" :checked-color="appTheme" :name="item"
                      shape="round"></van-checkbox>
                  </div>
                  <div v-else @click.stop="openListMore(item)"
                    class="clouddisk_grid_more flex_box flex_align_center flex_justify_content">
                    <van-icon :color="'#999'" :size="($general.appFontSize + 1) + 'px'" :name="'ellipsis'"></van-icon>
                  </div>
                </li>
              </ul>
            </van-checkbox-group>
          </template>
        </template>
        <div v-else-if="listData.length == 0">
          <van-empty :style="$general.loadConfiguration(-2)">
          </van-empty>
        </div>
      </van-list>
    </van-pull-refresh>
    <!--加载中提示 首次为骨架屏-------------------------------------------->
    <div v-if="showSkeleton" class="notText">
      <van-skeleton v-for="(item, index) in 3" :key="index" title :row="3"></van-skeleton>
    </div>

    <!-- 更多按钮------------------------------------------ -->
    <transition name="van-fade">
      <ul v-if="footerBtnsShow" class="footer_btn_box" :style="$general.loadConfiguration()">
        <template v-for="(item, index) in footerBtns" :key="index">
          <div v-if="showFooterBtns(item)">
            <div is-link @click="showPopups(1)" class="van-button-box flex_box flex_align_center flex_justify_content"
              :style="$general.loadConfigurationSize(26) + 'background:' + (item.bg || appTheme)">
              <template v-if="item.type == 'icon'">
                <van-icon :color="item.color || '#fff'" :size="($general.appFontSize + 5) + 'px'"
                  :class-prefix="item.prefix" :name="item.iconName"></van-icon>
              </template>
            </div>
            <van-popup v-model:show="showPopup" position="bottom" :style="{ height: '20%' }" closeable round>
              <div class="flex_box flex_align_center flex_justify_content file_options">
                <div class="cloudDisk_icon_file" @click="showPopups(2)">
                  <img :src="require('../../assets/img/icon_folder_add.png')"
                    :style="$general.loadConfigurationSize(13)" class="clouddisk_icon" />
                  <div>新建文件夹</div>
                </div>

                <div class="cloudDisk_icon_file" @click="openFile">
                  <img :src="require('../../assets/img/icon_files.png')" :style="$general.loadConfigurationSize(13)"
                    class="clouddisk_icon" />
                  <div>上传文件</div>
                </div>
                <van-uploader accept=".pdf,.doc.,rar,.zip,.docx,image/*,audio/*,video/*" v-model="fileList"
                  :after-read="afterReadFile" style="display: none;" ref="file">
                </van-uploader>
              </div>
            </van-popup>
            <van-popup v-model:show="showAddFile" position="bottom" :style="{ height: '40%' }" round>
              <div class="flex_box flex_align_center flex_justify_content file_options add_file">
                <div :style="$general.loadConfiguration(-3) + 'color:#333'" @click="deleteAddFile">取消</div>
                <div style="font-weight:bold;">新建个人文件夹</div>
                <div :style="$general.loadConfiguration(-3) + 'color:' + appTheme" @click="createBucket(newFileName)">确定
                </div>
              </div>
              <div class="flex_box flex_align_center flex_justify_content flex_flex_direction_column"
                style="height:250px;">
                <img :src="require('../../assets/img/icon_folder_add.png')" :style="$general.loadConfigurationSize(13)"
                  class="clouddisk_icon" />
                <van-cell-group inset>
                  <van-field v-model="newFileName" placeholder="新建文件夹" style="background:#ede8e8;"
                    input-align="center" />
                </van-cell-group>
              </div>
            </van-popup>
          </div>
        </template>
      </ul>
    </transition>

    <!-- <div :style="{paddingBottom:(safeAreaBottom+(showFooter() || (isSelect && getLlistOperate().length)?80:0))+'px'}"></div> -->
    <!-- 首页底部切换按钮------------------------------------------ -->
    <transition name="van-slide-up">
      <footer v-if="showFooter()" :style="'padding-bottom:' + (safeAreaBottom) + 'px'" class="footer flex_box">
        <template v-for="(item, index) in mainBtns.data" :key="index">
          <div v-if="item.show" class="footer_item T-flexbox-vertical flex_align_center flex_justify_content"
            @click="switchFooter(index)">
            <img :src="item.url" :style="$general.loadConfigurationSize(5)" />
            <!-- :alt="cacheImg(item)" -->
            <p class="footer_item_p text_one2"
              :style="$general.loadConfiguration(-6) + ';' + (mainBtns.active == index ? 'color:' + appTheme : 'color:#adadad')">
              {{ item.title }}</p>
            <div v-if="item.pointNumber > 0" class="redDot_box flex_box flex_align_center flex_justify_content"
              :class="item.pointType == 'big' ? 'redDot_big' : 'redDot_small'"
              :style="(item.pointType == 'big' ? $general.loadConfiguration(-4) + $general.loadConfigurationSize(4) : $general.loadConfigurationSize(-6)) + (careMode ? (item.pointType == 'big' ? ';top:-0.01rem;right: calc(50% - 0.26rem);' : 'top:0;right: calc(50% - 0.2rem);') : '')"
              v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></div>
          </div>
        </template>
      </footer>
    </transition>
    <!-- 选择状态底部按钮------------------------------------------ -->
    <transition name="van-slide-up">
      <footer v-if="isSelect && getLlistOperate().length" :style="'padding-bottom:' + (safeAreaBottom) + 'px'"
        class="footer_select">
        <div class="footer_select_warp flex_box flex_align_center flex_justify_content" v-if="isSHowFooter">
          <template v-for="(item, index) in getLlistOperate()" :key="index">
            <div v-if="getLlistOperate().length > 5 ? index < 4 : true">
              <div class="footer_select_item click T-flexbox-vertical flex_align_center flex_justify_content"
                @click="item.disabled ? '' : footerBtnClick(item)">
                <van-icon :color="item.disabled ? '#ccc' : (item.color || '#333')"
                  :size="($general.appFontSize + 5) + 'px'" :class-prefix="item.prefix"
                  :name="item.iconName"></van-icon>
                <div class="footer_select_text"
                  :style="$general.loadConfiguration(-4) + 'color: ' + (item.disabled ? '#ccc' : '#333')">{{ item.name
                  }}</div>
              </div>
            </div>
          </template>
          <div v-if="getLlistOperate().length > 5"
            class="footer_select_item click T-flexbox-vertical flex_align_center flex_justify_content"
            @click="footerBtnClick({ click: 'select_more' })">
            <van-icon :color="'#333'" :size="($general.appFontSize + 5) + 'px'" :name="'ellipsis'"></van-icon>
            <div class="footer_select_text" :style="$general.loadConfiguration(-4)">更多</div>
          </div>
        </div>
      </footer>
    </transition>
    <div class="footer_btn_box">
      <van-popup v-model:show="editFile" position="bottom" :style="{ height: '40%' }" round>
        <div class="flex_box flex_align_center flex_justify_content file_options add_file">
          <div :style="$general.loadConfiguration(-3) + 'color:#333'" @click="editFile = false">取消</div>
          <div style="font-weight:bold;">文件重命名</div>
          <div :style="$general.loadConfiguration(-3) + 'color:' + appTheme" @click="editBucket(newFileName)">确定</div>
        </div>
        <div class="flex_box flex_align_center flex_justify_content flex_flex_direction_column" style="height:250px;">
          <img :src="editFileImg" :style="$general.loadConfigurationSize(13)" class="clouddisk_icon" />
          <van-cell-group inset>
            <van-field v-model="newFileName" placeholder="文件重命名" style="background:#ede8e8;" input-align="center" />
          </van-cell-group>
        </div>
      </van-popup>
    </div>
    <div v-if="showActionSheet || showActionSheetSend || showActionSheetMore"
      style="position:fixed;top:0;width:100%;height:100vh;z-index: 99999;">
      <van-action-sheet v-model:show="showActionSheet" :round="true" :closeable="false" safe-area-inset-bottom
        :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
        <actionSHeet v-if="showActionSheet" @cancelActionSheet="cancelActionSheet" @fileShare="fileShare"
          @fileMove="fileMove" @fileCopy="fileCopy" :type="actiontype" :baseData="baseData" :baseType="baseType"
          :baseTitle="baseTitle" :operateIds="actionoperateIds"></actionSHeet>
      </van-action-sheet>
      <van-action-sheet v-model:show="showActionSheetSend" :round="true" :closeable="false" safe-area-inset-bottom
        :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
        <actionSheetSend v-if="showActionSheetSend" @cancelActionSheetSend="cancelActionSheetSend"
          :nBaseType="nowLevel[0].id" :operateIds="actionoperateIds"></actionSheetSend>
      </van-action-sheet>
      <van-action-sheet v-model:show="showActionSheetMore" :round="true" :closeable="false" safe-area-inset-bottom
        :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
        <div v-if="actions.length" class="morelist_box">
          <div v-for="(item, index) in actions" :key="index" @click="item.disabled ? '' : itemSelect(item, index)"
            class="morelist_item click flex_box flex_align_center">
            <van-icon :color="item.disabled ? '#ccc' : (item.color || '#333')" :size="((appFontSize + 5)) + 'px'"
              :class-prefix="item.prefix" :name="item.iconName"></van-icon>
            <div class="morelist_name"
              :style="$general.loadConfiguration(-3) + 'color: ' + (item.disabled ? '#ccc' : '#333')">
              {{ item.name }}</div>
          </div>
        </div>
      </van-action-sheet>
    </div>
    <van-overlay :show="overlayShow" :z-index="999999">
      <div style="width:clac(100% - 20px);margin-left:10px;margin-top:100%;text-align:center;">
        <van-circle v-model:current-rate="percentage" :rate="30" :speed="100" :text="percentage + '%'">
          <div style="color:#fff;text-align:center;margin-top:40%;">
            {{ percentage + '%' }}
          </div>
        </van-circle>
        <!-- <van-progress :percentage="percentage" /> -->
      </div>
    </van-overlay>
  </div>
</template>
<script>
import actionSHeet from './actionSHeet_share'
import actionSheetSend from './actionSheet_send'
import { useRouter } from 'vue-router'
import { inject, reactive, onMounted, toRefs, watch, nextTick } from 'vue'
import { NavBar, Sticky, Image as VanImage, Popover, Icon, Skeleton, Toast, Popup, Uploader, Dialog, ActionSheet, Progress, Circle, Overlay } from 'vant'
export default {
  name: 'cloudDisk',
  components: {
    actionSHeet,
    actionSheetSend,
    [Icon.name]: Icon,
    [Overlay.name]: Overlay,
    [Circle.name]: Circle,
    [Progress.name]: Progress,
    [Dialog.name]: Dialog,
    [ActionSheet.name]: ActionSheet,
    [Popover.name]: Popover,
    [VanImage.name]: VanImage,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Skeleton.name]: Skeleton,
    [Popup.name]: Popup,
    [Uploader.name]: Uploader
  },
  setup () {
    const router = useRouter()
    // const route = useRoute()
    const $api = inject('$api')
    // const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    // const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const downloadBtn = { name: '下载', type: 'ib', click: 'download', iconName: 'down', prefix: undefined, disabled: false }
    const sharedBtn = { name: '共享', type: 'ib', click: 'shared', iconName: 'share-o', prefix: undefined, disabled: false }
    const unSharedBtn = { name: '取消共享', type: 'ib', click: 'unshared', iconName: 'share-o', prefix: undefined, disabled: false }
    const renameBtn = { name: '重命名', type: 'ib', click: 'rename', iconName: 'new-o', prefix: undefined, disabled: false }
    const deleteBtn = { name: '删除', type: 'ib', click: 'delete', iconName: 'delete-o', prefix: undefined, disabled: false }
    const sendBtn = { name: '发送', type: 'ib', click: 'send', iconName: 'guide-o', prefix: undefined, disabled: false }
    const moveBtn = { name: '移动', type: 'ib', click: 'move', iconName: 'peer-pay', prefix: undefined, disabled: false }
    const copyBtn = { name: '复制', type: 'ib', click: 'copy', iconName: 'points', prefix: undefined, disabled: false }
    const reductionBtn = { name: '还原', type: 'ib', click: 'reduction', iconName: 'replay', prefix: undefined, disabled: false }
    const thoroughDeleteBtn = { name: '彻底删除', type: 'ib', click: 'thoroughDelete', iconName: 'delete-o', prefix: undefined, disabled: false }
    const data = reactive({
      thoroughDeleteBtn: thoroughDeleteBtn,
      title: '个人文件', // 页面标题
      nowLevel: [// 当前页面层级 默认首页  key代表某个页面 name页面标题 id或其它参数自定义
      ],
      appTheme: $appTheme,
      safeAreaBottom: 0,
      safeAreaTop: 0,
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索关键词',
      moreType: { showPopover: false, value: '', unreadAll: 0, data: [{ text: '收件箱(个人)', value: '1', unread: 0 }, { text: '收件箱(处室)', value: '2', unread: 0 }, { text: '发件箱', value: '3' }, { text: '回收站', value: '4' }] },
      sortType: { showPopover: false, value: { text: '默认排序 ↑', value: '1' }, data: [{ text: '默认排序 ↑', value: '1' }, { text: '时间排序 ↑', value: '2' }, { text: '时间排序 ↓', value: '3' }] },
      nowStickyH: 0,
      showType: 0, // 列表展示类型 0默认正常列表  1方格列表
      scrollTop: 0, // 页面划动距离
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      listData: [
        // {"id":"rendaplatformperson/1_430000/xxx","name":"xxx","dirType":"rendaplatformperson","type":"文件夹","userId":"1_430000","userName":"技术支持","departmentId":null,"parentId":"rendaplatformperson/1_430000","parentIds":null,"rootPath":"rendaplatformperson/1_430000","lastModifyDate":"2022-03-18 17:49:09","lastModifyTime":0,"shareObjectList":null,"isShare":null,"delFlag":null,"size":0,"historyId":null,"dirMapping":null,"delUserId":null,"delUserName":null,"iconInfo":{"name":"icon_folder.png","type":"folder"}},
        // {"id":"rendaplatformperson/1_430000/个人文件夹","name":"个人文件夹","dirType":"rendaplatformperson","type":"文件夹","userId":"1_430000","userName":"技术支持","departmentId":null,"parentId":"rendaplatformperson/1_430000","parentIds":null,"rootPath":"rendaplatformperson/1_430000","lastModifyDate":"2022-03-29 14:36:57","lastModifyTime":0,"shareObjectList":[{"shareDate":"2022-03-30 19:10:23","shareUser":"技术支持","shareFolder":"湖南省共享盘","shareFolderId":"f1723bbb-0e12-4687-bf98-1aa8312825f5","authority":"1","isShare":"1","hasShareParent":"0","delUserId":null,"delUserName":null}],"isShare":"1","delFlag":null,"size":222872,"historyId":null,"dirMapping":null,"delUserId":null,"delUserName":null,"iconInfo":{"name":"icon_folder.png","type":"folder"}},
        // {"id":"rendaplatformperson/1_430000/委员信息@<EMAIL>","name":"委员信息@<EMAIL>","dirType":null,"userId":"1_430000","userName":"技术支持","departmentId":null,"bucketName":null,"bucketId":"rendaplatformperson/1_430000","rootPath":"rendaplatformperson/1_430000","type":"图片","searchWord":null,"lastModifyDate":"2022-04-25 16:00:24","lastModifyTime":1650873624986,"size":7458,"shareUrl":null,"shareObjectList":[],"isShare":null,"dirMapping":null,"delFlag":null,"delUserId":null,"delUserName":null,"historyId":null,"iconInfo":{"name":"icon_pic.png","type":"image","convertType":"23"}},
        // {"id":"rendaplatformperson/1_430000/新建 Microsoft Excel 工作表.xlsx","name":"新建 Microsoft Excel 工作表.xlsx","dirType":null,"userId":"1_430000","userName":"技术支持","departmentId":null,"bucketName":null,"bucketId":"rendaplatformperson/1_430000","rootPath":"rendaplatformperson/1_430000","type":"文档","searchWord":null,"lastModifyDate":"2022-06-02 17:15:55","lastModifyTime":1654161355381,"size":9489,"shareUrl":null,"shareObjectList":[],"isShare":null,"dirMapping":null,"delFlag":null,"delUserId":null,"delUserName":null,"historyId":null,"iconInfo":{"name":"icon_excel.png","type":"excel","convertType":"61"}},
        // {"id":"rendaplatformperson/1_430000/新建 Microsoft Word 文档.docx","name":"新建 Microsoft Word 文档.docx","dirType":null,"userId":"1_430000","userName":"技术支持","departmentId":null,"bucketName":null,"bucketId":"rendaplatformperson/1_430000","rootPath":"rendaplatformperson/1_430000","type":"文档","searchWord":null,"lastModifyDate":"2022-06-02 17:16:34","lastModifyTime":1654161394921,"size":12238,"shareUrl":null,"shareObjectList":[],"isShare":null,"dirMapping":null,"delFlag":null,"delUserId":null,"delUserName":null,"historyId":null,"iconInfo":{"name":"icon_word.png","type":"word","convertType":"61"}},
        // {"id":"rendaplatformperson/1_430000/新建 Microsoft PowerPoint 演示文稿.pptx","name":"新建 Microsoft PowerPoint 演示文稿.pptx","dirType":null,"userId":"1_430000","userName":"技术支持","departmentId":null,"bucketName":null,"bucketId":"rendaplatformperson/1_430000","rootPath":"rendaplatformperson/1_430000","type":"文档","searchWord":null,"lastModifyDate":"2022-06-02 17:18:11","lastModifyTime":1654161491241,"size":33639,"shareUrl":null,"shareObjectList":[],"isShare":null,"dirMapping":null,"delFlag":null,"delUserId":null,"delUserName":null,"historyId":null,"iconInfo":{"name":"icon_ppt.png","type":"ppt","convertType":"61"}}
      ], // 列表数据
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [{ name: '新增', type: 'icon', click: 'add', iconName: 'plus', prefix: undefined }], // 底部按钮集合 top为返回顶部 btn为按钮

      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
        { show: true, type: 'select', key: '', title: '请选择届', value: '', defaultValue: '', data: [{ text: '所有', value: '' }, { text: '十二届', value: '十二' }, { text: '十一届', value: '十一' }] },
        { show: true, type: 'select', key: '', title: '请选择次', value: '', defaultValue: '', data: [{ text: '所有', value: '' }, { text: '一次', value: '一' }, { text: '二次', value: '二' }, { text: '三次', value: '三' }] },
        { show: true, type: 'switch', key: '', title: '是否主并提案', value: false, defaultValue: false, data: [] }
      ], // 筛选集合

      mainBtns: {
        active: 0,
        data: [
          { show: true, id: 'person', title: '个人文件', url: require('../../assets/img/icon_foot_personal_in.png'), defaultImg: require('../../assets/img/icon_foot_personal.png'), selectImg: require('../../assets/img/icon_foot_personal_in.png'), infoName: '', pointType: 'big', pointNumber: 0, remarks: '' },
          { show: true, id: 'depart', title: '处室文件', url: require('../../assets/img/icon_foot_office.png'), defaultImg: require('../../assets/img/icon_foot_office.png'), selectImg: require('../../assets/img/icon_foot_office_in.png'), infoName: '', pointType: 'big', pointNumber: 0, remarks: '' },
          { show: true, id: 'share', title: '共享文件', url: require('../../assets/img/icon_foot_share.png'), defaultImg: require('../../assets/img/icon_foot_share.png'), selectImg: require('../../assets/img/icon_foot_share_in.png'), infoName: '', pointType: 'big', pointNumber: 0, remarks: '' }
        ]
      },
      isSelect: false, // 是否选择操作中
      listSelect: [], // 选中数组
      baseListData: {},
      pageNot: { type: '', url: '', text: '', summary: '' },
      showSkeleton: true,
      openListMore: false,
      showPopup: false,
      showAddFile: false,
      editFile: false,
      editFileImg: require('../../assets/img/fileicon/icon_unknown.png'),
      newFileName: '',
      file: null, // 附件
      hasSelectOk: false,
      isSHowFooter: true,
      showActionSheet: false,
      actiontype: '',
      actionoperateIds: '',
      baseData: '',
      baseType: '',
      baseTitle: '',
      showActionSheetMore: false,
      showActionSheetSend: false,
      actions: [],
      percentage: 0,
      overlayShow: false
    })
    onMounted(() => {
      initCloudDisk()
      getData()
    })
    watch(() => data.isSelect, (newName, oldName) => {
      calculateButtons()
    })
    watch(() => data.listSelect, (newName, oldName) => {
      if (data.isSelect) {
        calculateButtons()
      }
    }, { immediate: true, deep: true })

    // 初始化
    const initCloudDisk = () => {
      // setTimeout(function () {
      //   data.nowStickyH = $api.offset($api.dom('.headerPlaceholder')).h
      // }, 50)
      // 判断是否有处室文件
      // $general.getItemForKey('depart', data.mainBtns.data, 'id').show = data.officeId
      data.nowLevel.push({ key: 'basePage', name: '个人文件', id: 'person', isFolder: true })
    }

    // 获取列表数据
    const getData = async (_type) => {
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      if (lastLevel.oldData && lastLevel.oldData.length) {
        data.showSkeleton = false
        data.listData = lastLevel.oldData
        data.baseListData = JSON.parse(JSON.stringify(data.listData))// 保存一下元始数据
        lastLevel.oldData = []
        sortList()
        return
      }
      if (!_type) {
        data.pageNo = 1
      }
      // var url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/listAllObjects?'
      var url = 'http://localhost:8080/fileStore' + '/' + (data.nowLevel[0].id) + '/listAllObjects?'
      var postParam = {
        search: data.seachText,
        type: ''
      }
      switch (lastLevel.key) {
        case 'basePage':// 在首页
          getUnReadNum()// 获取一下收件箱未读
          if (lastLevel.id === 'share') {
            postParam.folderId = '/'
          }
          url = url + 'folderId=/'
          break
        case 'fileList':// 在详细列表页
          if (data.nowLevel[0].id === 'share') {
            postParam[lastLevel.isFolder ? 'folderId' : 'parentBucketId'] = lastLevel.id
            if (lastLevel.isFolder) {
              url = url + '&folderId=' + lastLevel.id
            } else {
              url = url + '&parentBucketId=' + lastLevel.id
            }
          } else {
            postParam.bucketId = lastLevel.id
            url = url + 'bucketId=' + lastLevel.id
          }
          break
        case 'inboxList':// 收件箱(个人/处室)
          url = sessionStorage.getItem('fileStoreUrl') + '/inbox/listAllInboxs?'
          if (lastLevel.type !== '1') {
            postParam.type = lastLevel.type
            url = url + '&type=2'
          }
          break
        case 'outboxList':// 发件箱
          url = sessionStorage.getItem('fileStoreUrl') + '/inbox/listAllOutboxs?'
          break
        case 'recycleList':// 回收站
          url = sessionStorage.getItem('fileStoreUrl') + '/recycle/listAllFiles?bucketId=' + (lastLevel.id || '')
          postParam.bucketId = lastLevel.id || ''
          break
      }
      const res = await $api.cloudDisk.cloudDiskFileList(url, postParam)
      data.showSkeleton = false
      var code = res ? res.code : ''
      data.pageNot.type = res ? (code === 1 ? 0 : 1) : 1// 类型 列表中只有有网和无网的情况
      data.pageNot.text = res && code !== 1 ? res.message || res.prompt : ''// 只有接口报的异常才改文字
      if (!_type) { // 有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
        data.pageNo = 1
        data.listData = []
      }
      if (code === 1) {
        var attribute = res ? res.attribute || {} : {}
        var showList = []
        var result = $general.isParameters(attribute.result) ? attribute.result : ''
        if ($general.isArray(result)) { // 直接是数组
          showList = [result]
        } else {
          var bucketList = result.bucketList || []// 所有文件夹
          var fileList = result.fileList || []// 所有文件
          var folderList = result.folderList || []// 共享文件夹
          showList = [folderList, bucketList, fileList]
        }
        showList.forEach(function (_nItem) {
          _nItem.forEach(function (_eItem) {
            var baseItem = ''
            if (_eItem.fileObject) {
              baseItem = JSON.parse(JSON.stringify(_eItem))
              _eItem = _eItem.fileObject
              _eItem.nId = _eItem.id// 下载使用本级id
              _eItem.id = baseItem.id// 删除使用父级id
            }
            _eItem.userId = _eItem.userId || ''
            _eItem.userName = _eItem.userName || _eItem.createUser || ''
            _eItem.name = _eItem.name || _eItem.foldName || ''
            _eItem.time = (baseItem ? baseItem.sendDate : '') || _eItem.lastModifyDate || _eItem.modifyDate || ''
            _eItem.isShare = false// 是否已共享
            _eItem.isDownLoad = lastLevel.downLoad || false// 共享盘中是否可以下载
            if (_eItem.shareObjectList && _eItem.shareObjectList.length) {
              _eItem.shareObjectList.forEach(function (_shareItem) {
                if (_shareItem.isShare === 1) {
                  _eItem.isShare = true
                }
                if (lastLevel.isFolder && _shareItem.shareFolderId === lastLevel.id && _shareItem.authority === '1') { // 当前父级是共享文件夹  是当前文件夹判断 是可以下载
                  _eItem.isDownLoad = true
                }
              })
            }
            if (_eItem.bucketId || (_eItem.type !== '文件夹' && !_eItem.foldName)) {
              _eItem.iconInfo = $general.getFileTypeAttr(_eItem.name)
            } else {
              _eItem.iconInfo = { name: 'icon_folder.png', type: 'folder' }// 是文件夹
            }
            data.listData.push(_eItem)
          })
        })
        data.baseListData = JSON.parse(JSON.stringify(data.listData))// 保存一下元始数据
        // console.log(JSON.stringify(data.baseListData));
        sortList()
        data.pageNot.text = !data.listData.length ? '' : '已加载完'//
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        data.finished = true // 没有分页
        // if (data.dataList.length >= total) {
        // }
      }
    }
    // 获取可操作的底部菜单
    const getLlistOperate = () => {
      let showList = []
      if (data.nowLevel.length) {
        var lastLevel = data.nowLevel[data.nowLevel.length - 1]
        if (lastLevel.key === 'basePage') {
          if (lastLevel.id === 'person' || lastLevel.id === 'depart') {
            showList = [downloadBtn, sharedBtn, renameBtn, deleteBtn, sendBtn, moveBtn, copyBtn]
          } else if (lastLevel.id === 'share') {
            showList = [unSharedBtn]
          }
        } else if (lastLevel.key === 'fileList') {
          if (data.nowLevel[0].id === 'person' || data.nowLevel[0].id === 'depart') {
            showList = [downloadBtn, sharedBtn, renameBtn, deleteBtn, sendBtn, moveBtn, copyBtn]
          } else if (data.nowLevel[0].id === 'share') {
            showList = [downloadBtn, unSharedBtn, sendBtn]
          }
        } else if (lastLevel.key === 'inboxList') {
          showList = [downloadBtn, deleteBtn]
        } else if (lastLevel.key === 'outboxList') {
          showList = [deleteBtn]
        } else if (lastLevel.key === 'recycleList') {
          showList = [reductionBtn, thoroughDeleteBtn]
        }
      }
      return showList
    }
    // 切换处室 个人
    const switchInbox = () => {
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      var nowItem = $general.getItemForKey(lastLevel.type === '1' ? '2' : '1', data.moreType.data, 'value')
      data.nowLevel[data.nowLevel.length - 1] = { key: 'inboxList', name: nowItem.text, id: '', type: nowItem.value }
      data.seachText = ''
      setTitle()
      getData()
    }
    // 列表样式切换
    const switchShowType = () => {
      data.showType = (data.showType === 0 ? 1 : 0)
    }
    // 打开pop框 顶部更多回调
    const onPopMoreSelect = (_action) => {
      // console.log(JSON.stringify(_action))
      data.moreType.showPopover = false
      data.nowLevel[data.nowLevel.length - 1].seachText = data.seachText// 先保存一下当前搜索条件
      data.nowLevel[data.nowLevel.length - 1].oldData = JSON.parse(JSON.stringify(data.baseListData))// 保存一下当前列表数据 返回时回显
      data.seachText = ''
      switch (_action.value) {
        case '1':// 收件个人
        case '2':// 收件处室
          data.nowLevel.push({ key: 'inboxList', name: _action.text, id: '', type: _action.value })
          break
        case '3':// 发件
          data.nowLevel.push({ key: 'outboxList', name: _action.text, id: '' })
          break
        case '4':// 回收
          data.nowLevel.push({ key: 'recycleList', name: _action.text, id: '' })
          break
      }
      setTitle()
      getData()
    }
    // 获取收件箱未读
    const getUnReadNum = async () => {
      data.moreType.unreadAll = 0
      const url = sessionStorage.getItem('fileStoreUrl')
      const res = await $api.cloudDisk.cloudDiskInbox(url)
      // const { data: item, errCode } = res
      if (res.code === 1) {
        var result = res.attribute.result
        $general.getItemForKey('1', data.moreType.data, 'value').unread = result['1']
        $general.getItemForKey('2', data.moreType.data, 'value').unread = result['2']
        data.moreType.unreadAll = result['1'] + result['2']
      }
    }

    // 点击排序回调
    const onPopSortSelect = (_action) => {
      data.sortType.value = _action
      sortList()
    }
    // 设置当前页面标题
    const setTitle = (_name) => {
      data.title = _name || (data.nowLevel[data.nowLevel.length - 1].name)
    }
    // 是否显示底部3个切换按钮
    const showFooter = () => {
      var isShow = false
      if (data.nowLevel.length && (data.nowLevel[data.nowLevel.length - 1].key === 'basePage')) { // 首页才显示
        isShow = true
      }
      return !data.isSelect && data.mainBtns.data.length && isShow
    }

    // 是否显示顶部三个点
    const showMore = () => {
      var isShow = false
      if (data.nowLevel.length && (data.nowLevel[data.nowLevel.length - 1].key === 'basePage')) { // 首页才显示
        isShow = true
      }
      return !data.isSelect && isShow
    }

    // 是否显示底部按钮
    const showFooterBtns = (_item) => {
      var isShow = false
      switch (_item.click) {
        case 'add':// 显示新增按钮  首页和普通列表详情页  分享页面不显示新增
          if (data.nowLevel.length && data.nowLevel[0].id !== 'share' && (data.nowLevel[data.nowLevel.length - 1].key === 'basePage' ||
            data.nowLevel[data.nowLevel.length - 1].key === 'fileList')) {
            isShow = true
          }
          if (data.nowLevel.length && data.nowLevel[0].id === 'share' && data.nowLevel[data.nowLevel.length - 1].isFolder) { // 在分享中 是文件夹 就可以有新增
            isShow = true
          }
          break
      }
      return !data.isSelect && isShow
    }
    // 给列表排序
    const sortList = () => {
      // 1 默认接口返回顺序  2所有时间升序  3时间降序
      if (data.sortType.value.value === '1') {
        data.listData = JSON.parse(JSON.stringify(data.baseListData))
      } else if (data.sortType.value.value === '2') {
        data.listData.sort(function (a, b) {
          return dayjs(a.time).valueOf() - dayjs(b.time).valueOf()
        })
      } else if (data.sortType.value.value === '3') {
        data.listData.sort(function (a, b) {
          return dayjs(b.time).valueOf() - dayjs(a.time).valueOf()
        })
      }
    }
    // 单击事件
    const msgImgTap = (_index) => {
      var _item = data.listData[_index]
      setTimeout(function () { // 适配点击“点”操作
        !data.openListMore && openDetails(_item)
      }, 50)
    }
    // 长按事件
    const msgImgPress = (_index) => {
      var _item = data.listData[_index]
      openListMore(_item)
    }
    // 底部切换事件
    const switchFooter = (_index) => {
      data.mainBtns.active = _index
      var footerItems = data.mainBtns.data
      for (var i = 0; i < footerItems.length; i++) {
        if (i === _index) {
          footerItems[i].url = footerItems[i].selectImg
          continue
        }
        footerItems[i].url = footerItems[i].defaultImg
      }
      data.showSkeleton = true
      data.seachText = ''
      data.listData = []
      $general.getItemForKey('basePage', data.nowLevel, 'key').name = data.mainBtns.data[data.mainBtns.active].title
      $general.getItemForKey('basePage', data.nowLevel, 'key').id = data.mainBtns.data[data.mainBtns.active].id
      setTitle()
      getData()
    }

    // 点击点 或长按
    const openListMore = (_item) => {
      if (data.isSelect) return
      data.openListMore = true
      setTimeout(function () {
        data.openListMore = false
      }, 200)
      data.isSelect = true
      data.listSelect.push(_item)
    }
    const calculateButtons = async () => {
      const areaId = JSON.parse(sessionStorage.getItem('areaId'))
      const lastLevel = data.nowLevel[data.nowLevel.length - 1]
      // console.log(getLlistOperate())
      data.isSHowFooter = false
      getLlistOperate().forEach(function (_eItem) {
        switch (_eItem.click) {
          case 'download':
            _eItem.disabled = !data.listSelect.length
            // 如果在共享盘里 选择了不能下载的
            data.nowLevel[0].id === 'share' && data.listSelect.forEach(function (_dItem) {
              if (!_dItem.isDownLoad || _dItem.foldName) { // 不能下载 或者是共享文件夹
                _eItem.disabled = true
              }
            })
            break
          case 'shared':
            _eItem.disabled = !data.listSelect.length
            // 选择的数据 如果有不是自己上传的 就不能共享  因为共享了不能删除
            data.listSelect.forEach(function (_dItem) {
              if (_dItem.userId !== (data.user.id + '_' + areaId)) {
                _eItem.disabled = true
              }
            })
            break
          case 'unshared':
            _eItem.disabled = data.listSelect.length !== 1// 暂时只能单选取消
            // 选择的数据 如果有不是自己上传的 就不能取消共享  因为共享了不能删除
            data.listSelect.forEach(function (_dItem) {
              if (_dItem.userId !== (data.user.id + '_' + areaId)) {
                _eItem.disabled = true
              }
            })
            break
          case 'rename':
            _eItem.disabled = data.listSelect.length !== 1
            break
          case 'delete':
            _eItem.disabled = !data.listSelect.length
            // 选择的数据 如果有不是自己上传的 就不能删除
            data.listSelect.forEach(function (_dItem) {
              if ((_dItem.userId !== (data.user.id + '_' + areaId)) && lastLevel.key !== 'inboxList') { // 收件箱不计算是否自己
                _eItem.disabled = true
              }
            })
            break
          case 'send':
            if (data.nowLevel[0].id === 'share') { // 分享时发送 肯定只能一个 因为只有链接
              _eItem.disabled = data.listSelect.length !== 1
            } else {
              _eItem.disabled = !data.listSelect.length
            }
            // 如果在共享盘里 选择了不能下载的
            data.listSelect.forEach(function (_dItem) {
              if ((data.nowLevel[0].id === 'share' && !_dItem.isDownLoad) || (_dItem.iconInfo && _dItem.iconInfo.type === 'folder')) { // 不能下载 或者是 文件夹
                _eItem.disabled = true
              }
            })
            break
          case 'move':
            _eItem.disabled = !data.listSelect.length
            break
          case 'copy':
            _eItem.disabled = !data.listSelect.length
            // 如果在共享盘里 选择了不能下载的
            data.nowLevel[0].id === 'share' && data.listSelect.forEach(function (_dItem) {
              if (!_dItem.isDownLoad || _dItem.foldName) { // 不能下载 或者是共享文件夹
                _eItem.disabled = true
              }
            })
            break
          case 'reduction':
            _eItem.disabled = data.listSelect.length !== 1
            break
          case 'thoroughDelete':
            _eItem.disabled = !data.listSelect.length
            break
        }
      })
      nextTick(() => {
        data.isSHowFooter = true
      })
    }
    // 关闭页面 放到frame中 配合划动返回做操作
    const close = () => {
      // for (var i = 0; i < T.frames().length; i++) {
      //   if (T.frames()[i].name && T.frames()[i].name.indexOf('up_') === 0) { // 在最前面的
      //     T.sendEvent({ name: T.frames()[i].name, extra: { type: 'close', callback: winActualName } })
      //     return
      //   }
      // }
      if (data.isSelect) { // 有选择 返回先取消
        data.isSelect = false
        data.listSelect = []
      } else if (data.nowLevel.length > 1) {
        data.nowLevel.pop()
        data.seachText = data.nowLevel[data.nowLevel.length - 1].seachText || ''// 返回还原搜索
        setTitle()
        getData()
      }
    }
    // 点击进入详情 是选择还是打开文件夹 预览附件
    const openDetails = (_item) => {
      if (data.isSelect) {
        var nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
        // 在就删除这个
        if (nItem) {
          if (!nItem.notDel) { // 为非不可删除时 才能删除 否则不能动
            $general.delItemForKey(nItem, data.listSelect, 'id')
          }
        } else {
          data.listSelect.push(_item)
        }
        return
      }
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      if (_item.iconInfo.type === 'folder') { // 文件夹
        lastLevel.seachText = data.seachText// 先保存一下当前搜索条件
        lastLevel.oldData = JSON.parse(JSON.stringify(data.baseListData))// 保存一下当前列表数据 返回时回显
        data.seachText = ''
        var nowKey = 'fileList'
        if (lastLevel.key === 'recycleList') {
          nowKey = lastLevel.key
        }
        data.nowLevel.push({ key: nowKey, name: _item.name, id: _item.id, isFolder: !!_item.foldName, downLoad: _item.isDownLoad, lastFolderId: _item.foldName ? _item.id : lastLevel.lastFolderId })
        setTitle()
        onRefresh()
      } else {
        annexClick(_item)
      }
    }
    const annexClick = (item) => {
      var url = sessionStorage.getItem('fileStoreUrl') + '/person/downloadObject2?id=' + (item.id) + '&clientId=' + sessionStorage.getItem('BigDataUser')
      var param = {
        id: item.id,
        url: url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    const itemSelect = (item, index) => {
      console.log(index)
      console.log(item)
      footerBtnClick(item)
    }

    const footerBtnClick = (_item, _other) => {
      // console.log(JSON.stringify(_item))
      const lastLevel = data.nowLevel[data.nowLevel.length - 1]
      switch (_item.click) {
        case 'add':
          var addButtons = [{ icon: '../../assets/img/icon_folder_add.png', name: '新建文件夹', type: 'newFolder' }]
          if (data.nowLevel[0].id !== 'share') {
            addButtons.push({ icon: '../../assets/img/icon_files.png', name: '上传文件', type: 'uploadFiles' })
          }
          break
        case 'select_more':// 点击操作状态下更多
          var nowSelectList = []
          for (var i = 4; i < getLlistOperate().length; i++) {
            nowSelectList.push(getLlistOperate()[i])
          }
          data.actions = nowSelectList
          data.showActionSheetMore = true
          break
        case 'download':
          var downloadName = ''
          var downLoadUrl = ''
          var downLoadIds = ''
          var size = 0
          if (data.listSelect.length === 1 && data.listSelect[0].iconInfo.type !== 'folder') { // 下载只选择一条 并且不是文件夹的时候
            downloadName = $general.getItemForKey(data.listSelect[0].id, data.listData, 'id').name
            downLoadUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/downloadObject?id=' + encodeURIComponent(data.listSelect[0].id)
            size += data.listSelect[0].size
            if (lastLevel.key === 'inboxList') { // 收件箱
              downLoadUrl = sessionStorage.getItem('fileStoreUrl') + '/inbox/downloadInbox?id=' + encodeURIComponent(data.listSelect[0].nId)
            }
          } else {
            downloadName = dayjs().unix() + '.zip'
            data.listSelect.forEach(function (_eItem) {
              downLoadIds += (downLoadIds ? ',,' : '') + (_eItem.nId || _eItem.id)
              size += _eItem.size
            })
            downLoadUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/downloadZip?ids=' + encodeURIComponent(downLoadIds)
            if (lastLevel.key === 'inboxList') { // 收件箱
              downLoadUrl = sessionStorage.getItem('fileStoreUrl') + '/inbox/downloadZip?ids=' + encodeURIComponent(downLoadIds)
            }
          }
          if ($general.isAPICloud()) {
            // openDialogCallback({
            //   onlyName: 'up_openDialogCallback_download',
            //   otherClass: 'msgContentLeft',
            //   title: '确定下载该文件吗？',
            //   // eslint-disable-next-line no-undef
            //   msg: "默认下载位置：\n<span style='color:" + data.appTheme + ";'>" + (api.fsDir) + '/云盘</span>',
            //   buttons: ['确定', '取消'],
            //   type: 'download',
            //   downLoadUrl: downLoadUrl,
            //   downloadName: downloadName
            // }, function (ret, err) {
            //   if (ret.buttonIndex === 1) {
            //     close()
            //   }
            // }, 'prompt')
            Dialog.confirm({
              title: '确定下载该文件吗？',
              // eslint-disable-next-line no-undef
              message: "默认下载位置：\n<span style='color:" + data.appTheme + ";'>" + (api.fsDir) + '/云盘</span>',
              allowHtml: true,
              confirmButtonColor: data.appTheme
            }).then(() => {
              var filePath = downLoadUrl
              var fileName = downloadName // 取到真实文件名
              // eslint-disable-next-line no-undef
              var localName = api.fsDir + '/云盘/' + fileName
              downLoadUrl = ''// 清空链接 防止二次下载
              // eslint-disable-next-line no-undef, camelcase
              var general_fs = api.require('fs')
              if (!general_fs.existSync({ path: localName }).exist) { // 判断是否在手机内存中
                var param = {
                  url: (filePath),
                  encode: false,
                  savePath: localName,
                  report: true,
                  cache: false,
                  allowResume: false,
                  headers: {
                    Authorization: JSON.parse(sessionStorage.token),
                    clientTypeId: sessionStorage.BigDataUser
                  }
                }
                console.log(JSON.stringify(param))
                // eslint-disable-next-line no-undef
                api.download(param, function (ret, err) {
                  console.log(JSON.stringify(ret))
                  data.overlayShow = true
                  data.percentage = 0
                  if (ret) {
                    if (ret.state === 1) {
                      window.isDownload = false
                      data.overlayShow = false
                      data.percentage = '下载完成'
                    } else if (ret.state === 2) {
                      data.overlayShow = false
                      window.isDownload = false
                      data.percentage = '下载失败'
                    } else if (ret.state === 0) {
                      data.percentage = parseInt(ret.percent)
                    }
                  } else {
                    data.overlayShow = false
                    window.isDownload = false
                    data.percentage = '下载失败'
                  }
                })
              } else {
                window.isDownload = false
                data.percentage = '已下载'
                Toast('已下载，很勿重复下载。')
              }
            })
          } else {
            Dialog.confirm({
              title: '下载',
              message: '确定下载该文件吗？',
              confirmButtonColor: data.appTheme
            }).then(async () => {
              // const res = await $api.cloudDisk.download(downLoadUrl, {})
              // console.log(res)
              // const link = document.createElement('a')
              // const blob = new Blob([res], { type: 'application/octet-stream;charset=UTF-8;' }) // 文件流处理
              // // link.style.display = 'none' // 去除a标签的样式
              // // 设置连接
              // link.href = URL.createObjectURL(blob)
              // link.download = downloadName
              // document.body.appendChild(link)
              // // 模拟点击事件
              // link.click()
              // link.remove()
              // console.log(size)
              data.overlayShow = true
              data.percentage = 0
              const oReq = new XMLHttpRequest()
              // const downloadName = downloadName// 获取文件名
              oReq.open('GET', downLoadUrl, true)
              oReq.setRequestHeader('Authorization', JSON.parse(sessionStorage.token))
              oReq.setRequestHeader('clientTypeId', sessionStorage.BigDataUser)
              oReq.setRequestHeader('u-login-areaId', sessionStorage.areaId)
              oReq.responseType = 'blob'
              oReq.onprogress = function (oEvent) {
                // 用于监听下载进度，需要知道文件大小
                var times = window.setTimeout(function () { // 1秒钟之后执行
                  let loadSIze = (oEvent.loaded / size) * 100
                  loadSIze = Number(loadSIze.toFixed(2))
                  console.log('下载进度:' + loadSIze)
                  data.percentage = loadSIze
                  if (oEvent.loaded === size) { // 监听下载进度
                    data.overlayShow = false
                    data.percentage = 100
                    window.clearTimeout(times) // 去除定时器
                  }
                }, 1000)
              }
              oReq.onload = function (oEvent) {
                const content = oReq.response
                const elink = document.createElement('a')
                elink.download = downloadName
                elink.style.display = 'none'
                const blob = new Blob([content])
                elink.href = URL.createObjectURL(blob)
                document.body.appendChild(elink)
                setTimeout(() => {
                  elink.click()
                  document.body.removeChild(elink)
                }, 1000)
              }
              oReq.send()
              close()
              // on confirm
            }).catch(() => {
              // on cancel
            })
          }
          break
        case 'shared':
          var operateIds = ''
          data.listSelect.forEach(function (_eItem) {
            operateIds += (operateIds ? ',,' : '') + _eItem.id
          })
          Dialog.confirm({
            title: '共享',
            message: '共享该文件到共享文件中，所有人可见',
            confirmButtonColor: data.appTheme,
            confirmButtonText: '下一步'
          }).then(() => {
            data.showActionSheet = true
            data.actiontype = 'share'
            data.actionoperateIds = operateIds
            // on confirm
          }).catch(() => {
            // on cancel
          })
          break
        case 'rename':
          var nowItem = $general.getItemForKey(data.listSelect[0].id, data.listData, 'id')
          data.editFile = true
          data.newFileName = nowItem.name.indexOf('.') !== -1 ? (nowItem.name.substring(0, nowItem.name.lastIndexOf('.'))) : nowItem.name
          data.editFileImg = require('../../assets/img/fileicon/' + nowItem.iconInfo.name)
          break
        case 'delete':
          var deleteBucketId = ''
          var deleteFileId = ''// 文件夹、文件id
          // var deleteBucketOK = true
          // var deleteFileOK = true// 是否删除对应成功 都成功才刷新页面
          data.listSelect.forEach(function (_eItem) {
            if (_eItem.iconInfo.type === 'folder') {
              // deleteBucketOK = false
              deleteBucketId += (deleteBucketId ? ',,' : '') + _eItem.id
            } else {
              // deleteFileOK = false
              deleteFileId += (deleteFileId ? ',,' : '') + _eItem.id
            }
          })
          Dialog.confirm({
            // title: '标题',
            message: lastLevel.key === 'inboxList' || lastLevel.key === 'outboxList' ? '确定删除该文件吗？' : '确定删除该文件/文件夹吗？(如已共享，则会同步删除)',
            confirmButtonColor: data.appTheme
          }).then(() => {
            // on confirm
            if (deleteBucketId) {
              deleteBucket(deleteBucketId)
            }
            if (deleteFileId) {
              deleteFile(deleteFileId, lastLevel)
            }
          }).catch(() => {
            // on cancel
          })
          break
        case 'send':
          // eslint-disable-next-line no-redeclare
          var operateIds = ''
          data.listSelect.forEach(function (_eItem) {
            operateIds += (operateIds ? ',,' : '') + _eItem.id
          })
          data.actionoperateIds = operateIds
          data.showActionSheetSend = true
          // T.openDialogCallback({
          //   onlyName: 'up_openDialogCallback_send',
          //   title: 'send',
          //   operateIds: operateIds,
          //   nBaseType: data.nowLevel[0].id
          // }, function (ret, err) {
          //   if (ret.buttonIndex == 1) {
          //     data.getData(0)
          //     data.close()
          //   }
          // }, 'actionSheet_send')
          break
        case 'move':
          // eslint-disable-next-line no-redeclare
          var operateIds = ''
          data.listSelect.forEach(function (_eItem) {
            operateIds += (operateIds ? ',,' : '') + _eItem.id
          })
          data.showActionSheet = true
          data.actiontype = 'move'
          data.actionoperateIds = operateIds
          data.baseType = data.nowLevel[0].id
          data.baseTitle = data.nowLevel[0].name
          // T.openDialogCallback({
          //   onlyName: 'up_openDialogCallback_move',
          //   title: '移动到',
          //   type: 'move',
          //   operateIds: operateIds,
          //   baseType: data.nowLevel[0].id,
          //   baseTitle: data.nowLevel[0].name
          // }, function (ret, err) {
          //   if (ret.buttonIndex == 1) {
          //     setTimeout(() => {
          //       data.fileMove(operateIds, ret)
          //     }, 300)
          //   }
          // }, 'actionSheet_share')
          break
        case 'copy':
          // eslint-disable-next-line no-redeclare
          var operateIds = ''
          data.listSelect.forEach(function (_eItem) {
            operateIds += (operateIds ? ',,' : '') + _eItem.id
          })
          data.showActionSheet = true
          data.actiontype = 'copy'
          data.actionoperateIds = operateIds
          data.baseData = data.mainBtns.data
          // T.openDialogCallback({
          //   onlyName: 'up_openDialogCallback_copy',
          //   title: '复制到',
          //   type: 'copy',
          //   baseData: data.mainBtns.data
          // }, function (ret, err) {
          //   if (ret.buttonIndex == 1) {
          //     setTimeout(() => {
          //       data.fileCopy(operateIds, ret)
          //     }, 300)
          //   }
          // }, 'actionSheet_share')
          break
        case 'unshared':
          // var operateIds = "";
          // data.listSelect.forEach(function (_eItem) {
          // operateIds += (operateIds?",,":"") + _eItem.id;
          // });
          var postUrl = ''
          var postParam = {}
          if (data.listSelect[0].foldName) {
            postUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/removeFolder?'
            postParam.folderId = data.listSelect[0].id
          } else if (data.listSelect[0].iconInfo.type === 'folder') { // 是普通文件夹
            postUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/removeShareBucket?'
            postParam.folderId = lastLevel.lastFolderId
            postParam.bucketId = data.listSelect[0].id
          } else { // 普通文件
            postUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/removeShareObject?'
            postParam.folderId = lastLevel.lastFolderId
            postParam.id = data.listSelect[0].id
          }
          Dialog.confirm({
            message: '确定取消文件/文件夹共享吗？',
            confirmButtonColor: data.appTheme
          }).then(async () => {
            // on confirm
            const ret = await $api.cloudDisk.generalPost(postUrl, postParam)
            if (ret && ret.code === 1) {
              Toast('操作成功')
              getData()
              close()
            } else {
              Toast(ret ? (ret.message || ret.prompt || '操作失败') : '网络错误')
            }
          }).catch(() => {
            // on cancel
          })
          break
        case 'reduction':
          Dialog.confirm({
            message: '确定恢复该文件' + (data.listSelect[0].iconInfo.type === 'folder' ? '夹' : '') + '吗？',
            confirmButtonColor: data.appTheme
          }).then(() => {
            reductionFile()
            // on confirm
          }).catch(() => {
            // on cancel
          })
          break
        case 'thoroughDelete':
          // eslint-disable-next-line no-redeclare
          var operateIds = '';
          (_other ? data.listData : data.listSelect).forEach(function (_eItem) {
            operateIds += (operateIds ? '|' : '') + _eItem.id
          })
          Dialog.confirm({
            message: _other ? '确定全部清除回收站文件吗？' : '确定从回收站清除该文件/文件夹吗？',
            confirmButtonColor: data.appTheme
          }).then(async () => {
            // on confirm
            const ret = await $api.cloudDisk.generalPost(sessionStorage.getItem('fileStoreUrl') + '/recycle/removeAll?', { ids: operateIds })
            if (ret && ret.code === 1) {
              Toast('操作成功')
              getData()
              !_other && close()
            } else {
              Toast(ret ? (ret.message || ret.prompt || '操作失败') : ('网络错误'))
            }
          }).catch(() => {
            // on cancel
          })
          break
      }
    }
    // const openDialogCallback = (_param, callback, _who) => {
    //   // eslint-disable-next-line no-undef
    //   var name = _param.onlyName || ('up_' + (_who || 'dialogcallback') + '_' + (api.frameName ? api.frameName : api.winName))
    //   var o = { onlyCallback: name + '_callback' }
    //   o = $general.setNewJSON(o, _param)
    //   if ($general.isAPICloud()) {
    //     // eslint-disable-next-line no-undef
    //     api.removeEventListener({ name: o.onlyCallback })
    //     $general.addEventListener(o.onlyCallback, function (ret, err) {
    //       if ($general.isFunction(callback)) {
    //         callback(ret.value)
    //       }
    //       // eslint-disable-next-line no-undef
    //       api.removeEventListener({ name: o.onlyCallback })
    //     })
    //     $general.openFrame(name, 'widget://wgt/A00004/html/api_dialog/' + (_who || 'dialogcallback') + '.html', o, false, false, 0, 0)
    //     $general.sendEvent({ name: name, extra: { type: 'open', param: o } })
    //   }
    // }
    const cancelActionSheet = (msg) => {
      console.log(msg)
      if (msg === 'share') {
        closed()
      } else {
        data.showActionSheet = false
      }
    }
    const cancelActionSheetSend = (msg) => {
      data.showActionSheetSend = false
    }
    const closed = () => {
      data.showActionSheet = false
      data.actiontype = ''
      data.actionoperateIds = ''
      close()
    }
    const deleteFile = async (ids, lastLevel) => {
      var postUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/removeObject?'
      var postParam = { id: ids }
      if (lastLevel.key === 'inboxList') {
        postUrl = sessionStorage.getItem('fileStoreUrl') + '/inbox/removeInboxs?'
        postParam.type = lastLevel.type
      } else if (lastLevel.key === 'outboxList') {
        postUrl = sessionStorage.getItem('fileStoreUrl') + '/inbox/removeOutboxs?'
        delete postParam.id
        postParam.ids = ids
      }
      const res = await $api.cloudDisk.generalPost(postUrl, postParam)
      if (res && res.code === 1) {
        Toast('操作成功！')
        data.listSelect = []
        getData()
      } else {
        Toast(res.message || '操作失败！')
      }
    }
    const deleteBucket = async (ids) => {
      var postParam = {
        bucketId: ids
      }
      var postUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/removeBucket?'
      const res = await $api.cloudDisk.generalPost(postUrl, postParam)
      if (res && res.code === 1) {
        Toast('操作成功！')
        data.listSelect = []
        getData()
      } else {
        Toast(res.message || '操作失败！')
      }
    }
    const editBucket = async (_name) => {
      var nowItem = $general.getItemForKey(data.listSelect[0].id, data.listData, 'id')
      var postParam = {
        id: nowItem.id,
        fileName: _name,
        bucketName: _name
      }

      var postUrl = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/' + (nowItem.iconInfo.type === 'folder' ? 'bucketRename' : 'fileRename') + '?'
      const res = await $api.cloudDisk.generalPost(postUrl, postParam)
      if (res && res.code === 1) {
        Toast('操作成功！')
        data.editFile = false
        data.newFileName = ''
        getData()
        close()
      } else {
        Toast('操作失败！')
      }
    }
    // 恢复文件操作
    const reductionFile = async (_item) => {
      var nowItem = data.listSelect[0]
      var postUrl = ''
      var postParam = {
        id: nowItem.id
      }
      if (nowItem.dirType === '处室' || nowItem.dirType === '个人') { // 收件箱-处室 个人
        postUrl = sessionStorage.getItem('fileStoreUrl') + '/inbox/recoverInbox?'
      } else if (nowItem.dirType === (sessionStorage.getItem('BigDataUser') + 'person')) {
        postUrl = sessionStorage.getItem('fileStoreUrl') + '/person/recycle/restoreObject?'
        if (nowItem.iconInfo.type === 'folder') {
          postUrl = sessionStorage.getItem('fileStoreUrl') + '/person/recycle/restoreBucket?'
        }
      } else {
        postUrl = sessionStorage.getItem('fileStoreUrl') + '/depart/recycle/restoreObject?'
        if (nowItem.iconInfo.type === 'folder') {
          postUrl = sessionStorage.getItem('fileStoreUrl') + '/depart/recycle/restoreBucket?'
        }
      }
      console.log('==' + postUrl)
      console.log(postParam)
      const res = await $api.cloudDisk.generalPost(postUrl, postParam)
      if (res) {
        if (res.code === 1) {
          Toast('操作成功')
          getData()
          close()
        } else {
          Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
          // if (res.prompt = ('恢复的位置包含同名文件' + (nowItem.iconInfo.type == 'folder' ? '夹' : ''))) {
          //   T.openDialogCallback({
          //     onlyName: 'up_openDialogCallback_prompt',
          //     title: ret.prompt,
          //     buttons: ['保留两个文件' + (nowItem.iconInfo.type == 'folder' ? '夹' : ''), '替换文件']
          //   }, function (ret, err) {
          //     if (ret.buttonIndex == 1) {
          //       T.openDialogCallback({
          //         onlyName: 'up_openDialogCallback_rename2',
          //         title: '文件' + (nowItem.iconInfo.type == 'folder' ? '夹' : '') + '重命名',
          //         icon: '../../images/fileicon/' + nowItem.iconInfo.name,
          //         seachText: nowItem.name.indexOf('.') != -1 ? (nowItem.name.substring(0, nowItem.name.lastIndexOf('.'))) : nowItem.name,
          //         dotSamename: true
          //       }, function (ret, err) {
          //         if (ret.buttonIndex && ret.text) {
          //           data.reductionFile({ newfileName: ret.text })
          //         }
          //       }, 'actionSheet_input')
          //     } else if (ret.buttonIndex == 2) {
          //       data.reductionFile({ force: 1 })
          //     }
          //   }, 'actionSheet')
          // }
        }
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
      // }, '恢复', 'post', { values: T.setNewJSON(postParam, _item || {}) })
    }
    // 新建文件夹
    const createBucket = async (_name) => {
      // T.showProgress('创建中')
      if (!_name) {
        Toast('请输入文件夹名字！')
        return
      }
      var url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/createBucket?'
      var postParam = {
        bucketName: _name
      }
      if (data.nowLevel[0].id === 'share') {
        url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/createFolder?'
        delete postParam.bucketName
        postParam.folderName = _name
      }
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      switch (lastLevel.key) {
        case 'basePage':// 在首页
          break
        case 'fileList':// 在详细列表页
          if (data.nowLevel[0].id === 'share') {
            postParam.parentId = lastLevel.id
          } else {
            postParam.parentBucketId = lastLevel.id
          }
          break
      }
      const res = await $api.cloudDisk.cloudDiskAddFile(url, postParam)
      // T.hideProgress()
      if (res && res.code === 1) {
        Toast('操作成功')
        getData()
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
      deleteAddFile()
    }
    // 打开附件
    const openFile = () => {
      data.file[0].chooseFile()
    }
    const afterReadFile = async (file) => {
      const formData = new FormData()
      formData.append('attachment', file.file)
      // formData.append('bucketId', '')
      formData.append('force', '')
      var url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/uploadObject?'
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      switch (lastLevel.key) {
        case 'basePage':// 在首页
          break
        case 'fileList':// 在详细列表页
          // formData.bucketId = lastLevel.id
          formData.append('bucketId', lastLevel.id)
          break
      }
      const res = await $api.cloudDisk.cloudDiskUploadFile(url, formData)
      if (res && res.code === 1) {
        Toast('操作成功')
        data.showPopup = false
        getData()
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
    }

    // 共享文件
    const fileShare = async (_item) => {
      var url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/shares?'
      var postParam = {
        id: _item.ids,
        folderName: _item.level.name,
        folderId: _item.level.id,
        authority: _item.download ? 1 : 0
      }
      const res = await $api.cloudDisk.generalPost(url, postParam)
      if (res && res.code === 1) {
        Toast('操作成功')
        getData()
        closed()
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
    }
    // 移动文件
    const fileMove = async (_item) => {
      var url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/move?'
      var baseId = ''
      if (data.nowLevel[0].id === 'depart') {
        baseId = sessionStorage.getItem('fileStoreUrl') + 'department/' + data.user.officeId + '_' + sessionStorage.getItem('areaId')
      } else {
        baseId = sessionStorage.getItem('fileStoreUrl') + data.nowLevel[0].id + '/' + data.user.id + '_' + sessionStorage.getItem('areaId')
      }
      var postParam = {
        id: _item.ids,
        dirId: _item.level.id || baseId
      }
      const res = await $api.cloudDisk.generalPost(url, postParam)
      if (res && res.code === 1) {
        Toast('操作成功')
        getData()
        closed()
        data.showActionSheetMore = false
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
    }
    // 复制文件
    const fileCopy = async (_item) => {
      // T.showProgress('复制中')
      const url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/copyAll?'
      const postParam = {
        ids: _item.ids,
        dirId: _item.level.id || (_item.level.baseId === 'depart' ? 'department' : 'person')
      }
      const res = await $api.cloudDisk.generalPost(url, postParam)
      console.log(res)
      //   T.hideProgress()
      if (res && res.code === 1) {
        Toast('操作成功')
        getData()
        closed()
        data.showActionSheetMore = false
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
    }
    // 设置选中状态
    const isSelectValue = (_item) => {
      return $general.getItemForKey(_item.id, data.listSelect, 'id')
    }
    // 设置不可选的状态
    const isDisabled = (_item) => {
      return $general.getItemForKey(_item.id, data.listSelect, 'id').notDel
    }
    // 是否是取消全选
    const checkAllHasCancel = () => {
      var hasCancel = true
      data.listData.forEach(function (_item, index) {
        var nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
        if (!nItem && !isDisabled(_item)) {
          hasCancel = false
        }
      })
      return hasCancel
    }
    // 点选全选
    const checkAll = () => {
      if (checkAllHasCancel()) {
        // data.listSelect = [];
        data.listData.forEach(function (_item, index) {
          const nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
          if (nItem && !nItem.notDel) { // 为非不可删除时才能删除 否则不能动
            $general.delItemForKey(nItem, data.listSelect, 'id')
          }
        })
      } else {
        data.listData.forEach(function (_item, index) {
          const nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
          if (!nItem) {
            data.listSelect.push(_item)
          }
        })
      }
    }

    const showPopups = (type) => {
      if (type === 1) {
        data.showPopup = true
      } else {
        data.showAddFile = true
        data.showPopup = false
      }
    }
    const deleteAddFile = () => {
      data.showAddFile = false
    }
    const test = (val) => {
      console.log(val)
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }

    return { ...toRefs(data), onRefresh, onLoad, showPopups, closed, itemSelect, cancelActionSheet, cancelActionSheetSend, test, deleteAddFile, openFile, afterReadFile, footerBtnClick, $general, checkAll, confirm, isSelectValue, isDisabled, checkAllHasCancel, switchShowType, fileShare, fileCopy, createBucket, fileMove, reductionFile, openListMore, switchFooter, openDetails, msgImgTap, msgImgPress, close, data, dayjs, getUnReadNum, sortList, getData, calculateButtons, getLlistOperate, showMore, onPopMoreSelect, initCloudDisk, switchInbox, showFooterBtns, showFooter, setTitle, onPopSortSelect, editBucket }
  }
}
</script>
<style lang="less" scoped>
.cloudDisk {
  background-color: #fff;

  .header {
    position: fixed;
    width: 100%;
    z-index: 2;
    height: 40px;

    .new_title {
      font-weight: bold;
    }

    .van-popover__action {
      --van-popover-arrow-size: 25px;

      .redDot_big {
        position: absolute;
        top: 1px;
        right: calc(50% - 23px);
      }

      .van-popover__action .redDot_big {
        right: 5px;
      }
    }
  }

  .search_box {
    padding-top: 42px;
  }

  .switchShow_box {
    border-top: 10px solid #f8f8f8;
    background-color: #fff;
    height: 48px;
  }

  .search_input::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }

  .switchShow_warp {
    color: #666666;
    padding: 10px 15px;
  }

  .sort {
    width: 100px;
    height: 150px;
    font-size: 13px;
    font-family: simplified;
  }

  .clouddisk_icon {
    margin: 16px 15px;
    object-fit: cover;
  }

  .clouddisk_warp {
    padding: 10px 0;
  }

  .clouddisk_more {
    padding: 15px;
  }

  .clouddisk_name {
    color: #333333;
  }

  .clouddisk_add {
    margin-top: 6px;
    color: #999999;
  }

  .clouddisk_add_line {
    margin: 0 10px;
    width: 1px;
    background: #eee;
  }

  .clouddisk_grid_box {
    padding: 14px 2px 0 15px;
  }

  .clouddisk_grid_item {
    margin: 0 10px 14px 0;
    border: 1px solid rgba(153, 153, 153, 0.1);
    border-radius: 4px;
    width: calc(33.33% - 13px);
    height: 130px;
    display: flex;
    flex-direction: column;
  }

  .clouddisk_grid_icon {
    margin: 10px auto;
    object-fit: cover;
  }

  .clouddisk_grid_name {
    color: #333333;
    margin: 0 10px;
    text-align: center;
    // min-height: 250px;
    text-overflow: ellipsis;
    height: 45px;
  }

  .clouddisk_grid_more {
    margin: 5px;
  }

  .footer {
    background-color: #fefefe;
    border-top: 1px solid rgba(234, 234, 234, 0.5);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
  }

  .footer_item {
    width: 100%;
    position: relative;
    min-height: 56px;
    padding: 5px 0;
  }

  .footer_item img {
    margin-bottom: 2px;
  }

  .footer_item_p {
    margin-top: 2px;
  }

  .redDot_small {
    position: absolute;
    top: 5px;
    right: calc(50% - 18px);
  }

  .footer_select {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;
    padding: 0 15px;
  }

  .footer_select_warp {
    background-color: #fff;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08);
    opacity: 1;
    border-radius: 4px;
    padding: 0 10px;
    margin-bottom: 10px;
    justify-content: space-between;
  }

  .footer_select_item {
    padding: 14px 5px;
    // width: 100%;
  }

  .footer_select_text {
    margin-top: 5px;
  }

  .morelist_box {
    background-color: #fff;
    padding: 0.04rem 0;
  }

  .morelist_item {
    padding: 15px;
  }

  .morelist_name {
    margin-left: 10px;
  }

  .footer_btn_box {
    bottom: 130px;
    right: 10px;

    .van-button-box {
      border-radius: 50%;
    }

    .file_options {
      justify-content: space-around;
      height: 100%;

      .cloudDisk_icon_file {
        text-align: center;
      }
    }

    .add_file {
      height: 50px;
      border-bottom: 1px solid #ede8e8;
    }
  }
}
</style>

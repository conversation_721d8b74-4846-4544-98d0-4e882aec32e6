<template>
  <div class="breadCrumbs">
    <div v-for="(item,index) in data"
         :key="item.id"
         @click="itemClick(item,index != data.length-1)">
      <span>{{item.name}}</span>
      <van-icon name="arrow"
                v-if="index != data.length-1" />
    </div>
  </div>
</template>
<script>
import { reactive, toRefs } from 'vue'
export default {
  name: 'breadCrumbs',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props, { emit }) {
    const data = reactive({
    })
    const itemClick = (row, type) => {
      if (type) {
        emit('row-click', row)
      }
    }
    return { ...toRefs(data), itemClick }
  }
}
</script>
<style lang="less">
.breadCrumbs {
  display: flex;
  align-items: center;
  padding: 0 16px;
  div {
    display: flex;
    align-items: center;
    font-size: 13px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 14px;
    color: #999999;
    padding: 8px 0;
    .van-icon {
      font-size: 12px;
      margin: 0 6px;
    }
  }
}
</style>

<template>
  <div class="social">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <div v-if="showSearch"
           id="search"
           class="search_box"
           :style="$general.loadConfiguration()">
        <div class="search_warp flex_box">
          <div @click="search();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="$general.data.appFontSize+'px'"
                      :color="'#666'"
                      :name="'search'"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"
                action="javascript:return true;"> <input id="searchInput"
                   class="flex_placeholder"
                   :style="$general.loadConfiguration(-1)"
                   :placeholder="seachPlaceholder"
                   maxlength="100"
                   type="text"
                   ref="btnSearch"
                   @keyup.enter="search()"
                   v-model="seachText" />
            <div v-if="seachText"
                 @click="seachText='';search();"
                 class="search_btn flex_box flex_align_center flex_justify_content">
              <van-icon :size="$general.data.appFontSize+'px'"
                        :color="'#999'"
                        :name="'clear'"></van-icon>
            </div>
          </form>
          <van-dropdown-menu class="search-dropdown-menu flex_box flex_align_center"
                             :active-color="appTheme"
                             :style="$general.loadConfiguration(-3)">
            <van-dropdown-item title="筛选"
                               ref="filter">
              <template v-for="(item,index) in filters"
                        :key="index">
                <van-cell v-if="item.show"
                          :title="item.title"
                          :style="$general.loadConfiguration()">
                  <template v-slot:right-icon>
                    <!--选择-->
                    <van-dropdown-menu v-if="item.type == 'select'"
                                       :active-color="appTheme">
                      <van-dropdown-item v-model="item.value"
                                         get-container="#search"
                                         :options="item.data"></van-dropdown-item>
                    </van-dropdown-menu>
                    <!--开关-->
                    <van-switch v-else-if="item.type == 'switch'"
                                :active-color="appTheme"
                                v-model="item.value"
                                :size="((appFontSize+8)*0.01)+'rem'"></van-switch>
                    <!--其它只展示文字-->
                    <div v-else
                         :style="loadConfiguration()">{{item.value}}</div>
                  </template>
                </van-cell>
              </template>
              <div class="flex_box">
                <van-button block
                            @click="onReset">重置</van-button>
                <van-button block
                            :color="appTheme"
                            @click="onConfirm">确认</van-button>
              </div>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
      </div>
      <van-tabs v-model:active="switchs.value"
                :color="appTheme"
                swipeable
                sticky
                :offset-top="isShowHead?'46px':'0'"
                :title-active-color="appTheme"
                :ellipsis="false">
        <van-tab v-for="(item,index) in switchs.data"
                 :key="index"
                 :title="item.label"
                 :name="item.value">
          <!--搜索-->
          <van-pull-refresh v-model="refreshing"
                            @refresh="onRefresh">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      offset="52"
                      @load="onLoad">
              <!--数据列表-->
              <ul class="list_box">
                <li class="list_item_box flex_box"
                    v-for="(item) in dataList"
                    :key="item.id"
                    @click="openDetails(item)">
                  <div class="list_item">
                    <div class="list_title"
                         :style="$general.loadConfiguration(1)"
                         v-html="item.title"></div>
                    <div class="bottom_box flex_box">
                      <div class="bottom_left flex_box">
                        <div class="list_name"
                             v-if="item.source"
                             :style="$general.loadConfiguration(-3)">{{item.source}}</div>
                        <div class="list_time"
                             :style="$general.loadConfiguration(-3)">{{item.time}}</div>
                      </div>
                      <div class="bottom_right"
                           v-if="item.type"
                           :style="$general.loadConfiguration(-3)">
                        <van-tag :color="getColor(item.currentProcessStatus)"
                                 :text-color="getTextColor(item.currentProcessStatus)">{{item.currentProcessStatus}}</van-tag>
                      </div>
                    </div>
                  </div>
                  <div class="right_btn"
                       v-if="switchs.value == 'draftBox'">
                    <div class="btn_box"
                         @click="openDetails(item)">编辑</div>
                    <div class="btn_box"
                         style="background: #8C96A2"
                         @click.stop="deleteitem(item)">删除</div>
                  </div>
                </li>
              </ul>

              <!--加载中提示 首次为骨架屏-->
              <div v-if="showSkeleton"
                   class="notText">
                <van-skeleton v-for="(item,index) in 3"
                              :key="index"
                              title
                              :row="3"></van-skeleton>
              </div>
              <template v-else-if="dataList.length == 0">
                <van-empty :style="$general.loadConfiguration(-2)">
                  <!-- <template #description>
                    <div class="van-empty__description_text"
                         :style="$general.loadConfiguration(-1)"
                         v-html="'暂无数据'"></div>
                  </template> -->
                </van-empty>
              </template>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
      <div class="addmsg"
           v-if="user.otherInfo.userOtherInfo.isRepresenter || user.otherInfo.userOtherInfo.isMember"
           @click="newClick"
           :style="'background:'+appTheme">撰写</div>
    </div>
  </div>
  <van-action-sheet v-model:show="show"
                    :actions="actions"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'social',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filter: null,
      filters: [
        { show: true, type: 'select', key: 'years', title: '年份', value: '', defaultValue: '', data: [{ text: '请选择', value: '' }, { text: '2021年', value: '2021' }, { text: '2020年', value: '2020' }, { text: '2019年', value: '2019' }, { text: '2018年', value: '2018' }] },
        { show: true, type: 'select', key: 'firstCategory', title: '类别', value: '', defaultValue: '', data: [{ text: '请选择', value: '' }] }
      ], // 筛选集合
      backgroundcolor: ['#FFE7DC', '#DCEBFF', '#EBF0F6'],
      textcolor: ['#FE7530', '#3088FE', '#9CA6B3'],
      showSearch: true,
      description: '',
      show: false,
      actions: [],
      dialogShow: false,
      dialogTitle: '',
      showKeyboard: true,
      value: '',
      signlength: 4,
      nowItem: '',
      ifInit: false
    })
    onMounted(() => {
      data.switchs.data = [{ label: '所有', value: 'all' }]
      if (data.user.otherInfo.userOtherInfo.isRepresenter || data.user.otherInfo.userOtherInfo.isMember) {
        data.switchs.data = [{ label: '所有', value: 'all' }, { label: '我的', value: 'my' }, { label: '草稿箱', value: 'draftBox' }]
      }
      if (data.title) {
        document.title = data.title
      }
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    watch(() => data.switchs.value, (newName, oldName) => {
      onRefresh()
    })
    const search = () => {
      onRefresh()
    }
    // 初始化
    const initSocial = async () => {
      const res = await $api.activity.treelist({ treeType: 6 })
      data.ifInit = true
      if (res) {
        var datas = res.data || {}
        var firstCategory = $general.getItemForKey('firstCategory', data.filters)
        firstCategory.data = [{ text: '请选择', value: '' }]
        datas.forEach(function (item, index) {
          var param = {}
          param.text = item.label
          param.value = item.id
          firstCategory.data.push(param)
        })
        var years = $general.getItemForKey('years', data.filters)
        var nowYear = new Date().getFullYear()
        years.data = [{ text: '请选择', value: '' }]
        for (var i = 0; i < 4; i++) {
          var itemYear = nowYear - i
          var param = {}
          param.text = itemYear + '年'
          param.value = itemYear
          years.data.push(param)
        }
      }
      onRefresh()
    }
    // 列表请求
    const getList = async () => {
      if (!data.ifInit) {
        initSocial()
        return
      }
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      if (data.pageNo === 1) {
        data.dataList = []
      }
      console.log(data.dataList)
      console.log(data.pageNo)
      const param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText
      }
      if (data.switchs.value === 'my') {
        param.isMine = 1
      } else if (data.switchs.value === 'draftBox') {
        param.processStatus = '20'
        param.isMine = 1
      } else {
        if (data.user.otherInfo.userOtherInfo.isRepresenter === 'true' || data.user.otherInfo.userOtherInfo.isMember === 'true') { // 用户是否代表/委员
          param.processStatus = '120'
        }
      }
      for (var i = 0; i < data.filters.length; i++) {
        var nItem = data.filters[i]
        param[nItem.key] = nItem.value
      }

      const res = await $api.social.getSocialList(param)

      var { data: list, total } = res
      list.forEach(item => {
        const itemData = item
        item.id = itemData.id || ''// id
        item.title = itemData.titile || ''// 标题
        if (data.seachText) {
          item.title = item.title.replace(new RegExp(data.seachText, 'gm'), "<font color='" + data.appTheme + "'>" + data.seachText + '</font>')
        }
        item.content = itemData.content || ''//
        item.source = itemData.firstReflecterName || ''// 姓名
        item.time = dayjs(itemData.createDate).format('YYYY-MM-DD HH:mm:ss') || ''// 时间
        var category = itemData.category || ''
        if (category) {
          category = category.split('-')[0]
        }
        item.type = category || ''// 标签
        item.relateType = data.relateType
        item.currentProcessStatus = itemData.currentProcessStatus || ''// 状态
        data.dataList.push(item)
      })
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }

    const onSelect = (item) => {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      data.show = false
      // var touchItem = data.dataList[data.onTouchIndex]
      switch (item.name) {
        case '':
          break
      }
    }
    const confirm = async () => {
      console.log(data.value)
    }

    const listBtnClick = (_nItem, _item) => {
      if (!_nItem.isClick) return
      switch (_nItem.click) {
        case 'details':
          openDetails(_item)
          break
      }
    }
    const openDetails = rows => {
      console.log(rows)
      if (data.switchs.value === 'draftBox') {
        router.push({ name: 'socialNew', query: { id: rows.id } })
      } else {
        router.push({ path: 'socialDetails', query: { id: rows.id, title: rows.title, currentProcessStatus: rows.currentProcessStatus } })
      }
    }

    const getColor = (type) => {
      var color = data.backgroundcolor[0]
      switch (type) {
        case '完结':
          color = data.backgroundcolor[0]
          break
        case '待接收':
          color = data.backgroundcolor[1]
          break
        case '领导审批':
          color = data.backgroundcolor[1]
          break
        case '处长主任审核':
          color = data.backgroundcolor[0]
          break
        case '拟稿':
          color = data.backgroundcolor[2]
          break
      }
      return color
    }
    const getTextColor = (type) => {
      var color = data.textcolor[0]
      switch (type) {
        case '完结':
          color = data.textcolor[0]
          break
        case '待接收':
          color = data.textcolor[1]
          break
        case '领导审批':
          color = data.textcolor[1]
          break
        case '处长主任审核':
          color = data.textcolor[0]
          break
        case '拟稿':
          color = data.textcolor[2]
          break
      }
      return color
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }

    // 筛选重置事件
    const onReset = () => {
      for (var i = 0; i < data.filters.length; i++) {
        data.filters[i].value = data.filters[i].defaultValue
      }
    }
    // 筛选确定事件
    const onConfirm = () => {
      data.filter.toggle()
      onRefresh()
    }
    // 删除
    const deleteitem = (_item) => {
      Dialog.confirm({
        title: '提示',
        message: '您将删除本条社情民意！'
      })
        .then(async () => {
          // on confirm
          const res = await $api.social.delsSocial({ ids: _item.id })
          if (res) {
            onRefresh()
          }
        })
        .catch(function () {
          // on cancel
        })
    }
    const onClickLeft = () => history.back()

    const newClick = () => {
      router.push({ name: 'socialNew' })
    }

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, search, openDetails, listBtnClick, confirm, onSelect, newClick, getColor, getTextColor, onReset, onConfirm, deleteitem }
  }
}
</script>
<style lang="less" scoped>
.social {
  background: #f8f8f8;
  .van-tabs {
    background: #fff;
  }
  .search_box {
    background: #ffffff;
  }
  .a_box_warp {
    background: #ffffff;
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
    overflow: hidden;
  }
  .a_search_box {
    padding: 14px 15px 0 15px;
  }
  .a_search_select_box {
    padding: 2px 0 2px 10px;
  }
  .a_search_select_text {
    color: #222222;
    font-weight: 500;
    line-height: 1.46;
  }
  .a_search_box form {
    padding: 0 13px;
  }
  .a_search_btn_box {
    padding: 9px 16px;
  }
  .a_search_select_text_icon {
    position: relative;
    margin-left: 6px;
    width: 13px;
  }
  .a_search_select_text_icon::after {
    position: absolute;
    top: 50%;
    margin-top: -5px;
    border: 3px solid;
    border-color: transparent transparent #222 #222;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    opacity: 0.8;
    content: "";
  }
  .a_select_btn_box {
    background: #666666;
    margin-left: 5px;
    font-weight: 500;
    line-height: 1.5;
    color: #ffffff;
    padding: 7px 11px;
    border-radius: 2px;
  }

  .search-dropdown-menu {
    margin-right: 15px;
    padding: 10px 0;
  }
  .van-dropdown-menu.van-hairline--top-bottom::after {
    border-width: 0 0;
  }
  .list_item_box {
    width: calc(100% - 32px);
    left: 0;
    right: 0;
    margin: 10px auto 0;
    background: #ffffff;
    box-shadow: 0px 2px 10px rgba(24, 64, 118, 0.1);
    opacity: 1;
    border-radius: 4px;
  }
  .list_title {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    opacity: 1;
    padding: 10px;
  }
  .bottom_box {
    padding: 0 10px 10px;
    color: #999999;
    width: 100%;
  }
  .bottom_left {
    width: 70%;
  }
  .list_name {
    margin-right: 10px;
  }
  .bottom_right {
    width: 30%;
  }
  .bottom_right .van-tag {
    float: right; /*margin-right:-10px;width:65px;overflow: hidden;white-space:nowrap;*/
  }
  .list_item {
    width: 100%;
  }
  .right_btn {
    width: 60px;
  }
  .btn_box {
    text-align: center;
    background: #3088fe;
    margin-top: 6px;
    margin-right: 10px;
    padding: 5px 0;
    color: #fff;
    font-size: 12px;
    border-radius: 2px;
  }
  .addmsg {
    width: 56px;
    height: 56px;
    text-align: center;
    background: #3088fe;
    box-shadow: 0 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    opacity: 1;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    color: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 50px;
    margin: auto;
  }
  #app .van-dropdown-item {
    margin-top: 10px;
  }
}
</style>

import { tabbarStore } from './modules/tabbar'
import {
  createStore
} from 'vuex'

export default createStore({
  state: {
    speechShow: false,
    speechStauts: false,
    speechContent: '测试',
    allNumber: 0,
    eliminateStatus: false,
    positionObj: {},
    formObj: JSON.parse(sessionStorage.getItem('formObj')) || {},
    searchList: {
      seachStatus: '',
      typeStatus: ''
    }
  },
  mutations: {
    setStatus (state, stauts) {
      state.speechStauts = stauts
    },
    setSpeechContent (state, content) {
      state.speechContent = content
    },
    setSpeechShow (state, show) {
      state.speechShow = show
    },
    setAllNumber (state, num) {
      state.allNumber = num
    },
    setPositionObj (state, obj) {
      state.positionObj = obj
    },
    setForm (state, obj) {
      sessionStorage.setItem('formObj', JSON.stringify(obj))
      state.formObj = JSON.parse(sessionStorage.getItem('formObj'))
    },
    eliminate (state, e) {
      state.eliminateStatus = e
    },
    setsearchList (state, data) {
      state.searchList = data
    }
  },
  actions: {},
  modules: {
    tabbarStore
  }
})

import {
  HTTP
} from '../http.js'
class keyWork extends HTTP {
  general (url, params) {
    return this.request({
      url: url,
      data: params
    })
  }

  // 列表
  keyWorkList (params) {
    return this.request({
      url: '/linkagefocuswork/list',
      data: params
    })
  }

  // 获取类别
  keyWorkTree (params) {
    return this.request({
      url: '/linkagestructure/tree',
      data: params
    })
  }

  // 详情
  keyWorkInfo (params) {
    return this.request({
      url: `/linkagefocuswork/info/${params}`
    })
  }

  // 详情
  keyWorkTrackingList (params) {
    return this.request({
      url: '/linkageworktracking/list',
      data: params
    })
  }

  // 新增
  saveKeyWork (params) {
    return this.request({
      url: '/linkagefocuswork/add',
      data: params
    })
  }
}
export {
  keyWork
}

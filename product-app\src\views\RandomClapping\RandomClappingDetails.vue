<template>
  <div class="RandomClappingDetails">
    <!-- 详情区 -->
    <div class="RandomClappingDetails-detail">
      <div class="details-title">{{ details.title }}</div>
      <div class="details-type"
           v-if="details.messageTypeName">
        <div>{{ details.messageTypeName || '' }}</div>
      </div>
      <div class="details-btn">
        <div class="details-left">
          <img :src="setImg(details.userImage) || defImg"
               alt="">
          <div class="details-name">{{ details.userName }}</div>
        </div>
        <div class="details-time">{{ details.messageDate }}</div>
      </div>
      <div class="details-content"
           v-html="details.messageMessage"></div>
      <div class="imageBox">
        <div class="details-image"
             v-for="item in details.imageVo"
             :key="item.id">
          <van-image width="2rem"
                     height="2rem"
                     fit="cover"
                     @click="previewImg(details.imageVo)"
                     :src="setImg(item.url)" />
        </div>
      </div>
      <div class="details_site">
        <div class="details_site_title"><van-icon name="location-o" /></div>
        <div class="details_site_text"> {{ address }}</div>
      </div>
    </div>

    <!-- 办理进度 -->
    <div class="RandomClappingDetails-schedule RandomClappingDetails-bg">
      <div class="RandomClappingDetails-title">
        <p></p> <span>办理进度</span>
      </div>
      <van-steps direction="vertical"
                 active-color="#3493FF"
                 :active="scheduleData.length">
        <van-step v-for="item in scheduleData"
                  :key="item.id">
          <div class="schedule-box schedule-boxs">
            <div class="schedule-state">{{ item.flowName }}
              <span>{{ ['11', '1', '2', '3', '7', '8'].includes(item.flowId) ? (['11', '1', '3'].includes(item.flowId) ? item.remarks : '至 ' + item.remarks) : '' }}</span>
            </div>
            <div class="schedule-time">{{ item.createDate }}</div>
          </div>
        </van-step>
      </van-steps>
    </div>

    <!-- 回复内容 -->
    <div class="RandomClappingDetails-reply RandomClappingDetails-bg"
         v-if="replyData.length">
      <div class="RandomClappingDetails-title">
        <p></p> <span>回复单位</span>
      </div>
      <div class="replyBox"
           v-for="(item, index) in replyData"
           :key="item.id">
        <div class="reply-name">{{ item.userName }} <span>
            {{ replyData.length > 1 && index == 0 ? '二次办理回复' : '回复' }}</span></div>
        <div class="reply-content"
             v-html="item.messageMessage"></div>
        <div class="imageBox">
          <div class="details-image"
               v-for="it in item.imageVo"
               :key="it.id">
            <van-image width="2rem"
                       height="2rem"
                       fit="cover"
                       @click="previewImg(item.imageVo)"
                       :src="setImg(it.url)" />
          </div>
        </div>
        <div class="result reply-name"
             v-if="item.standbyOne">
          评价结果: <span>{{ item.standbyOne == '1' ? '满意' : item.standbyOne == '2' ? '基本满意' : '不满意' }}</span>
        </div>
      </div>
    </div>

    <!-- 评价 -->
    <div class="RandomClappingDetails-evaluate RandomClappingDetails-bg"
         v-if="details.userId == user.id && replyData.length && !['2', '4', '5', '6'].includes(details.state)">
      <div class="RandomClappingDetails-title">
        <p></p> <span>结果评价</span>
      </div>
      <van-radio-group v-model="evaluateId"
                       shape="dot"
                       :disabled="['4', '5', '6'].includes(details.state)"
                       class="evaluate-radio"
                       direction="horizontal">
        <van-radio v-for="item in evaluateData"
                   :key="item.id"
                   :name="item.id">{{ item.name }}</van-radio>
      </van-radio-group>
      <!--  -->
      <van-button type="primary"
                  size="small"
                  :disabled="['4', '5', '6'].includes(details.state)"
                  class="evaluate-submit"
                  @click="Submit">提交评价</van-button>
    </div>

    <!-- 评论 -->
    <div class="RandomClappingDetails-comment RandomClappingDetails-bg">
      <div class="RandomClappingDetails-title">
        <p></p> <span>评论</span>
        <van-button type="primary"
                    size="small"
                    @click="popupshow = true">添加评论</van-button>
      </div>
      <div class="commentBox">
        <ul>
          <li class="commentBox-li"
              v-for="item in dataList"
              :key="item.id">
            <div class="commentBox-li-btm">
              <div class="commentBox-li-btm-left">
                <img :src="setImg(item.userImage) || defImg"
                     alt="">
              </div>
              <div class="commentBox-li-btm-right">
                <div class="commentBox-li-btm-right-top">
                  <div class="commentBox-li-btm-right-top-name">{{ item.userName }}</div>
                </div>
                <div class="commentBox-li-btm-right-btm">
                  <div class="commentBox-li-btm-right-btm-time">{{ item.messageDate }}</div>
                  <div class="like-and-leave-message">
                    <div class="like"
                         @click.stop="downLike(item)">
                      <van-icon name="good-job-o"
                                :color="item.isLike ? '#1989fa' : ''" /> {{ item.likeCount }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="commentBox-content"
                 v-html="item.messageMessage"></div>
          </li>
        </ul>
      </div>
    </div>
    <van-popup v-model:show="popupshow"
               closeable
               position="bottom"
               :style="{ height: '35%' }">
      <div class="RandomClappingUpload-box">
        <van-cell-group inset>
          <van-field class="newContent"
                     v-model="content"
                     name="content"
                     label="内容"
                     rows="6"
                     maxlength="200"
                     type="textarea"
                     placeholder="请输入不超过200字的问题描述" />
        </van-cell-group>
      </div>
      <!-- <div class="RandomClappingUpload-box">
        <div class="picUploader">
          <div class="picUploader_title">
            上传图片(最多上传{{ Uploadmax }}张)
          </div>
          <div class="imgloager">
            <div class="img_box"
                 v-for="(nItem, nIndex) in attachmentIds"
                 :key="nIndex">
              <van-icon name="clear"
                        @click.stop="$general.delItemForKey(nIndex, attachmentIds)"
                        class="clear" />
              <van-image @click.stop="ImagePreview([nItem.url])"
                         width="2rem"
                         height="2rem"
                         fit="cover"
                         :src="nItem.url" />
            </div>
            <van-uploader :preview-full-image="true"
                          ref="vanUploader"
                          accept="image/*"
                          capture="camera"
                          multiple
                          :disabled="UploadData.length == Uploadmax"
                          :after-read="imgUploader"
                          :max-count="Uploadmax">
            </van-uploader>
            <van-uploader :preview-full-image="true"
                          ref="vanUploader"
                          accept="image/*"
                          multiple
                          :disabled="UploadData.length == Uploadmax"
                          :after-read="imgUploader"
                          :max-count="Uploadmax">
              <div class="photo">
                <van-icon name="photo" />
              </div>
            </van-uploader>
          </div>
        </div>
      </div> -->
      <div class="RandomClappingUpload-submit"
           @click="commentSubmit">提交</div>
    </van-popup>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Uploader, Image as VanImage, Step, Steps, RadioGroup, Radio, Toast, Button, Popup, Dialog } from 'vant'
// import SelectMap from '@/components/SelectMap/index.vue'
export default ({
  name: 'RandomClappingDetails',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    [Step.name]: Step,
    [Steps.name]: Steps,
    [RadioGroup.name]: RadioGroup,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Uploader.name]: Uploader,
    [Radio.name]: Radio
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    // const $general = inject('$general')
    const data = reactive({
      id: route.query.id || '',
      loading: false,
      defImg: require('../../assets/img/icon_def_head_img.png'),
      finished: false,
      refreshing: false,
      popupshow: false,
      pageNo: 1,
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      total: 0,
      Uploadmax: 3,
      content: '',
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      address: '',
      details: {
      },
      dataList: [
      ],
      scheduleData: [
      ],
      replyData: [
      ],
      evaluateId: '1',
      evaluateData: [
        {
          id: '1',
          name: '满意'
        },
        {
          id: '2',
          name: '基本满意'
        },
        {
          id: '3',
          name: '不满意'
        }
      ]
    })
    onMounted(() => { getInfo() })
    const setImg = (url) => {
      if (url && !window.location.origin.includes('localhost') && !window.location.origin.includes('http://**************')) {
        console.log()
        return window.location.origin + '/lzt' + url.split('lzt')[1]
      } else {
        return url
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
    }
    const ImagePreview = (url) => {
      router.push({ path: '/openImg', query: { url: url.map(e => e.url).join(',') } })
    }
    const getInfo = async () => {
      const { data: List } = await $api.RandomClapping.representativemessageGetAppListInfo({
        id: data.id
      })
      const { data: lists } = await $api.RandomClapping.representativemessageInfo(data.id)
      data.details = List
      data.address = lists.address
      if (data.details.imageVo.length) {
        data.details.imageVo.forEach(e => { e.url = setImg(e.fullUrl) })
      }
      data.dataList = data.details.commentList || []
      data.replyData = data.details.replyListVos || []
      data.replyData.forEach(e => {
        e.imageVo.forEach(item => {
          item.url = setImg(item.fullUrl)
        })
      })
      console.log(data.details)
      getFlowsApp()
    }
    const getFlowsApp = async () => {
      const { data: List } = await $api.RandomClapping.representativeflowGetFlowsApp({
        messageId: data.id
      })
      // console.log(List)
      data.scheduleData = List
    }
    const imgUploader = async (file) => {
      console.log(file, 'file')
      console.log(data.images, 'data.images')
      const item = { url: file.content, uploadUrl: '', name: '', uploadId: '', status: 'uploading', module: 'lzbl' }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'lzbl')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.status = 'done'
        item.url = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.status = 'failed'
        item.error = ret.errmsg || ''
      }
      data.attachmentIds.push(item)
    }
    // 提交评价
    const Submit = async () => {
      Dialog.confirm({
        message: '是否确认提交满意度评价',
        confirmButtonColor: '#389eff'
      })
        .then(async () => {
          const res = await $api.RandomClapping.replyEvaluate({
            messageId: data.id,
            areaId: data.areaId,
            status: data.evaluateId,
            remarks: data.evaluateId
          })
          await $api.RandomClapping.representativereplyReplyEvaluateContent({
            replyId: data.replyData[0].id,
            status: data.evaluateId
          })
          if (res.errcode === 200) {
            Toast('评价成功')
            getInfo()
          }
        })
        .catch(() => {
          Toast('取消评价')
        })
    }

    const previewImg = (info) => {
      console.log(info)
      ImagePreview(info)
    }
    // 提交评论
    const commentSubmit = async () => {
      var params = {
        userName: data.user.userName,
        messageDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        messageMessage: data.content,
        image: data.attachmentIds.map(item => item.uploadId).join(','),
        userId: data.user.id,
        messageId: data.id
      }
      const { errcode } = await $api.RandomClapping.representativecommentAdd(params)
      if (errcode === 200) {
        Toast('评论成功')
        getInfo()
        data.popupshow = false
      }
      // console.log(params, 'params')
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? 'representativelike/addLikeApp' : 'representativelike/cancelLike'
      var params = {
        messageId: _id,
        userId: data.user.id,
        areaId: data.areaId
      }
      await $api.general.fabulous({ url, params })
      // onRefresh()
    }
    const downLike = (item) => {
      item.isLike = !item.isLike
      if (item.isLike) {
        item.likeCount++
        fabulousInfo(item.isLike, item.id)
      } else {
        item.likeCount--
        fabulousInfo(item.isLike, item.id)
      }
    }
    return { ...toRefs(data), dayjs, setImg, route, router, $api, onRefresh, Submit, commentSubmit, previewImg, imgUploader, downLike, fabulousInfo }
  }
})
</script>
<style lang='less'>
.RandomClappingDetails {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;

  .RandomClappingUpload-submit {
    position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    width: 90%;
    height: 56px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }

  .RandomClappingUpload-box {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../assets/font/PingFang Medium_downcc.otf");
  }

  .RandomClappingDetails-detail {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 12px;

    .details_site {
      display: flex;
      align-items: center;
      // justify-content: space-between;
      // flex-direction: column;
      padding-top: 10px;
      // border-top: 1px solid #7e7e93;

      >div {
        font-size: 12px;
        color: #7e7e93;
      }

      .details_site_title {
        font-size: 14px;
      }
    }

    .details-type {
      display: flex;
      align-items: center;
      justify-content: end;

      >div {
        font-size: 12px;
        padding: 3px 3px;
        background: #a4adb3;
        color: #Fff;
        border-radius: 2px;
      }
    }

    .details-title {
      font-weight: 600;
      font-size: 19px;
      color: #333333;
      line-height: 26px;
    }

    .details-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 13px;

      .details-left {
        display: flex;
        align-items: center;

        img {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          margin-right: 10px;
        }

        .details-name {
          font-weight: 500;
          font-size: 15px;
          color: #999999;
        }
      }

      .details-time {
        font-size: 14px;
        color: #7e7e7e;
      }
    }

    .details-content {
      margin-top: 20px;
      font-weight: 500;
      font-size: 17px;
      color: #333333;
      line-height: 28px;
      text-indent: 2em;
    }
  }

  .imageBox {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .details-image {
      margin-right: 10px;
    }
  }

  .RandomClappingDetails-bg {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 12px;
  }

  .RandomClappingDetails-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: PingFangSC-Medium;

    >span {
      font-weight: 600;
      font-size: 19px;
      color: #333333;
      flex: 1;
    }

    p {
      width: 2px;
      position: relative;
      height: 16px;
      background: #3894ff;
      margin-right: 8px;
      border-radius: 5px;
    }
  }

  .RandomClappingDetails-schedule {
    .schedule-box {
      width: 100%;
      padding: 14px 10px;
      display: flex;
      // align-items: center;
      justify-content: space-between;
      background: #f4f6f8;
      border-radius: 10px;
      flex-direction: column;
      align-items: left;

      .schedule-state {
        font-weight: 600;
        font-size: 15px;
        color: #000000;
        font-family: PingFangSC-Medium;

        >span {
          // font-size: 12px;
        }
      }

      .schedule-time {
        font-size: 14px;
        margin-top: 8px;
        color: #7e7e7e;
      }
    }

    .van-hairline::after {
      border: none !important;
    }
  }

  .RandomClappingDetails-reply {
    .replyBox {
      margin-top: 12px;
      border-bottom: 1px solid #f1f4f9;

      .reply-name {
        font-weight: 600;
        font-size: 15px;
        color: #000000;
        font-family: PingFangSC-Medium;
        margin-bottom: 5px;

        span {
          font-weight: 500;
          font-size: 15px;
          color: #666;
        }
      }

      .reply-content {
        font-weight: 500;
        font-size: 17px;
        color: #333333;
        line-height: 28px;
        padding: 0px 2px;
        font-family: PingFangSC-Medium;
      }
    }
  }

  .RandomClappingDetails-evaluate {
    .evaluate-radio {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 10px;
    }

    .evaluate-submit {
      width: 100%;
      height: 46px;
      background: #3088fe;
      box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 46px;
      text-align: center;
      color: #ffffff;
      border-radius: 5px;
    }
  }

  .RandomClappingDetails-comment {
    .commentBox {
      background: #fff;

      .commentBox-li {
        padding: 12px 0xp;
        border-bottom: 1px solid #e8e8e8;

        .commentBox-li-top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .commentBox-title {
            width: 70%;
            font-size: 17px;
            color: #333333;
            font-weight: 700;
            font-family: PingFangSC-Medium;
          }

          .commentBox-state {
            width: 56px;
            height: 21px;
            text-align: center;
            line-height: 21px;
            background: linear-gradient(0deg, #3894ff, #38c3ff);
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
          }

          .commentBox-state2 {
            width: 56px;
            height: 21px;
            text-align: center;
            line-height: 21px;
            background: linear-gradient(0deg, #f6a531, #fddf2f);
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
          }
        }

        .commentBox-li-btm {
          display: flex;
          align-items: center;
          margin-top: 8px;
          height: 60px;

          .commentBox-li-btm-left {
            img {
              width: 45px;
              height: 55px;
              border-radius: 5px;
              overflow: hidden;
              margin: 0 5px;
            }
          }

          .commentBox-li-btm-right {
            margin-left: 3px;
            width: 100%;
            height: 100%;

            .commentBox-li-btm-right-top {
              display: flex;
              align-items: center;
              margin-bottom: 10px;
              margin-top: 5px;

              .commentBox-li-btm-right-top-name {
                font-size: 16px;
                color: #7e7e7e;
                margin-right: 10px;
              }

              .commentBox-li-btm-right-top-position {
                font-size: 13px;
                color: #3291ff;
                background: #e9f3ff;
                padding: 3px 6px;
                border-radius: 2px;
              }
            }

            .commentBox-li-btm-right-btm {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .commentBox-li-btm-right-btm-time {
                font-size: 14px;
                color: #7e7e7e;
              }

              .like-and-leave-message {
                display: flex;
                align-items: center;

                .like {
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                  color: #7e7e7e;

                  .van-icon {
                    font-size: 16px;
                  }
                }

                .leave-message {
                  margin-left: 10px;
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                  color: #7e7e7e;

                  .van-icon {
                    font-size: 16px;
                    margin-top: 2px;
                  }
                }
              }
            }
          }
        }

        .commentBox-content {
          font-weight: 500;
          font-size: 17px;
          color: #333333;
          line-height: 28px;
          font-family: PingFangSC-Medium;
          padding: 10px 0px;
        }

        .commentBox-reply {
          width: 100%;
          padding: 8px 12px;
          background: #f4f6f8;
          border-radius: 4px;

          .commentBox-reply-title {
            font-weight: 600;
            font-size: 15px;
            color: #000000;
            font-family: PingFangSC-Medium;
            margin-bottom: 5px;

            span {
              font-weight: 500;
              font-size: 15px;
              color: #666;
            }
          }

          .commentBox-reply-content {
            font-weight: 500;
            font-size: 17px;
            color: #333333;
            line-height: 28px;
            font-family: PingFangSC-Medium;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="suggestSatisfaction">
    <ul class="">
      <li>
        <!--<div class="evaluate_hint_box van-hairline--bottom" :style="$general.loadConfiguration()">沟通情况{{hasSubmit?'（请填写有无和次数）':''}}</div>-->
        <div class="evaluate_item_box van-hairline--bottom"
             :style="$general.loadConfiguration(1)">
          <div class="evaluate_item van-hairline--bottom flex_align_center">
            <div :style="$general.loadConfiguration()"
                 class="evaluate_item_name">办理单位</div>
            <div :style="$general.loadConfiguration(-2)"
                 class="field_bg"
                 v-if="areaId !== '370202' && areaId !== '370203'">
              <div class="field_bg_text"
                   v-if="groupName != '主办：'"
                   v-html="groupName"></div>
              <div class="field_bg_text"
                   v-if="groupName1 != '协办：'"
                   v-html="groupName1"></div>
              <div class="field_bg_text"
                   v-if="groupName2 != '分办：'"
                   v-html="groupName2"></div>
            </div>
            <div :style="$general.loadConfiguration(-2)"
                 class="field_bg"
                 v-if="areaId === '370202' || areaId === '370203'">
              <div class="field_bg_text">{{ groupType }}:{{ groupNames }} </div>
            </div>
          </div>
          <!-- <div class="evaluate_item van-hairline--bottom flex_align_center" >
						<div :style="$general.loadConfiguration()" class="evaluate_item_name">电话/电邮沟通</div>
						<div :style="$general.loadConfiguration(-2)" class="field_bg">
							<van-field v-model="contactPhone" :readonly="!hasSubmit" class="flex_placeholder" clearable input-align="left" maxlength="100" :placeholder="hasSubmit?'请输入':''"></van-field>
						</div>
					</div> -->
          <div v-if="areaId === '370202' || areaId === '370203'">
            <div class="evaluate_item van-hairline--bottom flex_align_center">
              <div class="suggestSatisfactionItemLabelAll "
                   v-if="groupType == '协办' && areaId === '370203'">您对协办单位建议办理情况的评价（单选）</div>
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name"
                   v-else>您对建议办理情况的总体评价(单选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-radio-group v-model="ruleForm.resultEvaluate"
                                 direction="horizontal">
                  <van-radio name="1">满意</van-radio>
                  <van-radio name="2">基本满意</van-radio>
                  <van-radio name="3">不满意</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center"
                 v-if="areaId === '370203' && groupType != '协办'">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">建议办理前是否与您进行了沟通联系（单选）</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-radio-group v-model="ruleForm.beforeCommunicated"
                                 direction="horizontal">
                  <van-radio name="1">曾面对面沟通</van-radio>
                  <van-radio name="2">曾电话沟通</van-radio>
                  <van-radio name="3">未沟通</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center"
                 v-if="areaId === '370202' || (areaId === '370203' && groupType != '协办')">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">除汇报办理方案及答复意见，建议办理期间是否与您进行了其他的沟通联系(单选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-radio-group v-model="ruleForm.otherCommunicated"
                                 direction="horizontal">
                  <van-radio name="1">曾面对面沟通</van-radio>
                  <van-radio name="2">曾电话沟通</van-radio>
                  <van-radio name="3">未沟通</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center"
                 v-if="areaId === '370202' || (areaId === '370203' && groupType != '协办')">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">建议办理答复方式(单选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-radio-group v-model="ruleForm.wayOfReply"
                                 direction="horizontal">
                  <van-radio name="1">面复</van-radio>
                  <van-radio name="2">函复</van-radio>
                  <van-radio name="3">未答复</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center"
                 v-if="areaId !== '370203'">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">办理方案情况(多选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-checkbox-group v-model="ruleForm.projectSituation"
                                    direction="horizontal">
                  <van-checkbox shape="square"
                                name="1">内容充实</van-checkbox>
                  <van-checkbox shape="square"
                                name="2">有针对性</van-checkbox>
                  <van-checkbox shape="square"
                                name="3">内容空洞</van-checkbox>
                  <van-checkbox shape="square"
                                name="4">敷衍了事</van-checkbox>
                </van-checkbox-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">答复意见情况(多选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-checkbox-group v-model="ruleForm.opinionSituation"
                                    direction="horizontal">
                  <van-checkbox shape="square"
                                name="1">内容充实</van-checkbox>
                  <van-checkbox shape="square"
                                name="2">有针对性</van-checkbox>
                  <van-checkbox shape="square"
                                name="3">内容空洞</van-checkbox>
                  <van-checkbox shape="square"
                                name="4">敷衍了事</van-checkbox>
                </van-checkbox-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">建议提出的问题是否落实解决(单选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-radio-group v-model="ruleForm.isSolve"
                                 direction="horizontal">
                  <van-radio name="1">已解决</van-radio>
                  <van-radio name="2">正在解决</van-radio>
                  <van-radio name="3">未解决</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center"
                 v-if="areaId === '370202' || (areaId === '370203' && groupType != '协办')">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">建议落实率(0-100)%</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <input type="number"
                       class="flex_align_center_text"
                       min="0"
                       max="100"
                       v-model="ruleForm.practicableRate"
                       required
                       pattern="[0-9]+"
                       title="请输入0~100之间的数字">
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">建议是否需要续办续复(单选)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-radio-group v-model="ruleForm.isRenew"
                                 direction="horizontal">
                  <van-radio name="1">是</van-radio>
                  <van-radio name="2">否</van-radio>
                </van-radio-group>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom flex_align_center">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">其他建议办理工作的有关意见及要求 (选填)</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <textarea name=""
                          id=""
                          v-model="ruleForm.opinion"
                          cols="30"
                          rows="10"
                          class="flex_align_center_textarea"></textarea>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="evaluate_item van-hairline--bottom">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">信函沟通</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-field v-model="contactLetter"
                           :readonly="!hasSubmit"
                           class="flex_placeholder"
                           clearable
                           input-align="left"
                           maxlength="100"
                           :placeholder="hasSubmit ? '请输入' : ''"></van-field>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">当面沟通</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-field v-model="contactFace"
                           :readonly="!hasSubmit"
                           class="flex_placeholder"
                           clearable
                           input-align="left"
                           maxlength="100"
                           :placeholder="hasSubmit ? '请输入' : ''"></van-field>
              </div>
            </div>
            <div class="evaluate_item van-hairline--bottom">
              <div :style="$general.loadConfiguration()"
                   class="evaluate_item_name">未联系</div>
              <div :style="$general.loadConfiguration(-2)"
                   class="field_bg">
                <van-field v-model="contactNone"
                           :readonly="!hasSubmit"
                           class="flex_placeholder"
                           clearable
                           input-align="left"
                           maxlength="100"
                           :placeholder="hasSubmit ? '请输入' : ''"></van-field>
              </div>
            </div>
          </div>
        </div>
        <div class="evaluate_item_box van-hairline--bottom"
             v-if="areaId !== '370202' && areaId !== '370203'"
             :style="$general.loadConfiguration(1)">
          <div class="evaluate_item van-hairline--bottom">
            <div :style="$general.loadConfiguration()"
                 class="evaluate_item_name">办理态度</div>
            <div class="field_bg">
              <div class="evaluate_item_radio_box flex_placeholder"
                   :style="$general.loadConfiguration(-1)">
                <van-radio-group :disabled="!hasSubmit"
                                 v-model="attitudeScore"
                                 class="flex_box flex_align_center">
                  <van-radio v-for="(item, index) in evaluateresulttype"
                             :key="index"
                             :checked-color="appTheme"
                             class="flex_justify_content"
                             :icon-size="(($general.appFontSize - 1) * 0.01) + 'rem'"
                             :name="index">{{ item }}</van-radio>
                </van-radio-group>
              </div>
            </div>
          </div>
          <!--<div class="evaluate_item van-hairline--bottom flex_box flex_align_center" >
						<div class="evaluate_item_name">办理效率</div>
						<div class="evaluate_item_radio_box flex_placeholder" :style="$general.loadConfiguration(-1)">
							<van-radio-group :disabled="!hasSubmit" v-model="efficiencyScore" class="flex_box flex_align_center">
								<van-radio v-for="(item,index) in scoreData" class="flex_justify_content" :icon-size="(($general.appFontSize-1)*0.01)+'rem'" :name="index">{{item}}</van-radio>
							</van-radio-group>
						</div>
					</div>-->
          <div class="evaluate_item van-hairline--bottom"
               v-if="areaId !== '370202'">
            <div :style="$general.loadConfiguration()"
                 class="evaluate_item_name">办理结果</div>
            <div class="field_bg">
              <div class="evaluate_item_radio_box flex_placeholder"
                   :style="$general.loadConfiguration(-1)">
                <van-radio-group :disabled="!hasSubmit"
                                 v-model="resultScore"
                                 class="flex_box flex_align_center">
                  <van-radio v-for="(item, index) in evaluateresulttype"
                             :key="index"
                             :checked-color="appTheme"
                             class="flex_justify_content"
                             :icon-size="(($general.appFontSize - 1) * 0.01) + 'rem'"
                             :name="index">{{ item }}</van-radio>
                </van-radio-group>
              </div>
            </div>
          </div>
          <!--<div class="evaluate_item flex_box flex_align_center" >
						<div class="evaluate_item_name" :class="hasSubmit?'evaluate_required':''">总体评价</div>
						<div class="evaluate_item_radio_box flex_placeholder" :style="$general.loadConfiguration(-1)">
							<van-radio-group :disabled="!hasSubmit" v-model="overallScore" class="flex_box flex_align_center">
								<van-radio v-for="(item,index) in scoreData" class="flex_justify_content" :icon-size="(($general.appFontSize-1)*0.01)+'rem'" :name="index">{{item}}</van-radio>
							</van-radio-group>
						</div>
					</div>-->
        </div>
      </li>
      <!--<li>
				<div class="evaluate_hint_box van-hairline--bottom" :style="$general.loadConfiguration()">总计</div>
				<div class="evaluate_table_box">
					<div class="evaluate_table" :style="$general.loadConfiguration(1)">
						<div class="evaluate_table_item flex_box flex_align_center">
							<div class="evaluate_table_average">平均分</div>
							<div class="evaluate_table_total">总分</div>
							<div class="evaluate_table_satisfaction">满意度</div>
						</div>
						<div class="evaluate_table_item flex_box flex_align_center">
							<div class="evaluate_table_average1" v-html="getAverage()"></div>
							<div class="evaluate_table_total1" v-html="getTotal()"></div>
							<div class="evaluate_table_satisfaction1" v-html="getSatisfaction()"></div>
						</div>
					</div>
				</div>
			</li>-->
      <li class="evaluate_item_box"
          v-if="areaId !== '370202' && areaId !== '370203'">
        <div class="evaluate_hint_box van-hairline--bottom"
             :style="$general.loadConfiguration()">对建议工作的建议</div>
        <div :style="$general.loadConfiguration(-2)"
             class="field_bg">
          <van-field style="border-radius: 0.04rem;"
                     v-model="advice"
                     :readonly="!hasSubmit"
                     type="textarea"
                     rows="5"
                     maxlength="300"
                     show-word-limit
                     :placeholder="hasSubmit ? '建议内容' : ''"></van-field>
        </div>
      </li>
    </ul>
    <footer class="footer"
            :style="$general.loadConfiguration() + 'padding-bottom:' + ((0 + 16) * 0.01) + 'rem'">
      <div class="flex_align_center_button"
           v-if="areaId === '370202' || areaId === '370203'">
        <button style="background: #00000000;border: 1px solid #A0A0A0;"
                @click="router.go(-1)">取消</button>
        <button style="background: #3088FE; color: #fff;"
                @click="submission">提交</button>
      </div>
      <van-button v-else
                  loading-type="spinner"
                  style="width: 100%;"
                  :loading-size="(($general.appFontSize - 4) * 0.01) + 'rem'"
                  size="large"
                  :disabled="ifDisabled"
                  :color="appTheme"
                  @click="submit"
                  :loading="ifLoading"
                  loading-text="提交中...">{{ '提交' }}</van-button>
    </footer>
  </div>
</template>
<script>
/* eslint-disable */
import { Toast } from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, ref, toRefs, onMounted, nextTick } from 'vue'
import selectUser from './components/selectUser'
import chooseHandleUnit from './components/chooseHandleUnit'
import subjectTerm from './components/subjectTerm'
export default {
  name: 'suggestSatisfaction',
  components: {
    selectUser,
    chooseHandleUnit,
    subjectTerm
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      appTheme: '#3088fe',
      user: JSON.parse(sessionStorage.getItem('user')),
      logo: true,
      areaId: sessionStorage.getItem('areaId') || '',
      ifDisabled: false,// 是否禁用提交
      ifLoading: false,// 是否正在提交
      siteId: route.query.id,
      actionid: '',
      editId: '',
      hasSubmit: false,
      actions: [],
      scoreData: { '很满意': '很满意', '满意': '满意', '基本满意': '基本满意', '不满意': '不满意' },
      evaluateresulttype: {},
      proposalevaluateattitude: {},
      ruleForm: {
        projectSituation: [],
        opinionSituation: [],
        resultEvaluate: '',
        otherCommunicated: '',
        wayOfReply: '',
        isSolve: '',
        isRenew: '',
        practicableRate: '',
        opinion: '',
        beforeCommunicated: ''
      },
      contactPhone: '',
      contactLetter: '',
      contactFace: '',
      contactNone: '',
      attitudeScore: '',
      efficiencyScore: '',
      resultScore: '',
      overallScore: '',
      advice: '',
      groupName: '',
      groupName1: '',
      groupName2: '',
      groupNames: route.query.groupName,
      groupType: route.query.groupType
    })
    const user = ref(null)
    const unit = ref(null)
    const type = ref(null)
    const onSubmit = () => {
      if (data.logo) {
        generalAdd()
      } else {
        generalAdd(1)
      }
    }
    onMounted(() => {
      getpubkvs()
      getDatas(data)
    })
    const submit = async () => {
      if (!data.attitudeScore) {//
        T.toast('请选择办理态度');
        return;
      }
      if (!data.resultScore) {//
        T.toast('请选择办理结果');
        return;
      }
      var postData = {
        dataId: route.query.recordId,
        contactPhone: data.contactPhone || "",
        contactLetter: data.contactLetter || "",
        contactFace: data.contactFace || "",
        contactNone: data.contactNone || "",
        attitude: data.attitudeScore,
        result: data.resultScore,
        advice: data.advice || "",
        doNotCheckAttitude: 1,
      };
      data.ifLoading = true;
      const ret = await $api.general.general('/suggest/saveFlowEvaluate', postData)
      data.ifLoading = false;
      var { errcode, errmsg } = ret
      if (errcode === 200) {
        Toast.success(errmsg)
        router.go(-1)
      }
    }
    const submission = async () => {
      if (data.areaId == '370202' || (data.areaId == '370203' && data.groupType != '协办')) {
        if (data.ruleForm.otherCommunicated == '') {
          T.toast('请选择其他的联系沟通')
          return
        }
        if (data.ruleForm.beforeCommunicated == '' && (data.areaId == '370203' && data.groupType != '协办')) {
          T.toast('请选择建议办理前是否与您进行了沟通联系')
          return
        }
        if (data.ruleForm.opinionSituation.length == 0) {
          T.toast('请至少选择一个答复意见情况')
          return
        }
        if (!/^(100|[1-9]\d|\d)$/.test(data.ruleForm.practicableRate)) {
          T.toast('建议落实率只能是0-100整数')
          return
        }
        if (data.ruleForm.wayOfReply == '') {
          T.toast('请选择答复方式')
          return
        }
      }
      if (data.ruleForm.resultEvaluate == '') {
        T.toast('请选择对建议办理情况的总体评价')
        return
      }

      if (data.ruleForm.isSolve == '') {
        T.toast('请选择是否落实解决')
        return
      }
      if (data.ruleForm.isRenew == '') {
        T.toast('是否需要续办续复')
        return
      }
      // if (data.ruleForm.projectSituation.length==0 ) {
      // 	T.toast('请至少选择一个办理方案情况')
      // 	return
      // }
      var postData = {
        dataId: route.query.recordId,
        resultEvaluate: data.ruleForm.resultEvaluate,
        otherCommunicated: data.ruleForm.otherCommunicated,
        wayOfReply: data.ruleForm.wayOfReply,
        isSolve: data.ruleForm.isSolve,
        isRenew: data.ruleForm.isRenew,
        opinionSituation: data.ruleForm.opinionSituation ? data.ruleForm.opinionSituation.join(',') : '',
        projectSituation: data.ruleForm.projectSituation ? data.ruleForm.projectSituation.join(',') : '',
        practicableRate: data.ruleForm.practicableRate + '',
        opinion: data.ruleForm.opinion ? data.ruleForm.opinion : '',
        groupName: route.query.groupName || '',
        groupType: data.groupType,
        beforeCommunicated: data.ruleForm.beforeCommunicated
      }
      data.ifLoading = true;
      const res = await $api.general.general('/suggest/saveFlowEvaluate', postData)
      data.ifLoading = false;
      var { errcode, errmsg } = res
      if (errcode === 200) {
        Toast.success(errmsg)
        router.go(-1)
      }
    }
    const getDatas = async () => {
      console.log(data)
      data.hasSubmit = false
      var postParam = {
        "dataId": route.query.recordId,
        "groupName": data.areaId === '370202' ? route.query.groupName : '',
        "groupType": data.areaId === '370203' ? route.query.groupType : ''
      };
      const ret = await $api.general.general('/suggest/flowEvaluateDetail', postParam)
      if (!ret) {//是网络错误
      } else {
        var code = ret.errcode || 0;
        var datas = ret.data || {};
        var evaluateDetailVo = datas.lastEvaluate || {};
        var proposalMemberVo = datas.suggestUserVo || {};
        var flowTransactList = datas.flowTransactList || '';
        if (flowTransactList && flowTransactList.length != 0) {
          data.groupName = '主办：';
          data.groupName1 = '协办：';
          data.groupName2 = '分办：';
          flowTransactList.forEach(function (item, index) {
            if (item.transactType == 1) {
              data.groupName += item.groupName
            } else if (item.transactType == 2) {
              data.groupName1 += (index != 1 ? '、' : '') + item.groupName
            } else {
              data.groupName2 += (index != 0 ? '、' : '') + item.groupName
            }
          })
        }
        // var processState = data.processState || '';
        data.editId = evaluateDetailVo.id
        if (datas.id) {
          // var sendUserId = proposalMemberVo.userId || "";//发起人id
          if (data.areaId === '370202' || data.areaId === '370203') {
            data.hasSubmit = false;
            if (evaluateDetailVo.id) {
              data.ruleForm.resultEvaluate = evaluateDetailVo.resultEvaluate
              data.ruleForm.otherCommunicated = evaluateDetailVo.otherCommunicated
              data.ruleForm.wayOfReply = evaluateDetailVo.wayOfReply
              data.ruleForm.isSolve = evaluateDetailVo.isSolve
              data.ruleForm.isRenew = evaluateDetailVo.isRenew
              data.ruleForm.opinionSituation = evaluateDetailVo.opinionSituation ? evaluateDetailVo.opinionSituation.split(',') : ''
              data.ruleForm.projectSituation = evaluateDetailVo.projectSituation ? evaluateDetailVo.projectSituation.split(',') : ''
              data.ruleForm.practicableRate = evaluateDetailVo.practicableRate
              data.ruleForm.opinion = evaluateDetailVo.opinion
              data.ruleForm.beforeCommunicated = evaluateDetailVo.beforeCommunicated
              // console.log(this.ruleForm)
            }
          }
          if (evaluateDetailVo.id) {
            data.contactPhone = evaluateDetailVo.contactPhone || "";
            data.contactLetter = evaluateDetailVo.contactLetter || "";
            data.contactFace = evaluateDetailVo.contactFace || "";
            data.contactNone = evaluateDetailVo.contactNone || "";
            data.attitudeScore = (evaluateDetailVo.attitude || "") + "";
            data.resultScore = (evaluateDetailVo.result || "") + "";
            data.advice = evaluateDetailVo.advice || "";
            data.contactPhone = (data.contactPhone == "empty") ? "" : data.contactPhone;
            data.contactLetter = (data.contactLetter == "empty") ? "" : data.contactLetter;
            data.contactFace = (data.contactFace == "empty") ? "" : data.contactFace;
            data.contactNone = (data.contactNone == "empty") ? "" : data.contactNone;
            data.advice = (data.advice == "empty") ? "" : data.advice;
          }
          // if(userId == sendUserId && (data.processState != "200") ){
          // 	data.hasSubmit = true;
          // 	console.log('市北')
          // }
          console.log(data)
          if (datas.processState != "200") {
            data.hasSubmit = true;
          }
        }
      }
    }
    const getpubkvs = async () => {
      const ret = await $api.general.general('/dictionary/pubkvs', {
        types: 'suggest_evaluate_attitude,suggest_evaluate_result'
      })
      var result_type = ret.data.suggest_evaluate_result || []
      result_type.forEach(function (item, index) {
        data.evaluateresulttype[item.id] = item.value
      })
      var evaluate_attitude = ret.data.suggest_evaluate_attitude || []
      evaluate_attitude.forEach(function (item, index) {
        data.proposalevaluateattitude[item.id] = item.value
      })
    }
    const generalAdd = async (type) => {
      var userData = []
      var hopeGroupIds = []
      var hopeGroupNames = []
      data.userData.forEach(item => {
        userData.push(item.userId)
      })
      data.unitData.forEach(item => {
        hopeGroupIds.push(item.id)
        hopeGroupNames.push(item.value)
      })
      var datas = {
        title: data.form.title,
        content: data.form.content,
        draftsFlag: type,
        sourceType: '2',
        mainSubmitUser: data.user.id,
        ifJointly: data.form.ifJointly ? '1' : '0',
        jointSubmitUserIds: userData.join(','),
        ifInvestigate: data.form.ifInvestigate ? '1' : '0', // 是否调研
        ifAdvicePublic: data.form.ifAdvicePublic ? '1' : '0', // 是否公开
        ifSecret: data.form.ifSecret ? '1' : '0', // 是否涉密
        contactUserName: data.form.contactUserName, // 联系人姓名
        contactUserAddress: data.form.contactUserAddress, // 联系人地址
        contactUserPhone: data.form.contactUserPhone, // 联系人电话
        hopeGroupIds: hopeGroupIds.join(','),
        hopeGroupNames: hopeGroupNames.join(',')
      }
      const res = await $api.general.general('/proposal/add', datas)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        Toast.success(errmsg)
        router.go(-1)
      }
    }
    return { ...toRefs(data), user, unit, type, onSubmit, $general, submission, router, submit }
  }
}
/* eslint-disable */
</script>
<style lang="less">
.suggestSatisfaction {
  width: 100%;
  min-height: 100%;
  background-color: #fff;

  li {
    background: #FFF;
    margin-bottom: 0.1rem;
  }

  .evaluate_hint_box {
    width: 100%;
    height: 0.43rem;
    line-height: 0.48rem;
    color: #333;
    box-sizing: border-box;
  }

  .evaluate_item_box {
    width: calc(100% - 0.3rem);
    left: 0;
    right: 0;
    margin: auto;
    box-sizing: border-box;
  }

  .evaluate_item {
    width: 100%;
  }

  .evaluate_item,
  .evaluate_item_name {
    padding-top: 0.05rem;
  }

  .van-field {
    height: 100%;
  }

  .evaluate_item_radio_box .van-radio {
    width: 100%;
  }

  #app .evaluate_item_radio_box .van-radio__label {
    margin-left: 0.01rem;
  }

  .evaluate_required {
    position: relative;
  }

  .evaluate_required:after {
    content: "*";
    color: red;
    position: absolute;
    top: 0;
    left: -0.09rem;
  }

  .evaluate_table_box {
    padding: 0.11rem;
    box-sizing: border-box;
  }

  .evaluate_table {
    border-radius: 0.05rem;
  }

  .evaluate_table_item {
    width: 100%;
    font-size: inherit;
    font-family: inherit;
  }

  .evaluate_table_average,
  .evaluate_table_total,
  .evaluate_table_satisfaction {
    width: 100%;
    text-align: center;
    font-size: inherit;
    font-family: inherit;
    background: #EEEEEE;
    line-height: 0.3rem;
  }

  .evaluate_table_average {
    border-top-left-radius: 0.05rem;
    border-top: 1px solid #A0A0A0;
    border-left: 1px solid #A0A0A0;
    border-bottom: 1px solid #A0A0A0;
  }

  .evaluate_table_total {
    border-top: 1px solid #A0A0A0;
    border-left: 1px solid #A0A0A0;
    border-bottom: 1px solid #A0A0A0;
  }

  .evaluate_table_satisfaction {
    border-top: 1px solid #A0A0A0;
    border-left: 1px solid #A0A0A0;
    border-right: 1px solid #A0A0A0;
    border-bottom: 1px solid #A0A0A0;
    border-top-right-radius: 0.05rem;
  }

  .evaluate_table_average1,
  .evaluate_table_total1,
  .evaluate_table_satisfaction1 {
    width: 100%;
    text-align: center;
    font-size: inherit;
    font-family: inherit;
    background: #FFF;
    line-height: 0.3rem;
  }

  .evaluate_table_average1 {
    border-bottom-left-radius: 0.05rem;
    border-left: 1px solid #BFBFBF;
    border-bottom: 1px solid #BFBFBF;
  }

  .evaluate_table_total1 {
    border-left: 1px solid #BFBFBF;
    border-bottom: 1px solid #BFBFBF;
  }

  .evaluate_table_satisfaction1 {
    border-left: 1px solid #BFBFBF;
    border-right: 1px solid #BFBFBF;
    border-bottom: 1px solid #BFBFBF;
    border-bottom-right-radius: 0.05rem;
  }

  .field_bg {
    width: 100%;
    left: 0;
    right: 0;
    margin: auto;
    padding: 0.1rem 0;
  }

  .field_bg_text {
    font-size: inherit;
    font-family: inherit;
    font-weight: 400;
    color: #666666;
    opacity: 1;
  }

  #app .van-cell {
    background: #F8F9FA;
    border-radius: 0.02rem;
    height: 0.3rem;
    padding: 0 0.11rem;
  }

  #app .van-field {
    height: auto;
  }

  .custom-button {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    font-size: 14px;
    background: #3088FE;
    color: #fff;
    text-align: center;
    line-height: 25px;
  }

  .flex_align_center_textarea {
    width: 100%;
    background: #fafafa;
    border-radius: 10px;
    padding: 5px;
  }

  .flex_align_center_text {
    width: 100%;
    background: #fff;
    border-radius: 5px;
    height: 40px;
    border: 1px solid #6e6e6e;
    padding: 3px;
  }

  .flex_align_center_button {
    width: 100%;
    height: 60px;
    display: flex;
    padding: 0 20px;
    align-items: center;
    justify-content: space-around;
  }

  .flex_align_center_button>button {
    width: 42%;
    height: 100%;
    border-radius: 10px;
  }

}
</style>

<template>
  <div class="selectUnit">

    <van-collapse v-model="activeNames"
                  accordion
                  style="margin-top: 20px;">
      <van-collapse-item v-for="(item) in listData"
                         :key="item.id"
                         :title="item.label"
                         :name="item.id"
                         :disabled="!item.children.length">
        <van-checkbox-group v-model="chooseUnitItemList"
                            direction="horizontal">
          <van-checkbox class="unit-item-ck"
                        shape="square"
                        v-for="(item2) in item.children"
                        :key="item2.id"
                        :name="item2.id">
            <span class="check-lable"> {{item2.label}}</span>
          </van-checkbox>
        </van-checkbox-group>
      </van-collapse-item>
    </van-collapse>
    <div class="dialog-body">
      <footer v-if="!closeOK"
              :style="{paddingBottom:(safeAreaBottom)+'px'}">
        <div class="flex_box flex_justify_content"
             :style="$general.loadConfiguration()">
          <van-button style="padding: 4px 29px;margin:15px;"
                      :color="appTheme"
                      @click="selectOK()">确定</van-button>
        </div>
      </footer>
    </div>
  </div>

</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, ActionSheet, Overlay, Uploader, ImagePreview, Checkbox, CheckboxGroup, Collapse, CollapseItem, Popup } from 'vant'
import moment from 'moment'
export default {
  name: 'selectUnit',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Popup.name]: Popup,
    [Collapse.name]: Collapse,
    [CollapseItem.name]: CollapseItem,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [Uploader.name]: Uploader,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: [
    'selectUnitParams'
  ],
  setup (props, context) {
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      send: route.query.send || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      show: true,
      params: {},
      seachText: '', // 搜索词
      listData: [

      ],
      select: [
        // { id: 1, name: '超级管理员', url: '../../../images/btn_home_sectors_h.png', notDel: true }
      ],
      selectMax: 0, // 是否有选择限制  为0不限制
      caveat: '', // 提示词
      selectStyle: 0, // 样式  0为主题词样式  1为送交办单位

      closeOK: false, // 是否关闭确定按钮
      activeNames: [],
      chooseUnitItemList: []
    })

    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      const selectUnitParams = props.selectUnitParams
      console.log(selectUnitParams)
      data.params = selectUnitParams.params
      data.caveat = selectUnitParams.caveat
      data.closeOK = true
      data.title = selectUnitParams.title
      data.seachText = ''
      data.seachPlaceholder = '搜索'
      data.select = selectUnitParams.selectData || []// 接收页面数据否则为空
      data.selectMax = selectUnitParams.selectMax || 0// 是否有选择限制  为0不限制
      data.selectStyle = selectUnitParams.selectStyle || 0
      data.showSkeleton = false
      data.chooseUnitItemList = selectUnitParams.chooseUnitItemList
      getTreelist()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    const getTreelist = async () => {
      const res = $api.general.treelist(data.params)
      res.then(ret => {
        console.log(ret)
        data.listData = ret.data
      })
    }

    const search = () => {

    }
    const onRefresh = () => {

    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), moment, $general, search, onClickLeft, onRefresh, onLoad }
  }
}
</script>

<style lang="less" scoped>
html,
body {
  background: rgba(0, 0, 0, 0.25);
}
#app {
  height: 100%;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
}
.selectUnit {
  .dialog-body {
    background-color: #fff;
  }

  .close-unit {
    padding: 20px 20px 0px 20px;
    box-sizing: border-box;
    bottom: 0;
    width: 100%;
    /* position: absolute; */
  }

  .van-collapse-box {
    padding-bottom: 84px;
  }

  .unit-item-ck {
    /* width: 33.33%; */
    width: 50%;
    margin: 0;
    padding: 8px;
    box-sizing: border-box;
  }

  .van-checkbox__label {
    font-size: 18px;
  }

  .check-lable {
    font-size: 16px;
  }

  .dialog-body {
    position: fixed;
    bottom: 50px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0);
  }

  .van-collapse-item__title--disabled,
  .van-collapse-item__title--disabled .van-cell__title span,
  .van-collapse-item__title--disabled .van-cell__right-icon {
    color: #c8c9cc !important;
  }
}
</style>

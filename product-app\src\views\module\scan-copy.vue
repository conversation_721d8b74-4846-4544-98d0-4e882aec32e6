<template>
  <div class="scan">
    <div class="reader-box">
      <div class="reader"
           id="reader"></div>
      <div class="area flex_placeholder">
        <div class="scanArea_prompt">将二维码/条码放入框内，即可自动扫描</div>
      </div>
    </div>
    <footer class="flex_box footer_box">
      <div class="footer_body">
        <svg t="1650876409672"
             class="icon"
             viewBox="0 0 1024 1024"
             version="1.1"
             xmlns="http://www.w3.org/2000/svg"
             p-id="2058">
          <path d="M213.333333 213.333333v128H128V128h213.333333v85.333333h-128z m0 597.333334h128v85.333333H128V682.666667h85.333333v128z m597.333334-597.333334h-128V128h213.333333v213.333333h-85.333333v-128z m0 597.333334v-128h85.333333v213.333333H682.666667v-85.333333h128zM128 469.333333h768v85.333334H128v-85.333334z"
                :fill="appTheme"
                p-id="2059"></path>
        </svg>
        <div :style="'margin-top:0.03rem;font-size:14px;color:' + appTheme">扫码</div>
      </div>
    </footer>
  </div>
</template>
<script>
import { Html5Qrcode } from 'html5-qrcode'
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { Image as VanImage, ImagePreview, Dialog, Toast } from 'vant'
export default {
  name: 'scan',
  components: {
    // Html5Qrcode,
    [Dialog.Component.name]: Dialog.Component,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  setup () {
    const $appTheme = inject('$appTheme')
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      appTheme: $appTheme,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      html5Qrcode: null
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      if (navigator) {
        if (navigator.mediaDevices.getUserMedia || navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia) {
          getUserMedia({ video: true }) // 调用用户媒体设备，访问摄像头、录音
        } else {
          alert('你的浏览器不支持访问用户媒体设备')
        }
      } else {
        alert('正在开发中')
      }
    })
    const getUserMedia = (constrains) => {
      if (navigator.mediaDevices.getUserMedia) {
        // 最新标准API
        navigator.mediaDevices.getUserMedia(constrains).then(stream => { success(stream) }).catch(err => { error(err) })
      } else if (navigator.webkitGetUserMedia) {
        // webkit内核浏览器
        navigator.webkitGetUserMedia(constrains).then(stream => { success(stream) }).catch(err => { error(err) })
      } else if (navigator.mozGetUserMedia) {
        // Firefox浏览器
        navigator.mozGetUserMedia(constrains).then(stream => { success(stream) }).catch(err => { error(err) })
      } else if (navigator.getUserMedia) {
        // 旧版API
        navigator.getUserMedia(constrains).then(stream => { success(stream) }).catch(err => { error(err) })
      }
    }
    // 成功的回调函数
    const success = (stream) => {
      alert('已点击允许,开启成功')
      startScan()
    }
    // 异常的回调函数
    const error = (error) => {
      alert('访问用户媒体设备失败：' + error.name + ', ' + error.message)
    }
    const startScan = () => {
      Html5Qrcode.getCameras().then(devices => {
        if (devices && devices.length) {
          data.html5Qrcode = new Html5Qrcode('reader')
          data.html5Qrcode.start({
            facingMode: 'environment'
          }, {
            fps: 24,
            qrbox: 280
          }, async (decodeText, decodeResult) => {
            if (decodeText) {
              var params = decodeText.split('|')
              switch (params[1]) {
                case 'activitySignIn':
                case 'meetingSignIn':
                  var id = params[2]
                  if (params[1] === 'meetingSignIn') {
                    try {
                      const res = await $api.conferenceActivitiesFile.conferenceAddMySignIn({
                        activityId: id,
                        conferenceId: id,
                        signInType: 'qrCode',
                        userId: data.user.id,
                        dataId: id,
                        type: 'signIn'
                      })
                      if (!res) {
                        Toast('请检查网络!')
                        return
                      }
                      if (res) {
                        var code = res.errcode || 0
                        if (code === 200) {
                          Dialog.alert({
                            title: '提示',
                            message: res.errmsg || res.data,
                            confirmButtonText: '我知道了'
                          }).then(async () => {
                            setTimeout(() => {
                              router.go(-1)
                            }, 1000)
                          }).catch(() => {
                            // on cancel
                          })
                        }
                      }
                    } catch (error) {
                      Dialog.alert({
                        title: '提示',
                        message: error.data.errmsg || error.data.data,
                        confirmButtonText: '我知道了'
                      }).then(async () => {
                        setTimeout(() => {
                          router.go(-1)
                        }, 1000)
                      }).catch(() => {
                        // on cancel
                      })
                      return
                    }
                  } else if (params[1] === 'activitySignIn') {
                    try {
                      const res = await $api.conferenceActivitiesFile.activityaddMySignIn({
                        activityId: id,
                        conferenceId: id,
                        signInType: 'qrCode',
                        userId: data.user.id,
                        dataId: id,
                        type: 'signIn'
                      })
                      if (!res) {
                        Toast('请检查网络!')
                        return
                      }
                      if (res) {
                        var code1 = res.errcode || 0
                        if (code1 === 200) {
                          Dialog.alert({
                            title: '提示',
                            message: res.errmsg || res.data,
                            confirmButtonText: '我知道了'
                          }).then(async () => {
                            setTimeout(() => {
                              router.go(-1)
                            }, 1000)
                          }).catch(() => {
                            // on cancel
                          })
                        }
                      }
                    } catch (error) {
                      Dialog.alert({
                        title: '提示',
                        message: error.data.errmsg || error.data.data,
                        confirmButtonText: '我知道了'
                      }).then(async () => {
                        setTimeout(() => {
                          router.go(-1)
                        }, 1000)
                      }).catch(() => {
                        // on cancel
                      })
                      return
                    }
                  }
                  break
                case 'login':
                  var qrCodeId = params[2]
                  var postParams = {
                    qrCodeId: qrCodeId
                  }
                  console.log(postParams, 'postParams')
                  try {
                    const res3 = await $api.conferenceActivitiesFile.apptoken(postParams)
                    if (!res3) {
                      Toast('请检查网络!')
                      return
                    }
                    if (res3) {
                      var code3 = res3.errcode || 0
                      if (code3 === 200) {
                        Dialog.alert({
                          title: '提示',
                          message: res3.errmsg || res3.data || '登录成功',
                          confirmButtonText: '我知道了'
                        }).then(async () => {
                          setTimeout(() => {
                            router.go(-1)
                          }, 1000)
                        }).catch(() => {
                          // on cancel
                        })
                      }
                    }
                  } catch (error) {
                    Dialog.alert({
                      title: '提示',
                      message: error.data.errmsg || error.data.data,
                      confirmButtonText: '我知道了'
                    }).then(async () => {
                      setTimeout(() => {
                        router.go(-1)
                      }, 1000)
                    }).catch(() => {
                      // on cancel
                    })
                    // return
                  }
                  break
                default:
                  break
              }
              stopScan()
            }
          }, (err) => {
            console.log('err', err)
          })
        }
      })
    }
    // const showPermissionPrompt = () => {
    //   // 弹出提示框或弹出窗口，提醒用户开启摄像头权限
    //   Dialog.alert({
    //     title: '提示',
    //     message: '请允许访问摄像头以继续操作',
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消'
    //   }).then(async () => {
    //     // 用户点击确认，跳转到应用程序权限或隐私设置页面
    //     openAppSettings()
    //   }).catch(() => {
    //     // 用户点击取消，执行其他操作或显示进一步的提示信息
    //     // 在这里处理取消操作
    //   })
    // }
    // const openAppSettings = () => {
    //   alert('1111111111111111111111')
    //   // window.location.href = 'app-settings:'
    //   window.location.href = 'intent:#Intent;action=android.settings.APPLICATION_DETAILS_SETTINGS;data=package:YOUR_PACKAGE_NAME;end;'
    //   // 跳转到应用程序权限或隐私设置页面
    //   // if (typeof cordova !== 'undefined' && cordova.plugins.settings) {
    //   //   cordova.plugins.settings.open('application_details')
    //   // } else if (typeof appAvailability !== 'undefined') {
    //   //   appAvailability.check(
    //   //     'com.android.settings',
    //   //     function () {
    //   //       window.location.href = 'intent:#Intent;action=android.settings.APPLICATION_DETAILS_SETTINGS;data=package:YOUR_PACKAGE_NAME;end;'
    //   //     },
    //   //     function () {
    //   //       console.error('应用程序设置页面不可用')
    //   //     }
    //   //   )
    //   // } else {
    //   //   console.error('无法打开应用程序设置页面')
    //   // }
    // }
    const stopScan = () => {
      data.html5Qrcode.stop()
      setTimeout(() => {
        router.go(-1)
      }, 1000)
    }

    return { ...toRefs(data), dayjs, router, $general }
  }
}
</script>
<style lang="less" scoped>
.scan {
  box-sizing: border-box;
  padding-bottom: 50px;
  .container {
    height: 100%;
  }
  .reader-box {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .reader {
    width: 540rpx;
    height: 540rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .area {
    // background: rgba(0, 0, 0, 0.4);
    position: relative;
    top: 33%;
  }
  .scanArea2_2 {
    width: 2.1rem;
    position: relative;
  }
  .scanArea2_2 img {
    width: 100%;
  }
  .scanArea_prompt {
    padding-top: 20px;
    width: 100%;
    color: #bcbcbc;
    text-align: center;
  }
  .footer_box {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    .footer_body {
      color: #b7b7b7;
      text-align: center;
    }
  }
}
</style>

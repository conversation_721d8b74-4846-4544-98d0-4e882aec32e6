<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="chart-title">
        <div style="margin-right: 6px;">
          <p style="width: 4px;height: 8px;background: #7EDBFB;"></p>
          <p style="width: 4px;height: 8px;background: #2E86EE;"></p>
        </div>
        <span>{{ title }}</span>
      </div>
    </div>
    <v-chart :option="chartOptions"
             autoresize
             class="chart"></v-chart>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { use } from 'echarts/core'
import VChart from 'vue-echarts'
import { PieChart } from 'echarts/charts'
import { TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([PieChart, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer])

export default defineComponent({
  name: 'EChartsDonutComponent',
  components: {
    VChart
  },
  props: {
    title: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      required: true
    }
  },
  setup (props) {
    const chartOptions = ref({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}件 ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: '0%',
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 12,
        textStyle: {
          fontSize: 12,
          color: '#666' // 修改图例文字颜色
        },
        data: props.data.map(item => item.name),
        formatter: function (name) {
          const item = props.data.find(item => item.name === name)
          const percentage = ((item.value / props.data.reduce((a, b) => a + b.value, 0)) * 100).toFixed(1)
          return `${name} ${item.value}件 (${percentage}%)`
        }
      },
      color: ['#1989fa', '#3ba272', '#91cc75', '#ea7ccc', '#fac858', '#ee6666', '#73c0de', ' #5470c6', '#fc8452', '#9a60b4'],
      series: [
        {
          name: '处理情况',
          type: 'pie',
          radius: ['30%', '50%'],
          center: ['50%', '38%'],
          avoidLabelOverlap: false,
          minAngle: 20,
          label: {
            normal: {
              show: true,
              position: 'outside',
              // formatter: '{b}\n {d}%',
              formatter: params => {
                return `{a|${params.name}}\n${params.percent}%`
              },
              textStyle: {
                fontSize: 12
              },
              rich: {
                a: {
                  color: 'gray',
                  lineHeight: 22
                },
                b: {
                  color: 'black',
                  lineHeight: 22,
                  padding: [4, 0, 0, 0]
                }
              }
            }
          },
          labelLine: {
            show: true
            // length: 20,
            // length2: 10,
            // smooth: 0.2
          },
          data: props.data
        }
      ]
    })

    watch(() => props.data, (newData) => {
      chartOptions.value.series[0].data = newData
      chartOptions.value.legend.data = newData.map(item => item.name)
    })

    return {
      chartOptions
    }
  }
})
</script>

<style scoped>
.chart-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 10px;
  font-size: 16px;
  color: #333;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 15px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 0; */
}

.chart-title {
  display: flex;
  align-items: center;
}

.chart-title span {
  font-weight: 600;
  font-size: 16px;
  color: #061a31;
}

.icon-image {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.chart {
  width: 100%;
  height: 420px;
  /* 进一步调整高度以适应内容 */
}
</style>

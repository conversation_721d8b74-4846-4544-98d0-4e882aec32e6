<template>
  <div class="resumptionStudyColumnList">
    <van-pull-refresh v-model="refreshing"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="onLoad"
                :immediate-check="false">
        <van-cell v-for="item in dataList"
                  :key="item.id">
          <!-- 数据列表 -->
          <div class="representative_box_li"
               :key="item.id"
               @click="skipDetails(pageType, item.id, item.externalLinks)">
            <div class="representative_box_right">
              <p class="representative_title">{{ item.title }}</p>
              <p class="representative_time">{{ item.createDate?dayjs(item.createDate).format('YYYY-MM-DD'): '' }}</p>
            </div>
            <div class="representative_box_left"
                 v-if="item.leftImage">
              <img class="representative_img"
                   :src="item.leftImage"
                   alt="">
            </div>
          </div>
        </van-cell>
      </van-list>
    </van-pull-refresh>
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog } from 'vant'
export default {
  name: 'resumptionStudyColumnList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      id: route.query.id || '',
      pageType: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      columnList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      columnList()
    }
    // 跳详情
    const skipDetails = (type, _id, url, isFollow, publishBy, isFabulous, fabulousCount) => {
      if (url != null) {
        window.location.href = url
      } else {
        router.push({ name: 'newsDetails', query: { id: _id, type, isFollow, publishBy, ifIike: isFabulous, fabulousCount: fabulousCount } })
      }
    }
    // 栏目列表
    const columnList = async () => {
      var params = {
        module: 3,
        structureId: data.id,
        pageNo: data.pageNo,
        pageSize: data.pageSize
      }
      var res = await $api.news.getColumnList(params)
      data.dataList = data.dataList.concat(res.data)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    return { ...toRefs(data), $general, dayjs, skipDetails, onLoad, onRefresh }
  }
}
</script>

<style lang="less" scoped>
.resumptionStudyColumnList {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }
  .survey_box2 {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .survey_left2 {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      box-sizing: border-box;
      .survey_title2 {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .survey_time2 {
        font-size: 14px;
      }
    }
    .survey_right2 {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      > img {
        width: 100%;
        height: 80%;
      }
    }
  }
  .survey_box {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .survey_left {
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      box-sizing: border-box;
      .survey_title {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .survey_time {
        font-size: 14px;
      }
    }
    .survey_right {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      > img {
        width: 100%;
        height: 80%;
      }
    }
  }
  .issue {
    width: 50px;
    height: 50px;
    color: #fff;
    text-align: center;
    line-height: 50px;
    background: #3894ff;
    position: fixed;
    border-radius: 50%;
    z-index: 9999;
    bottom: 30px;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .announcement {
    width: 100%;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .announcement_title {
      font-weight: 700;
    }
    .announcement_text {
      color: #666;
      text-align: right;
      font-size: 14px;
    }
  }
  ::v-deep .van-tabs__line {
    background: #000 !important;
  }
  ::v-deep .van-tab__text--ellipsis {
  }
  ::v-deep .van-tab--active {
    color: #000 !important;
  }
  .representative_box_li {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .representative_box_right {
      width: 65%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .representative_title {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .representative_time {
        font-size: 14px;
        color: #a8a8a8;
        margin: 5px 0;
      }
    }
    .representative_box_left {
      width: 35%;
      height: 100%;
      .representative_img {
        width: 90%;
        height: 90%;
        margin: 5px;
      }
    }
  }
  .van-image {
    // margin-bottom: 10px;
    .van-image_img {
      border-radius: 5px !important;
    }
  }
  .situation_li {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .situation_li_title {
      margin: 10px 0 0px 0;
      position: relative;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .situation_li_title_sp {
        position: absolute;
        bottom: 0px;
        right: 10px;
        display: inline-block;
        width: 8px;
        height: 8px;
        background: red;
        border-radius: 50%;
      }
    }
    .situation_li_time {
      font-size: 14px;
      color: #a8a8a8;
      margin: 5px 0;
    }
  }
  .menu {
    background: #fff;
    padding: 8px 0 25px 0;
  }
  .menu_warp {
  }
  .menu .menu_item {
    position: relative;
    width: 25%;
    padding: 10px 0;
  }
  .menu .menu_item p {
    color: #3e3e3e;
    margin-top: 3px;
    text-align: center;
  }
  .menu .menu_item .footer_item_hot {
    position: absolute;
    top: 4px;
    right: 25%;
    width: 10px;
    height: 10px;
    background: #f92323;
    border-radius: 50%;
  }
  .menu .menu_item .footer_item_hot_big {
    position: absolute;
    top: 1px;
    right: 20%;
    width: 20px;
    height: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
  }
}
.representativeCircle_box_li {
  width: 100%;
  padding-bottom: 5px;
  .representativeCircle_box_top {
    width: 100%;
    height: 35px;
    margin: 5px 0;
    display: flex;
    align-items: center;
    position: relative;
    .attention {
      text-align: center;
      position: absolute;
      top: 0;
      right: 10px;
      width: 80px;
      height: 80%;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40px;
      color: #3894ff;
      border: 1px solid #3894ff;
    }
    .attentionDel {
      color: #666;
      border: 1px solid #666;
    }
    .representativeCircle_box_top_headImg {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin: 5px;
    }
    .representativeCircle_box_name {
      font-size: 16px;
      .representativeCircle_box_congressStr {
        font-size: 14px;
        color: #4c4c4c;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
      }
    }
  }
  .representativeCircle_box_center {
    box-sizing: border-box;
    .representativeCircle_box_center_content {
      padding-left: 13px;
      margin: 5px 0;
    }
    .representativeCircle_box_center_attachmentList {
      width: 95%;
      margin: auto;
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-between;
      .van-image {
        margin: 5px;
      }
    }
  }
}
.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    color: #a8a8a8;
  }
  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .representativeCircle_box_buttom_comment {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
    .representativeCircle_box_buttom_like {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
}
.likeComment_box {
  background: #f7f7f7;
  margin: 0 5px 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;
  .comment_box {
    margin: 0 5px 0px;
  }
  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }
  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;
    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;
      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }
      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }
      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}
.T-flex-flow-row-wrap-Liaison {
  width: 100%;
  // margin: 0 auto;
  display: flex;
  align-items: center;
  height: 140px;
  overflow: hidden;
  .T-flex-flow-row-wrap-Liaison-img {
    width: 30%;
    height: 100px;
    border-radius: 10px;
    margin-right: 10px;
  }
  .T-flex-flow-row-wrap-Liaison-right {
    width: 70%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .T-flex-flow-row-wrap-Liaison-title {
      width: 100%;
      margin-bottom: 50px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .T-flex-flow-row-wrap-Liaison-but {
      width: 100%;
      display: flex;
      align-items: center;
      height: 100%;
      justify-content: space-between;
      overflow: hidden;
      .T-flex-flow-row-wrap-Liaison-time {
        color: #a8a8a8;
        font-size: 16px;
        width: 50%;
      }
      .T-flex-flow-row-wrap-Liaison-text {
        font-size: 16px;
        width: 50%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.house_content_bottom {
  width: 100%;
  background: #ffffff;
  padding: 8px 14px;
}
</style>

<template>
  <div class="MessageInfoPage">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="noticeDetailsBox">
        <div class="noticeTitle">{{details.noticeTitle}}</div>
        <div class="noticeInfo">
          <div>{{details.publishDate}}</div>
          <div>{{details.org}}</div>
        </div>
        <div class="noticeContent"
             v-html="details.content"></div>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'MessageInfoPage',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      details: {},
      refreshing: false
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getInfo()
    })
    const onRefresh = () => {
      getInfo()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.filePath,
        name: item.fileName
      }
      router.push({ name: 'superFile', query: param })
    }
    // 列表请求
    const getInfo = async () => {
      const res = await $api.notice.noticeInfo(data.id)
      var { data: details } = res
      console.log('details==>', details)
      data.details = details
      data.refreshing = false
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, annexClick }
  }
}
</script>
<style lang="less">
.MessageInfoPage {
  width: 100%;
  min-height: 100%;
  background: #eee;
  // background: #f8f8f8;
  .noticeDetailsBox {
    width: 100%;
    padding: 16px;
    background-color: #fff;
    margin-bottom: 10px;
    .noticeTitle {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .noticeInfo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 0;
      div {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #999999;
      }
    }
    .noticeContent {
      width: 100%;
      img {
        width: 100%;
      }
    }
  }
  .readUserBox {
    padding: 16px;
    padding-bottom: 52px;
    background-color: #fff;
    .number {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 20px;
      color: #333333;
      position: relative;
      padding-left: 6px;
      margin-bottom: 6px;
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #3088fe;
        opacity: 1;
        border-radius: 10px;
      }
    }
    .readUser {
      overflow: hidden;
      span {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 20px;
        color: #666666;
      }
    }
    .viewMore {
      font-size: 10px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 14px;
      color: #3088fe;
      text-align: right;
    }
  }
  .receipt {
    position: fixed;
    top: 68%;
    right: 16px;
    width: 44px;
    height: 44px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 44px;
    color: #ffffff;
    text-align: center;
  }
  .van-popup {
    .van-icon-cross {
      font-size: 16px;
    }
    .noticeTitlePopup {
      height: 44px;
      line-height: 40px;
      padding-top: 4px;
      text-align: center;
      position: relative;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 600;
      color: #333333;
      &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 343px;
        height: 0px;
        border-bottom: 1px solid #999999;
        opacity: 0.2;
      }
    }
    .van-field__body {
      font-size: 14px;
    }
    .van-radio-group {
      padding: 16px;
      .van-radio {
        padding: 6px;
      }
      .van-radio__label {
        font-size: 14px;
      }
    }
    .van-checkbox-group {
      padding: 16px;
      .van-checkbox {
        padding: 6px;
      }
      .van-checkbox__label {
        font-size: 14px;
      }
    }
    .noticeButton {
      width: 100%;
      padding-top: 52px;
      padding-bottom: 82px;
      display: flex;
      justify-content: center;
      .van-button {
        width: 82px;
      }
      .van-button__text {
        font-size: 14px;
      }
    }
  }
}
</style>

<template>
  <div class="leavingMessage">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">
      </van-nav-bar>
    </van-sticky>
    <div :style="'height: 180px;background-image:url('+userInfo.url+');filter: blur(2px);background-repeat: no-repeat;background-size: cover;background-position:center'"></div>
    <div class="msg_body flex_box">
      <div class="msg_body_bg">
        <van-image @click="previewImg(userInfo)"
                   :style="'width:70px;height:70px;'"
                   class="user_img"
                   round
                   fit="contain"
                   :src="userInfo.url"></van-image>
        <div class="user_name">{{userInfo.name}}</div>
      </div>
      <div class="flex_placeholder"></div>
    </div>
    <div style="margin-top: 10px;">
      <div :style="'font-size:15px;'">
        <van-cell v-for="(item,index) in items"
                  :key="index"
                  :title="item.label"
                  :value="item.value"></van-cell>
      </div>
    </div>
    <div class="submit-content">
      <h3>给代表留言</h3>
      <van-form @submit="onSubmit"   ref="replyform">
        <van-cell-group inset>
          <van-field v-model="form.title"
                    required
                    name="title"
                    show-word-limit
                    label-width="4.8em"
                    label="标题"
                    placeholder="请输入标题"
                    :rules="rules.title" />
          <van-field class="newContent"
                   v-model="form.content"
                   required
                   name="content"
                   label="内容"
                   rows="6"
                   show-word-limit
                   type="textarea"
                   placeholder="请输入内容"
                   :rules="rules.content" />
          <van-field v-model="form.userName"
                    required
                    name="userName"
                    show-word-limit
                    label-width="4.8em"
                    label="姓名"
                    placeholder="请输入姓名"
                    :rules="rules.userName" />
          <van-field v-model="form.phone"
                    required
                    name="phone"
                    show-word-limit
                    label-width="4.8em"
                    label="电话"
                    placeholder="请输入电话"
                    :rules="rules.phone" />
          <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   label="是否公开">
          <template #input>
            <van-switch v-model="form.publicWill"
                        size="20" />
          </template>
        </van-field>
        </van-cell-group>
        <div class="newButton">
          <van-button type="primary"
                      @click="submitClick"
                      :color="appTheme"
                      native-type="submit">提交留言</van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject, ref } from 'vue'
import { Toast, NavBar, Sticky, CellGroup, Button, Field, Popup, Image as VanImage, Divider, ActionSheet, Grid, GridItem, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
export default {
  name: 'leavingMessage',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Button.name]: Button,
    [CellGroup.name]: CellGroup,
    [Popup.name]: Popup,
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Divider.name]: Divider,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Field.name]: Field
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const $general = inject('$general')
    const route = useRoute()
    const replyform = ref(null)
    const data = reactive({
      // eslint-disable-next-line eqeqeq
      title: route.query.title || '用户详情',
      id: route.query.id,
      user: JSON.parse(sessionStorage.getItem('user')),
      conversationType: Number(route.query.conversationType),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      userInfo: { url: '../../../images/icon_default_user.png', name: '', id: 0 },

      items: [
        { label: '职务', key: 'position', value: '' },
        // { label: '联系方式', key: 'mobile', value: '' },
        { label: '邮箱', key: 'email', value: '' }
      ],

      imgBox: { name: '委员说', data: [] },
      isAttention: false,
      hasShow: false, // 是否显示委员说关注
      type: route.query.type,
      form: {
        title: '',
        content: '',
        phone: '',
        userName: '',
        publicWill: true
      },
      rules: {
        title: [{ required: true, message: '请输入标题' }],
        content: [{ required: true, message: '请输入内容' }],
        phone: [{ required: true, message: '请输入电话号码' }],
        userName: [{ required: true, message: '请输入姓名' }]
      }

    })
    onMounted(() => {
      getMemberList()
    })

    const getMemberList = async () => {
      var { data: info } = await $api.rongCloud.getUserInfo({
        id: data.id
      })
      data.userInfo.id = info.id
      data.userInfo.name = info.userName || ''
      data.title = info.userName || ''
      data.userInfo.url = info.fullImgUrl || ''
      for (var i = 0; i < data.items.length; i++) {
        data.items[i].value = info[data.items[i].key] || ''
      }
    }
    // 是否有搜索对象
    const hasSeach = () => {
      var hasSeach = false
      data.members.data.forEach(function (_eItem, _eIndex, _eArr) {
        // eslint-disable-next-line eqeqeq
        if (_eItem.name.indexOf(data.seachText) != -1) {
          hasSeach = true
        }
      })
      return hasSeach
    }
    const previewImg = (info) => {
      ImagePreview([info.url])
    }
    const onClickLeft = () => history.back()
    const onSubmit = () => {

    }
    const submitClick = async (type) => {
      var datas = {
        title: data.form.title,
        content: data.form.content,
        phone: data.form.phone,
        userName: data.form.userName,
        publicWill: data.form.publicWill ? '1' : '0' // 是否公开
      }
      const res = await $api.leavingMessage.writeLetterToRepresentative(datas)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        Toast.success(errmsg)
        replyform.value.resetFields()
      }
    }
    return { ...toRefs(data), onClickLeft, hasSeach, previewImg, onSubmit, submitClick }
  }
}
</script>
<style lang="less">
.leavingMessage {
  width: 100%;
  .msg_body {
    background: #fff;
    min-height: 90px;
    padding: 17px 14px;
    position: relative;
  }
  .msg_body_bg {
    position: absolute;
    top: -26px;
    left: 15px;
  }
  .user_img {
    background: #fff;
  }
  .user_name {
    margin-top: 5px;
    text-align: center;
    color: #333;
    font-weight: bold;
  }
  #app .msg_body .van-button {
    padding: 5px 15px;
    height: inherit;
    border-radius: 13px;
  }
  #app .van-cell {
    padding: 17px 14px;
    color: #333;
    font-weight: 600;
  }
  #app .van-cell__value {
    color: #666;
    font-weight: 600;
  }
  .submit-content {
    background: #fff;
    margin-top: 20px;
    h3 {
      padding: 10px;
    }
    .van-form {
      width: 100%;
      .newContent {
        flex-wrap: wrap;
        .van-cell__title {
          width: 100%;
          margin-bottom: 6px;
        }
        .van-field__body {
          background-color: #e8e8e8;
          padding: 6px 12px;
        }
      }
    }
    .newButton {
      display: flex;
      justify-content: space-around;
      padding: 36px 18px;
      padding-bottom: 88px;
      .van-button {
        width: 128px;
        height: 36px;
      }
    }
  }
}
</style>

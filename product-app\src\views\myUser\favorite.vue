<template>
  <div class="favorite">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <van-search v-model="keyword"
                  @search="search"
                  @clear="search"
                  placeholder="请输入搜索关键词" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <ul class="vue_newslist_box">
          <van-checkbox-group v-model="nowSelect">
            <div v-for="(item,index) in listData"
                 :key="index"
                 class="list_box van-hairline--bottom">
              <van-cell clickable
                        @click="openDetails(item)">
                <div class="flex_box flex_align_center">
                  <div :style="$general.loadConfiguration(-3)">
                    <van-tag plain
                             class="flex_box flex_align_center flex_justify_content"
                             :color="appTheme">{{item.areaName?item.areaName:''}}{{item.type}}</van-tag>
                  </div>
                  <div class="flex_placeholder"></div>
                  <div :style="$general.loadConfiguration(-3)+'color:#777;margin-right:0.1rem;'">{{dayjs(item.time).format('YYYY-MM-DD')}}</div>
                  <div @click.stop
                       class="flex_box flex_align_center flex_justify_content"
                       :style="$general.loadConfigurationSize(4)">
                    <van-checkbox v-if="bulkOperations || item.select"
                                  :name="item.baseId"
                                  ref="checkboxs"
                                  :checked-color="appTheme"
                                  :icon-size="((1+3)*0.01)+'rem'"></van-checkbox>
                    <van-icon v-else
                              @click="openMenu(item)"
                              :color="'#666'"
                              :size="((1+3)*0.01)+'rem'"
                              name="ellipsis"></van-icon>
                  </div>
                </div>
                <div class="text_two"
                     :style="$general.loadConfiguration(-1)+'margin-top:0.1rem;'">
                  <span v-if="item.isTop == '1'"
                        class="vue_newslist_top"
                        :style="$general.loadConfiguration(-4)">
                    <van-tag plain
                             :color="appTheme">置顶</van-tag>
                  </span>
                  <span class="inherit"
                        v-html="item.title"></span>
                </div>
              </van-cell>
            </div>
          </van-checkbox-group>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>

import { useRoute, useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, Grid, GridItem, Image as VanImage, ActionSheet, Tag, NavBar, Sticky } from 'vant'
export default {
  name: 'favorite',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Grid.name]: Grid,
    [Tag.name]: Tag,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const dayjs = require('dayjs')
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      appTheme: $appTheme,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id || JSON.parse(sessionStorage.getItem('user').id) || '',
      pageNo: 1,
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      listData: [],
      bulkOperations: false,
      nowSelect: []
    })
    onMounted(() => {
      // onRefresh()
    })
    const onRefresh = () => {
      console.log('下拉刷新了')
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.loading = true
      getList()
    }
    // 搜索
    const search = () => {
      onRefresh()
    }
    // 获取列表
    const getList = async () => {
      var res = await $api.general.favoriteList({
        pageNo: data.pageNo,
        pageSize: 1000,
        keyword: data.keyword,
        createBy: data.id,
        module: '1'
      })
      var { data: list } = res
      console.log('list===>', list)
      window.listData = JSON.parse(JSON.stringify(list))
      list.forEach(item => {
        item.id = item.keyId || ''
        item.baseId = item.id || ''
        item.source = item.org || ''
        item.time = item.createDate || ''
        item.relateType = item.type || ''
        item.type = item.typeName || ''
        item.areaName = item.areaName || ''
        item.select = ($general.getItemForKey(item.baseId, window.listData, 'baseId') || { select: false }).select
      })
      data.listData = data.listData.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
      // if (data.listData.length >= total) {
      //   data.finished = true
      // }
    }
    // 进详情
    const openDetails = (row) => {
      if (row.relateType === '53') {
        router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title, type: 'favorite53' } })
      } else {
        router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title, type: 'favorite5' } })
      }
      // if (data.active === '0') {
      //   router.push({ name: 'meetingDetailFile', query: { id: row.id } })
      // } else {
      //   router.push({ name: 'activitesDetailFile', query: { id: row.id } })
      // }
    }
    return { ...toRefs(data), $general, search, dayjs, onLoad, onRefresh, openDetails }
  }
}
</script>
<style lang="less">
.favorite {
  width: 100%;
  padding-bottom: 50px;
  background: #f6f6f6;
  height: 100vh;
  #app .van-cell {
    padding: 10px 10px;
    box-shadow: 0px 2px 10px rgba(24, 64, 118, 0.08);
    opacity: 1;
    border-radius: 4px;
  }
  .list_box {
    padding: 0 16px;
    margin-bottom: 10px;
  }
  .list_box:first-child {
    margin-top: 12px;
  }
  #app .van-tag {
    min-width: 60px;
    min-height: 16px;
    text-align: center;
  }

  footer {
    background: #fff;
    padding: 7px 12px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
  }
}
</style>

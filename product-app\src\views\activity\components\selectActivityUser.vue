<template>
  <div class="selectActivityUser">
    <div class="searchUser">
      <van-search v-model="keyword"
                  show-action
                  placeholder="请输入搜索关键词"
                  @search="onSearch">
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    <div class="selectUserText">已选择人员</div>
    <div class="selectUserBox">
      <div class="selectUserItem"
           v-for="item in selectedUser"
           :key="item.userId+'user'"
           @click="removeUser(item)">
        <div class="selectUserImg"><img :src="item.headImg"
               alt=""></div>
        <div class="selectUserName">{{item.name}}</div>
      </div>
    </div>
    <div class="selectUserText">选择用户</div>
    <breadCrumbs :data="crumbs"
                 @row-click="crumbsClick"></breadCrumbs>
    <van-empty v-if="!current.length && !userData.length"
               description="暂无数据" />
    <van-cell-group>
      <van-cell :title="item.name"
                v-for="item in current"
                :key="item.id"
                @click="cellClick(item)"
                is-link />
    </van-cell-group>
     <van-checkbox-group v-model="checked">
      <van-cell-group>
        <van-cell v-for="item in userData"
                  clickable
                  :key="item.userId"
                  :title="item.userName"
                  @click="toggle(item)">
          <template #icon>
            <van-icon :name="item.headImg"
                      class="userDataImg" />
          </template>
          <template #right-icon>
            <van-checkbox :name="item.userId"
                          @click="change(item)"
                          :ref="el => checkboxRefs[item.userId] = el"
                          @click.stop />
          </template>
        </van-cell>
      </van-cell-group>
    </van-checkbox-group>
  </div>
</template>
<script>
import { inject, reactive, onMounted, toRefs } from 'vue'
import breadCrumbs from './breadCrumbs'
export default {
  name: 'selectActivityUser',
  components: {
    breadCrumbs
  },
  props: {
    pointCode: {
      type: String,
      // default: 'point_16'
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const $api = inject('$api')
    const data = reactive({
      keyword: '',
      crumbs: [],
      tree: [],
      current: [],
      userData: [],
      checked: [],
      checkboxRefs: [],
      selectedUser: []
    })
    onMounted(() => {
      obtain()
      pointrees()
    })
    const obtain = () => {
      var arr = []
      props.data.forEach(item => {
        arr.push(item.userId)
      })
      poinexistsids(arr.join(','))
    }
    const poinexistsids = async (ids) => {
      const res = await $api.general.poinexistsids(`${props.pointCode}?ids=${ids}`)
      var { data: userData } = res
      data.selectedUser = userData
      userData.forEach(item => {
        data.checked.push(item.userId)
      })
    }
    // 列表请求
    const pointrees = async () => {
      const res = await $api.general.pointrees(props.pointCode)
      var { data: tree } = res
      data.tree = tree
      data.current = tree
      data.treeId = '1'
      data.crumbs = [{ id: '1', name: '全部', children: tree }]
    }
    // 列表请求
    const users = async () => {
      const res = await $api.general.users({
        pointCode: props.pointCode,
        treeId: data.treeId,
        keyword: data.keyword
      })
      var { data: userData } = res
      data.userData = userData
    }
    const onSearch = () => {
      users()
    }
    const crumbsClick = (row) => {
      var arr = []
      var index = true
      data.crumbs.forEach(item => {
        if (index) {
          arr.push(item)
        }
        if (row.id === item.id) {
          index = false
        }
      })
      data.crumbs = arr
      data.treeId = row.id
      data.current = row.children
      if (row.id === '1') {
        data.userData = []
      } else {
        users()
      }
    }
    const cellClick = (row) => {
      data.treeId = row.id
      data.current = row.children
      data.crumbs.push(row)
      users()
    }
    const removeUser = (row) => {
      if (data.checked.includes(row.userId)) {
        data.selectedUser = data.selectedUser.filter(tab => tab.userId !== row.userId)
      } else {
        data.selectedUser.push(row)
      }
      data.checkboxRefs[row.userId].toggle()
    }
    const toggle = (row) => {
      if (data.checked.includes(row.userId)) {
        data.selectedUser = data.selectedUser.filter(tab => tab.userId !== row.userId)
      } else {
        data.selectedUser.push(row)
      }
      data.checkboxRefs[row.userId].toggle()
    }
    const change = (row) => {
      if (data.checked.includes(row.userId)) {
        data.selectedUser.push(row)
      } else {
        data.selectedUser = data.selectedUser.filter(tab => tab.userId !== row.userId)
      }
    }
    return { ...toRefs(data), onSearch, crumbsClick, cellClick, removeUser, toggle, change }
  }
}
</script>
<style lang="less">
.selectActivityUser {
 width: 100%;
  padding: 16px 0;
  .van-search {
    padding: 0 16px;
    .van-field__body {
      font-size: 14px;
      .van-field__control {
        color: #888;
        &::-webkit-input-placeholder {
          color: #888;
        }
      }
    }
    .van-search__action {
      div {
        font-size: 14px;
      }
    }
  }
  .selectUserText {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 600;
    line-height: 22px;
    color: #333333;
    padding: 9px 16px;
  }
  .selectUserBox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    max-height: 110px;
    overflow-y: scroll;
    padding: 0 16px;
    .selectUserItem {
      display: flex;
      align-items: center;
      padding: 2px 0;
      padding-right: 6px;
      margin-right: 12px;
      margin-bottom: 6px;
      .selectUserImg {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        overflow: hidden;
        img {
          height: 100%;
          margin: auto;
          display: block;
        }
      }
      .selectUserName {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 17px;
        color: #666666;
        padding-left: 6px;
      }
    }
  }
  .breadCrumbs {
    font-size: 13px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 14px;
    color: #999999;
    .van-icon {
      font-size: 12px;
      margin: 0 6px;
    }
  }
  .van-cell-group {
    .van-cell__title {
      font-size: 14px;
    }
  }
  .userDataImg {
    border-radius: 50%;
    overflow: hidden;
    margin-right: 6px;
    .van-icon__image {
      width: 22px;
      height: 22px;
    }
  }
}
</style>

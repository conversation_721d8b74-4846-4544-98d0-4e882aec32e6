{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\peopleInformation\\peopleList.vue?vue&type=template&id=276b6b99&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\peopleInformation\\peopleList.vue", "mtime": 1756437821491}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\peopleInformation\\peopleList.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvE,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACtG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC/D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC7E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACf,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdrd_h5/product-app/src/views/peopleInformation/peopleList.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"peopleList\">\r\n    <van-nav-bar v-if=\"isShowHead\" :title=\"title\" fixed placeholder safe-area-inset-top left-text=\"\" left-arrow\r\n      @click-left=\"onClickLeft\" />\r\n    <div :style=\"$general.loadConfiguration(1)\">\r\n      <div v-if=\"showSearch\" id=\"search\" class=\"search_box\" :style=\"$general.loadConfiguration()\">\r\n        <div class=\"search_warp flex_box\">\r\n          <div @click=\"search();\" class=\"search_btn flex_box flex_align_center flex_justify_content\">\r\n            <van-icon :size=\"$general.data.appFontSize + 'px'\" :color=\"'#666'\" :name=\"'search'\"></van-icon>\r\n          </div>\r\n          <form class=\"flex_placeholder flex_box flex_align_center search_input\" action=\"javascript:return true;\">\r\n            <input id=\"searchInput\" class=\"flex_placeholder\" :style=\"$general.loadConfiguration(-1)\"\r\n              :placeholder=\"seachPlaceholder\" maxlength=\"100\" type=\"text\" ref=\"btnSearch\" @keyup.enter=\"search()\"\r\n              v-model=\"seachText\" />\r\n            <div v-if=\"seachText\" @click=\"seachText = ''; search();\"\r\n              class=\"search_btn flex_box flex_align_center flex_justify_content\">\r\n              <van-icon :size=\"$general.data.appFontSize + 'px'\" :color=\"'#999'\" :name=\"'clear'\"></van-icon>\r\n            </div>\r\n          </form>\r\n          <van-dropdown-menu class=\"search-dropdown-menu flex_box flex_align_center\" :active-color=\"appTheme\"\r\n            :style=\"$general.loadConfiguration(-3)\">\r\n            <van-dropdown-item title=\"筛选\" ref=\"filter\">\r\n              <template v-for=\"(item, index) in filters\" :key=\"index\">\r\n                <van-cell v-if=\"item.show\" :title=\"item.title\" :style=\"$general.loadConfiguration()\">\r\n                  <template v-slot:right-icon>\r\n                    <!--选择-->\r\n                    <van-dropdown-menu v-if=\"item.type == 'select'\" :active-color=\"appTheme\">\r\n                      <van-dropdown-item v-model=\"item.value\" get-container=\"#search\"\r\n                        :options=\"item.data\"></van-dropdown-item>\r\n                    </van-dropdown-menu>\r\n                    <!--开关-->\r\n                    <van-switch v-else-if=\"item.type == 'switch'\" :active-color=\"appTheme\" v-model=\"item.value\"\r\n                      :size=\"$general.data.appFontSize + 8 + 'px'\"></van-switch>\r\n                    <!--其它只展示文字-->\r\n                    <div v-else :style=\"$general.loadConfiguration()\">{{ item.value }}</div>\r\n                  </template>\r\n                </van-cell>\r\n              </template>\r\n              <div class=\"flex_box\">\r\n                <van-button block @click=\"onReset\">重置</van-button>\r\n                <van-button block :color=\"appTheme\" @click=\"onConfirm\">确认</van-button>\r\n              </div>\r\n            </van-dropdown-item>\r\n          </van-dropdown-menu>\r\n        </div>\r\n      </div>\r\n      <van-pull-refresh v-model=\"refreshing\" @refresh=\"onRefresh\">\r\n        <van-list v-model:loading=\"loading\" :finished=\"finished\" finished-text=\"没有更多了\" offset=\"52\" @load=\"onLoad\">\r\n          <!--数据列表-->\r\n          <ul class=\"vue_newslist_box\">\r\n            <van-swipe-cell v-for=\"(item, index) in dataList\" :key=\"index\" class=\"van-hairline--bottom\">\r\n              <div class=\"top_box\">\r\n                <div class=\"flex_box flex_align_center\" :class=\"item.sex == 1 ? 'man' : 'woman'\"\r\n                  :style=\"$general.loadConfiguration(-3)\">\r\n                  <img :style=\"$general.loadConfigurationSize(-1, 'h')\"\r\n                    :src=\"item.sex == 1 ? require('../../assets/img/man.png') : require('../../assets/img/woman.png')\" />\r\n                  <div v-if=\"item.age\" style=\"min-width: 2.3em;margin-left: 5px;\" class=\"inherit flex_placeholder\">\r\n                    {{ item.age + (item.age ? '岁' : '') }}</div>\r\n                </div>\r\n              </div>\r\n              <van-cell clickable class=\"vue_newslist_item \" @click=\"openDetails(item)\">\r\n                <div class=\"flex_box\">\r\n                  <img class=\"vue_newslist_img\" v-if=\"item.url\" :src=\"item.url\" />\r\n                  <div class=\"flex_placeholder vue_newslist_warp\">\r\n                    <div class=\"vue_newslist_title text_two\" :style=\"$general.loadConfiguration(0)\">\r\n                      {{ item.name }}\r\n                    </div>\r\n                    <div class=\"vue_newslist_title text_two team\" :style=\"$general.loadConfiguration(-2)\">\r\n                      {{ item.team }}\r\n                    </div>\r\n                    <div class=\"vue_newslist_title text_two des\" :style=\"$general.loadConfiguration(-2)\">\r\n                      {{ item.des }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </van-cell>\r\n            </van-swipe-cell>\r\n          </ul>\r\n        </van-list>\r\n      </van-pull-refresh>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted, watch } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, SwipeCell } from 'vant'\r\nexport default {\r\n  name: 'peopleList',\r\n  components: {\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [SwipeCell.name]: SwipeCell,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const $ifzx = inject('$ifzx')\r\n    const $appTheme = inject('$appTheme')\r\n    const $general = inject('$general')\r\n    const $isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: $ifzx,\r\n      appTheme: $appTheme,\r\n      isShowHead: $isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      seachPlaceholder: '搜索',\r\n      keyword: '',\r\n      seachText: '',\r\n      showSkeleton: true,\r\n      loading: false,\r\n      finished: false,\r\n      refreshing: false,\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      dataList: [],\r\n      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用\r\n      filter: null,\r\n      filters: [\r\n        { show: $ifzx, type: 'select', key: 'committee', title: '请选择所属专委会', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: $ifzx, type: 'select', key: 'deleId', title: '请选择界别', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: !$ifzx, type: 'select', key: 'representerElement', title: '请选择所属结构', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: !$ifzx, type: 'select', key: 'representerTeam', title: '请选择代表团', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: true, type: 'select', key: 'party', title: '请选择党派', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: true, type: 'select', key: 'nation', title: '请选择民族', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        {\r\n          show: true,\r\n          type: 'select',\r\n          key: 'birthday',\r\n          title: '请选择年龄',\r\n          value: '',\r\n          defaultValue: '',\r\n          data: [{ text: '所有', value: '' }, { text: '0岁-9岁', value: '0' }, { text: '10岁-19岁', value: '10' }, { text: '20岁-29岁', value: '20' }, { text: '30岁-39岁', value: '30' }, { text: '40岁-49岁', value: '40' }, { text: '50岁-59岁', value: '50' }, { text: '60岁-69岁', value: '60' }, { text: '70岁-79岁', value: '70' }, { text: '80岁以上', value: '80' }, { text: '其他', value: '999' }]\r\n        },\r\n        { show: true, type: 'select', key: 'sex', title: '请选择性别', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: true, type: 'select', key: 'hasVacant', title: '请选择是否出缺', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] }\r\n      ], // 筛选集合\r\n      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },\r\n      showSearch: true\r\n\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      var key = route.query.key\r\n      var type = route.query.type\r\n      console.log(key + '===' + type)\r\n      if (key) {\r\n        if (type === 'representerElement') {\r\n          $general.getItemForKey('representerElement', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'representerTeam') {\r\n          $general.getItemForKey('representerTeam', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'deleId') {\r\n          $general.getItemForKey('deleId', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'party') {\r\n          $general.getItemForKey('party', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'nation') {\r\n          $general.getItemForKey('nation', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'sex') {\r\n          $general.getItemForKey('sex', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'birthday') {\r\n          $general.getItemForKey('birthday', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'hasVacant') {\r\n          $general.getItemForKey('hasVacant', data.filters, 'key').value = key\r\n        }\r\n      }\r\n      getFiter()\r\n      setTimeout(() => {\r\n        onRefresh()\r\n      }, 100)\r\n    })\r\n    const getFiter = async () => {\r\n      var param = {\r\n        types: 'representer_element,representer_team,vacant_type,party_type,nation_type,sex,yes_no'\r\n      }\r\n      if (data.SYS_IF_ZX) {\r\n        param.types = 'committee_type,vacant_type,dele_type,party_type,nation_type,sex,yes_no'\r\n      }\r\n      const res = await $api.general.pubkvs(param)\r\n      if (res) {\r\n        var datas = res.data\r\n        var committee = datas.committee_type || []\r\n        for (var i in committee) {\r\n          var item = {}\r\n          item.text = committee[i].value\r\n          item.value = committee[i].id\r\n          $general.getItemForKey('committee', data.filters, 'key').data.push(item)\r\n        }\r\n        var deleId = datas.dele_type || []\r\n        deleId.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('deleId', data.filters, 'key').data.push(item)\r\n        })\r\n        var representerElement = datas.representer_element || []\r\n        representerElement.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('representerElement', data.filters, 'key').data.push(item)\r\n        })\r\n        var representerTeam = datas.representer_team || []\r\n        representerTeam.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('representerTeam', data.filters, 'key').data.push(item)\r\n        })\r\n        var hasVacant = data.vacant_type || []\r\n        hasVacant.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('party', data.filters, 'key').data.push(item)\r\n        })\r\n        var party = datas.party_type || []\r\n        party.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('party', data.filters, 'key').data.push(item)\r\n        })\r\n        var nation = datas.nation_type || []\r\n        nation.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('nation', data.filters, 'key').data.push(item)\r\n        })\r\n        var sex = datas.sex || []\r\n        sex.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('sex', data.filters, 'key').data.push(item)\r\n        })\r\n      }\r\n    }\r\n    const getInfo = async () => {\r\n      const param = {\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        keyword: data.seachText,\r\n        memberType: (data.SYS_IF_ZX ? 1 : 3),\r\n        startBirthday: '',\r\n        endBirthday: '',\r\n        isUsing: '1'\r\n      }\r\n      for (var i = 0; i < data.filters.length; i++) {\r\n        const filtersItem = data.filters[i]\r\n        if (filtersItem.key === 'birthday') {\r\n          if (filtersItem.value) {\r\n            var birthday = $general.getItemForKey('birthday', data.filters, 'key').data\r\n            var nItem = $general.getItemForKey(filtersItem.value, birthday, 'value')\r\n            console.log(nItem.value)\r\n            console.log(nItem.text)\r\n            if (nItem.text === '其他') {\r\n              param.startBirthday = 999\r\n              param.endBirthday = 1000\r\n            } else if (nItem.text.indexOf('-') >= 0) {\r\n              param.startBirthday = nItem.text.split('-')[0].split('岁')[0]\r\n              param.endBirthday = nItem.text.split('-')[1].split('岁')[0]\r\n            } else if (nItem.text.indexOf('以上') >= 0) {\r\n              param.startBirthday = nItem.text.split('岁以上')[0]\r\n              param.endBirthday = 1000\r\n            } else {\r\n              param.startBirthday = nItem.text.split('岁')[0]\r\n              param.endBirthday = 1000\r\n            }\r\n          }\r\n        } else {\r\n          param[filtersItem.key] = filtersItem.value\r\n        }\r\n      }\r\n      const res = await $api.peopleInformation.getMemberList(param)\r\n      var { data: list, total } = res\r\n      const newData = []\r\n      list.forEach(item => {\r\n        const _eItem = item\r\n        item.name = _eItem.userName || ''// 姓名\r\n        item.url = _eItem.headImg || ''// 头像\r\n        item.team = _eItem.representerTeam || _eItem.deleId || ''// 代表团 界别\r\n        item.des = _eItem.position || ''// 职务\r\n        item.sex = _eItem.sex === '女' ? 0 : 1// 性别\r\n        item.age = _eItem.age || ''// 年龄\r\n        item.relateType = 'representer'// 类型\r\n        newData.push(item)\r\n      })\r\n      data.dataList = data.dataList.concat(newData)\r\n      data.showSkeleton = false\r\n      data.loading = false\r\n      data.refreshing = false\r\n      // 数据全部加载完成\r\n      if (data.dataList.length >= total) {\r\n        data.finished = true\r\n      }\r\n    }\r\n    watch(() => data.dataList, (newName, oldName) => {\r\n\r\n    })\r\n\r\n    // 筛选重置事件\r\n    const onReset = () => {\r\n      for (var i = 0; i < data.filters.length; i++) {\r\n        data.filters[i].value = data.filters[i].defaultValue\r\n      }\r\n    }\r\n    // 筛选确定事件\r\n    const onConfirm = () => {\r\n      data.filter.toggle()\r\n      onRefresh()\r\n    }\r\n\r\n    const onRefresh = () => {\r\n      data.pageNo = 1\r\n      data.dataList = []\r\n      data.showSkeleton = true\r\n      data.loading = true\r\n      data.finished = false\r\n      getInfo()\r\n    }\r\n    const onLoad = () => {\r\n      data.pageNo = data.pageNo + 1\r\n      getInfo()\r\n    }\r\n    const openDetails = (_item) => {\r\n      router.push({ name: 'personData', query: { id: _item.id } })\r\n    }\r\n\r\n    const onClickLeft = () => history.back()\r\n\r\n    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, confirm, openDetails, onReset, onConfirm }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.peopleList {\r\n  background: #f8f8f8;\r\n\r\n  .a_box_warp {\r\n    background: #ffffff;\r\n    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);\r\n    opacity: 1;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .search_box {\r\n    background: #ffffff;\r\n  }\r\n\r\n  .a_search_box {\r\n    padding: 14px 15px 0 15px;\r\n  }\r\n\r\n  .a_search_select_box {\r\n    padding: 2px 0 2px 10px;\r\n  }\r\n\r\n  .a_search_select_text {\r\n    color: #222222;\r\n    font-weight: 500;\r\n    line-height: 1.46;\r\n  }\r\n\r\n  .a_search_box form {\r\n    padding: 0 13px;\r\n  }\r\n\r\n  .a_search_btn_box {\r\n    padding: 9px 16px;\r\n  }\r\n\r\n  .a_search_select_text_icon {\r\n    position: relative;\r\n    margin-left: 6px;\r\n    width: 13px;\r\n  }\r\n\r\n  .a_search_select_text_icon::after {\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -5px;\r\n    border: 3px solid;\r\n    border-color: transparent transparent #222 #222;\r\n    -webkit-transform: rotate(-45deg);\r\n    transform: rotate(-45deg);\r\n    opacity: 0.8;\r\n    content: \"\";\r\n  }\r\n\r\n  .a_select_btn_box {\r\n    background: #666666;\r\n    margin-left: 5px;\r\n    font-weight: 500;\r\n    line-height: 1.5;\r\n    color: #ffffff;\r\n    padding: 7px 11px;\r\n    border-radius: 2px;\r\n  }\r\n\r\n  #app .vue_newslist_item {\r\n    padding: 15px 15px;\r\n  }\r\n\r\n  #app .vue_newslist_box .van-cell {\r\n    background-color: rgba(0, 0, 0, 0) !important;\r\n  }\r\n\r\n  .vue_newslist_img {\r\n    width: 55px;\r\n    min-height: 0;\r\n    height: 74px;\r\n    border-radius: 2px;\r\n    margin-right: 10px;\r\n    background-position: center;\r\n  }\r\n\r\n  .van-hairline--bottom {\r\n    width: calc(100% - 32px);\r\n    left: 0;\r\n    right: 0;\r\n    margin: auto;\r\n    margin-top: 10px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 3px 20px rgba(34, 85, 172, 0.12);\r\n    opacity: 1;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .vue_newslist_title {\r\n    font-weight: 600;\r\n    color: #222222;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .vue_newslist_warp {\r\n    padding-bottom: 0;\r\n    position: relative;\r\n  }\r\n\r\n  .team {\r\n    font-weight: 500;\r\n    color: #222222;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  .des {\r\n    font-weight: 500;\r\n    color: #222222;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  .top_box {\r\n    position: absolute;\r\n    right: 15px;\r\n    top: 15px;\r\n    color: #6499f0;\r\n    opacity: 1;\r\n    z-index: 99;\r\n  }\r\n\r\n  .woman {\r\n    color: #f06981;\r\n  }\r\n\r\n  .van-swipe-cell__wrapper {\r\n    position: relative;\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div class="electronicreadingHome">
    <!-- 竖屏 -->
    <div v-if="Verticalscreen">
      <div class="userName_box">
        <div class="topName">{{userName}}</div>
      </div>
      <div class="ToggleScreen"
           @click="VerticalscreenClick"> <img src="../../assets/img/qingdao/geng_qiehuan.png"
             alt=""
             style="width: 0.4rem;height: 0.4rem;margin-right: 0.1rem;">切换横屏</div>
      <div class="contentBox">
        <van-grid clickable
                  :column-num="2"
                  :gutter="10">
          <van-grid-item v-for="(item,index) in conferenceTypeList"
                         :key="index"
                         @click="itemClick(item)">
            <template v-slot:default>
              <van-image fit="cover"
                         style="margin-bottom:8px;width: 1.2rem;"
                         :src="item.normal"></van-image>
              <div style="color: #000;padding:1px 10px;font-size: 0.3rem;"
                   v-html="item.value"></div>
            </template>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <!-- 点击按钮来切换横屏 -->
    <div class="clickAutomatic"
         v-if="TogglehorizontalScreen">
      <div class="userName_box">
        <div class="topName">{{userName}}</div>
      </div>
      <div class="ToggleScreen"
           @click="toggleScreenClick">
        <img src="../../assets/img/qingdao/geng_qiehuan.png"
             alt="">切换竖屏
      </div>
      <div class="contentBoxs">
        <van-grid clickable
                  :column-num="4"
                  :gutter="10">
          <van-grid-item v-for="(item,index) in conferenceTypeList"
                         :key="index"
                         @click="itemClick(item)"
                         style="width: 50px;">
            <template v-slot:default>
              <van-image fit="cover"
                         style="margin-bottom:5px;width: 1.2rem;"
                         :src="item.normal"></van-image>
              <div style="color: #000;padding:1px 10px;font-size: 0.3rem;"
                   v-html="item.value"></div>
            </template>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <!-- 手机自动旋转切换横屏 -->
    <div v-if="toggleScreen1"
         class="AutomaticRotation">
      <div class="userName_box">
        <div class="topName">{{userName}}</div>
      </div>
      <div class="ToggleScreen"
           @click="toggleScreenClick">
        <img src="../../assets/img/qingdao/geng_qiehuan.png"
             alt=""
             style="width: 0.4rem;height: 0.4rem;margin-right: 0.1rem;">切换竖屏
      </div>
      <div class="contentBoxs">
        <van-grid clickable
                  :column-num="4"
                  :gutter="10">
          <van-grid-item v-for="(item,index) in conferenceTypeList"
                         :key="index"
                         @click="itemClick(item)"
                         style="width: 50px;">
            <template v-slot:default>
              <van-image fit="cover"
                         style="margin-bottom:5px;width: 0.8rem;"
                         :src="item.normal"></van-image>
              <div style="color: #000;padding:1px 8px;font-size: 0.18rem;"
                   v-html="item.value"></div>
            </template>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
             showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
    <van-overlay :show="isShowLoading">
      <div style="text-align: center; padding-top: 100%">
        <van-loading size="24px"
                     vertical
                     text-color="#0094ff"
                     color="#0094ff">加载中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Toast, Loading, Overlay } from 'vant'
export default {
  name: 'electronicreadingHome',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      userName: '',
      showSkeleton: true,
      isShowLoading: true,
      conferenceTypeList: [],
      toggleScreen1: false, // 手机自动旋转切换横屏
      Verticalscreen: true, // 竖屏
      TogglehorizontalScreen: false // 横屏
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      console.log('app进入会议了！')
      data.isShowLoading = true
      if (JSON.parse(sessionStorage.getItem('user'))) {
        data.isShowLoading = false
      }
      const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token')
      if (!token) {
        data.Verticalscreen = true
        setTimeout(() => {
          sessionStorage.setItem('Sys_token', JSON.stringify(api.getPrefs({ sync: true, key: 'Sys_token' }))) // eslint-disable-line
          sessionStorage.setItem('areaId', JSON.stringify(api.getPrefs({ sync: true, key: 'SYS_SiteID' }))) // eslint-disable-line
          api.setInterfaceStyle({ style: 'light' }) // eslint-disable-line
          console.log('Sys_token------------->>>>>>')
          console.log(JSON.stringify(sessionStorage.getItem('Sys_token')))
          const user = {
            userName: api.getPrefs({ sync: true, key: 'Sys_UserName' }), // eslint-disable-line
            position: api.getPrefs({ sync: true, key: 'Sys_Position' }), // eslint-disable-line
            headImg: window.localStorage.getItem('Sys_AppPhoto')
          }
          data.userName = user.userName
          sessionStorage.setItem('user', JSON.stringify(user))
          onRefresh()
          isPc()
          createPd()
        }, 800)
      } else {
        data.Verticalscreen = true
        setTimeout(() => {
          console.log('走到else了')
          data.userName = JSON.parse(sessionStorage.getItem('user')).userName || ''
          onRefresh()
          isPc()
          createPd()
          data.isShowLoading = false
        }, 2000)
      }
    })
    const createPd = () => {
      if (window.orientation === 90 || window.orientation === -90) {
        data.toggleScreen1 = true
        data.Verticalscreen = false
        data.TogglehorizontalScreen = false
      }
      window.addEventListener('onorientationchange' in window ? 'orientationchange' : 'resize', function () {
        if (window.orientation === 180 || window.orientation === 0) {
          data.toggleScreen1 = false
          data.Verticalscreen = true
          data.TogglehorizontalScreen = false
        }
        if (window.orientation === 90 || window.orientation === -90) {
          data.toggleScreen1 = true
          data.Verticalscreen = false
          data.TogglehorizontalScreen = false
        }
      })
    }
    // 判读是否是PC打开
    const isPc = () => {
      var plat = navigator.platform
      var win = plat.indexOf('Win') === 0
      var mac = plat.indexOf('Mac') === 0
      if (win || mac) {
        return true
      } else {
        return false
      }
    }
    const onRefresh = () => {
      data.conferenceTypeList = []
      dictionaryPubkvs()
    }
    const onLoad = () => {
    }
    // 点击切换横屏事件
    const VerticalscreenClick = async (id) => {
      console.log('点击切换横屏了')
      console.log(window.orientation)
      // 竖屏关闭
      data.Verticalscreen = false
      // 横屏开启
      data.TogglehorizontalScreen = true
      if (isPc()) {
        data.toggleScreen1 = true
        data.TogglehorizontalScreen = false
        data.Verticalscreen = false
      }
      if (window.orientation === 90 || window.orientation === -90) {
        data.TogglehorizontalScreen = false
        data.toggleScreen1 = true
        data.Verticalscreen = false
      }
    }
    // 点击切换竖屏事件
    const toggleScreenClick = async (id) => {
      // 横屏关闭
      data.TogglehorizontalScreen = false
      // 竖屏开启
      data.Verticalscreen = true
      // 自动旋转
      data.toggleScreen1 = false
    }
    // 获取所有会议列表
    const myProposalList = async (id) => {
      var res = []
      var datas = {
        meetingType: id
      }
      res = await $api.electronicreading.getHistoricalConference(datas) // 全会
      var { data: list } = res
      if (list) {
        if (id === '1' || id === '2') {
          router.push({
            path: 'electronicreadingItemDetails2',
            query: {
              TogglehorizontalScreen: data.TogglehorizontalScreen ? '1' : '0',
              Verticalscreen: data.Verticalscreen ? '1' : '0',
              toggleScreen1: data.toggleScreen1 ? '1' : '0',
              meetingTypeId: id,
              id: list[0].id
            }
          })
        } else {
          router.push({
            path: 'electronicreadingItemDetails',
            query: {
              TogglehorizontalScreen: data.TogglehorizontalScreen ? '1' : '0',
              Verticalscreen: data.Verticalscreen ? '1' : '0',
              toggleScreen1: data.toggleScreen1 ? '1' : '0',
              meetingTypeId: id,
              id: list[0].id
            }
          })
        }
      } else {
        Toast('暂无会议信息')
      }
    }
    // 获取会议类型
    const dictionaryPubkvs = async () => {
      data.conferenceTypeList = []
      const res = await $api.activity.dictionaryPubkvs({
        types: 'conference_type'
      })
      if (res) {
        var { data: list } = res
        var imgs = [
          { id: '1', normal: require('../../assets/img/qingdao/quanti.png') },
          { id: '2', normal: require('../../assets/img/qingdao/changwei.png') },
          { id: '3', normal: require('../../assets/img/qingdao/dangzu.png') },
          { id: '4', normal: require('../../assets/img/qingdao/zhuxi.png') },
          { id: '5', normal: require('../../assets/img/qingdao/jiguan.png') },
          { id: '6', normal: require('../../assets/img/qingdao/qita.png') },
          { id: '7', normal: require('../../assets/img/qingdao/qita.png') },
          { id: '8', normal: require('../../assets/img/qingdao/qita8.png') },
          { id: '9', normal: require('../../assets/img/qingdao/qita9.png') },
          { id: '10', normal: require('../../assets/img/qingdao/qita10.png') },
          { id: '11', normal: require('../../assets/img/qingdao/qita11.png') },
          { id: '12', normal: require('../../assets/img/qingdao/qita12.png') },
          { id: '13', normal: require('../../assets/img/qingdao/qita13.png') }
        ]
        var conferenceTypeList = list.conference_type.map(item => {
          var imgItem = imgs.find(({ id }) => id === item.id)
          return {
            ...imgItem,
            id: item.id,
            value: item.value
          }
        })
        data.conferenceTypeList = data.conferenceTypeList.concat(conferenceTypeList)
      } else {
        Toast('稍后在尝试下~')
      }
    }
    // 点击某一个会议类型进详情
    const itemClick = async (_item) => {
      sessionStorage.setItem('meetingTypeId', _item.id)
      myProposalList(_item.id)
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, itemClick, VerticalscreenClick, toggleScreenClick }
  }
}
</script>
<style lang="less" scoped>
.van-grid {
  width: 80%;
  .van-grid-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10px;
  }
}
::v-deep .van-grid-item__content {
  width: 113px;
  height: 114px;
  padding: 0px 0px !important;
  border-radius: 10px !important;
  // background-color: aqua;
}
.electronicreadingHome {
  // min-height: 100vh;
  background: linear-gradient(
    180deg,
    rgb(74, 195, 160),
    rgb(4, 87, 192)
  ) !important;
  .userName_box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .topName {
      width: 200px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom: 1px solid #fff;
      color: #fff;
      font-weight: bold;
      font-size: 0.5rem;
    }
  }
  .ToggleScreen {
    position: absolute;
    top: 20px;
    right: 10px;
    color: #ffffff;
    background: #5a9cff;
    padding: 5px;
    border-radius: 50px;
    font-size: 14px;
    display: flex;
  }
  .contentBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
  // 点击按钮切换横竖屏样式
  .clickAutomatic {
    width: 100vh;
    height: 100vh;
    transform: rotate(90deg);
    transform-origin: 50vw 50vw;
    white-space: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    .ToggleScreen img {
      width: 0.4rem;
      height: 0.5rem;
      margin-right: 0.1rem;
    }
    .contentBoxs {
      width: 100%;
      height: 100%;
      margin-top: 30px;
      .van-grid {
        width: 96%;
        .van-grid-item {
          display: flex;
          justify-content: center;
          align-items: center;
          padding-bottom: 10px;
        }
      }
    }
  }
  // 手机自动旋转切换横竖屏样式
  .AutomaticRotation {
    .userName_box {
      .topName {
        width: 120px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: 1px solid #fff;
        color: #fff;
        font-weight: bold;
        font-size: 0.35rem;
      }
    }
    .ToggleScreen {
      position: absolute;
      top: 5px;
      right: 20px;
      color: #ffffff;
      background: #5a9cff;
      font-size: 12px;
      display: flex;
    }
    .contentBoxs {
      width: 100%;
      height: 100%;
      margin-top: 15px;
      .van-grid {
        width: 100%;
        .van-grid-item {
          display: flex;
          justify-content: center;
          align-items: center;
          padding-bottom: 10px;
        }
        ::v-deep .van-grid-item__content {
          width: 70px;
          height: 70px;
          padding: 0px 0px !important;
          border-radius: 10px !important;
          // background-color: aqua;
        }
      }
    }
  }
  // van-popup--center van-toast van-toast--middle van-toast--text
  .van-popup {
    transform: rotate(90deg);
  }
}
</style>

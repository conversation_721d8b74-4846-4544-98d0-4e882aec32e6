// 全文检索
const searchHome = () => import('@/views/searchAll/searchHome')
const searchAll = () => import('@/views/searchAll/searchAll')
const searchDetails = () => import('@/views/searchAll/searchDetails')

const searchAlls = [{
  path: '/searchHome',
  name: 'searchHome',
  component: searchHome,
  meta: {
    title: '全文检索',
    keepAlive: true
  }
}, {
  path: '/searchAll',
  name: 'searchAll',
  component: searchAll,
  meta: {
    title: '全文检索',
    keepAlive: true
  }
}, {
  path: '/searchDetails',
  name: 'searchDetails',
  component: searchDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}]
export default searchAlls

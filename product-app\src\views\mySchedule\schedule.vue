<template>
  <div class="schedule">
    <van-calendar title="我的日程"
                  :poppable="false"
                  :show-confirm="false"
                  :style="{ height: '400px' }"
                  color="#1989fa"
                  :min-date="minDate"
                  :max-date="maxDate"
                  @select="selectDate"
                  @month-show="monthFn" >
      <template #bottom-info="Day">
        <div :class="getDay(Day) == 1?'day': ''"></div>
      </template>
    </van-calendar>
    <div class="today">
      <div class="today_title">
        今日日程
      </div>
      <van-empty description="今日暂无日程"
                 v-if="todayList.length==0" />
      <div class="today_list"
           v-for="item in todayList"
           :key="item.id"
           @click="popupDetails(item)">
        <div class="today_list_title">
          {{ item.title }}
        </div>
        <div class="today_list_startTime">
          开始时间: {{ item.startTime }}
        </div>
        <div class="today_list_endTime">
          结束时间: {{ item.endTime }}
        </div>
      </div>
    </div>
    <!-- 添加弹出框 -->
    <van-popup v-model:show="show"
               round
               position="bottom"
               :style="{ height: '80%' }"
               closeable>
      <div class="popup_title">
        {{ text }}
      </div>
      <div class="popup"
           v-if="scheduleDetails.title">
        <div class="today_list_title">
          {{ scheduleDetails.title }}
        </div>
        <div class="today_list_startTime">
          开始时间: {{ scheduleDetails.startTime }}
        </div>
        <div class="today_list_endTime">
          结束时间: {{ scheduleDetails.endTime }}
        </div>
        <div class="popup_content">
          {{ scheduleDetails.content }}
        </div>
      </div>
      <div class="form"
           v-else>
        <div class="select_time">
          <div class="select_time_startTime">
            开始时间: <span @click="startPopup">{{ form.startTime }}</span>
          </div>
          <div class="select_time_endTime">
            结束时间: <span @click="endPopup">{{ form.endTime }}</span>
          </div>
        </div>
        <div class="addTitle">
          <input type="text"
                 placeholder="请输入标题"
                 v-model="form.title">
        </div>
        <div class="addContent">
          <textarea placeholder="请输入内容"
                    v-model="form.content"></textarea>
        </div>
        <button class="confirm"
                @click="confirm">确定</button>
      </div>
      <!-- 时间弹出框 -->
      <!-- <van-popup v-model:show="showTime"
                 round
                 position="bottom"
                 :style="{ height: '50%' }">
        <van-datetime-picker v-model="currentDate"
                             type="datetime"
                             title="滑动选择时间"
                             :min-date="minDate"
                             :max-date="maxDate"
                             @confirm="startTime" />
      </van-popup> -->
      <van-popup v-model:show="showTime"
                 round
                 position="bottom"
                 :style="{ height: '50%' }">
        <van-datetime-picker v-model="currentDate"
                             type="datetime"
                             title="滑动选择时间"
                             :min-date="minDate"
                             :max-date="maxDate"
                             @confirm="startTime" />
      </van-popup>
      <van-popup v-model:show="showEnd"
                 round
                 position="bottom"
                 :style="{ height: '50%' }">
        <van-datetime-picker v-model="endDate"
                             type="datetime"
                             title="滑动选择时间"
                             :min-date="minDate"
                             :max-date="maxDate"
                             @confirm="endTime" />
      </van-popup>
    </van-popup>
    <div class="addSchedule"
         @click="popupDetails('add')">
      +
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="4"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Calendar, Popup, Toast, Empty } from 'vant'
export default ({
  name: 'schedule',
  components: {
    [Calendar.name]: Calendar,
    [Popup.name]: Popup,
    [Toast.name]: Toast,
    [Empty.name]: Empty
  },
  setup () {
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      startDate: new Date('2022-01-01'),
      endDates: new Date('2025-01-01'),
      endDate: new Date(),
      dateList: [],
      scheduleList: [],
      minDate: new Date('2021-01-01'),
      maxDate: new Date('2025-01-01'),
      todayList: [],
      scheduleDetails: {},
      show: false,
      showTime: false,
      showEnd: false,
      currentDate: new Date(),
      text: '日程详情',
      form: {
        startTime: '请选择开始时间',
        endTime: '请选择结束时间',
        title: '',
        content: ''
      },
      showSkeleton: true
    })
    onMounted(() => {
    })

    // 拿2022年至今的日期
    while (data.startDate <= data.endDates) {
      var year = data.startDate.getFullYear()
      var month = data.startDate.getMonth() + 1
      var monthStr = month < 10 ? '0' + month : month
      data.dateList.push(`${year}-${monthStr}`)
      data.startDate.setMonth(month)
    }
    // var getData = () => {
    //   setTimeout(() => {
    //     Promise.all(data.dateList.map(item => {
    //       return $api.mySchedule.getSchedule({ date: item })
    //     })).then((res) => {
    //       var date = res.map(it => {
    //         return [...it.data]
    //       })
    //       console.log(date)
    //       date.map(item => {
    //         data.scheduleList.push(...item)
    //       })
    //       data.scheduleList.forEach(item => {
    //         item.status = item.personalScheduleVos.length > 0 ? 1 : 0
    //       })
    //       console.log('=========>', data.scheduleList)
    //       if (data.scheduleList.length > 0) {
    //         data.showSkeleton = false
    //       }
    //       selectDate()
    //     }, 10)
    //   })
    // }
    // "date":"2022-11"
    // 选择的日期
    const selectDate = (val) => {
      var date = dayjs(val).format('YYYY-MM-DD')
      data.todayList = []
      if (data.scheduleList.find(item => item.date === date)) {
        data.todayList = data.scheduleList.find(item => item.date === date).personalScheduleVos
      }
    }
    const monthFn = (e) => {
      // console.log(e)
      var date = dayjs(e.date).format('YYYY-MM')
      $api.mySchedule.getSchedule({ date: date }).then(res => {
        data.scheduleList.push(...res.data)
        // console.log(data.scheduleList)
        selectDate(new Date())
      })
    }
    const getDay = ({ date }) => {
      const dates = dayjs(date).format('YYYY-MM-DD')
      if (data.scheduleList.find(item => item.date === dates)) {
        return data.scheduleList.find(item => item.date === dates).personalScheduleVos.length > 0 ? 1 : 0
      } else {
        return 0
      }
    }
    // 弹框
    const popupDetails = (item) => {
      if (item === 'add') {
        data.scheduleDetails = {}
        data.text = '新建日程'
      } else {
        data.scheduleDetails = item
        data.text = '日程详情'
      }
      data.show = true
    }
    // 开始时间的确定
    const startTime = (e) => {
      data.form.startTime = dayjs(e).format('YYYY-MM-DD HH:mm:ss')
      data.showTime = false
    }
    // 结束时间的确定
    const endTime = (e) => {
      data.form.endTime = dayjs(e).format('YYYY-MM-DD HH:mm:ss')
      data.showEnd = false
    }
    // 开始时间
    const startPopup = () => {
      data.showTime = true
    }
    // 结束时间
    const endPopup = () => {
      if (data.form.startTime !== '请选择开始时间') {
        data.showEnd = true
      } else {
        Toast('请先选择开始时间')
        return false
      }
    }
    // 添加确定
    const confirm = async () => {
      if (data.form.startTime === '请选择开始时间') {
        Toast('请选择开始时间')
      } else if (data.form.endTime === '请选择结束时间') {
        Toast('请选择结束时间')
      } else if (data.form.title === '') {
        Toast('请填标题')
      } else if (data.form.startTime === '') {
        Toast('请填内容')
      } else {
        const res = await $api.mySchedule.addSchedule(data.form)
        Toast(res.errmsg)
        data.todayList = []
        data.scheduleList = []
        data.show = false
        data.form = {}
        monthFn({ date: new Date() })
        // getDay()
        // getData()
      }
    }
    return { ...toRefs(data), endPopup, confirm, startPopup, startTime, endTime, popupDetails, selectDate, dayjs, getDay, monthFn }
  }
})
</script>
<style lang='less' scoped>
.schedule {
  width: 100%;
  height: 100%;
  .addTitle {
    width: 100%;
    height: 50px;
    margin: 10px 0;
    background: #f4f6f8;
    border-radius: 10px;
    box-sizing: border-box;
    > input {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      padding-left: 10px;
      box-sizing: border-box;
    }
  }
  .addContent {
    width: 100%;
    height: 220px;
    background: #f4f6f8;
    border-radius: 10px;
    box-sizing: border-box;
    > textarea {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      padding: 10px 0 0 10px;
      box-sizing: border-box;
    }
  }
  .form {
    padding: 15px;
    box-sizing: border-box;
    .confirm {
      width: 100%;
      height: 40px;
      border-radius: 20px;
      background: #3894ff;
      color: #fff;
      margin-top: 30px;
    }
  }
  .select_time {
    width: 100%;
    box-sizing: border-box;
    .select_time_startTime {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > span {
        color: #3894ff;
      }
    }
    .select_time_endTime {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > span {
        color: #3894ff;
      }
    }
  }
  .addSchedule {
    bottom: 20px;
    left: 50%;
    transform: translate(-50%, 0);
    position: fixed;
    width: 60px;
    height: 60px;
    background: #3894ff;
    border-radius: 50%;
    color: #fff;
    font-size: 35px;
    text-align: center;
    line-height: 60px;
  }
  .popup_title {
    margin-top: 10px;
    text-align: center;
    width: 100%;
    height: 40px;
    font-size: 20px;
    font-weight: 700;
  }
  .popup {
    padding: 0 20px 20px;
    width: 100%;
    height: 100%;
    .today_list_title {
    }
    .today_list_startTime {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }
    .today_list_endTime {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }
    .popup_content {
      width: 100%;
      font-size: 20px;
      font-weight: 700;
      padding-left: 20px;
      margin-top: 20px;
    }
  }
  .day {
    width: 5px;
    height: 5px;
    background: #1989fa;
    border-radius: 50%;
    margin: 0 auto;
  }
  .today {
    width: 100%;
    // height: 450px;
    // overflow: auto;
    background: #fff;
    box-sizing: border-box;
    padding: 10px;
    margin-top: 10px;
    .today_title {
      width: 100%;
      height: 40px;
      font-size: 20px;
      font-weight: 700;
      padding-left: 20px;
    }
    .today_list {
      width: 100%;
      height: 90px;
      background: #f4f9fd;
      border-radius: 10px;
      margin-top: 10px;
      box-sizing: border-box;
      border-left: 5px solid #3894ff;
      padding: 10px;
      .today_list_title {
      }
      .today_list_startTime {
        font-size: 14px;
        color: #666;
        margin-top: 5px;
      }
      .today_list_endTime {
        font-size: 14px;
        color: #666;
        margin-top: 5px;
      }
    }
  }
}
</style>

import { HTTP } from '../http.js'
class suggest extends HTTP {
  // 所有建议
  suggestList (params) {
    return this.request({ url: '/suggest/list', data: params })
  }

  // 详情
  suggestInfo (params) {
    return this.request({ url: `/suggest/info/${params}` })
  }

  // 办理中建议
  allTransactList (params) {
    return this.request({ url: '/suggest/allTransactList', data: params })
  }

  // 已答复建议
  allAnsweredSuggestList (params) {
    return this.request({ url: '/suggest/allAnsweredSuggestList', data: params })
  }

  // 获取建议审查详情
  auditDetail (params) {
    return this.request({ url: '/suggest/auditDetail', data: params })
  }

  // 办理中建议详情
  transactSuggestDetail (params) {
    return this.request({ url: '/suggest/transactSuggestDetail', data: params })
  }

  // 获取建议分类和主题词
  chooseList (params) {
    return this.request({ url: '/submission/suggest/topic/chooseList', data: params })
  }

  // 办理单位沟通情况列表
  flowContactList (params) {
    return this.request({ url: '/suggest/flowContactList', data: params })
  }

  // 超管获取答复件列表
  answerList (params) {
    return this.request({ url: '/suggest/answerList', data: params })
  }

  // 通过办理记录id获取答复件详情
  flowAnswerDetail (params) {
    return this.request({ url: '/suggest/flowAnswerDetail', data: params })
  }

  // 获取建议进程
  nodeInfo (params) {
    return this.request({ url: 'suggest/nodeInfo?', data: params })
  }

  // 满意度测评详情
  flowEvaluateDetail (params) {
    return this.request({ url: '/suggest/flowEvaluateDetail', data: params })
  }

  mySuggestList (params) {
    return this.request({ url: '/suggest/mySuggestList', data: params })
  }

  suggestUrl (url, params) {
    return this.request({ url: url, data: params })
  }
}
export {
  suggest
}

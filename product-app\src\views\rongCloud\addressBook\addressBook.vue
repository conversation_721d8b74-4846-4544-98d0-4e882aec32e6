<template>
  <div class="addressBook">
    <!--搜索-->
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div id="search"
           class="search_box"
           style="background:#fff;">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"><input :style="'font-size:13px;'"
                   :placeholder="'搜索'"
                   maxlength="100"
                   type="search"
                   v-model="keyword" /></form>
        </div>
      </div>
      <!--三个排序按钮-->
      <template v-if="category.data.length > 1">
        <div class="contacts_icon_body flex_box">
          <div v-for="(item,index) in category.data"
               :key="index"
               class="contacts_icon_item  T-flexbox-vertical flex_align_center"
               @click="switchCategory(item)">
            <div class="contacts_icon_item_name"
                 :style="'font-size:15px;background: '+(item.value == category.value?'#DCEBFF':'#F4F4F5')+';color:' + (item.value == category.value?appTheme:'#666666')">{{item.name}}</div>
          </div>
        </div>
      </template>
      <!--级别选择 -->
      <div v-if="level.data.length != 0"
           class="flex-wrap flex_box">
        <div id="navigation"
             class="flex-con flex_placeholder">
          <div class="header_css">
            <div v-for="(item,index) in level.data"
                 :key="index"
                 @click="clickLevel(item)"
                 :style="'font-size:13px;'"
                 v-html="item.name + ((index!=level.data.length-1)?'&nbsp;&nbsp;>&nbsp;&nbsp;':'')"></div>
          </div>
        </div>
        <div v-if="ifReturn && userList.length"
             class="checkAllBtn"
             @click="checkAll"
             :style="'font-size:14px;color:'+appTheme">{{isHaveCheckUser?'取消':''}}全选</div>
      </div>
    </van-sticky>
    <div v-if="selectUser.length != 0"
         class="sel_warp">
      <div v-for="(uItem,uIndex) in selectUser"
           :key="uIndex"
           class="sel_item"
           @click="clickUser(uItem,true)">
        <van-image v-if="!uItem.notDel"
                   class="sel_del"
                   fit="cover"
                   round
                   :src="icon_upload_delete"
                   :style="'width:20px;height:20px;'"
                   @click.stop="$general.delItemForKey(uIndex,selectUser)"></van-image>
        <van-image :style="'width:45px;height:45px;'"
                   fit="contain"
                   round
                   :src="uItem.url"></van-image>
        <div class="sel_name text_one2"
             :style="'font-size:12px;'">{{uItem.name}}</div>
      </div>
    </div>

    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--列表-->
        <div class="content">
          <div v-if="!ifSearch">
            <div v-for="(item,index) in groupList"
                 :key="index"
                 class="group_item flex_box flex_align_center"
                 @click="clickGroup(item)">
              <div class="group_name flex_placeholder">{{item.name+(category.value == '11'?('('+item.total+'人)'):'')}}</div>
              <van-icon class="group_img"
                        :size="17"
                        name="arrow"></van-icon>
            </div>
          </div>
          <van-checkbox-group v-model="checked">
            <div v-for="(item,index) in userList"
                 :key="index"
                 class="user_item flex_box flex_align_center"
                 @click="clickUser(item)">
              <van-image :style="'width:35px;height:35px;'"
                         class="user_img"
                         round
                         fit="contain"
                         :src="item.url"></van-image>
              <div class="user_name flex_placeholder">{{item.name}}</div>
              <van-checkbox v-if="ifReturn"
                            :name="item.id"
                            :disabled="isDisabled(item)"
                            :icon-size="20"
                            :checked-color="appTheme"
                            shape="square"></van-checkbox>
            </div>
          </van-checkbox-group>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, Image as VanImage, NavBar, Sticky } from 'vant'
// import moment from 'moment'
export default {
  name: 'addressBook',
  components: {
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  props: {
    isSelectUser: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      icon_upload_delete: require('../../../assets/img/icon_upload_delete.png'),
      user: JSON.parse(sessionStorage.getItem('user')),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      keyword: '',
      ifSearch: false, // 是否搜索中
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      switchs: {
        value: '',
        data: [
          // {id: "430000", value: "湖南省"}, {id: "430100", value: "长沙市"}, {id: "430104", value: "岳麓区"}, {id: "-1", value: "全省通讯录"}
        ]
      },
      category: { value: '11', data: [{ name: '政协委员', value: '11' }, { name: '政协机关', value: '12' }] }, // 类别 政协委员还是政协机关
      level: { value: 0, data: [{ name: '全部', id: 0 }] },

      groupSource: [], // 分组数据源
      groupList: [
        // { name: '湖南省', id: 0 }
      ],
      userList: [
      ],
      // 选择的用户集合 id name url notDel[是否可以删除选择]
      selectUser: [
        // { id: 1, name: '超级管理员', url: '../../../images/btn_home_sectors_h.png', notDel: true }
      ],
      checked: [],
      onlyPage: -1, // 只显示哪一个选择
      ifReturn: false, // 是否返回人员
      ifYourself: true, // 是否能选择当前用户
      ifReturnNull: false, // 是否能选择返回空 默认不能
      selectMax: 0, // 是否有选择限制  为0不限制
      isHaveCheckUser: false, // 是否已经有选择的对象
      title: route.query.title
    })
    watch(() => data.selectUser, (newName, oldName) => {
      console.log(newName)
      data.checked = []
      data.selectUser.forEach(element => {
        data.checked.push(element.id)
      })
    })
    watch(() => data.keyword, (newName, oldName) => {
      console.log(newName)
      onRefresh()
    })
    onMounted(() => {
      onRefresh()
      obtain()
    })
    const obtain = () => {
      data.ifReturn = props.isSelectUser
      data.selectUser = data.selectUser.concat(props.data)
      data.selectUser.forEach(element => {
        data.checked.push(element.id)
      })
    }
    if (data.title) {
      document.title = data.title
    }
    const getChaters = async () => {
      var datas = {}
      if (data.ifSearch) {
        datas = {
          keyword: data.keyword,
          groupType: data.category.value
        }
      } else {
        datas = {
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          groupType: data.category.value,
          groupId: data.level.data[data.level.data.length - 1].id,
          provinceAreaId: data.switchs.value
        }
      }
      const { data: list, total } = await $api.rongCloud.getChaters(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = {}; var itemData = _eItem
        item.id = itemData.userId || ''// id
        item.name = itemData.userName || ''// 名字
        item.mobile = itemData.mobile || ''
        // if ($api.trim(that.seachText)) {
        //   item.name = item.name.replace(new RegExp(that.seachText, 'g'), '<span style="color:' + that.appTheme + ';" class="inherit">' + that.seachText + '</span>')
        // }
        item.moreText = itemData.groupName || ''
        item.areaId = itemData.areaId || ''
        item.url = itemData.headImg || require('../../../assets/img/icon_default_user.png')// 头像
        newData.push(item)
      })
      data.userList = data.userList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.userList.length >= total) {
        data.finished = true
      }
    }
    const switchCategory = (_item) => {
      data.category.value = _item.value
      data.level.data = [{ name: '全部', id: 0 }]
      data.seachText = ''
      onRefresh()
    }
    const getData = async () => {
      var datas = {
        treeType: data.category.value
      }
      const { data: list } = await $api.rongCloud.getTxlist(datas)
      data.groupSource = data.groupSource.concat(list)
      showGroup(list)
    }
    // 展示分组
    const showGroup = (_list) => {
      var nowId = data.level.data[data.level.data.length - 1].id
      console.log(nowId)
      const newData = []
      data.groupList = []
      _list.forEach(element => {
        var item = element
        // var itemData = element
        // item.id = itemData.id || ''// id
        // item.sort = itemData.sort || ''// sort
        // item.name = itemData.name || ''// 名字
        // item.total = itemData.total || '0'// 人数
        // var parentId = itemData.parentId || '0'
        // var children = itemData.children || []
        // console.log(parentId)
        // eslint-disable-next-line eqeqeq
        // if (parentId == nowId) { // 是当前一级就添加到组里面
        newData.push(item)
        // } else {
        //   children.reverse()
        //   showGroup(children)
        // }
      })
      data.groupList = data.groupList.concat(newData)
      data.groupList.sort(function (a, b) {
        return a.sort - b.sort
      })
    }
    const openDetails = (row) => {
      console.log(row)
      router.push({ name: 'addressBookDetails', query: { id: row.id } })
    }
    // 点击级别
    const clickLevel = (_item) => {
      var nLevel = []
      for (var i = 0; i < data.level.data.length; i++) {
        var nItem = data.level.data[i]
        console.log(nItem)
        nLevel.push(nItem)
        if (nItem.id === _item.id) {
          data.level.data = nLevel
          // setTimeout(function () {
          //   navigation.refresh()
          //   navigation.scrollToElement($api.domAll('#navigation .header_css div')[that.level.data.length - 1], 200, true)
          // }, 100)
          // T.refreshHeaderLoading()
          if (_item.children && _item.children.length >= 0) {
            showGroup(_item.children)
          }
          onRefresh()
          return
        }
      }
    }
    const clickGroup = (_item) => {
      var nItem = [{ id: _item.id, name: _item.name, children: _item.children }]
      console.log(_item.children)
      showGroup(_item.children)
      // data.level.data.push(nItem)
      data.level.data = data.level.data.concat(nItem)
      console.log(data.level.data)
      // setTimeout(function () {
      //   navigation.refresh()
      //   navigation.scrollToElement($api.domAll('#navigation .header_css div')[data.level.data.length - 1], 200, true)
      // }, 100)
      setTimeout(() => {
        onRefresh()
      }, 100)
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.groupSource = []
      data.userList = []
      data.loading = true
      data.finished = false
      data.show = false
      data.ifSearch = !!data.keyword
      if (data.ifSearch) {
        data.level.data = [{ name: '全部', id: 0 }]
      }
      if (data.level.data.length <= 1 && !data.ifSearch) { // 分组时  只用获取一次  并且不是搜索的时候
        getData()
        data.loading = false
        data.refreshing = false
        data.finished = true
      } else {
        getChaters()
      }
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getChaters()
    }

    const clickUser = (_item, _details) => {
      if (data.ifReturn && !_details) {
        // eslint-disable-next-line eqeqeq
        if (data.ifYourself && data.userId == _item.id) {
          Toast('不能选择自己！')
          return
        }
        var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
        // 在就删除这个
        if (nItem) {
          if (!nItem.notDel) { // 为非不可删除时才能删除 否则不能动
            $general.delItemForKey(nItem, data.selectUser, 'id')
            var flag = false
            data.userList.forEach(item => {
              const checkitem = $general.getItemForKey(item.id, data.selectUser, 'id')// 找出这个对象看在不在
              // 在就删除这个
              if (checkitem) {
                if (!checkitem.notDel) {
                  data.isHaveCheckUser = true
                  flag = true
                }
              } else {
                if (!flag) { data.isHaveCheckUser = false }
              }
            })
          }
        } else {
          // eslint-disable-next-line eqeqeq
          if (data.selectMax != 0 && data.selectMax == data.selectUser.length) {
            Toast('最多只能选择' + data.selectMax + '人')
            return
          }
          data.isHaveCheckUser = true
          data.selectUser.push({ id: _item.id, name: _item.name, url: _item.url })
        }
        data.checked = []
        data.selectUser.forEach(element => {
          data.checked.push(element.id)
        })
      } else {
        // eslint-disable-next-line eqeqeq
        if (data.switchs.value == '-1') _item.province = true
        router.push({ name: 'personData', query: { id: _item.id } })
      }
    }
    // 设置选中状态
    const isSelect = (_item) => {
      console.log(_item.id)
      return $general.getItemForKey(_item.id, data.selectUser, 'id')
    }
    // 设置不可选的状态
    const isDisabled = (_item) => {
      return $general.getItemForKey(_item.id, data.selectUser, 'id').notDel
    }
    const checkAll = () => {
      if (data.isHaveCheckUser) {
        data.userList.forEach(function (_item, index) {
          var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
          // 在就删除这个
          if (nItem) {
            if (!nItem.notDel) { // 为非不可删除时  才能删除 否则不能动
              $general.delItemForKey(nItem, data.selectUser, 'id')
            }
          }
        })
      } else {
        data.userList.forEach(function (_item, index) {
          var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
          // 在就删除这个
          if (nItem) {
          } else {
            if (data.selectMax !== 0 && data.selectMax === data.selectUser.length) {
              Toast('最多只能选择' + data.selectMax + '人')
              return
            }
            if (data.ifYourself && data.user.id === _item.id) {
            } else {
              data.selectUser.push({ id: _item.id, name: _item.name, url: _item.url })
            }
          }
        })
      }
      data.checked = []
      data.selectUser.forEach(element => {
        data.checked.push(element.id)
      })
      data.isHaveCheckUser = !data.isHaveCheckUser
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), $general, onRefresh, onLoad, onClickLeft, openDetails, btnSearch, switchCategory, clickGroup, clickLevel, clickUser, isSelect, isDisabled, checkAll }
  }
}
</script>
<style lang="less" scoped>
.addressBook {
  width: 100%;
  background: #f9f9f9;
  /*通讯录 选择的人**************************************************************/
  .sel_warp {
    padding: 5px;
    background: #fff;
    box-sizing: border-box;
    width: 100%;
    white-space: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .sel_warp .sel_item {
    width: 70px;
    text-align: center;
    display: inline-block;
    position: relative;
    margin: 10px 5px;
    box-sizing: border-box;
  }
  .sel_warp .sel_item .sel_name {
    color: #555;
    margin-top: 5px;
  }
  .sel_warp .sel_del {
    position: absolute;
    top: -5px;
    right: 2px;
    z-index: 3;
  }
  .search_warp {
    background: #f8f8f8;
    .search_input {
      padding-left: 10px;
    }
  }
  .contacts_icon_body {
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #ccc;
    z-index: 5;
  }
  .contacts_icon_item {
    width: 100%;
    padding: 8px 0;
    box-sizing: border-box;
  }
  .contacts_icon_item_name {
    padding: 4px 16px;
    border-radius: 13px;
    text-align: center;
  }
  .contacts_icon_item div {
    margin-top: 3px;
  }

  .flex-wrap {
    border-bottom: 1px solid #ccc;
    background: #fff;
  }
  .flex-con {
    width: 0;
    height: 45px;
    overflow: hidden;
    position: relative;
    -webkit-overflow-scrolling: touch;
    touch-action: pan-y;
  }
  .header_css {
    white-space: nowrap;
    line-height: 45px;
    position: absolute;
    padding: 0 10px;
  }
  .header_css div {
    line-height: 45px;
    color: #666;
    font-size: 16px;
    display: inline-block;
  }
  .header_css div:last-child {
    color: #333;
  }
  .header_css div:first-child {
    color: #666;
  }

  .user_item,
  .group_item {
    border-bottom: 1px solid #ccc;
    background: #fff;
    width: 100%;
    padding: 12px;
    position: relative;
    box-sizing: border-box;
  }
  .user_item {
    padding: 6px 10px;
  }
  .group_img,
  .user_img {
    margin-right: 10px;
  }
  .user_name {
    color: #333333;
    margin-left: 14px;
  }
  .checkAllBtn {
    background: #fff;
    margin-right: 16px;
    padding: 10px 0;
  }
  .province_menu {
    width: 33.33%;
    overflow: auto;
  }
  .province_item {
    background: #f8f8f8;
    padding: 12px 15px;
  }
  body .van-tab--active {
    font-weight: 600;
  }
}
</style>

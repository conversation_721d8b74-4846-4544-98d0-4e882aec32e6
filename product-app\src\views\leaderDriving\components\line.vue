<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'line',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: Nav<PERSON><PERSON>,
    [Sticky.name]: <PERSON><PERSON>
  },
  props: {
    color: String,
    id: String,
    list: Array,
    status: String
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      var chartDom = document.getElementById(props.id)
      myChart = echarts.init(chartDom)
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
      data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
      setOptions()
    })
    watch(props, (val) => {
      if (val.status && props.id === 'line1') {
        setOptions()
      } else if (val.status && props.id === 'line2') {
        setOptions()
      } else if (val.status && props.id === 'line3') {
        setOptions()
      } else if (val.status && props.id === 'line4') {
        setOptions()
      }
    }, { deep: true })
    const setOptions = () => {
      // console.log(parseInt(data.viewportWidth * 0.03))
      var options = {
        grid: {
          top: '7%',
          left: '4%',
          right: '0%',
          bottom: '1%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis', // 触发类型——坐标轴
          // 鼠标移入条目下面的背景
          axisPointer: {
            type: 'line',
            z: 0,
            lineStyle: {
              type: 'solid',
              color: 'rgba(225,225,225,.3)',
              width: 18
            }
          },
          formatter: function (params) {
            return `${params[0].name}<br>${params[0].marker}人数：${params[0].data.text}<br>活跃度：${params[0].data.value}%`
          }
        },
        xAxis: {
          type: 'category',
          data: ['9-7', '9-8', '9-9', '9-10', '9-11', '9-12', '9-13'],
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#B0BCC2',
              width: '0'
            }
          },
          axisLabel: {
            interval: 0, // 坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            rotate: 38 // 调整数值改变倾斜的幅度（范围-90到90）
          },
          axisTick: {
            show: false
          },
          splitLine: { show: '' }
        },
        yAxis: {
          type: 'value',
          splitLine: { // 网格线
            show: false // 隐藏或显示
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#DCE4E8',
              width: '1'
            },
            show: true
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            formatter: '{value} %'
          }
        },
        series: [
          {
            data: [9, 10, 10, 10, 5, 6, 7],
            type: 'line',
            smooth: true,
            // symbol: 'none',
            // symbol:none, // 去掉小圆点
            showSymbol: false, // 是否默认展示圆点
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#57D764' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(253, 254, 255)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            itemStyle: {
              normal: {
                color: '#57D764' // 改变折线点的颜色
              }
            },
            lineStyle: {
              color: '#57D764'
            }
          }
        ]
      }
      var options3 = {
        grid: {
          top: '10%',
          left: '4%',
          right: '0%',
          bottom: '1%',
          containLabel: true
        },
        legend: { right: 10, top: -15, padding: [15, 0, 10, 0], data: ['机关', '代表'] },
        tooltip: {
          trigger: 'axis', // 触发类型——坐标轴
          // 鼠标移入条目下面的背景
          axisPointer: {
            type: 'line',
            z: 0,
            lineStyle: {
              type: 'solid',
              color: 'rgba(225,225,225,.3)',
              width: 18
            }
          },
          formatter: params => {
            var resultData = params[0]
            var outputData = params[1]
            if (resultData && outputData) {
              var tipsHtml = `<div style="width:fit-content">
                <div style="margin-bottom:6px;font-size:14px;">${resultData.name}</div>
                <div style="display:flex;margin-bottom:2px">
                  <div><span style="width:10px;height:10px;border-radius: 50%;display:inline-block;background-color:${resultData.color}"></span></div>
                  <div style="padding:0 16px 0 6px;width:auto;font-size:14px;">机关人数：${resultData.data.text}</div>
                  <div style="color:rgb(102,102,102);font-size:14px;">活跃度：${resultData.data.value}%</div>
                </div>
                <div style="display:flex;">
                  <div><span style="width:10px;height:10px;border-radius: 50%;display:inline-block;background-color:${outputData.color}"></span></div>
                  <div style="padding:0 16px 0 6px;width:auto;font-size:14px;">代表人数：${outputData.data.text}</div>
                  <div style="text-align:right;color:rgb(102,102,102);font-size:14px;">活跃度：${outputData.data.value}%</div>
                </div>
              </div>`
              return tipsHtml
            } else if (resultData) {
              return `${params[0].name}<br>${params[0].marker}人数:${params[0].data.text}<br>活跃度：${params[0].data.value}%`
            } else if (outputData) {
              return `${params[1].name}<br>${params[1].marker}人数:${params[1].data.text}<br>活跃度：${params[1].data.value}%`
            } else {
              return false
            }
          }
        },
        xAxis: {
          type: 'category',
          data: ['9-7', '9-8', '9-9', '9-10', '9-11', '9-12', '9-13'],
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#B0BCC2',
              width: '0'
            }
          },
          axisLabel: {
            interval: 0, // 坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            rotate: 38 // 调整数值改变倾斜的幅度（范围-90到90）
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          splitLine: { // 网格线
            show: false // 隐藏或显示
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#DCE4E8',
              width: '1'
            },
            show: true
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            formatter: '{value} %'
          }
        },
        series: [
          {
            name: '机关',
            data: [200, 232, 422, 833, 544, 455, 766],
            type: 'line',
            smooth: true,
            // symbol: 'none',
            showSymbol: false, // 是否默认展示圆点
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#3894FF' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(56,148,255,0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            itemStyle: {
              normal: {
                color: '#3894FF' // 改变折线点的颜色
              }
            },
            lineStyle: {
              color: '#3894FF'
            }
          },
          {
            name: '代表',
            data: [140, 832, 722, 433, 244, 555, 366],
            type: 'line',
            smooth: true,
            // symbol: 'none',
            showSymbol: false, // 是否默认展示圆点
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#FFD056' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(255,255,255,0)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            itemStyle: {
              normal: {
                color: '#FFD056' // 改变折线点的颜色
              }
            },
            lineStyle: {
              color: '#FFD056'
            }
          }
        ]
      }
      nextTick(() => {
        if (props.id === 'line1') {
          options.series[0].data = props.list.map(v => {
            return {
              text: v.num,
              value: parseFloat(v.activation)
            }
          })
          options.xAxis.data = props.list.map(item => item.name)
          myChart.setOption(options)
        } else if (props.id === 'line2') {
          options.series[0].itemStyle.normal.color = '#3894FF'
          options.series[0].lineStyle.color = '#3894FF'
          options.series[0].areaStyle.color.colorStops[0].color = '#3894FF'
          options.series[0].data = props.list.map(v => {
            return {
              text: v.num,
              value: parseFloat(v.activation)
            }
          })
          options.xAxis.data = props.list.map(item => item.name)
          myChart.setOption(options)
        } else if (props.id === 'line3') {
          options3.series[0].data = props.list.map(v => {
            return {
              text: v.numOff,
              value: parseFloat(v.activationOff)
            }
          })
          options3.series[1].data = props.list.map(v => {
            return {
              text: v.numMem,
              value: parseFloat(v.activationMem)
            }
          })
          options3.xAxis.data = props.list.map(item => item.name)
          myChart.setOption(options3)
        } else if (props.id === 'line4') {
          options.series[0].itemStyle.normal.color = '#FF7D7D'
          options.series[0].lineStyle.color = '#FF7D7D'
          options.series[0].areaStyle.color.colorStops[0].color = '#FF7D7D'
          options.series[0].data = props.list.map(v => {
            return {
              text: v.num,
              value: parseFloat(v.activation)
            }
          })
          options.xAxis.data = props.list.map(item => item.name)
          myChart.setOption(options)
        }
      })
    }
    return { ...toRefs(data), general, confirm }
  }
}
</script>
<style lang="less" scoped>
#line1 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
 }
 #line2 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
 }
 #line3 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
 }
 #line4 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
 }
</style>

import { HTTP } from '../http.js'
class cloudDisk extends HTTP {
  // 下载文件流
  download (url, params) {
    return this.request({
      url: url,
      responseType: 'arraybuffer', // 表明返回服务器返回的数据类型
      data: params,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json;application/octet-stream'
      }
    })
  }

  // 通用post
  generalPost (url, params) {
    return this.request({
      url: url,
      data: params
    })
  }

  // 通用get
  generalGet (url, params) {
    return this.request({
      url: url,
      data: params,
      method: 'GET'
    })
  }

  // 文件列表
  cloudDiskFileList (url, params) {
    return this.request({
      url: url,
      data: params,
      method: 'GET'
      // header: header
    })
  }

  // 收件箱未读
  cloudDiskInbox (url, params) {
    return this.request({
      url: url + '/inbox/getUnReadNum',
      data: params,
      method: 'GET'
    })
  }

  // 新建文件夹
  cloudDiskAddFile (url, params) {
    return this.request({
      url: url,
      data: params
    })
  }

  // 上传附件
  cloudDiskUploadFile (url, params) {
    console.log(params)
    return this.request({
      url: url,
      data: params
    })
  }
}
export {
  cloudDisk
}

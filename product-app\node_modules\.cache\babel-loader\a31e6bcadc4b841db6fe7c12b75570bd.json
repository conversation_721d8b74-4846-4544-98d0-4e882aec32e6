{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue?vue&type=template&id=6c3331de&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue", "mtime": 1756438284587}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\babel.config.js", "mtime": 1754028950133}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IFsiaWQiXTsKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCB7CiAgICBpZDogJHByb3BzLmlkCiAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfMSk7Cn0="}, {"version": 3, "names": ["_createElementBlock", "id", "$props", "_hoisted_1"], "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue"], "sourcesContent": ["<template>\r\n  <div :id=\"id\">\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nimport { debounce } from '../../../utils/debounce.js'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'\r\nexport default {\r\n  name: 'pie',\r\n  components: {\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: <PERSON><PERSON>\r\n  },\r\n  props: {\r\n    color: String,\r\n    id: String,\r\n    list: Array\r\n  },\r\n  setup (props) {\r\n    const route = useRoute()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    // const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      viewportWidth: ''\r\n    })\r\n    var myChart = null\r\n    onMounted(() => {\r\n      nextTick(() => {\r\n        var chartDom = document.getElementById(props.id)\r\n        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth\r\n        myChart = echarts.init(chartDom)\r\n        setOptions()\r\n      })\r\n      // 监听窗口尺寸变化事件\r\n      window.addEventListener('resize', debounce(() => {\r\n        myChart.resize() // 调整图表大小\r\n        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth\r\n        setOptions()\r\n      }, 500))\r\n    })\r\n    const setOptions = () => {\r\n      // console.log(parseInt(data.viewportWidth * 0.04))\r\n      var options = {\r\n        legend: {\r\n          orient: 'horizontal', // 或 'horizontal'\r\n          bottom: -parseInt(data.viewportWidth * 0.013),\r\n          left: 'center',\r\n          width: parseInt(data.viewportWidth * 0.9),\r\n          height: parseInt(data.viewportWidth * 0.4),\r\n          padding: [parseInt(data.viewportWidth * 0.1), parseInt(data.viewportWidth * 0.01)],\r\n          // 设置图例文字的样式\r\n          textStyle: {\r\n            fontSize: parseInt(data.viewportWidth * 0.03),\r\n            color: '#ADADAD'\r\n          },\r\n          formatter: function (name) { // 该函数用于设置图例显示后的百分比\r\n            var total = 0\r\n            var value\r\n            props.list.map(v => {\r\n              return {\r\n                value: v.value,\r\n                name: v.name\r\n              }\r\n            }).forEach((item) => {\r\n              total += Number(item.value)\r\n              if (item.name === name) {\r\n                value = item.value\r\n              }\r\n            })\r\n            var p = Math.round(((value / total) * 100)) // 求出百分比\r\n            return `${name} | ${p}%` // 返回出图例所显示的内容是名称+百分比\r\n          },\r\n          itemWidth: parseInt(data.viewportWidth * 0.04),\r\n          itemHeight: parseInt(data.viewportWidth * 0.03)\r\n        },\r\n        color: ['#3893ff', '#a938fb', '#ffd434', '#ff7389', '#17c78a', '#ff7a2d'],\r\n        series: [\r\n          {\r\n            name: '代表年龄分析',\r\n            type: 'pie',\r\n            startAngle: 20, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.04) + '%', parseInt(data.viewportWidth * 0.1) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.015),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.009)\r\n            },\r\n            label: {\r\n              bleedMargin: 10,\r\n              show: true,\r\n              position: 'outside',\r\n              // data: ['f'],\r\n              formatter: function (params) {\r\n                return `${params.name}\\n${params.value}人`\r\n              },\r\n              fontSize: parseInt(data.viewportWidth * 0.03),\r\n              alignTo: 'labelLine'\r\n            },\r\n            labelLine: {\r\n              show: true,\r\n              length: parseInt(data.viewportWidth * 0.05),\r\n              length2: parseInt(data.viewportWidth * 0.12)\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine' },\r\n              { value: 735, name: 'Direct' },\r\n              { value: 580, name: 'Email' },\r\n              { value: 484, name: 'Union Ads' },\r\n              { value: 300, name: 'Video Ads' },\r\n              { value: 200, name: 'ghjkgjkAds' }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      var options2 = {\r\n        tooltip: {\r\n          trigger: 'item'\r\n        },\r\n        series: [\r\n          {\r\n            name: '性别',\r\n            type: 'pie',\r\n            startAngle: 60, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.11) + '%', parseInt(data.viewportWidth * 0.18) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.015),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.01)\r\n            },\r\n            label: {\r\n              show: false\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine', itemStyle: { color: '#3da2ff' } },\r\n              { value: 735, name: 'Direct', itemStyle: { color: '#ff738c' } }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      var options3 = {\r\n        color: ['#67DBFF', '#FF61C9', '#64A2FF', '#FF9567', '#BCFF87', '#FF6D6D', '#61E89F', '#BC87FF', '#FFD056'],\r\n        series: [\r\n          {\r\n            name: '类型占比',\r\n            type: 'pie',\r\n            minAngle: 30,\r\n            // startAngle: 90, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.06) + '%', parseInt(data.viewportWidth * 0.12) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.0),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.008)\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'outside',\r\n              // data: ['f'],\r\n              formatter: function (params) {\r\n                return `${params.data.proportion}%\\n${params.name}`\r\n              },\r\n              fontSize: parseInt(data.viewportWidth * 0.024),\r\n              alignTo: 'labelLine',\r\n              rich: {\r\n                name: {\r\n                  fontSize: 14,\r\n                  color: '#333',\r\n                  align: 'center'\r\n                },\r\n                value: {\r\n                  fontSize: 12,\r\n                  color: '#999'\r\n                }\r\n              }\r\n            },\r\n            labelLayout: function (params) {\r\n              const isLeft = params.labelRect.x < myChart.getWidth() / 2\r\n              const points = params.labelLinePoints\r\n              points[2][0] = isLeft\r\n                ? params.labelRect.x\r\n                : params.labelRect.x + params.labelRect.width\r\n              return {\r\n                labelLinePoints: points\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: true,\r\n              length: parseInt(data.viewportWidth * 0.08),\r\n              length2: parseInt(data.viewportWidth * 0.1)\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine' },\r\n              { value: 735, name: 'Direct' },\r\n              { value: 580, name: 'Email' },\r\n              { value: 484, name: 'Union Ads' },\r\n              { value: 300, name: 'Video Ads' },\r\n              { value: 200, name: 'ghjkgjkAds' }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      nextTick(() => {\r\n        if (props.id === 'pie1') {\r\n          options.series[0].data = props.list\r\n          myChart.setOption(options)\r\n        } else if (props.id === 'pie2') {\r\n          options2.series[0].data = props.list\r\n          myChart.setOption(options2)\r\n        } else if (props.id === 'pie3') {\r\n          options3.series[0].data = props.list\r\n          myChart.on('click', (e) => {\r\n            // console.log(e)\r\n            // window.location.href = e.data.url\r\n          })\r\n          myChart.setOption(options3)\r\n        }\r\n      })\r\n    }\r\n    return { ...toRefs(data), general }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#pie1 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 260px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#pie2 {\r\n  width: 100%;\r\n  height: 100%;\r\n  // margin: 10px 0;\r\n  box-sizing: border-box;\r\n  background: #fff;\r\n}\r\n\r\n#pie3 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 220px;\r\n  margin: 10px 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#pie4 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 100%;\r\n  // margin: 10px 0;\r\n  box-sizing: border-box;\r\n}\r\n</style>\r\n"], "mappings": ";;;uBACEA,mBAAA,CACM;IADAC,EAAE,EAAEC,MAAA,CAAAD;EAAE,wBAAAE,UAAA", "ignoreList": []}]}
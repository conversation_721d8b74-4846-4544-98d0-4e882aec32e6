<template>
  <div class="performanceFilesList">
    <van-nav-bar v-if="isShowHead" :title="title" fixed placeholder safe-area-inset-top left-text="" left-arrow
      @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <div class="" v-if="dataList.length != 0">
        <div style="position: fixed;width:100%;z-index: 1;">
          <div class="performance_ranking_imgbox"
            :style="'background: url(' + (SYS_IF_ZX ? require('../../assets/img/icon_performance_ranking1.png') : require('../../assets/img/bg_performance_ranking_rd.png')) + ') no-repeat;background-size: 100% 100%;'">
            <div style="background:#fff;position:absolute;top:10px;right:10px;color:#fff;border-radius:5px;">
              <van-dropdown-menu class="search-dropdown-menu flex_box flex_align_center" :active-color="appTheme"
                :style="$general.loadConfiguration(-3) + 'max-width:100px;'">
                <van-dropdown-item @change="onRefresh();" v-model="years.value" get-container="#search"
                  :options="years.dataFiter"></van-dropdown-item>
              </van-dropdown-menu>
            </div>
            <!--再来放文字人头-->
            <div class="performance_ranking_box">
              <div class="flex_box flex_align_center flex_justify_content">
                <div>
                  <div class="flex_box flex_align_end" style="margin-bottom: 7px;">
                    <!--第二名-->
                    <div v-for="(item, index) in [2, 1, 3]" :key="index"
                      class="station_item T-flexbox-vertical flex_align_center flex_justify_content"
                      :style="index == 1 ? 'padding-bottom:7px;margin:0 17px;' : ''">
                      <template v-if="dataList.length >= item">
                        <div @click="openDetails(dataList[item - 1])" class="user_radius station_img_box"
                          :style="'background:#fff;' + 'border-radius: 50%;' + $general.loadConfigurationSize(index == 1 ? 44 : 34) + 'border:' + ((index == 1 ? '3' : '2')) + 'px solid ' + (index == 1 ? '#FFC934' : (index == 0 ? '#C3D0E1' : '#D88904')) + ';'">
                          <div class="station_num_box flex_box flex_align_center flex_justify_content">
                            <div :style="{ 'background': ['#FFC934', '#C3D0E1', '#D88904'][item - 1] }"
                              class="station_num flex_box flex_align_center flex_justify_content">
                              {{ dataList[item - 1].score }}
                              <!-- <van-icon :size="(appFontSize)+'px'"
                                        :color="['#FFC934','#C3D0E1','#D88904'][item-1]"
                                        :name="'medal'"></van-icon> -->
                            </div>
                          </div>
                          <img class="user_radius"
                            style="width: 100%;height: 100%;object-fit: contain;border-radius: 50%; "
                            :src="dataList[item - 1].url" />
                        </div>
                        <div :style="$general.loadConfiguration(-1) + 'font-size:14px;'" class="station_name text_one2">
                          {{ dataList[item - 1].username }}</div>
                        <!-- <div :style="$general.loadConfiguration(-4)+'color:#FFF;margin-top:3px;font-size:12px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'"
                             class="text_one2"
                             v-html="dataList[item-1].delegationview"></div> -->
                        <!-- <div class="station_ci flex_box flex_align_center"
                             :style="(index==1?'margin-top:23px;':'')">
                          <img :style="$general.loadConfigurationSize(-4,'h')"
                               class="station_ci_icon"
                               src="../../assets/img/icon_performance_ranking.png"
                               alt="" />
                          <div :style="$general.loadConfiguration(-3)"
                               style="white-space:nowrap">
                            {{dataList[item-1].score}}</div>
                        </div> -->
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--搜索-->
          <div id="search" class="search_box" :style="$general.loadConfiguration()">
            <div class="search_warp flex_box">
              <div @click="onRefresh();" class="search_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="(appFontSize) + 'px'" :color="'#666'" class-prefix="icon" :name="'sousuo'"></van-icon>
              </div>
              <form class="flex_placeholder flex_box flex_align_center search_input" action="javascript:return true;">
                <input class="flex_placeholder" :style="$general.loadConfiguration(-1)" :placeholder="seachPlaceholder"
                  maxlength="100" type="text" @keyup.enter="onRefresh()" v-model="seachText" />
                <div v-if="seachText" @click="seachText = ''; btnSearch();"
                  class="search_btn flex_box flex_align_center flex_justify_content">
                  <van-icon :size="(appFontSize) + 'px'" :color="'#ccc'" :name="'clear'"></van-icon>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class="bed_hedgehopping"></div>
      </div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
          <!--数据列表-->
          <ul v-if="dataList.length != 0" class="vue_newslist_box">
            <li v-for="(item, index) in dataList" :key="index">
              <div @click="openDetails(item)" class="flex_box flex_align_center van-hairline--bottom click"
                style="padding: 14px 15px 14px 10px;background: #FFF;" :style="item.style">
                <div v-if="item.isMe" class="rankMe" :style="$general.loadConfiguration(-4) + 'background:' + appTheme">
                  我
                </div>
                <div v-if="item.ranking" style="min-width: 40px;margin-right:12px;"
                  class="flex_box flex_justify_content">
                  <div v-if="item.ranking > 3"
                    :style="$general.loadConfiguration(-1) + 'font-weight: bold;color:#848493;'"
                    v-html="(item.ranking < 10 ? '0' : '') + item.ranking"></div>
                  <van-icon v-else :size="(appFontSize + 10) + 'px'"
                    :color="['#ffeb43', '#e4e7e7', '#ff9265'][item.ranking - 1]" :name="'medal'"></van-icon>
                </div>
                <div class="user_radius" :style="$general.loadConfigurationSize(26) + 'margin-right:10px;'">
                  <img style="width: 100%;height: 100%;object-fit: contain;" :src="item.url" />
                </div>
                <div class="flex_placeholder_name">
                  <div :style="$general.loadConfiguration(-1) + 'color:#333;font-weight: 600;'">{{ item.name }}</div>
                  <div :style="$general.loadConfiguration(-3) + 'color:#666;margin-top:2px;'" v-html="item.score">
                  </div>
                </div>
                <div class="flex_placeholder_num">
                  <van-icon name="good-job-o" :color="item.num == 0 ? '#000' : appTheme"
                    @click.stop="clickNum(index)" />
                  <div style="width: 5px;"></div>
                  <div :style="$general.loadConfiguration(-1) + 'color:' + (item.num == 0 ? '#000' : appTheme) + ';'">{{
                    item.num }}
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'performanceFilesList',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      appFontSize: $general.data.appFontSize,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId') || '',
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      seachTextAjax: false,
      pageNo: 1,
      pageSize: 1000,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },

      myDuty: {}, // 我的排名
      years: { value: '', data: [], dataFiter: [] },
      delegation: { value: '', data: [{ text: '所有', value: '' }] }

    })
    onMounted(() => {
      var nowYear = new Date().getFullYear()
      data.years.dataFiter = [{ text: '年度积分', value: nowYear }, { text: '总积分', value: '' }]
      data.years.value = nowYear
      getdelegation()
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const getdelegation = async () => {
      const res = await $api.activity.dictionaryPubkvs({ types: data.SYS_IF_ZX ? 'dele_type' : 'representer_team' })
      if (res) {
        var datas = res ? res.data || {} : {}
        datas = datas[data.SYS_IF_ZX ? 'dele_type' : 'representer_team'] || []
        data.delegation.data = [{ text: '所有', value: '' }]
        datas.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          data.delegation.data.push({ text: _eItem.value, value: _eItem.id })
        })
        if (data.delegation.value && !$general.getItemForKey(data.delegation.value, data.delegation.data, 'value')) {
          data.delegation.value = ''
          data.getData(0)
        }
      }
    }

    const getList = async () => {
      const param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText,
        year: data.years.value,
        areaId: data.areaId
      }
      const res = await $api.performanceFiles.getGenerateDuty(param)
      var { data: list } = res
      data.seachTextAjax = !!data.seachText
      const newData = []
      list.dutyNumListVos.forEach(item => {
        const _eItem = item
        _eItem.id = item.id
        _eItem.name = item.username
        _eItem.delegationView = item.delegationView || item.dele || item.delegation || '&nbsp;'
        _eItem.url = item.headimg
        _eItem.score = item.score
        _eItem.num = item.total || item.count || '0'
        _eItem.ranking = item.rank || ''
        _eItem.isMe = item.id === data.user.id
        _eItem.style = ''
        _eItem.relateType = data.relateType
        newData.push(_eItem)
      })
      var myDuty = res.data.myDuty || {}
      if (myDuty.userId && data.pageNo === 1) { // 如果有排名 并且是下拉刷新的时候
        window.hasMy = true
        var item = {}
        item.isMe = true
        item.style = 'margin-bottom:0.1rem;'
        item.relateType = data.relateType
        data.myDuty = item
        // newData.splice(3, 0, data.myDuty)
      }
      data.dataList = data.dataList.concat(newData)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      data.finished = true
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      // getList()
    }
    const openDetails = (rows) => {
      console.log(rows)
      // router.push({ name: 'performanceFiles', query: { id: rows.id, title: rows.name, year: data.years.value } })
      router.push({ name: 'resumption', query: { id: rows.userid, uid: rows.id, title: '履职详情', year: data.years.value } })
    }
    const clickNum = (e) => {
      console.log(e)
      if (data.dataList[e].num === '0') {
        data.dataList[e].num = '1'
      } else {
        data.dataList[e].num = '0'
      }
    }
    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, clickNum, confirm, openDetails }
  }
}
</script>
<style lang="less" scoped>
.performanceFilesList {
  background: #f8f8f8;

  .search_box {
    background: #ffffff;
  }

  .performance_ranking_imgbox {
    width: 100%;
    height: 300px;
    position: relative;
  }

  .bed_hedgehopping {
    height: 350px;
  }

  .flex_placeholder_name {
    width: 48%;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .performance_ranking_box {
    position: absolute;
    bottom: 100px;
    width: 100%;
  }

  .station_item {
    width: 96px;
  }

  .station_name {
    color: #fff;
    margin-top: 18px;
  }

  .station_img_box {
    overflow: visible;
    position: relative;
  }

  .flex_placeholder_num {
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: right;
  }

  .station_num_box {
    position: absolute;
    bottom: -15px;
    left: 0;
    right: 0;
  }

  .station_num {
    // width: 30px !important;
    // height: 16px !important;
    padding: 1px 5px;
    background: #ffffff;
    font-size: 14px;
    border-radius: 5px;
  }

  .station_ci {
    margin-top: 15px;
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
    border-radius: 12px;
    padding: 2px 12px;
    color: #fff;
    line-height: 1.2;
  }

  .station_ci_icon {
    margin-right: 3px;
  }

  .header {
    position: fixed;
    width: 100%;
    z-index: 1;
  }

  .title_name {
    font-weight: bold;
    color: #fefefe;
    margin-right: 5px;
  }

  .rankMe {
    position: absolute;
    right: 0;
    top: 0;
    padding: 1px 8px;
    color: #ffffff;
    border-bottom-left-radius: 5px;
  }
}
</style>

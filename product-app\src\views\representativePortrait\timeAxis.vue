<template>
  <div class="timeAxis">
    <van-swipe class="container_swipe"
               vertical
               duration="1500"
               :show-indicators="false"
               :loop="false"
               :autoplay="80000">
      <van-swipe-item>
        <div class="pages"
             id="pages1"
             :class="{ active: isPages1Visible }">
          <img src="../../assets/img/timeAxis/g_bg001.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item1">
            <div class="pages_item1_text1">尊敬的<span>{{userName}}</span>代表</div>
            <div class="pages_item1_text2">您好</div>
            <div class="pages_item1_text3">感谢您登陆<span>青岛人大APP</span></div>
            <div class="pages_item1_text4">2023<span>年</span></div>
            <div class="pages_item1_text5">是青岛人大APP正式上线运行的<span>第一年</span></div>
            <div class="pages_item1_text6">这一年</div>
            <div class="pages_item1_text7">青岛人大APP<span>陪伴您度过了</span></div>
            <div class="pages_item1_text8">每一个履职时光</div>
            <div class="pages_item1_text8">记录下了您的每一次履职实践</div>
          </div>
          <div class="pages1_gif1">
            <img src="../../assets/img/timeAxis/g_gif001.gif"
                 alt=""
                 style="width:100%;height:100%;">
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
      <van-swipe-item>
        <div class="pages"
             id="pages2"
             :class="{ active: isPages2Visible }">
          <img src="../../assets/img/timeAxis/g_bg002.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item2">
            <div class="pages_item2_yearMonthDay">
              <img src="../../assets/img/timeAxis/g_year.png"
                   alt=""
                   style="width:1.8rem;height:1.1rem;">年
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">月
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">日
              <div class="year2">{{year2}}</div>
              <div class="month2">{{month2}}</div>
              <div class="day2">{{day2}}</div>
            </div>
            <div class="pages_item2_text1">您第一次登录<span>青岛人大APP</span></div>
            <div class="pages_item2_text2">全年</div>
            <div class="pages_item2_text3">您登录青岛人大<span>{{loginTimes}}</span>次</div>
            <div class="pages_item2_text4">您最晚在线时间为</div>
            <div class="pages_item2_text5">{{ lastLogin }}</div>
            <div class="pages_item2_text6">{{ lastLoginTime }}</div>
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
      <van-swipe-item v-show="recommendationSubmitCount!==0">
        <div class="pages"
             id="pages3"
             :class="{ active: isPages3Visible }">
          <img src="../../assets/img/timeAxis/g_bg003.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item3">
            <div class="pages_item3_yearMonthDay">
              <img src="../../assets/img/timeAxis/g_year.png"
                   alt=""
                   style="width:1.8rem;height:1.1rem;">年
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">月
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">日
              <div class="year3">{{year3}}</div>
              <div class="month3">{{month3}}</div>
              <div class="day3">{{day3}}</div>
            </div>
            <div class="pages_item3_text1">您第一次提出<span>代表建议</span></div>
            <div class="pages_item3_text2">全年</div>
            <div class="pages_item3_text3">您共提出代表建议</div>
            <div class="pages_item3_text4">{{recommendationSubmitCount}}<span>条</span></div>
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
      <van-swipe-item>
        <div class="pages"
             id="pages4"
             :class="{ active: isPages4Visible }">
          <div style="position: relative;">
            <img src="../../assets/img/timeAxis/g_bg004.png"
                 alt=""
                 style="width:100%;height:100%;">
            <div class="pages_item4_numData">
              <div class="pages_item4_num1">{{publishDuty}}</div>
              <div class="pages_item4_num2">{{dutyFabulou}}</div>
            </div>
          </div>
          <div class="pages_item4">
            <div class="pages_item4_yearMonthDay">
              <img src="../../assets/img/timeAxis/g_year.png"
                   alt=""
                   style="width:1.8rem;height:1.1rem;">年
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">月
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">日
              <div class="year4">{{year4}}</div>
              <div class="month4">{{month4}}</div>
              <div class="day4">{{day4}}</div>
            </div>
            <div class="pages_item4_text1">您第一次发布<span>履职圈</span></div>
            <div class="pages_item4_text2">全年</div>
            <div class="pages_item4_text3">您共发布履职圈<span>{{publishDuty}}</span>条</div>
            <div class="pages_item4_text4">{{dutyFabulou}}<span>人次为您点赞</span></div>
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
      <van-swipe-item>
        <div class="pages"
             id="pages5"
             :class="{ active: isPages5Visible }">
          <img src="../../assets/img/timeAxis/g_bg005.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item5">
            <div class="pages_item5_yearMonthDay">
              <img src="../../assets/img/timeAxis/g_year.png"
                   alt=""
                   style="width:1.8rem;height:1.1rem;">年
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">月
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">日
              <div class="year5">{{year5}}</div>
              <div class="month5">{{month5}}</div>
              <div class="day5">{{day5}}</div>
            </div>
            <div class="pages_item5_text1">您第一次参与<span>意见征集</span></div>
            <div class="pages_item5_text2">全年</div>
            <div class="pages_item5_text3">您共参与意见征集<span>{{participateServer}}</span>次</div>
            <div class="pages_item5_text4">发表意见</div>
            <div class="pages_item5_text5">{{publishServerTownhallAdvice}}<span>次</span></div>
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
      <van-swipe-item>
        <div class="pages"
             id="pages6"
             :class="{ active: isPages6Visible }">
          <img src="../../assets/img/timeAxis/g_bg006.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item6">
            <div class="pages_item6_yearMonthDay">
              <img src="../../assets/img/timeAxis/g_year.png"
                   alt=""
                   style="width:1.8rem;height:1.1rem;">年
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">月
              <img src="../../assets/img/timeAxis/g_day.png"
                   alt=""
                   style="width:1rem;height:1.1rem;">日
              <div class="year6">{{year6}}</div>
              <div class="month6">{{month6}}</div>
              <div class="day6">{{day6}}</div>
            </div>
            <div class="pages_item6_text1">您第一次参加<span>线上履职学习</span></div>
            <div class="pages_item6_text2">全年</div>
            <div class="pages_item6_text3">您观看专题讲座<span>{{watchVideoTime}}</span>次</div>
            <div class="pages_item6_text4">阅读电子书籍<span>{{readBookTime}}</span>次</div>
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
      <van-swipe-item>
        <div class="pages"
             id="pages7"
             :class="{ active: isPages7Visible }">
          <img src="../../assets/img/timeAxis/g_bg007.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item7">
            <div class="pages_item7_text1">2024<span>年</span></div>
            <div class="pages_item7_text2">青岛人大APP</div>
            <div class="pages_item7_text3">将继续陪伴</div>
            <div class="pages_item7_text4">您的<span>履职之路</span></div>
          </div>
          <div class="more">
            <div class="drop">︽</div>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
    <!-- <div class="container">
      <div class="pages"
           id="pages1"
           :class="{ active: isPages1Visible }">
        <img src="../../assets/img/timeAxis/g_bg001.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item1">
          <div class="pages_item1_text1">尊敬的<span>{{userName}}</span>代表</div>
          <div class="pages_item1_text2">您好</div>
          <div class="pages_item1_text3">感谢您登陆<span>青岛人大APP</span></div>
          <div class="pages_item1_text4">2023<span>年</span></div>
          <div class="pages_item1_text5">是青岛人大APP正式上线运行的<span>第一年</span></div>
          <div class="pages_item1_text6">这一年</div>
          <div class="pages_item1_text7">青岛人大APP<span>陪伴您度过了</span></div>
          <div class="pages_item1_text8">每一个履职时光</div>
          <div class="pages_item1_text8">记录下了您的每一次履职实践</div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
      <div class="pages"
           id="pages2"
           :class="{ active: isPages2Visible }">
        <img src="../../assets/img/timeAxis/g_bg002.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item2">
          <div class="pages_item2_yearMonthDay">
            <img src="../../assets/img/timeAxis/g_year.png"
                 alt=""
                 style="width:1.8rem;height:1.1rem;">年
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">月
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">日
            <div class="year2">{{year2}}</div>
            <div class="month2">{{month2}}</div>
            <div class="day2">{{day2}}</div>
          </div>
          <div class="pages_item2_text1">您第一次登录<span>青岛人大APP</span></div>
          <div class="pages_item2_text2">全年</div>
          <div class="pages_item2_text3">您登录青岛人大<span>{{loginTimes}}</span>次</div>
          <div class="pages_item2_text4">您最晚在线时间为</div>
          <div class="pages_item2_text5">{{ lastLogin }}</div>
          <div class="pages_item2_text6">{{ lastLoginTime }}</div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
      <div class="pages"
           v-show="recommendationSubmitCount!==0"
           id="pages3"
           :class="{ active: isPages3Visible }">
        <img src="../../assets/img/timeAxis/g_bg003.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item3">
          <div class="pages_item3_yearMonthDay">
            <img src="../../assets/img/timeAxis/g_year.png"
                 alt=""
                 style="width:1.8rem;height:1.1rem;">年
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">月
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">日
            <div class="year3">{{year3}}</div>
            <div class="month3">{{month3}}</div>
            <div class="day3">{{day3}}</div>
          </div>
          <div class="pages_item3_text1">您第一次提出<span>代表建议</span></div>
          <div class="pages_item3_text2">全年</div>
          <div class="pages_item3_text3">您共提出代表建议</div>
          <div class="pages_item3_text4">{{recommendationSubmitCount}}<span>条</span></div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
      <div class="pages"
           id="pages4"
           :class="{ active: isPages4Visible }">
        <div style="position: relative;">
          <img src="../../assets/img/timeAxis/g_bg004.png"
               alt=""
               style="width:100%;height:100%;">
          <div class="pages_item4_numData">
            <div class="pages_item4_num1">{{publishDuty}}</div>
            <div class="pages_item4_num2">{{dutyFabulou}}</div>
          </div>
        </div>
        <div class="pages_item4">
          <div class="pages_item4_yearMonthDay">
            <img src="../../assets/img/timeAxis/g_year.png"
                 alt=""
                 style="width:1.8rem;height:1.1rem;">年
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">月
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">日
            <div class="year4">{{year4}}</div>
            <div class="month4">{{month4}}</div>
            <div class="day4">{{day4}}</div>
          </div>
          <div class="pages_item4_text1">您第一次发布<span>履职圈</span></div>
          <div class="pages_item4_text2">全年</div>
          <div class="pages_item4_text3">您共发布履职圈<span>{{publishDuty}}</span>条</div>
          <div class="pages_item4_text4">{{dutyFabulou}}<span>人次为您点赞</span></div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
      <div class="pages"
           id="pages5"
           :class="{ active: isPages5Visible }">
        <img src="../../assets/img/timeAxis/g_bg005.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item5">
          <div class="pages_item5_yearMonthDay">
            <img src="../../assets/img/timeAxis/g_year.png"
                 alt=""
                 style="width:1.8rem;height:1.1rem;">年
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">月
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">日
            <div class="year5">{{year5}}</div>
            <div class="month5">{{month5}}</div>
            <div class="day5">{{day5}}</div>
          </div>
          <div class="pages_item5_text1">您第一次参与<span>意见征集</span></div>
          <div class="pages_item5_text2">全年</div>
          <div class="pages_item5_text3">您共参与意见征集<span>{{participateServer}}</span>次</div>
          <div class="pages_item5_text4">发表意见</div>
          <div class="pages_item5_text5">{{publishServerTownhallAdvice}}<span>次</span></div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
      <div class="pages"
           id="pages6"
           :class="{ active: isPages6Visible }">
        <img src="../../assets/img/timeAxis/g_bg006.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item6">
          <div class="pages_item6_yearMonthDay">
            <img src="../../assets/img/timeAxis/g_year.png"
                 alt=""
                 style="width:1.8rem;height:1.1rem;">年
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">月
            <img src="../../assets/img/timeAxis/g_day.png"
                 alt=""
                 style="width:1rem;height:1.1rem;">日
            <div class="year6">{{year6}}</div>
            <div class="month6">{{month6}}</div>
            <div class="day6">{{day6}}</div>
          </div>
          <div class="pages_item6_text1">您第一次参加<span>线上履职学习</span></div>
          <div class="pages_item6_text2">全年</div>
          <div class="pages_item6_text3">您观看专题讲座<span>{{watchVideoTime}}</span>次</div>
          <div class="pages_item6_text4">阅读电子书籍<span>{{readBookTime}}</span>次</div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
      <div class="pages"
           id="pages7"
           :class="{ active: isPages7Visible }">
        <img src="../../assets/img/timeAxis/g_bg007.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item7">
          <div class="pages_item7_text1">2024<span>年</span></div>
          <div class="pages_item7_text2">青岛人大APP</div>
          <div class="pages_item7_text3">将继续陪伴</div>
          <div class="pages_item7_text4">您的<span>履职之路</span></div>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div> -->
    <van-overlay :show="isShowLoading">
      <div style="text-align: center; padding-top: 100%">
        <van-loading size="24px"
                     vertical
                     text-color="#0094ff"
                     color="#0094ff">加载中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'timeAxis',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  setup () {
    // const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      isShowLoading: true,
      userName: '',
      year: '2023',
      month: '12',
      day: '18',
      isPages1Visible: false,
      isPages2Visible: false,
      isPages3Visible: false,
      isPages4Visible: false,
      isPages5Visible: false,
      isPages6Visible: false,
      isPages7Visible: false,
      year2: '', // 第一次登录年
      month2: '', // 第一次登录月
      day2: '', // 第一次登录日
      loginTimes: 0, // 登录次数
      lastLogin: '', // 最晚时间 年月日
      lastLoginTime: '', // 最晚时间 时分秒

      year3: '2023', // 第一次提出代表建议年
      month3: '12', // 第一次提出代表建议月
      day3: '18', // 第一次提出代表建议日
      recommendationSubmitCount: 0, // 代表建议数量

      year4: '2023', // 第一次发布履职圈年
      month4: '12', // 第一次发布履职圈月
      day4: '18', // 第一次发布履职圈日
      publishDuty: 0, // 共发布履职圈
      dutyFabulou: 0, // 共点赞

      year5: '2023', // 第一次参与意见征集年
      month5: '12', // 第一次参与意见征集月
      day5: '18', // 第一次参与意见征集日
      participateServer: 0, // 共参与意见征集
      publishServerTownhallAdvice: 0, // 共发表意见

      year6: '2023', // 第一次参加线上履职学习年
      month6: '12', // 第一次参加线上履职学习月
      day6: '18', // 第一次参加线上履职学习日
      watchVideoTime: 0, // 观看专题讲座时长
      readBookTime: 0 // 阅读电子数据时长
    })

    if (data.title) {
      document.title = data.title
    }

    onMounted(() => {
      console.log('app进入时光轴')
      data.userName = data.user.userName
      memberPortraitDutyTime()
      getPages3Recommendation()
      data.isShowLoading = false
      const options = {
        root: null,
        rootMargin: '0px',
        threshold: 0.5
      }
      setTimeout(() => {
        const observer = new IntersectionObserver(handleIntersection, options)
        observer.observe(document.getElementById('pages1'))
        observer.observe(document.getElementById('pages2'))
        if (data.recommendationSubmitCount !== 0) {
          observer.observe(document.getElementById('pages3'))
        }
        observer.observe(document.getElementById('pages4'))
        observer.observe(document.getElementById('pages5'))
        observer.observe(document.getElementById('pages6'))
        observer.observe(document.getElementById('pages7'))
      }, 2000)
    })
    const handleIntersection = (entries) => {
      entries.forEach(entry => {
        console.log('entry==>', entry.target)
        if (entry.target.id === 'pages1') {
          data.isPages1Visible = entry.isIntersecting
        } else if (entry.target.id === 'pages2') {
          data.isPages2Visible = entry.isIntersecting
        } else if (entry.target.id === 'pages3') {
          data.isPages3Visible = entry.isIntersecting
        } else if (entry.target.id === 'pages4') {
          data.isPages4Visible = entry.isIntersecting
        } else if (entry.target.id === 'pages5') {
          data.isPages5Visible = entry.isIntersecting
        } else if (entry.target.id === 'pages6') {
          data.isPages6Visible = entry.isIntersecting
        } else if (entry.target.id === 'pages7') {
          data.isPages7Visible = entry.isIntersecting
        }
      })
    }
    // 获取数据
    const memberPortraitDutyTime = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyTime()
      console.log('获取数据===>>', res)
      // pages2第一次登录
      data.loginTimes = res.data.loginTimes
      data.lastLogin = res.data.lastLogin.split(' ')[0]
      data.lastLoginTime = res.data.lastLogin.substring(11, 17)
      data.year2 = res.data.firstLogin.substring(0, 4)
      data.month2 = res.data.firstLogin.substring(5, 7)
      data.day2 = res.data.firstLogin.substring(8, 10)
      // page4履职圈
      data.year4 = res.data.firstPublishDuty.substring(0, 4)
      data.month4 = res.data.firstPublishDuty.substring(5, 7)
      data.day4 = res.data.firstPublishDuty.substring(8, 10)
      data.dutyFabulou = res.data.dutyFabulou
      data.publishDuty = res.data.publishDuty
      // pages5意见征集
      data.year5 = res.data.firstPublishSurver.substring(0, 4)
      data.month5 = res.data.firstPublishSurver.substring(5, 7)
      data.day5 = res.data.firstPublishSurver.substring(8, 10)
      data.participateServer = res.data.participateServer
      data.publishServerTownhallAdvice = res.data.publishServerTownhallAdvice
      // pages6线上履职学习
      data.year6 = res.data.firstStudyDuty.substring(0, 4)
      data.month6 = res.data.firstStudyDuty.substring(5, 7)
      data.day6 = res.data.firstStudyDuty.substring(8, 10)
      data.watchVideoTime = res.data.watchVideoTime
      data.readBookTime = res.data.readBookTime
    }
    // 获取pages3代表建议
    const getPages3Recommendation = async () => {
      const res = await $api.representativePortrait.getPersonSubmitInfo({ personCode: data.user.id })
      console.log('获取pages3代表建议===>>', res)
      data.recommendationSubmitCount = res.result.submitCount
      data.year3 = res.result.firstSubmitTimeText.substring(0, 4)
      data.month3 = res.result.firstSubmitTimeText.substring(5, 7)
      data.day3 = res.result.firstSubmitTimeText.substring(8, 10)
    }
    const onRefresh = () => {
    }
    const onLoad = () => {
    }
    return { ...toRefs(data), onRefresh, onLoad, $general }
  }
}
</script>
<style lang="less" scoped>
.timeAxis {
  width: 100%;
  min-height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
  }

  #pages1 {
    // opacity: 0.7;
    // transition: opacity 2s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  #pages2 {
    // opacity: 0.5;
    // transition: opacity 3s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  // @keyframes bear {
  //   0% {
  //     background-position: 0 0;
  //   }
  //   100% {
  //     background-position: -1600px 0;
  //   }
  // }

  // @keyframes move {
  //   0% {
  //     left: 0;
  //   }
  //   100% {
  //     left: 50%;
  //     transform: translateX(-50%);
  //   }
  // }
  #pages3 {
    // opacity: 0.5;
    // transition: opacity 3s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  #pages4 {
    // opacity: 0.5;
    // transition: opacity 3s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  #pages5 {
    // opacity: 0.5;
    // transition: opacity 3s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  #pages6 {
    // opacity: 0.5;
    // transition: opacity 3s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  #pages7 {
    // opacity: 0.5;
    // transition: opacity 3s ease-in-out;
    // scroll-snap-align: start;
    height: 100vh;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    position: relative;
  }
  // #pages1.active {
  //   opacity: 1;
  // }
  // #pages2.active {
  //   opacity: 1;
  // }
  // #pages3.active {
  //   opacity: 1;
  // }
  // #pages4.active {
  //   opacity: 1;
  // }
  // #pages5.active {
  //   opacity: 1;
  // }
  // #pages6.active {
  //   opacity: 1;
  // }
  // #pages7.active {
  //   opacity: 1;
  // }
  // #pages1.active .pages_item1 {
  //   opacity: 1;
  // }
  #pages1.active .pages_item1 .pages_item1_text1 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text2 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text3 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text4 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text5 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text6 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text7 {
    opacity: 1;
  }
  #pages1.active .pages_item1 .pages_item1_text8 {
    opacity: 1;
  }
  #pages2.active .pages_item2 {
    opacity: 1;
  }
  #pages3.active .pages_item3 {
    transform: scaleX(1);
  }
  #pages4.active .pages_item4 {
    transform: scale(1);
  }
  #pages5.active .pages_item5 {
    opacity: 1;
  }
  #pages6.active .pages_item6 {
    transform: scale(1);
  }
  #pages7.active .pages_item7 {
    transform: scale(1);
  }
  .pages_item1 {
    // opacity: 0;
    // transition: opacity 3s ease-in-out;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    top: 2.5rem;
    left: 2rem;
    .pages_item1_text1 {
      color: #fff;
      margin-top: 0.5rem;
      font-size: 0.4rem;
      opacity: 0;
      transition: opacity 1s ease;
      span {
        font-size: 0.45rem;
        margin: 0 10px;
        font-weight: bold;
      }
    }
    .pages_item1_text2 {
      color: #fff;
      margin-top: 0.4rem;
      font-size: 0.4rem;
      opacity: 0;
      transition: opacity 2s ease;
    }
    .pages_item1_text3 {
      color: #fff;
      margin-top: 0.5rem;
      font-size: 0.4rem;
      opacity: 0;
      transition: opacity 4s ease;
      span {
        margin-left: 10px;
        font-weight: bold;
        font-size: 0.45rem;
      }
    }
    .pages_item1_text4 {
      color: #fff115;
      margin-top: 0.55rem;
      font-size: 0.8rem;
      font-weight: 800;
      opacity: 0;
      transition: opacity 6s ease;
      span {
        font-size: 0.4rem;
        margin-left: 10px;
        color: #fff;
      }
    }
    .pages_item1_text5 {
      color: #fff;
      margin-top: 0.25rem;
      font-size: 0.4rem;
      opacity: 0;
      transition: opacity 6s ease;
      span {
        margin-left: 6px;
        font-weight: bold;
        font-size: 0.45rem;
      }
    }
    .pages_item1_text6 {
      color: #fff;
      margin-top: 0.78rem;
      font-size: 0.4rem;
      opacity: 0;
      transition: opacity 7s ease;
    }
    .pages_item1_text7 {
      color: #fff;
      margin-top: 0.5rem;
      font-size: 0.45rem;
      font-weight: 800;
      opacity: 0;
      transition: opacity 7s ease;
      span {
        font-size: 0.4rem;
        margin-left: 10px;
        font-weight: 200;
      }
    }
    .pages_item1_text8 {
      color: #fff;
      margin-top: 0.5rem;
      font-size: 0.4rem;
      opacity: 0;
      transition: opacity 7s ease;
    }
  }
  .pages1_gif1 {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 100px;
    height: 100px;
  }
  .pages1_gif2 {
    position: absolute;
    bottom: 3rem;
    right: 3rem;
    width: 200px;
    height: 200px;
  }
  .more {
    position: absolute;
    bottom: 1rem;
    left: 4.5rem;
    .drop {
      font-size: 30px;
      animation: drop 1s linear infinite;
    }
    // .text {
    //   color: #454545;
    // }
  }
  @keyframes drop {
    0% {
      opacity: 0;
      margin-top: 0px;
    }

    25% {
      opacity: 0.5;
      margin-top: -10px;
    }

    50% {
      opacity: 1;
      margin-top: -20px;
    }

    75% {
      opacity: 0.5;
      margin-top: -30px;
    }

    100% {
      opacity: 0;
      margin-top: -40px;
    }
  }
  .pages_item2 {
    opacity: 0;
    transition: opacity 3s ease-in-out;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    top: 3.5rem;
    right: 1rem;

    .pages_item2_yearMonthDay {
      color: #fff;
      font-size: 0.45rem;
      position: relative;
      letter-spacing: 1px;
      margin-left: 0.7rem;
      .year2 {
        position: absolute;
        top: 0.32rem;
        left: 0.2rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .month2 {
        position: absolute;
        top: 0.32rem;
        left: 2.6rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .day2 {
        position: absolute;
        top: 0.32rem;
        left: 4.15rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
    }
    .pages_item2_text1 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.3rem;
      text-align: right;

      span {
        color: #fff;
        font-size: 0.5rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item2_text2 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 1.1rem;
      letter-spacing: 3px;
      text-align: right;
    }
    .pages_item2_text3 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.4rem;
      letter-spacing: 2px;
      text-align: right;

      span {
        font-size: 0.8rem;
        color: #fff115;
        margin: 0 0.1rem;
        font-weight: bold;
      }
    }
    .pages_item2_text4 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.8rem;
      letter-spacing: 2px;
      text-align: right;
    }
    .pages_item2_text5 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.4rem;
      letter-spacing: 2px;
      font-weight: bold;
      text-align: right;
    }
    .pages_item2_text6 {
      color: #fff115;
      font-size: 0.8rem;
      margin-top: 0.45rem;
      font-weight: bold;
      text-align: right;
    }
  }
  .pages_item3 {
    transform: scaleX(0);
    transition: transform 3s ease-in-out;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    top: 5.5rem;
    right: 1rem;
    text-align: right;
    .pages_item3_yearMonthDay {
      color: #fff;
      font-size: 0.45rem;
      position: relative;
      letter-spacing: 1px;
      .year3 {
        position: absolute;
        top: 0.32rem;
        left: 0.2rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .month3 {
        position: absolute;
        top: 0.32rem;
        left: 2.55rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .day3 {
        position: absolute;
        top: 0.32rem;
        left: 4.15rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
    }
    .pages_item3_text1 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.3rem;
      span {
        color: #fff;
        font-size: 0.5rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item3_text2 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 1rem;
    }
    .pages_item3_text3 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.3rem;
    }
    .pages_item3_text4 {
      color: #fff115;
      font-size: 0.8rem;
      font-weight: bold;
      margin-top: 0.4rem;
      span {
        font-weight: 200;
        font-size: 0.45rem;
        margin-left: 0.1rem;
      }
    }
  }
  .pages_item4_numData {
    position: absolute;
    top: 5.5rem;
    right: 3.5rem;
    .pages_item4_num1 {
      margin-top: 2rem;
      font-size: 22px;
      color: #ffffff;
    }
    .pages_item4_num2 {
      margin-top: 2rem;
      font-size: 22px;
      color: #ffffff;
    }
  }
  .pages_item4 {
    transform: scale(-1);
    transition: transform 3s ease-in-out;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    bottom: 2rem;
    right: 2rem;

    .pages_item4_yearMonthDay {
      color: #fff;
      font-size: 0.45rem;
      position: relative;
      letter-spacing: 1px;
      .year4 {
        position: absolute;
        top: 0.32rem;
        left: 0.2rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .month4 {
        position: absolute;
        top: 0.32rem;
        left: 2.55rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .day4 {
        position: absolute;
        top: 0.32rem;
        left: 4.15rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
    }
    .pages_item4_text1 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.3rem;
      letter-spacing: 2px;
      span {
        color: #fff;
        font-size: 0.5rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item4_text2 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 1rem;
      letter-spacing: 2px;
    }
    .pages_item4_text3 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.2rem;
      letter-spacing: 2px;
      span {
        color: #fff115;
        font-size: 0.7rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item4_text4 {
      color: #fff115;
      font-size: 0.7rem;
      font-weight: 800;
      letter-spacing: 2px;
      margin-top: 0.4rem;
      span {
        color: #fff;
        font-size: 0.45rem;
        font-weight: 400;
      }
    }
  }
  .pages_item5 {
    opacity: 0;
    transition: opacity 3s ease-in-out;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    top: 5rem;
    left: 1.5rem;
    .pages_item5_yearMonthDay {
      color: #fff;
      font-size: 0.45rem;
      position: relative;
      letter-spacing: 1px;
      .year5 {
        position: absolute;
        top: 0.32rem;
        left: 0.2rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .month5 {
        position: absolute;
        top: 0.32rem;
        left: 2.5rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .day5 {
        position: absolute;
        top: 0.32rem;
        left: 4.15rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
    }
    .pages_item5_text1 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.3rem;
      letter-spacing: 2px;
      span {
        color: #fff;
        font-size: 0.5rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item5_text2 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 1rem;
      letter-spacing: 2px;
    }
    .pages_item5_text3 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.2rem;
      letter-spacing: 2px;
      span {
        color: #fff115;
        font-size: 0.7rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item5_text4 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.6rem;
      letter-spacing: 2px;
    }
    .pages_item5_text5 {
      color: #fff115;
      font-size: 0.7rem;
      font-weight: 800;
      margin-top: 0.4rem;
      span {
        color: #fff;
        font-size: 0.45rem;
        margin-left: 0.1rem;
        letter-spacing: 2px;
        font-weight: 200;
      }
    }
  }
  .pages_item6 {
    transform: scaleX(0);
    transition: transform 3s ease-in;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    top: 5rem;
    left: 1.5rem;
    .pages_item6_yearMonthDay {
      color: #fff;
      font-size: 0.45rem;
      position: relative;
      letter-spacing: 1px;
      .year6 {
        position: absolute;
        top: 0.32rem;
        left: 0.2rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .month6 {
        position: absolute;
        top: 0.32rem;
        left: 2.5rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
      .day6 {
        position: absolute;
        top: 0.32rem;
        left: 4.15rem;
        color: #3e8cd4;
        font-size: 0.6rem;
      }
    }
    .pages_item6_text1 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.3rem;
      letter-spacing: 2px;
      span {
        color: #fff;
        font-size: 0.5rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item6_text2 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 1rem;
      letter-spacing: 2px;
    }
    .pages_item6_text3 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.2rem;
      letter-spacing: 2px;
      span {
        color: #fff115;
        font-size: 0.7rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
    .pages_item6_text4 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.2rem;
      letter-spacing: 2px;
      span {
        color: #fff115;
        font-size: 0.7rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
  }
  .pages_item7 {
    transform: scaleY(0);
    transition: transform 3s ease-in;
    margin-top: -1rem;
    margin-left: -1rem;
    position: absolute;
    top: 4rem;
    left: 4rem;
    .pages_item7_text1 {
      color: #fff115;
      font-size: 0.8rem;
      font-weight: bold;
      margin-top: 0.3rem;
      letter-spacing: 2px;
      text-align: center;
      span {
        color: #fff;
        font-size: 0.45rem;
        font-weight: 100;
        margin-left: 0.1rem;
      }
    }
    .pages_item7_text2 {
      color: #fff;
      font-size: 0.5rem;
      font-weight: bold;
      margin-top: 0.7rem;
      letter-spacing: 2px;
      text-align: center;
    }
    .pages_item7_text3 {
      color: #fff;
      font-size: 0.45rem;
      margin-top: 0.8rem;
      letter-spacing: 2px;
      text-align: center;
    }
    .pages_item7_text4 {
      color: #fff;
      font-size: 0.5rem;
      margin-top: 0.8rem;
      letter-spacing: 2px;
      text-align: center;
      span {
        color: #fff115;
        font-size: 0.7rem;
        font-weight: 800;
        margin-left: 0.1rem;
      }
    }
  }
}
</style>

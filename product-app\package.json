{"name": "product-app", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.1.0", "@rongcloud/engine": "^5.1.6", "@rongcloud/imlib-next": "^5.1.6", "@vue-office/docx": "^1.6.3", "@vue-office/pdf": "^2.0.10", "amfe-flexible": "^2.2.1", "axios": "^0.21.4", "babel-plugin-polyfill-corejs2": "^0.4.13", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.3", "echarts": "^5.5.0", "gdt-jsapi": "^1.9.34", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "html5-qrcode": "^2.3.8", "image-conversion": "^2.1.1", "js-md5": "^0.7.3", "mitt": "^3.0.0", "moment": "^2.29.3", "tesseract.js": "^4.1.2", "vant": "^3.4.1", "vconsole": "^3.9.4", "vue": "^3.2.13", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.14.10", "vue-draggable-next": "^2.2.1", "vue-echarts": "^6.7.3", "vue-loader": "^17.1.1", "vue-ls": "^4.2.0", "vue-router": "^4.2.5", "vue-seamless-scroll": "^1.1.23", "vuedraggable": "^2.24.3", "vuex": "^4.0.0-0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.2.13", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "compression-webpack-plugin": "^6.0.4", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^7.0.0", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss": "^8.3.8", "postcss-loader": "^6.1.1", "postcss-pxtorem": "^5.1.1"}}
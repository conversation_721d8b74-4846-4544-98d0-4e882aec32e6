<template>
  <div class="commonFilesList">
    <header id="dblclick" class="header flex_box"
      :style="$general.loadConfiguration(-3) + 'padding-top:' + safeAreaTop + 'px;background: #fff;'">
      <div class="btnLeft_box flex_box">
        <div v-if="isSelect" @click="close()" :style="$general.loadConfiguration(-1) + 'color:#333;margin-left:10px;'"
          class="img_btn flex_box flex_align_center flex_justify_content">取消</div>
        <div v-else @click="close()" :style="$general.loadConfiguration(-1) + 'color:#333;margin-left:10px;'"
          class="img_btn flex_box flex_align_center flex_justify_content">
          <van-icon v-if="nowLevel.length > 1" :color="$general.getHeadThemeRelatively()"
            :size="($general.appFontSize + 3) + 'px'" name="arrow-left"></van-icon>
        </div>
      </div>
      <div class="flex_placeholder flex_box">
        <div class="flex_placeholder flex_box flex_align_center flex_justify_content new_title">
          <span v-if="isSelect" :style="$general.loadConfiguration(3) + 'color:' + $general.getHeadThemeRelatively()"
            class="text_one2">已选中{{ listSelect.length }}个文件</span>
          <span v-else :style="$general.loadConfiguration(3) + 'color:' + $general.getHeadThemeRelatively()"
            class="text_one2" v-html="title"></span>
        </div>
      </div>
      <div id="btnRight_box" class="btnRight_box flex_box" style="margin-right: 5px;">
        <div v-if="isSelect" @click="checkAll"
          :style="$general.loadConfiguration(-1) + 'margin-right:10px;color:' + appTheme"
          class="img_btn flex_box flex_align_center flex_justify_content">{{ checkAllHasCancel() ? '取消' : '' }}全选</div>
        <template v-else>
          <div v-if="showMore()" :style="$general.loadConfiguration(-1) + 'margin-right:10px;color:' + appTheme"
            class="img_btn flex_box flex_align_center flex_justify_content">
            <van-popover get-container="#dblclick" v-model:show="moreType.showPopover" trigger="click"
              placement="bottom-end">
              <div v-for="(item, index) in moreType.data" :key="index" @click="onPopMoreSelect(item)" role="menuitem"
                class="van-popover__action" style="width:130px;height:40px;">
                <div class="van-popover__action-text" style="font-size:15px;">{{ item.text }}</div>
                <div v-if="item.unread && item.unread > 0"
                  class="redDot_box flex_box flex_align_center flex_justify_content" :class="'redDot_big'"
                  :style="$general.loadConfiguration(-4) + $general.loadConfigurationSize(4)"
                  v-html="item.unread > 99 ? '99+' : item.unread"></div>
              </div>
              <template #reference>
                <div style="position: relative;">
                  <van-icon :color="$general.getHeadThemeRelatively()" style="padding: 10px 0;"
                    :size="($general.appFontSize + 3) + 'px'" name="ellipsis"></van-icon>
                  <div v-if="moreType.unreadAll > 0" class="redDot_box flex_box flex_align_center flex_justify_content"
                    :class="'redDot_big'"
                    :style="$general.loadConfiguration(-4) + $general.loadConfigurationSize(4) + 'right:-10px;'"
                    v-html="moreType.unreadAll > 99 ? '99+' : moreType.unreadAll"></div>
                </div>
              </template>
            </van-popover>
          </div>
          <div v-else-if="(nowLevel.length && nowLevel[nowLevel.length - 1].key == 'inboxList')"
            class="flex_placeholder flex_box">
            <div @click="switchInbox()" class="img_btn flex_box flex_align_center flex_justify_content"
              :style="$general.loadConfiguration(-3) + 'color:#333;'">{{ nowLevel[nowLevel.length - 1].type ==
                "1" ? "处室" : "个人" }}收件箱</div>
          </div>
          <div v-else-if="(nowLevel.length && nowLevel[nowLevel.length - 1].key == 'recycleList')"
            @click="footerBtnClick(thoroughDeleteBtn, true)"
            class="img_btn flex_box flex_align_center flex_justify_content"
            :style="$general.loadConfiguration(-3) + 'color:#333;margin-right:10px;'">全部清除</div>
        </template>
      </div>
    </header>
    <div class="headerPlaceholder" :style="'padding-top:' + safeAreaTop + 'px;'"></div>
    <div v-if="(nowLevel.length && nowLevel[nowLevel.length - 1].key == 'recycleList')"
      :style="$general.loadConfiguration(-3) + 'padding:60px 15px 10px;color:#666;'">
      存放期限为30天，时间过后自动删除。
    </div>
    <div v-else id="search" class="search_box" :style="$general.loadConfiguration()">
      <div class="search_warp flex_box">
        <div @click="btnSearch();" class="search_btn flex_box flex_align_center flex_justify_content">
          <van-icon :size="($general.appFontSize - 10) + 'px'" :color="'#666'" class-prefix="icon"
            :name="'sousuo'"></van-icon>
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input" action="javascript:return true;"> <input
            id="searchInput" class="flex_placeholder search_input" :style="$general.loadConfiguration(-1)"
            :placeholder="seachPlaceholder" maxlength="100" type="search" ref="btnSearch" @keyup.enter="btnSearch()"
            v-model="seachText" />
          <div v-if="seachText" @click="seachText = ''; btnSearch();"
            class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="($general.appFontSize) + 'px'" :color="'#ccc'" :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>
    <div class="switchShow_box flex_box flex_align_center" :style="$general.loadConfiguration(-3)">
      <van-popover class="sort" get-container=".switchShow_box" v-model:show="sortType.showPopover" :offset="[15, 8]"
        trigger="click" placement="bottom-start" :actions="sortType.data" @select="onPopSortSelect">
        <template #reference>
          <div class="switchShow_warp click" :style="$general.loadConfiguration(-5)">{{ sortType.value.text }}</div>
        </template>
      </van-popover>
      <div class="flex_placeholder"></div>
      <div @click="switchShowType()" class="switchShow_warp flex_box flex_align_center flex_justify_content">
        <van-icon :color="'#666'" :size="($general.appFontSize + (showType == 0 ? 3 : 4)) + 'px'" class-prefix="icon"
          :name="showType == 0 ? 'gongge' : 'liebiao3'"></van-icon>
      </div>
    </div>
    <div style="margin: 0 15px;" class="clouddisk_after van-hairline--top"></div>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <template v-if="listData.length">
          <template v-if="showType == 0">
            <van-checkbox-group ref="checkboxGroup" v-model="listSelect">
              <ul>
                <li v-for="(item, index) in listData" :key="index" @click="msgImgTap(index)" v-press="() => {
                  msgImgPress(index)
                }
                  " class="flex_box flex_align_center">
                  <img :src="require('../../assets/img/fileicon/' + item.iconInfo.name)"
                    :style="$general.loadConfigurationSize(13)" class="clouddisk_icon" />
                  <div class="flex_placeholder">
                    <div class="clouddisk_warp flex_box flex_align_center">
                      <div class="flex_placeholder">
                        <div :style="$general.loadConfiguration(-1)" class="clouddisk_name text_one2">{{ item.name }}
                        </div>
                        <div :style="$general.loadConfiguration(-4)" class="clouddisk_add flex_box flex_align_center">
                          <template
                            v-if="item.shareObjectList && item.shareObjectList.length && (nowLevel.length && nowLevel[nowLevel.length - 1].id != 'share')">
                            <div class="inherit">已共享</div>
                            <div class="clouddisk_add_line" :style="$general.loadConfigurationSize(-2, 'h')"></div>
                          </template>
                          <template
                            v-if="item.userName && (nowLevel.length && nowLevel[nowLevel.length - 1].id != 'person')">
                            <div class="inherit">{{ item.userName }}</div>
                            <div class="clouddisk_add_line" :style="$general.loadConfigurationSize(-2, 'h')"></div>
                          </template>
                          <div class="inherit">{{ dayjs(item.time).format('YYYY-MM-DD HH:mm') }}</div>
                        </div>
                      </div>
                      <div v-if="isSelect" class="clouddisk_more flex_box flex_align_center flex_justify_content">
                        <div style="background:rgba(0,0,0,0);width:30px;height:30px;margin-right:-20px;z-index: 99999;">
                        </div>
                        <van-checkbox :icon-size="($general.appFontSize + 1) + 'px'" :checked-color="appTheme"
                          :name="item" shape="round"></van-checkbox>
                      </div>
                      <div v-else @click.stop="openListMore(item)"
                        class="clouddisk_more flex_box flex_align_center flex_justify_content">
                        <van-icon :color="'#999'" :size="($general.appFontSize + 1) + 'px'"
                          :name="'ellipsis'"></van-icon>
                      </div>
                    </div>
                    <div style="margin-right: 15px;" class="clouddisk_after van-hairline--top"></div>
                  </div>
                </li>
              </ul>
            </van-checkbox-group>
          </template>
          <template v-else-if="showType == 1">
            <van-checkbox-group ref="checkboxGroup" v-model="listSelect">
              <ul class="clouddisk_grid_box flex_box T-flex-flow-row-wrap">
                <li v-for="(item, index) in listData" :key="index" @click="msgImgTap(index)" v-press="() => {
                  msgImgPress(index)
                }
                  " class="clouddisk_grid_item"
                  :style="'border-color:' + (systemType == 'ios' ? 'rgba(153,153,153,0.1)' : 'rgba(153,153,153,0.2)')">
                  <img :src="require('../../assets/img/fileicon/' + item.iconInfo.name)"
                    :style="$general.loadConfigurationSize(22)" class="clouddisk_grid_icon" />
                  <div :style="$general.loadConfiguration()" class="clouddisk_grid_name text_two">{{ item.name }}
                  </div>

                  <div v-if="isSelect" class="clouddisk_grid_more flex_box flex_align_center flex_justify_content">
                    <div style="background:rgba(0,0,0,0);width:30px;height:30px;margin-right:-20px;z-index: 99999;">
                    </div>
                    <van-checkbox :icon-size="($general.appFontSize + 1) + 'px'" :checked-color="appTheme" :name="item"
                      shape="round"></van-checkbox>
                  </div>
                  <div v-else @click.stop="openListMore(item)"
                    class="clouddisk_grid_more flex_box flex_align_center flex_justify_content">
                    <van-icon :color="'#999'" :size="($general.appFontSize + 1) + 'px'" :name="'ellipsis'"></van-icon>
                  </div>
                </li>
              </ul>
            </van-checkbox-group>
          </template>
        </template>
        <div v-else-if="listData.length == 0">
          <van-empty :style="$general.loadConfiguration(-2)">
          </van-empty>
        </div>
      </van-list>
    </van-pull-refresh>
    <div v-if="showSkeleton" class="notText">
      <van-skeleton v-for="(item, index) in 3" :key="index" title :row="3"></van-skeleton>
    </div>
    <!-- 首页底部切换按钮 -->
    <transition name="van-slide-up">
      <footer v-if="showFooter()" :style="'padding-bottom:' + (safeAreaBottom) + 'px'" class="footer flex_box">
        <template v-for="(item, index) in mainBtns.data" :key="index">
          <div v-if="item.show" class="footer_item T-flexbox-vertical flex_align_center flex_justify_content"
            @click="switchFooter(index)">
            <img :src="item.url" :style="$general.loadConfigurationSize(5)" />
            <p class="footer_item_p text_one2"
              :style="$general.loadConfiguration(-6) + ';' + (mainBtns.active == index ? 'color:' + appTheme : 'color:#adadad')">
              {{ item.title }}</p>
            <div v-if="item.pointNumber > 0" class="redDot_box flex_box flex_align_center flex_justify_content"
              :class="item.pointType == 'big' ? 'redDot_big' : 'redDot_small'"
              :style="(item.pointType == 'big' ? $general.loadConfiguration(-4) + $general.loadConfigurationSize(4) : $general.loadConfigurationSize(-6)) + (careMode ? (item.pointType == 'big' ? ';top:-0.01rem;right: calc(50% - 0.26rem);' : 'top:0;right: calc(50% - 0.2rem);') : '')"
              v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></div>
          </div>
        </template>
      </footer>
    </transition>
    <!-- 选择状态底部按钮 -->
    <transition name="van-slide-up">
      <footer v-if="isSelect && getLlistOperate().length" :style="'padding-bottom:' + (safeAreaBottom) + 'px'"
        class="footer_select">
        <div class="footer_select_warp flex_box flex_align_center flex_justify_content" v-if="isSHowFooter">
          <template v-for="(item, index) in getLlistOperate()" :key="index">
            <div v-if="getLlistOperate().length > 5 ? index < 4 : true">
              <div class="footer_select_item click T-flexbox-vertical flex_align_center flex_justify_content"
                @click="item.disabled ? '' : footerBtnClick(item)">
                <van-icon :color="item.disabled ? '#ccc' : (item.color || '#333')"
                  :size="($general.appFontSize + 5) + 'px'" :class-prefix="item.prefix"
                  :name="item.iconName"></van-icon>
                <div class="footer_select_text"
                  :style="$general.loadConfiguration(-4) + 'color: ' + (item.disabled ? '#ccc' : '#333')">{{ item.name
                  }}</div>
              </div>
            </div>
          </template>
          <div v-if="getLlistOperate().length > 5"
            class="footer_select_item click T-flexbox-vertical flex_align_center flex_justify_content"
            @click="footerBtnClick({ click: 'select_more' })">
            <van-icon :color="'#333'" :size="($general.appFontSize + 5) + 'px'" :name="'ellipsis'"></van-icon>
            <div class="footer_select_text" :style="$general.loadConfiguration(-4)">更多</div>
          </div>
        </div>
      </footer>
    </transition>
    <div v-if="showActionSheet || showActionSheetSend || showActionSheetMore"
      style="position:fixed;top:0;width:100%;height:100vh;z-index: 99999;">
      <van-action-sheet v-model:show="showActionSheet" :round="true" :closeable="false" safe-area-inset-bottom
        :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
        <actionSHeet v-if="showActionSheet" @cancelActionSheet="cancelActionSheet" :type="actiontype"
          :baseData="baseData" :baseType="baseType" :baseTitle="baseTitle" :operateIds="actionoperateIds"></actionSHeet>
      </van-action-sheet>
      <van-action-sheet v-model:show="showActionSheetSend" :round="true" :closeable="false" safe-area-inset-bottom
        :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
        <actionSheetSend v-if="showActionSheetSend" @cancelActionSheetSend="cancelActionSheetSend"
          :nBaseType="nowLevel[0].id" :operateIds="actionoperateIds"></actionSheetSend>
      </van-action-sheet>
      <van-action-sheet v-model:show="showActionSheetMore" :round="true" :closeable="false" safe-area-inset-bottom
        :lazy-render="false" close-on-click-action :close-on-click-overlay="true">
        <div v-if="actions.length" class="morelist_box">
          <div v-for="(item, index) in actions" :key="index" @click="item.disabled ? '' : itemSelect(item, index)"
            class="morelist_item click flex_box flex_align_center">
            <van-icon :color="item.disabled ? '#ccc' : (item.color || '#333')" :size="((appFontSize + 5)) + 'px'"
              :class-prefix="item.prefix" :name="item.iconName"></van-icon>
            <div class="morelist_name"
              :style="$general.loadConfiguration(-3) + 'color: ' + (item.disabled ? '#ccc' : '#333')">
              {{ item.name }}</div>
          </div>
        </div>
      </van-action-sheet>
    </div>
    <van-overlay :show="overlayShow" :z-index="999999">
      <div style="width:clac(100% - 20px);margin-left:10px;margin-top:100%;text-align:center;">
        <van-circle v-model:current-rate="percentage" :rate="30" :speed="100" :text="percentage + '%'">
          <div style="color:#fff;text-align:center;margin-top:40%;">
            {{ percentage + '%' }}
          </div>
        </van-circle>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import actionSHeet from './actionSHeet_share'
import actionSheetSend from './actionSheet_send'
import { useRouter } from 'vue-router'
import { inject, reactive, onMounted, toRefs, watch, nextTick } from 'vue'
import { NavBar, Sticky, Image as VanImage, Popover, Icon, Skeleton, Toast, Popup, Uploader, Dialog, ActionSheet, Progress, Circle, Overlay } from 'vant'
export default {
  name: 'commonFilesList',
  components: {
    actionSHeet,
    actionSheetSend,
    [Icon.name]: Icon,
    [Overlay.name]: Overlay,
    [Circle.name]: Circle,
    [Progress.name]: Progress,
    [Dialog.name]: Dialog,
    [ActionSheet.name]: ActionSheet,
    [Popover.name]: Popover,
    [VanImage.name]: VanImage,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Skeleton.name]: Skeleton,
    [Popup.name]: Popup,
    [Uploader.name]: Uploader
  },
  setup () {
    const reductionBtn = { name: '还原', type: 'ib', click: 'reduction', iconName: 'replay', prefix: undefined, disabled: false }
    const thoroughDeleteBtn = { name: '彻底删除', type: 'ib', click: 'thoroughDelete', iconName: 'delete-o', prefix: undefined, disabled: false }
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      thoroughDeleteBtn: thoroughDeleteBtn,
      title: '个人文件', // 页面标题
      nowLevel: [], // 当前页面层级 默认首页  key代表某个页面 name页面标题 id或其它参数自定义
      appTheme: $appTheme,
      safeAreaBottom: 0,
      safeAreaTop: 0,
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索关键词',
      moreType: { showPopover: false, value: '', unreadAll: 0, data: [{ text: '收件箱(个人)', value: '1', unread: 0 }, { text: '收件箱(处室)', value: '2', unread: 0 }, { text: '发件箱', value: '3' }, { text: '回收站', value: '4' }] },
      sortType: { showPopover: false, value: { text: '默认排序 ↑', value: '1' }, data: [{ text: '默认排序 ↑', value: '1' }, { text: '时间排序 ↑', value: '2' }, { text: '时间排序 ↓', value: '3' }] },
      showType: 0, // 列表展示类型 0默认正常列表  1方格列表
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      listData: [],
      mainBtns: {
        active: 0,
        data: [
          { show: true, id: 'share', title: '常用文件', url: require('../../assets/img/icon_foot_share_in.png'), defaultImg: require('../../assets/img/icon_foot_share_in.png'), selectImg: require('../../assets/img/icon_foot_share_in.png'), infoName: '', pointType: 'big', pointNumber: 0, remarks: '' }
        ]
      },
      isSelect: false, // 是否选择操作中
      listSelect: [], // 选中数组
      baseListData: {},
      pageNot: { type: '', url: '', text: '', summary: '' },
      showSkeleton: true,
      openListMore: false,
      editFileImg: require('../../assets/img/fileicon/icon_unknown.png'),
      newFileName: '',
      isSHowFooter: true,
      showActionSheet: false,
      actiontype: '',
      actionoperateIds: '',
      baseData: '',
      baseType: '',
      baseTitle: '',
      showActionSheetMore: false,
      showActionSheetSend: false,
      actions: [],
      percentage: 0,
      overlayShow: false
    })
    onMounted(() => {
      initCloudDisk()
      getData()
    })
    watch(() => data.isSelect, (newName, oldName) => {
      calculateButtons()
    })
    watch(() => data.listSelect, (newName, oldName) => {
      if (data.isSelect) {
        calculateButtons()
      }
    }, { immediate: true, deep: true })
    // 初始化页面
    const initCloudDisk = () => {
      data.nowLevel.push({ key: 'basePage', name: '常用文件', id: 'share', isFolder: true })
    }
    // 获取列表数据
    const getData = async (_type) => {
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      if (lastLevel.oldData && lastLevel.oldData.length) {
        data.showSkeleton = false
        data.listData = lastLevel.oldData
        data.baseListData = JSON.parse(JSON.stringify(data.listData))// 保存一下元始数据
        lastLevel.oldData = []
        sortList()
        return
      }
      if (!_type) {
        data.pageNo = 1
      }
      // var url = sessionStorage.getItem('fileStoreUrl') + '/' + (data.nowLevel[0].id) + '/listAllObjects?'
      var url = window.location.origin + '/filestore/' + (data.nowLevel[0].id) + '/listAllObjects?'
      var postParam = {
        search: data.seachText,
        type: ''
      }
      switch (lastLevel.key) {
        case 'basePage':// 在首页
          if (lastLevel.id === 'share') {
            postParam.folderId = '/'
          }
          url = url + 'folderId=/'
          break
        case 'fileList':// 在详细列表页
          if (data.nowLevel[0].id === 'share') {
            postParam[lastLevel.isFolder ? 'folderId' : 'parentBucketId'] = lastLevel.id
            if (lastLevel.isFolder) {
              url = url + '&folderId=' + lastLevel.id
            } else {
              url = url + '&parentBucketId=' + lastLevel.id
            }
          } else {
            postParam.bucketId = lastLevel.id
            url = url + 'bucketId=' + lastLevel.id
          }
          break
        case 'inboxList':// 收件箱(个人/处室)
          url = window.location.origin + '/filestore' + '/inbox/listAllInboxs?'
          // url = 'http://localhost:8080/filestore' + '/inbox/listAllInboxs?'
          if (lastLevel.type !== '1') {
            postParam.type = lastLevel.type
            url = url + '&type=2'
          }
          break
        case 'outboxList':// 发件箱
          url = window.location.origin + '/filestore' + '/inbox/listAllOutboxs?'
          // url = 'http://localhost:8080/filestore' + '/inbox/listAllOutboxs?'
          break
        case 'recycleList':// 回收站
          url = window.location.origin + '/filestore' + '/recycle/listAllFiles?bucketId=' + (lastLevel.id || '')
          // url = 'http://localhost:8080/filestore' + '/recycle/listAllFiles?bucketId=' + (lastLevel.id || '')
          postParam.bucketId = lastLevel.id || ''
          break
      }
      const res = await $api.commonFiles.cloudDiskFileList(url, postParam)
      data.showSkeleton = false
      var code = res ? res.code : ''
      data.pageNot.type = res ? (code === 1 ? 0 : 1) : 1// 类型 列表中只有有网和无网的情况
      data.pageNot.text = res && code !== 1 ? res.message || res.prompt : ''// 只有接口报的异常才改文字
      if (!_type) { // 有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
        data.pageNo = 1
        data.listData = []
      }
      if (code === 1) {
        var attribute = res ? res.attribute || {} : {}
        var showList = []
        var result = $general.isParameters(attribute.result) ? attribute.result : ''
        if ($general.isArray(result)) { // 直接是数组
          showList = [result]
        } else {
          var bucketList = result.bucketList || []// 所有文件夹
          var fileList = result.fileList || []// 所有文件
          var folderList = result.folderList || []// 共享文件夹
          showList = [folderList, bucketList, fileList]
        }
        showList.forEach(function (_nItem) {
          _nItem.forEach(function (_eItem) {
            var baseItem = ''
            if (_eItem.fileObject) {
              baseItem = JSON.parse(JSON.stringify(_eItem))
              _eItem = _eItem.fileObject
              _eItem.nId = _eItem.id// 下载使用本级id
              _eItem.id = baseItem.id// 删除使用父级id
            }
            _eItem.userId = _eItem.userId || ''
            _eItem.userName = _eItem.userName || _eItem.createUser || ''
            _eItem.name = _eItem.name || _eItem.foldName || ''
            _eItem.time = (baseItem ? baseItem.sendDate : '') || _eItem.lastModifyDate || _eItem.modifyDate || ''
            _eItem.isShare = false// 是否已共享
            _eItem.isDownLoad = lastLevel.downLoad || false// 共享盘中是否可以下载
            if (_eItem.shareObjectList && _eItem.shareObjectList.length) {
              _eItem.shareObjectList.forEach(function (_shareItem) {
                if (_shareItem.isShare === 1) {
                  _eItem.isShare = true
                }
                if (lastLevel.isFolder && _shareItem.shareFolderId === lastLevel.id && _shareItem.authority === '1') { // 当前父级是共享文件夹  是当前文件夹判断 是可以下载
                  _eItem.isDownLoad = true
                }
              })
            }
            if (_eItem.bucketId || (_eItem.type !== '文件夹' && !_eItem.foldName)) {
              _eItem.iconInfo = $general.getFileTypeAttr(_eItem.name)
            } else {
              _eItem.iconInfo = { name: 'icon_folder.png', type: 'folder' }// 是文件夹
            }
            data.listData.push(_eItem)
          })
        })
        data.baseListData = JSON.parse(JSON.stringify(data.listData))// 保存一下元始数据
        sortList()
        data.pageNot.text = !data.listData.length ? '' : '已加载完'
        data.loading = false
        data.refreshing = false
        data.finished = true
      }
    }
    // 获取可操作的底部菜单
    const getLlistOperate = () => {
      let showList = []
      if (data.nowLevel.length) {
        var lastLevel = data.nowLevel[data.nowLevel.length - 1]
        if (lastLevel.key === 'recycleList') {
          showList = [reductionBtn, thoroughDeleteBtn]
        }
      }
      return showList
    }
    // 切换处室 个人
    const switchInbox = () => {
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      var nowItem = $general.getItemForKey(lastLevel.type === '1' ? '2' : '1', data.moreType.data, 'value')
      data.nowLevel[data.nowLevel.length - 1] = { key: 'inboxList', name: nowItem.text, id: '', type: nowItem.value }
      data.seachText = ''
      setTitle()
      getData()
    }
    // 列表样式切换
    const switchShowType = () => {
      data.showType = (data.showType === 0 ? 1 : 0)
    }
    // 打开pop框 顶部更多回调
    const onPopMoreSelect = (_action) => {
      data.moreType.showPopover = false
      data.nowLevel[data.nowLevel.length - 1].seachText = data.seachText// 先保存一下当前搜索条件
      data.nowLevel[data.nowLevel.length - 1].oldData = JSON.parse(JSON.stringify(data.baseListData))// 保存一下当前列表数据 返回时回显
      data.seachText = ''
      switch (_action.value) {
        case '1':// 收件个人
        case '2':// 收件处室
          data.nowLevel.push({ key: 'inboxList', name: _action.text, id: '', type: _action.value })
          break
        case '3':// 发件
          data.nowLevel.push({ key: 'outboxList', name: _action.text, id: '' })
          break
        case '4':// 回收
          data.nowLevel.push({ key: 'recycleList', name: _action.text, id: '' })
          break
      }
      setTitle()
      getData()
    }
    // 点击排序回调
    const onPopSortSelect = (_action) => {
      data.sortType.value = _action
      sortList()
    }
    // 设置当前页面标题
    const setTitle = (_name) => {
      data.title = _name || (data.nowLevel[data.nowLevel.length - 1].name)
    }
    // 是否显示顶部三个点
    const showMore = () => {
      var isShow = false
      if (data.nowLevel.length && (data.nowLevel[data.nowLevel.length - 1].key === 'basePage')) { // 首页才显示
        isShow = true
      }
      return !data.isSelect && isShow
    }
    // 给列表排序
    const sortList = () => {
      // 1 默认接口返回顺序  2所有时间升序  3时间降序
      if (data.sortType.value.value === '1') {
        data.listData = JSON.parse(JSON.stringify(data.baseListData))
      } else if (data.sortType.value.value === '2') {
        data.listData.sort(function (a, b) {
          return dayjs(a.time).valueOf() - dayjs(b.time).valueOf()
        })
      } else if (data.sortType.value.value === '3') {
        data.listData.sort(function (a, b) {
          return dayjs(b.time).valueOf() - dayjs(a.time).valueOf()
        })
      }
    }
    // 单击事件
    const msgImgTap = (_index) => {
      var _item = data.listData[_index]
      setTimeout(function () { // 适配点击“点”操作
        !data.openListMore && openDetails(_item)
      }, 50)
    }
    // 长按事件
    const msgImgPress = (_index) => {
      var _item = data.listData[_index]
      openListMore(_item)
    }
    // 底部切换事件
    const switchFooter = (_index) => {
      data.mainBtns.active = _index
      var footerItems = data.mainBtns.data
      for (var i = 0; i < footerItems.length; i++) {
        if (i === _index) {
          footerItems[i].url = footerItems[i].selectImg
          continue
        }
        footerItems[i].url = footerItems[i].defaultImg
      }
      data.showSkeleton = true
      data.seachText = ''
      data.listData = []
      $general.getItemForKey('basePage', data.nowLevel, 'key').name = data.mainBtns.data[data.mainBtns.active].title
      $general.getItemForKey('basePage', data.nowLevel, 'key').id = data.mainBtns.data[data.mainBtns.active].id
      setTitle()
      getData()
    }
    // 点击点 或长按
    const openListMore = (_item) => {
      if (data.isSelect) return
      data.openListMore = true
      setTimeout(function () {
        data.openListMore = false
      }, 200)
      data.isSelect = true
      data.listSelect.push(_item)
    }
    const calculateButtons = async () => {
      data.isSHowFooter = false
      getLlistOperate().forEach(function (_eItem) {
        switch (_eItem.click) {
          case 'reduction':
            _eItem.disabled = data.listSelect.length !== 1
            break
          case 'thoroughDelete':
            _eItem.disabled = !data.listSelect.length
            break
        }
      })
      nextTick(() => {
        data.isSHowFooter = true
      })
    }
    // 关闭页面 放到frame中 配合划动返回做操作
    const close = () => {
      if (data.isSelect) { // 有选择 返回先取消
        data.isSelect = false
        data.listSelect = []
      } else if (data.nowLevel.length > 1) {
        data.nowLevel.pop()
        data.seachText = data.nowLevel[data.nowLevel.length - 1].seachText || ''// 返回还原搜索
        setTitle()
        getData()
      }
    }
    // 点击进入详情 是选择还是打开文件夹 预览附件
    const openDetails = (_item) => {
      if (data.isSelect) {
        var nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
        // 在就删除这个
        if (nItem) {
          if (!nItem.notDel) { // 为非不可删除时 才能删除 否则不能动
            $general.delItemForKey(nItem, data.listSelect, 'id')
          }
        } else {
          data.listSelect.push(_item)
        }
        return
      }
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      if (_item.iconInfo.type === 'folder') { // 文件夹
        lastLevel.seachText = data.seachText// 先保存一下当前搜索条件
        lastLevel.oldData = JSON.parse(JSON.stringify(data.baseListData))// 保存一下当前列表数据 返回时回显
        data.seachText = ''
        var nowKey = 'fileList'
        if (lastLevel.key === 'recycleList') {
          nowKey = lastLevel.key
        }
        data.nowLevel.push({ key: nowKey, name: _item.name, id: _item.id, isFolder: !!_item.foldName, downLoad: _item.isDownLoad, lastFolderId: _item.foldName ? _item.id : lastLevel.lastFolderId })
        setTitle()
        onRefresh()
      } else {
        annexClick(_item)
      }
    }
    const annexClick = async (item) => {
      // var url = sessionStorage.getItem('fileStoreUrl') + '/doc/review?id=' + (item.id)
      // var param = {
      //   id: item.id,
      //   url: url,
      //   name: item.name
      // }
      // router.push({ name: 'superFile', query: param })
      console.log('item===>', item)
      var url = ''
      if (item.iconInfo.type === 'pdf' || item.iconInfo.type === 'word') {
        url = window.location.origin + '/filestore/doc/review?id=' + (item.id)
      } else {
        url = window.location.origin + '/filestore/doc/review?id=' + (item.nId)
      }
      var fileInfo = {
        fileType: item.iconInfo.type === 'word' ? 'pdf' : item.iconInfo.type,
        filePath: url
      }
      router.push({ name: 'previewFile', query: fileInfo })
    }
    const itemSelect = (item, index) => {
      footerBtnClick(item)
    }
    const footerBtnClick = (_item, _other) => {
      // const lastLevel = data.nowLevel[data.nowLevel.length - 1]
      switch (_item.click) {
        case 'select_more':// 点击操作状态下更多
          var nowSelectList = []
          for (var i = 4; i < getLlistOperate().length; i++) {
            nowSelectList.push(getLlistOperate()[i])
          }
          data.actions = nowSelectList
          data.showActionSheetMore = true
          break
        case 'reduction':
          Dialog.confirm({
            message: '确定恢复该文件' + (data.listSelect[0].iconInfo.type === 'folder' ? '夹' : '') + '吗？',
            confirmButtonColor: data.appTheme
          }).then(() => {
            reductionFile()
            // on confirm
          }).catch(() => {
            // on cancel
          })
          break
        case 'thoroughDelete':
          // eslint-disable-next-line no-redeclare
          var operateIds = ''
          var newArr = ''
          if (_other) {
            newArr = data.listData
          } else {
            newArr = data.listSelect
          }
          newArr.forEach(function (_eItem) {
            operateIds += (operateIds ? '|' : '') + _eItem.id
          })
          Dialog.confirm({
            message: _other ? '确定全部清除回收站文件吗？' : '确定从回收站清除该文件/文件夹吗？',
            confirmButtonColor: data.appTheme
          }).then(async () => {
            const ret = await $api.cloudDisk.generalPost(window.location.origin + '/filestore' + '/recycle/removeAll?', { ids: operateIds })
            if (ret && ret.code === 1) {
              Toast('操作成功')
              getData()
              !_other && close()
            } else {
              Toast(ret ? (ret.message || ret.prompt || '操作失败') : ('网络错误'))
            }
          }).catch(() => {
          })
          break
      }
    }
    const cancelActionSheet = (msg) => {
      console.log(msg)
      if (msg === 'share') {
        closed()
      } else {
        data.showActionSheet = false
      }
    }
    const cancelActionSheetSend = (msg) => {
      data.showActionSheetSend = false
    }
    const closed = () => {
      data.showActionSheet = false
      data.actiontype = ''
      data.actionoperateIds = ''
      close()
    }

    // 是否显示底部3个切换按钮
    const showFooter = () => {
      var isShow = false
      if (data.nowLevel.length && (data.nowLevel[data.nowLevel.length - 1].key === 'basePage')) { // 首页才显示
        isShow = true
      }
      return !data.isSelect && data.mainBtns.data.length && isShow
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }

    // 恢复文件操作
    const reductionFile = async (_item) => {
      var nowItem = data.listSelect[0]
      var postUrl = ''
      var postParam = {
        id: nowItem.id
      }
      if (nowItem.dirType === '处室' || nowItem.dirType === '个人') { // 收件箱-处室 个人
        postUrl = window.location.origin + '/filestore' + '/inbox/recoverInbox?'
      } else if (nowItem.dirType === (sessionStorage.getItem('BigDataUser') + 'person')) {
        postUrl = window.location.origin + '/filestore' + '/person/recycle/restoreObject?'
        if (nowItem.iconInfo.type === 'folder') {
          postUrl = window.location.origin + '/filestore' + '/person/recycle/restoreBucket?'
        }
      } else {
        postUrl = window.location.origin + '/filestore' + '/depart/recycle/restoreObject?'
        if (nowItem.iconInfo.type === 'folder') {
          postUrl = window.location.origin + '/filestore' + '/depart/recycle/restoreBucket?'
        }
      }
      const res = await $api.cloudDisk.generalPost(postUrl, postParam)
      if (res) {
        if (res.code === 1) {
          Toast('操作成功')
          getData()
          close()
        } else {
          Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
        }
      } else {
        Toast(res ? (res.message || res.prompt || '操作失败，请重试') : '接口异常，请联系技术')
      }
    }
    // 设置选中状态
    const isSelectValue = (_item) => {
      return $general.getItemForKey(_item.id, data.listSelect, 'id')
    }
    // 设置不可选的状态
    const isDisabled = (_item) => {
      return $general.getItemForKey(_item.id, data.listSelect, 'id').notDel
    }
    // 是否是取消全选
    const checkAllHasCancel = () => {
      var hasCancel = true
      data.listData.forEach(function (_item, index) {
        var nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
        if (!nItem && !isDisabled(_item)) {
          hasCancel = false
        }
      })
      return hasCancel
    }
    // 点选全选
    const checkAll = () => {
      if (checkAllHasCancel()) {
        data.listData.forEach(function (_item, index) {
          const nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
          if (nItem && !nItem.notDel) { // 为非不可删除时才能删除 否则不能动
            $general.delItemForKey(nItem, data.listSelect, 'id')
          }
        })
      } else {
        data.listData.forEach(function (_item, index) {
          const nItem = $general.getItemForKey(_item.id, data.listSelect, 'id')// 找出这个对象看在不在
          if (!nItem) {
            data.listSelect.push(_item)
          }
        })
      }
    }
    return { ...toRefs(data), onRefresh, onLoad, closed, itemSelect, cancelActionSheet, cancelActionSheetSend, footerBtnClick, $general, checkAll, isSelectValue, isDisabled, checkAllHasCancel, switchShowType, reductionFile, openListMore, switchFooter, openDetails, msgImgTap, msgImgPress, close, data, dayjs, sortList, getData, calculateButtons, getLlistOperate, showMore, onPopMoreSelect, initCloudDisk, switchInbox, showFooter, setTitle, onPopSortSelect }
  }
}
</script>
<style lang="less" scoped>
.commonFilesList {
  background-color: #fff;

  .header {
    position: fixed;
    width: 100%;
    z-index: 2;
    height: 40px;

    .new_title {
      font-weight: bold;
    }

    .van-popover__action {
      --van-popover-arrow-size: 25px;

      .redDot_big {
        position: absolute;
        top: 1px;
        right: calc(50% - 23px);
      }

      .van-popover__action .redDot_big {
        right: 5px;
      }
    }
  }

  .search_box {
    padding-top: 42px;
  }

  .switchShow_box {
    border-top: 10px solid #f8f8f8;
    background-color: #fff;
    height: 48px;
  }

  .search_input::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }

  .switchShow_warp {
    color: #666666;
    padding: 10px 15px;
  }

  .sort {
    width: 100px;
    height: 150px;
    font-size: 13px;
    font-family: simplified;
  }

  .clouddisk_icon {
    margin: 16px 15px;
    object-fit: cover;
  }

  .clouddisk_warp {
    padding: 10px 0;
  }

  .clouddisk_more {
    padding: 15px;
  }

  .clouddisk_name {
    color: #333333;
  }

  .clouddisk_add {
    margin-top: 6px;
    color: #999999;
  }

  .clouddisk_add_line {
    margin: 0 10px;
    width: 1px;
    background: #eee;
  }

  .clouddisk_grid_box {
    padding: 14px 2px 0 15px;
  }

  .clouddisk_grid_item {
    margin: 0 10px 14px 0;
    border: 1px solid rgba(153, 153, 153, 0.1);
    border-radius: 4px;
    width: calc(33.33% - 13px);
    height: 130px;
    display: flex;
    flex-direction: column;
  }

  .clouddisk_grid_icon {
    margin: 10px auto;
    object-fit: cover;
  }

  .clouddisk_grid_name {
    color: #333333;
    margin: 0 10px;
    text-align: center;
    text-overflow: ellipsis;
  }

  .clouddisk_grid_more {
    margin: 5px;
  }

  .footer {
    background-color: #fefefe;
    border-top: 1px solid rgba(234, 234, 234, 0.5);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
  }

  .footer_item {
    width: 100%;
    position: relative;
    min-height: 56px;
    padding: 5px 0;
  }

  .footer_item img {
    margin-bottom: 2px;
  }

  .footer_item_p {
    margin-top: 2px;
  }

  .redDot_small {
    position: absolute;
    top: 5px;
    right: calc(50% - 18px);
  }

  .footer_select {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;
    padding: 0 15px;
  }

  .footer_select_warp {
    background-color: #fff;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08);
    opacity: 1;
    border-radius: 4px;
    padding: 0 10px;
    margin-bottom: 10px;
    justify-content: space-between;
  }

  .footer_select_item {
    padding: 14px 5px;
    // width: 100%;
  }

  .footer_select_text {
    margin-top: 5px;
  }

  .morelist_box {
    background-color: #fff;
    padding: 0.04rem 0;
  }

  .morelist_item {
    padding: 15px;
  }

  .morelist_name {
    margin-left: 10px;
  }

  .footer_btn_box {
    bottom: 130px;
    right: 10px;

    .van-button-box {
      border-radius: 50%;
    }

    .file_options {
      justify-content: space-around;
      height: 100%;

      .cloudDisk_icon_file {
        text-align: center;
      }
    }

    .add_file {
      height: 50px;
      border-bottom: 1px solid #ede8e8;
    }
  }
}
</style>

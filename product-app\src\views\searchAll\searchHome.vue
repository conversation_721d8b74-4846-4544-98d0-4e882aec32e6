<template>
  <div class="searchHome">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div class="searchHome_top">
      <img src="../../assets/img/qingdao/hui.png"
           alt="">
      <div class="searchHome_top_text">
        全文检索
      </div>
    </div>
    <div class="search">
      <input type="text"
      v-model="keyword"
             class="search_inp" @keyup.enter="search()">
      <van-icon name="search"
                class="search_icon" />
    </div>
    <div class="search_tab">
      <div class="search_tab_item"
           v-for="(item, index) in searchTab"
           :key="index"
           @click="searchTabs(item)">{{ item.name }}</div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'searchHome',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      searchTab: [
        { name: '综合资讯', type: 'news' },
        { name: '通知公告', type: 'notice' },
        { name: '学习培训', type: 'study' },
        { name: '意见征集', type: 'survey' },
        { name: '知情明政', type: 'wszl' }
      ],
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] }

    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const onRefresh = () => {
    }
    const onLoad = () => {

    }

    const onClickLeft = () => history.back()
    const searchTabs = (item) => {
      router.push({ path: '/searchAll', query: { type: item.type } })
    }
    const search = () => {
      router.push({ path: '/searchAll', query: { keyword: data.keyword } })
    }
    return { ...toRefs(data), search, searchTabs, onClickLeft, onRefresh, onLoad, general, confirm }
  }
}
</script>
<style lang="less" scoped>
.searchHome {
  height: 100vh;
  width: 100%;
  background: #ffffff;
  overflow: hidden;

  .search_tab {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 35px;

    .search_tab_item {
      width: 33%;
      height: 45px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #2b67a3;
    }
  }

  .search {
    width: 98%;
    margin: auto;
    height: 50px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    padding: 5px 10px;
    display: flex;
    align-items: center;
    border-radius: 5px;
    margin-top: 30px;

    .search_inp {
      flex: 1;
      height: 100%;
      color: #3d3d3d;
      font-size: 18px;
    }

    .search_icon {
      font-size: 24px;
      color: #3d3d3d;
    }
  }

  .searchHome_top {
    // height: 300px;
    margin-top: 100px;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;

    >img {
      width: 50px;
      height: 50px;
      margin-bottom: 10px;
    }

    .searchHome_top_text {
      font-weight: 700;
      font-size: 20px;
    }
  }
}</style>

// 通知公告列表
const activityList = () => import('@/views/activity/activityList')
const activityNew = () => import('@/views/activity/activityNew')
const activityDetails = () => import('@/views/activity/activityDetails')
const activityParticipant = () => import('@/views/activity/activityParticipant')
const activityOtherInformation = () => import('@/views/activity/activityOtherInformation')
const activityOtherInformationDetails = () => import('@/views/activity/activityOtherInformationDetails')
const activityLeave = () => import('@/views/activity/activityLeave')

const activity = [{
  path: '/activityList',
  name: 'activityList',
  component: activityList,
  meta: {
    title: '活动',
    keepAlive: true
  }
},
{
  path: '/activityNew',
  name: 'activityNew',
  component: activityNew,
  meta: {
    title: '发起活动',
    keepAlive: true
  }
},
{
  path: '/activityDetails',
  name: 'activityDetails',
  component: activityDetails,
  meta: {
    title: '活动详情',
    keepAlive: false
  }
},
{
  path: '/activityParticipant',
  name: 'activityParticipant',
  component: activityParticipant,
  meta: {
    title: '人员详情',
    keepAlive: false
  }
},
{
  path: '/activityOtherInformation',
  name: 'activityOtherInformation',
  component: activityOtherInformation,
  meta: {
    title: '列表',
    keepAlive: false
  }
},
{
  path: '/activityOtherInformationDetails',
  name: 'activityOtherInformationDetails',
  component: activityOtherInformationDetails,
  meta: {
    title: '详情',
    keepAlive: false
  }
},
{
  path: '/activityLeave',
  name: 'activityLeave',
  component: activityLeave,
  meta: {
    title: '请假',
    keepAlive: false
  }
},
{
  path: '/conferenceActivities',
  name: 'conferenceActivities',
  component: () => import('@/views/conferenceActivitiesFile/conferenceActivities.vue'),
  meta: {
    title: '会议活动'
  }
},
{
  path: '/eventNoticeDetail',
  name: 'eventNoticeDetail',
  component: () => import('@/views/conferenceActivitiesFile/eventNoticeDetailFile/eventNoticeDetail.vue'),
  meta: {
    title: '详情', // 活动通知详情
    keepAlive: false
  }
},
{
  path: '/meetingNoticeDetail',
  name: 'meetingNoticeDetail',
  component: () => import('@/views/conferenceActivitiesFile/meetingNoticeDetailFile/meetingNoticeDetail.vue'),
  meta: {
    title: '详情', // 会议通知详情
    keepAlive: false
  }
},
{
  path: '/fileList',
  name: 'fileList',
  component: () => import('@/views/conferenceActivitiesFile/file/fileList.vue'),
  meta: {
    title: '附件', // 附件(外)
    keepAlive: false
  }
},
{
  path: '/fileList2',
  name: 'fileList2',
  component: () => import('@/views/conferenceActivitiesFile/file/fileList2.vue'),
  meta: {
    title: '附件', // 附件(内)
    keepAlive: false
  }
},
{
  path: '/vantEmptyGeneral',
  name: 'vantEmptyGeneral',
  component: () => import('@/views/component/vantEmptyGeneral.vue'),
  meta: {
    title: '暂无数据', // 缺省页面
    keepAlive: false
  }
},
{
  path: '/ConferenceAffairsManagement',
  name: 'ConferenceAffairsManagement',
  component: () => import('@/views/conferenceActivitiesFile/ConferenceAffairsManagement/ConferenceAffairsManagement.vue'),
  meta: {
    title: '会务管理', // 会务管理
    keepAlive: false
  }
}
]
export default activity

<template>
  <div id="app"
       class="committeeLivingRoom"
       :style="$general.loadConfiguration()">
    <template v-if="info.firstAjax">
      <div class="onduty_box">
        <div class="flex_box flex_align_center">
          <img class="onduty_user_img"
               :style="$general.loadConfigurationSize(22)"
               :src="info.userUrl" />
          <!-- :alt="cacheImg(user)"  -->
          <div class="flex_placeholder">
            <div class="flex_box flex_align_center">
              <div class="onduty_main_s"
                   :style="$general.loadConfiguration(-3)">{{info.userName}}</div>
              <div class="onduty_user_phone"
                   :style="$general.loadConfiguration(-4)">{{info.userPhone}}</div>
              <div class="flex_placeholder"></div>
              <div v-if="isStaff"
                   :style="$general.loadConfiguration(-6)+'color:'+(info.state==0?'#FE7530':info.state==1?'#3088FE':'#8C96A2')+';background:'+(info.state==0?'#FFE7DC':state==1?'#DCEBFF':'#EBF0F6')+';'"
                   class="review_status">{{info.state==0?'待审核':info.state==1?'审核通过':'审核不通过'}}</div>
            </div>
            <div v-if="info.userEmail"
                 class="onduty_user_email"
                 :style="$general.loadConfiguration(-4)">{{info.userNEmail}}
            </div>
          </div>
        </div>
        <div class="onduty_title"
             :style="$general.loadConfiguration(-1)">{{info.title}}</div>
        <div :style="$general.loadConfiguration(-3)+'color:#666;line-height:1.46;margin-top:0.1rem;'"
             v-html="info.content">
        </div>
        <div class="lam_time"
             :style="$general.loadConfiguration(-4)">{{dayjs(info.time).format('YYYY-MM-DD HH:mm')}}</div>
      </div>

      <div v-if="info.state == 1 && showReply()"
           style="border-top:0.1rem solid #e2dfdf;">
        <!--展示值班委员写回复-->
        <div v-if="ifCommitteeWY===true && showResponseBox()"
             class="onduty_box">
          <div class="flex_box flex_align_center">
            <div class="onduty_main_s flex_placeholder"
                 :style="$general.loadConfiguration(-3)">
              {{ifCommitteeWY===true?'我的':'委员'}}回复</div>
          </div>
          <div :style="$general.loadConfiguration(-3)">
            <van-field size="large"
                       v-model="reply.content"
                       type="textarea"
                       rows="9"
                       autosize
                       :placeholder="'回复内容...'"></van-field>
          </div>
        </div>
        <!--值班回复列表-->
        <div class="onduty_box"
             v-for="(item,index) in info.replys"
             :key="index">
          <div class="flex_box flex_align_center">
            <div class="onduty_main_s flex_placeholder"
                 :style="$general.loadConfiguration(-3)">
              {{ifCommitteeWY===true?'我的':'委员'}}回复</div>
            <van-icon v-if="ifCommitteeWY===true || isStaff===true"
                      @click="replyDel(item)"
                      class="review_del"
                      :size="((appFontSize-3)*0.01)+'px'"
                      color="#333"
                      name="delete-o"></van-icon>
            <div v-if="item.state && (ifCommitteeWY===true || isStaff===true)"
                 :style="$general.loadConfiguration(-6)+'color:'+(item.state==0?'#FE7530':item.state==1?'#3088FE':'#8C96A2')+';background:'+(item.state==0?'#FFE7DC':item.state==1?'#DCEBFF':'#EBF0F6')+';'"
                 class="review_status">{{item.state==0?'待审核':item.state==1?'审核通过':'审核不通过'}}</div>
          </div>
          <div :style="$general.loadConfiguration(-3)+'color:#666;line-height:1.46;margin-top:0.1rem;'"
               v-html="item.content"></div>
          <div class="lam_time"
               :style="$general.loadConfiguration(-4)">{{dayjs(item.time).format('YYYY-MM-DD HH:mm')}}
          </div>
        </div>
      </div>

    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    title
                    :row="3"
                    :key="index"></van-skeleton>
    </div>
    <!-- <template v-else-if="!title">
      <van-empty :style="$general.loadConfiguration(-2)"
                 :image="pageNot.url || pageNot.data[pageNot.type].url">
        <template #description>
          <div class="van-empty__description_text"
               :style="$general.loadConfiguration(-1)"
               v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
          <div class="van-empty__description_summary"
               :style="$general.loadConfiguration(-3)"
               v-html="pageNot.summary || pageNot.data[pageNot.type].summary"></div>
        </template>
        <div v-if="pageNot.hasBtn"
             :style="$general.loadConfiguration(-1)">
          <van-button v-if="(pageNot.type==2||pageNot.type==3)&&pageType=='page'"
                      @click.stop="T.closeWin()"
                      round
                      type="info"
                      size="large"
                      :color="appTheme">{{'返回'}}</van-button>
          <van-button v-else-if="pageNot.type==1||pageNot.type==4"
                      @click.stop="T.refreshHeaderLoading();"
                      round
                      type="info"
                      size="large"
                      :color="appTheme">{{'刷新'}}</van-button>
        </div>
      </van-empty>
    </template> -->

    <!--工作人员审核-->
    <template v-if="info.firstAjax && isStaff && (state == '0' || showStaffReview())">
      <div :style="'height: '+(safeAreaBottom+110)+'px;'"></div>
      <div class="review_box"
           :style="'bottom:'+(safeAreaBottom+58)+'px;'">
        <div class="flex_box flex_align_center flex_justify_content">
          <div @click="clickReview(1)"
               :style="$general.loadConfiguration(1)+'background:'+appTheme"
               class="review_item flex_box flex_align_center flex_justify_content">审核通过</div>
          <div @click="clickReview(0)"
               :style="$general.loadConfiguration(1)"
               class="review_item flex_box flex_align_center flex_justify_content">审核不通过</div>
        </div>
      </div>
    </template>
    <template v-if="info.firstAjax && ifCommitteeWY && state == 1 && showResponseBox()">
      <div :style="'height: '+(safeAreaBottom+110)+'px;'"></div>
      <div class="review_box"
           :style="'bottom:'+(safeAreaBottom+58)+'px;'">
        <div class="flex_box flex_align_center flex_justify_content">
          <div @click="clickReply(1)"
               :style="$general.loadConfiguration(1)+'background:'+appTheme"
               class="review_item flex_box flex_align_center flex_justify_content">提交</div>
        </div>
      </div>
    </template>

    <!--返回顶部 需要加一个空白占位 不然返回顶部 就会错位显示 -->
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box"
          :style="'bottom:'+(safeAreaBottom+58)+'px;'">
        {{'&nbsp;'}}
        <div :style="$general.loadConfiguration()">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((appFontSize+25))+'px'"
                        name="upgrade"></van-icon>
            </div>
            <div v-else-if="item.type == 'btn'"
                 class="van-button-box">
              <van-button loading-type="spinner"
                          :loading-size="((appFontSize))+'px'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)">{{item.name}}</van-button>
            </div>
            <div v-else-if="item.type == 'nBtn'"
                 class="footer_nBtn_box flex_box flex_align_center flex_justify_content"
                 :style="$general.loadConfigurationSize(26)+$general.loadConfiguration(-3)+'background:'+(item.color||appTheme)+';color:'+(T.isColorDarkOrLight(item.color||appTheme)=='light'?appTheme:'#fff')"
                 @click="footerBtnClick(item)">{{item.name}}</div>
          </template>
        </div>
      </ul>
    </transition>
    <!--不为一级页面时 适配底部条-->
    <footer v-if="pageType=='page'"
            :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer>
  </div>
</template>

<script>
import { Skeleton } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'lam_detail',
  components: {
    [Skeleton.name]: Skeleton
  },
  setup () {
    const route = useRoute()
    // const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      info: {},
      id: route.query.id,
      ifCommitteeWY: route.query.ifCommitteeWY,
      isStaff: route.query.isStaff,
      isCheck: route.query.isCheck,
      refreshing: false,
      footerBtnsShow: true,
      footerBtns: [],
      show: false
    })
    onMounted(() => {
      getData()
    })
    const onRefresh = () => {
      getData()
    }

    const getData = async () => {
      const res = await $api.committeeLivingRoom.committeeLivingRoomLetterInfo(data.id)
      const { data: info } = res
      const itemData = info
      info.title = itemData.title || ''// 标题
      info.firstAjax = itemData.title
      info.showSkeleton = false
      // item.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1// 类型
      // item.pageNot.text = ret && code != 200 ? ret.errmsg || ret.data : ''//
      info.content = itemData.content || ''
      info.id = itemData.id || ''
      info.state = itemData.isPublic
      info.userUrl = itemData.userHeadImg || ''
      info.userName = itemData.userName || ''
      info.userPhone = itemData.mobile || ''
      info.userEmail = itemData.email || ''
      info.time = itemData.createDate || ''
      info.replys = []
      var replyList = itemData.replyList || []
      replyList.forEach(function (_eItem, _eIndex, _eArr) {
        info.replys.push({
          id: _eItem.id,
          content: _eItem.content,
          time: _eItem.createDate,
          state: _eItem.isCheck || '0'
        })
      })
      data.info = info
      console.log(data.info)
    }
    // 是否展示回复栏
    const showReply = () => {
      var showReply = data.ifCommitteeWY
      data.info.replys.forEach(function (_eItem, _eIndex, _eArr) {
        if (_eItem.content && (_eItem.state === '1' || data.isStaff)) {
          showReply = true
        }
      })
      return showReply
    }

    // 是否展示值班委员输入框 在全部审核之后再展示   默认是true
    const showResponseBox = () => {
      var showReplyState = true
      data.info.replys.forEach(function (_eItem, _eIndex, _eArr) {
        if (_eItem.state === '0') {
          showReplyState = false
        }
      })
      return showReplyState
    }

    // 值班委员 删除回复
    // const replyDel = (_item) => {
    //   T.nPrompt({
    //     title: '温馨提示',
    //     msg: '确定要删除这条回复吗？',
    //     buttons: ['确定', '取消']
    //   }, function (ret, err) {
    //     if (ret.buttonIndex === 1) {
    //       T.showProgress("删除中");
    //       var url = zyUrl.getAppUrl() + 'comment/delsMyComment';
    //       var postParam = {
    //         keyId: recordId,
    //         type: relateType,
    //         areaId: zyUrl.getSiteID(),
    //         ids: _item.id
    //       };
    //       T.ajax({ u: url }, "comment/del", function (ret, err) {
    //         T.hideProgress()
    //         if (ret) {
    //           if (ret.errcode == "200") {
    //             T.toast(ret.errmsg);
    //             that.getData(0);
    //           } else {
    //             T.toast(T.NET_NO);
    //           }
    //         } else {
    //           T.toast(T.NET_ERR);
    //         }
    //       }, "删除回复", "post", {
    //         values: postParam
    //       })
    //     }
    //   })
    // }
    // 提交回复
    // clickReply: function (_status) {
    //   var that = this;
    //   if (!$api.trim(that.replyContent)) {
    //     T.toast('请输入回复内容');
    //     return;
    //   }
    //   T.nPrompt({
    //     title: '温馨提示',
    //     msg: '确认提交吗？',
    //     buttons: ['确定', '取消']
    //   }, function (ret, err) {
    //     if (ret.buttonIndex == 1) {
    //       T.showProgress("提交中");
    //       var url = zyUrl.getAppUrl() + "comment/save";
    //       T.ajax({ u: url }, "comment/save", function (ret, err) {
    //         T.hideProgress();
    //         if (ret) {
    //           if (ret.errcode == "200") {
    //             T.toast(ret.errmsg);
    //             that.getData(0);
    //             that.replyContent = "";
    //           } else {
    //             T.toast(T.NET_NO);
    //           }
    //         } else {
    //           T.toast(T.NET_ERR);
    //         }
    //       }, "留言回复", "post", {
    //         values: {
    //           "createBy": userId,
    //           "commentPid": "",
    //           "keyId": that.id,
    //           "content": that.replyContent,
    //           "extend": "1",
    //           "isCheck": that.isCheck == "0" ? "1" : "0",//先默认审核通过 后面去除
    //           "type": "50",//commentPid=="0"?relateType:'101'
    //           "scId": that.pageParam.officeId,
    //           "areaId": zyUrl.getSiteID()
    //         }
    //       });
    //     }
    //   });
    // }

    const showStaffReview = () => {
      var showStaffReview = ''
      for (var i = 0; i < data.info.replys.length; i++) {
        if (data.info.replys[i].state === '0') { // 回复中有待审核的
          showStaffReview = data.info.replys[i]
          break
        }
      }
      return showStaffReview
    }

    const test = (val) => { console.log(val) }
    return { ...toRefs(data), onRefresh, $general, test, dayjs, showReply, showResponseBox, showStaffReview }
  }
}
</script>

<style lang="less" scoped>
.committeeLivingRoom {
  .onduty_box {
    padding: 0.14rem 0.15rem;

    .onduty_user_img {
      object-fit: contain;
      border-radius: 50%;
      background: #ffffff;
      margin-right: 0.1rem;
    }

    .onduty_main_s {
      font-weight: 600;
      color: #333333;
      line-height: 1.46;
    }

    .onduty_user_phone {
      color: #333;
      margin-left: 0.1rem;
      line-height: 1.33;
    }

    .review_status {
      padding: 0.02rem 0.1rem;
      line-height: 1.3;
      border-radius: 0.02rem;
    }

    .onduty_user_email {
      color: #666666;
      margin-top: 0.03rem;
      line-height: 1.33;
    }

    .onduty_title {
      font-weight: 600;
      line-height: 1.46;
      color: #333333;
      margin-top: 0.1rem;
    }

    .lam_time {
      line-height: 1.3;
      color: #666666;
      margin-top: 0.05rem;
    }
  }

  .review_box {
    position: fixed;
    width: 100%;
  }

  .review_item {
    min-width: 1.27rem;
    padding: 0.04rem 0;
    background: #8c96a2;
    border-radius: 0.04rem;
    color: #fff;
    line-height: 1.41;
  }

  .review_item + .review_item {
    margin-left: 0.3rem;
  }

  #app .van-field {
    padding: 0.1rem 0rem;
  }

  .review_del {
    padding: 0.03rem;
    margin-right: 0.05rem;
  }
}
</style>

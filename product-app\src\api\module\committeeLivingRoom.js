import { HTTP } from '../http.js'
class committeeLivingRoom extends HTTP {
  // 列表
  committeeLivingRoomList (params) {
    return this.request({
      url: '/officeonlinetopic/list',
      data: params
    })
  }

  // 详情
  committeeLivingRoomInfo (params) {
    return this.request({
      url: `/officeonlinetopic/info/${params}`
    })
  }

  // 留言列表
  committeeLivingRoomLetterList (params) {
    return this.request({
      url: '/officeonlineletter/list',
      data: params
    })
  }

  // 留言列表详情
  committeeLivingRoomLetterInfo (params) {
    return this.request({
      url: `/officeonlineletter/info/${params}`
    })
  }

  // 留言列表详情
  addCommittee (params) {
    return this.request({
      url: '/officeonlineletter/add',
      data: params
    })
  }
}
export {
  committeeLivingRoom
}

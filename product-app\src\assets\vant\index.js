import { Button, Search, Tab, Tabs, DatetimePicker, Picker, Empty, Switch, Form, Collapse, CollapseItem, DropdownMenu, DropdownItem, Cell, RadioGroup, Radio, Checkbox, CheckboxGroup, Popup, List, Swipe, SwipeItem, PullRefresh, Field, CellGroup, Tabbar, TabbarItem, Icon, Popover } from 'vant'

const vant = { Button, Search, Tab, Tabs, DatetimePicker, Picker, Empty, Switch, Form, Collapse, CollapseItem, DropdownMenu, DropdownItem, Cell, RadioGroup, Radio, Checkbox, CheckboxGroup, Popup, List, Swipe, SwipeItem, PullRefresh, Field, CellGroup, Tabbar, TabbarItem, Icon, Popover }

export default {
  install (Vue) {
    Object.keys(vant).forEach((key) => {
      Vue.component(vant[key].name, vant[key])
    })
  }
}

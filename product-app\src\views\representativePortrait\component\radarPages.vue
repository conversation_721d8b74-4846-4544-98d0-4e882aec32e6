<template>
  <div class="pie_page">
    <div class="pie"
         :id="id"></div>
  </div>

</template>
<script>
// import { Toast } from 'vant'
// import { useRoute } from 'vue-router'
import { onMounted, reactive, toRefs, nextTick } from 'vue'
import * as echarts from 'echarts'
export default {
  name: 'radarChart1',
  props: {
    listData: Array,
    id: String
  },
  setup (props) {
    const data = reactive({})
    var myChart = null
    onMounted(() => {
      nextTick(() => {
        myChart = echarts.init(document.getElementById(props.id))
        pieBtn()
      })
      // 让图表跟随屏幕自动的去适应
      window.addEventListener('resize', function () {
        myChart.resize()
        pieBtn()
      })
    })
    const pieBtn = () => {
      var radarListData = props.listData
      var option = {
        radar: [{
          indicator: radarListData,
          center: ['50%', '50%'], // 控制雷达图的位置
          radius: 110, // 控制雷达图的大小
          // startAngle: 90,
          splitNumber: 4,
          // shape: 'circle',
          name: {
            // show: false,
            formatter: function (value, indicator) {
              // return '{a|' + indicator.num + '}{c|分}\n{b|' + value + '}'
              return '{b|' + value + '}'
            },
            rich: {
              a: {
                color: '#3272EE',
                fontSize: 38,
                align: 'center',
                fontFamily: 'YouSheBiaoTiHei-2'
              },
              b: {
                fontSize: 13,
                fontWidth: 'bold',
                color: '#204A86',
                padding: 1
              },
              c: {
                fontSize: 14,
                color: '#3272EE'
              }
            },
            textStyle: {
              color: '#fff'
            }
          },
          splitArea: {
            areaStyle: {
              color: ['rgba(113, 158, 251,0.6)', 'rgba(135, 171, 251, 0.5)'],
              shadowBlur: 0
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgb(118,162,247)',
              width: 2
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#D7E0E7',
              width: 1 // 分隔线线宽
            }
          }
        }
        ],
        series: [{
          name: '雷达图',
          type: 'radar',
          symbolSize: 0,
          symbol: 'circle',
          lineStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#fff'
            }, {
              offset: 1,
              color: '#fff'
            }], false),
            width: 4
          },
          itemStyle: {
            color: '#fff',
            // borderColor: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //   offset: 0,
            //   color: '#3894FF'
            // }, {
            //   offset: 1,
            //   color: '#3894FF'
            // }], false),
            borderWidth: 2,
            opacity: 1
          },
          // areaStyle: {
          //   normal: {
          //     color: 'rgba(56,148,255,0.5)'
          //   },
          //   emphasis: {
          //     color: 'rgba(56,148,255,0.5)'
          //   }
          // },
          // lineStyle: {
          //   normal: {
          //     color: '#f9d400',
          //     type: 'solid',
          //     width: 0
          //   },
          //   emphasis: {}
          // },
          data: [{
            value: radarListData.map(item => item.num),
            label: {
              show: false
            }
          }]
        }]
      }
      nextTick(() => {
        myChart.setOption(option)
      })
    }
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less">
.pie_page {
  text-align: center;
  .pie_mark {
    font-size: 20px;
    font-weight: bold;
  }
}
.pie {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
</style>

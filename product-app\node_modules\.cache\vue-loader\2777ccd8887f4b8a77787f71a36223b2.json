{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue", "mtime": 1756438284587}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue"], "names": [], "mappings": ";AAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/I,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACT,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACb;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;gBACtB,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB;YACF,CAAC;YACD,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE;cACJ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;cACjC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;cACjC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC;UACF;QACF;MACF;MACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE;cACJ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;cACvE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChE;UACF;QACF;MACF;MACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;cAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,CAAC,CAAC,EAAE;kBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd;cACF;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE;cACJ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;cACjC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;cACjC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC;UACF;QACF;MACF;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC;IACH;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACpC;AACF", "file": "D:/zy/xm/h5/qdrd_h5/product-app/src/views/leaderDriving/components/pie.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :id=\"id\">\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nimport { debounce } from '../../../utils/debounce.js'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'\r\nexport default {\r\n  name: 'pie',\r\n  components: {\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: <PERSON><PERSON>\r\n  },\r\n  props: {\r\n    color: String,\r\n    id: String,\r\n    list: Array\r\n  },\r\n  setup (props) {\r\n    const route = useRoute()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    // const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      viewportWidth: ''\r\n    })\r\n    var myChart = null\r\n    onMounted(() => {\r\n      nextTick(() => {\r\n        var chartDom = document.getElementById(props.id)\r\n        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth\r\n        myChart = echarts.init(chartDom)\r\n        setOptions()\r\n      })\r\n      // 监听窗口尺寸变化事件\r\n      window.addEventListener('resize', debounce(() => {\r\n        myChart.resize() // 调整图表大小\r\n        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth\r\n        setOptions()\r\n      }, 500))\r\n    })\r\n    const setOptions = () => {\r\n      // console.log(parseInt(data.viewportWidth * 0.04))\r\n      var options = {\r\n        legend: {\r\n          orient: 'horizontal', // 或 'horizontal'\r\n          bottom: -parseInt(data.viewportWidth * 0.013),\r\n          left: 'center',\r\n          width: parseInt(data.viewportWidth * 0.9),\r\n          height: parseInt(data.viewportWidth * 0.4),\r\n          padding: [parseInt(data.viewportWidth * 0.1), parseInt(data.viewportWidth * 0.01)],\r\n          // 设置图例文字的样式\r\n          textStyle: {\r\n            fontSize: parseInt(data.viewportWidth * 0.03),\r\n            color: '#ADADAD'\r\n          },\r\n          formatter: function (name) { // 该函数用于设置图例显示后的百分比\r\n            var total = 0\r\n            var value\r\n            props.list.map(v => {\r\n              return {\r\n                value: v.value,\r\n                name: v.name\r\n              }\r\n            }).forEach((item) => {\r\n              total += Number(item.value)\r\n              if (item.name === name) {\r\n                value = item.value\r\n              }\r\n            })\r\n            var p = Math.round(((value / total) * 100)) // 求出百分比\r\n            return `${name} | ${p}%` // 返回出图例所显示的内容是名称+百分比\r\n          },\r\n          itemWidth: parseInt(data.viewportWidth * 0.04),\r\n          itemHeight: parseInt(data.viewportWidth * 0.03)\r\n        },\r\n        color: ['#3893ff', '#a938fb', '#ffd434', '#ff7389', '#17c78a', '#ff7a2d'],\r\n        series: [\r\n          {\r\n            name: '代表年龄分析',\r\n            type: 'pie',\r\n            startAngle: 20, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.04) + '%', parseInt(data.viewportWidth * 0.1) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.015),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.009)\r\n            },\r\n            label: {\r\n              bleedMargin: 10,\r\n              show: true,\r\n              position: 'outside',\r\n              // data: ['f'],\r\n              formatter: function (params) {\r\n                return `${params.name}\\n${params.value}人`\r\n              },\r\n              fontSize: parseInt(data.viewportWidth * 0.03),\r\n              alignTo: 'labelLine'\r\n            },\r\n            labelLine: {\r\n              show: true,\r\n              length: parseInt(data.viewportWidth * 0.05),\r\n              length2: parseInt(data.viewportWidth * 0.12)\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine' },\r\n              { value: 735, name: 'Direct' },\r\n              { value: 580, name: 'Email' },\r\n              { value: 484, name: 'Union Ads' },\r\n              { value: 300, name: 'Video Ads' },\r\n              { value: 200, name: 'ghjkgjkAds' }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      var options2 = {\r\n        tooltip: {\r\n          trigger: 'item'\r\n        },\r\n        series: [\r\n          {\r\n            name: '性别',\r\n            type: 'pie',\r\n            startAngle: 60, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.11) + '%', parseInt(data.viewportWidth * 0.18) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.015),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.01)\r\n            },\r\n            label: {\r\n              show: false\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine', itemStyle: { color: '#3da2ff' } },\r\n              { value: 735, name: 'Direct', itemStyle: { color: '#ff738c' } }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      var options3 = {\r\n        color: ['#67DBFF', '#FF61C9', '#64A2FF', '#FF9567', '#BCFF87', '#FF6D6D', '#61E89F', '#BC87FF', '#FFD056'],\r\n        series: [\r\n          {\r\n            name: '类型占比',\r\n            type: 'pie',\r\n            minAngle: 30,\r\n            // startAngle: 90, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.06) + '%', parseInt(data.viewportWidth * 0.12) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.0),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.008)\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'outside',\r\n              // data: ['f'],\r\n              formatter: function (params) {\r\n                return `${params.data.proportion}%\\n${params.name}`\r\n              },\r\n              fontSize: parseInt(data.viewportWidth * 0.024),\r\n              alignTo: 'labelLine',\r\n              rich: {\r\n                name: {\r\n                  fontSize: 14,\r\n                  color: '#333',\r\n                  align: 'center'\r\n                },\r\n                value: {\r\n                  fontSize: 12,\r\n                  color: '#999'\r\n                }\r\n              }\r\n            },\r\n            labelLayout: function (params) {\r\n              const isLeft = params.labelRect.x < myChart.getWidth() / 2\r\n              const points = params.labelLinePoints\r\n              points[2][0] = isLeft\r\n                ? params.labelRect.x\r\n                : params.labelRect.x + params.labelRect.width\r\n              return {\r\n                labelLinePoints: points\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: true,\r\n              length: parseInt(data.viewportWidth * 0.08),\r\n              length2: parseInt(data.viewportWidth * 0.1)\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine' },\r\n              { value: 735, name: 'Direct' },\r\n              { value: 580, name: 'Email' },\r\n              { value: 484, name: 'Union Ads' },\r\n              { value: 300, name: 'Video Ads' },\r\n              { value: 200, name: 'ghjkgjkAds' }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      nextTick(() => {\r\n        if (props.id === 'pie1') {\r\n          options.series[0].data = props.list\r\n          myChart.setOption(options)\r\n        } else if (props.id === 'pie2') {\r\n          options2.series[0].data = props.list\r\n          myChart.setOption(options2)\r\n        } else if (props.id === 'pie3') {\r\n          options3.series[0].data = props.list\r\n          myChart.on('click', (e) => {\r\n            // console.log(e)\r\n            // window.location.href = e.data.url\r\n          })\r\n          myChart.setOption(options3)\r\n        }\r\n      })\r\n    }\r\n    return { ...toRefs(data), general }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#pie1 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 260px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#pie2 {\r\n  width: 100%;\r\n  height: 100%;\r\n  // margin: 10px 0;\r\n  box-sizing: border-box;\r\n  background: #fff;\r\n}\r\n\r\n#pie3 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 220px;\r\n  margin: 10px 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#pie4 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 100%;\r\n  // margin: 10px 0;\r\n  box-sizing: border-box;\r\n}\r\n</style>\r\n"]}]}
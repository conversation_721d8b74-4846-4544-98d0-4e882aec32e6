<template>
  <div class="newsZTList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <!-- <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" /> -->
      </div>
    </van-sticky>
    <div v-if="themeImg.url">
      <img style="width:100%;"
           fit="cover"
           :src="themeImg.url" />
    </div>
    <!--图标栏目-->
    <div v-if="menuList.length > 0"
         class="menu">
      <div class="flex_box T-flex-flow-row-wrap">
        <div v-for="(item,index) in menuList"
             :key="index"
             class="menu_item T-flexbox-vertical flex_align_center flex_justify_content"
             @click="itemClick(item)">
          <img :src="item.url"
               :style="'width:39px;height:39px;'" />
          <p v-html="item.label"
             :style="'font-size:13px;' + 'margin-top: 9px;'"></p>
          <p v-if="item.pointNumber > 0"
             class="flex_box flex_align_center flex_justify_content text_one"
             :class="item.pointType == 'big'?'footer_item_hot_big':'footer_item_hot'"
             :style="item.pointType == 'big'?'font-size:12px;'+'width:20px;height:20px;':'width:12px;height:12px;'"
             v-html="item.pointType == 'big'?(item.pointNumber>99?'99+':item.pointNumber):''"></p>
        </div>
      </div>
    </div>
    <van-tabs v-model:active="switchs.value"
              v-if="switchs.data>1"
              swipeable
              sticky
              offset-top="46px"
              :color="appTheme"
              :ellipsis="false"
              line-height='3'>
      <van-tab v-for="item in switchs.data"
               :title="item.label"
               :name="item.id"
               :key="item.id">
        <van-pull-refresh v-model="refreshing"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <!--数据列表-->
            <ul class="vue_newslist_box">
              <template v-for="(item,index) in dataList"
                        :key="index"
                        class="">
                <van-cell clickable
                          class="vue_newslist_item "
                          @click="details(item)">
                  <div class="flex_box">
                    <img class="vue_newslist_img"
                         v-if="item.url"
                         :src="item.url"
                         :alt="cacheImg(item)" />
                    <div class="flex_placeholder vue_newslist_warp">
                      <div class="vue_newslist_title text_two"
                           :style="'font-size:17px;'">
                        {{item.title}}
                      </div>
                      <div v-if="item.url"
                           class="vue_newslist_time"
                           :style="'font-size:13px;' + 'margin-bottom:9px;'">{{item.time.split(' ')[0]}}</div>
                      <div v-else
                           class="vue_newslist_summary text_one2"
                           :style="'font-size:13px;'"
                           v-html="item.content.replace(/<[^>]+>/g, '').replace(/\s*/g,'')"></div>
                      <div class="flex_box flex_align_center">
                        <div v-if="!item.url"
                             class="vue_newslist_time"
                             :style="'font-size:13px;'">{{item.time.split(' ')[0]}}</div>
                        <div class="vue_newslist_source"
                             :style="'font-size:13px;'">{{item.source || item.createBy}}</div>
                        <div class="flex_placeholder"></div>
                        <div v-if="item.type || item.state"
                             class="vue_newslist_more_right"
                             :style="'font-size:13px;'">
                          <van-tag v-if="item.type"
                                   plain
                                   :color="appTheme">{{item.type}}</van-tag>
                          <van-tag v-if="item.state"
                                   plain
                                   :color="appTheme">{{item.state}}</van-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </van-cell>
              </template>
            </ul>
            <!--加载中提示 首次为骨架屏-->
            <div v-if="showSkeleton"
                 class="notText">
              <van-skeleton v-for="(item,index) in 3"
                            :key="index"
                            title
                            :row="3"></van-skeleton>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <ul class="vue_newslist_box"
        v-else>
      <template v-for="(item,index) in dataList"
                :key="index"
                class="">
        <van-cell clickable
                  class="vue_newslist_item "
                  @click="details(item)">
          <div class="flex_box">
            <img class="vue_newslist_img"
                 v-if="item.url"
                 :src="item.url"
                 :alt="cacheImg(item)" />
            <div class="flex_placeholder vue_newslist_warp">
              <div class="vue_newslist_title text_two"
                   :style="'font-size:17px;'">
                {{item.title}}
              </div>
              <div v-if="item.url"
                   class="vue_newslist_time"
                   :style="'font-size:13px;' + 'margin-bottom:9px;'">{{item.time.split(' ')[0]}}</div>
              <div v-else
                   class="vue_newslist_summary text_one2"
                   :style="'font-size:13px;'"
                   v-html="item.content.replace(/<[^>]+>/g, '').replace(/\s*/g,'')"></div>
              <div class="flex_box flex_align_center">
                <div v-if="!item.url"
                     class="vue_newslist_time"
                     :style="'font-size:13px;'">{{item.time.split(' ')[0]}}</div>
                <div class="vue_newslist_source"
                     :style="'font-size:13px;'">{{item.source || item.createBy}}</div>
                <div class="flex_placeholder"></div>
                <div v-if="item.type || item.state"
                     class="vue_newslist_more_right"
                     :style="'font-size:13px;'">
                  <van-tag v-if="item.type"
                           plain
                           :color="appTheme">{{item.type}}</van-tag>
                  <van-tag v-if="item.state"
                           plain
                           :color="appTheme">{{item.state}}</van-tag>
                </div>
              </div>
            </div>
          </div>
        </van-cell>
      </template>
    </ul>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'newsZTList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      id: route.query.id,
      columnId: route.query.columnId,
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      dataList: [],
      switchs: { value: '', data: [] },
      themeImg: { url: '' },

      menuList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      if (data.columnId) {
        data.switchs.value = data.columnId
      }
      // browseSave()
      // getSpecialsubjectinfo()
      // getTree()
    })
    // const browseSave = async () => {
    //   const res = await $api.notice.browseSave({
    //     keyId: data.id,
    //     type: 5
    //   })
    //   console.log(res)
    // }
    // const getSpecialsubjectinfo = async () => {
    //   const { data: info } = await $api.news.getSpecialsubjectinfo({
    //     id: data.id
    //   })
    //   var themeImgs = info.coverImg || {}
    //   data.themeImg.url = themeImgs.fullUrl || ''
    // }
    watch(() => data.switchs.value, (newName, oldName) => {
      console.log(newName, oldName)
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 栏目请求
    // const getTree = async () => {
    //   const res = await $api.news.getSpecialsubjectColumnList({
    //     pageNo: 1,
    //     pageSize: 1000,
    //     subjectId: data.id,
    //     isOpen: 1
    //   })
    //   var { data: tree } = res
    //   data.switchs.data = []
    //   var newData = []
    //   tree.forEach(item => {
    //     item.label = item.name
    //     item.value = item.id
    //     var iconopen = item.iconopen || ''//
    //     var icon = item.icon || {}//
    //     var iconUrl = icon.fullUrl || ''
    //     // eslint-disable-next-line eqeqeq
    //     if (iconopen == '1') {
    //       item.url = iconUrl
    //       item.pointType = 'big'// 红点类型
    //       item.pointNumber = 0// 数量
    //       data.menuList.push(item)
    //     } else {
    //       newData.push(item)
    //     }
    //     // eslint-disable-next-line eqeqeq
    //     if (!data.switchs.value && iconopen != '1') { // 设置一个默认值,默认值不能为图标栏目的id
    //       data.switchs.value = item.value
    //     }
    //   })
    //   data.switchs.data = data.switchs.data.concat(newData)
    //   getList()
    // }
    // 列表请求
    const getList = async () => {
      var res = await $api.news.getSpecialsubjectRelateinfoList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        title: data.keyword,
        subjectId: data.id,
        columnId: data.switchs.value,
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1
      })

      var { data: list, total } = res
      list.forEach(item => {
        item.type = item.relateRecordType || ''
        item.class = item.infoClass || ''
        item.id = item.relateRecordId || ''
        item.time = item.createDate || ''// 时间
        if ((data.keyword).replace(/(^\s*)|(\s*$)/g, '')) {
          item.title = item.title.replace(new RegExp(data.keyword, 'g'), '<span style="color:' + data.appTheme + ';" class="inherit">' + data.keyword + '</span>')
        }
        // 去除HTML中的注释、去除HTML标签、去除HTML标签中的属性、去除所有空白字符即回车换行
        item.content = item.content ? DeleteHtmlFromStartToEnd(item.content, '<!--', '-->').replace(/<.*?>/g, '').replace(/&nbsp;/ig, '') : ''
      })
      data.dataList = data.dataList.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const DeleteHtmlFromStartToEnd = (str, begin, end) => {
      str = str.replace(begin + end, '')
      if (str.indexOf(begin) === -1) {
        return str
      }
      var substr = str.substring(str.indexOf(begin) + begin.length, str.indexOf(end))
      str = str.replace(substr, '')
      return DeleteHtmlFromStartToEnd(str, begin, end)
    }
    const onClickLeft = () => history.back()
    const itemClick = (row) => {
      console.log(row)
      router.push({ name: 'newsZTList', query: { id: row.id, title: row.name } })
    }
    const details = (row) => {
      console.log(row)
      // eslint-disable-next-line eqeqeq
      if (row.relateType == '5' || row.relateType == '53') {
        router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title } })
        // eslint-disable-next-line eqeqeq
      } else if (row.relateType == '27') {
        router.push({ name: 'noticeDetails', query: { id: row.id, title: row.title } })
      }
    }
    return { ...toRefs(data), search, onClickLeft, onRefresh, onLoad, details, itemClick }
  }
}
</script>

<style lang="less" scoped>
.newsZTList {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .menu {
    background: #fff;
    padding: 8px 0 25px 0;
  }
  .menu_warp {
  }
  .menu .menu_item {
    position: relative;
    width: 25%;
    padding: 10px 0;
  }
  .menu .menu_item p {
    color: #3e3e3e;
    margin-top: 3px;
    text-align: center;
  }
  .menu .menu_item .footer_item_hot {
    position: absolute;
    top: 4px;
    right: 25%;
    width: 10px;
    height: 10px;
    background: #f92323;
    border-radius: 50%;
  }
  .menu .menu_item .footer_item_hot_big {
    position: absolute;
    top: 1px;
    right: 20%;
    width: 20px;
    height: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
  }
}
</style>

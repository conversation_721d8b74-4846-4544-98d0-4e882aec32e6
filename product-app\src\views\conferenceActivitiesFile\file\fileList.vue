<template>
  <div class="fileList">

    <!--展示大号附件-->
    <div v-if="listData.length != 0"
         class="general_attach_big">
      <div v-for="(item,index) in listData"
           :key="index"
           class="flex_box flex_align_center">
        <div class="general_attach_big_item van-hairline--bottom flex_box flex_align_center click"
             @click="openDetails(item)">
          <img v-if="pageParam.isDetails"
               class="general_attach_big_icon"
               :style="general.loadConfigurationSize([26,32])"
               :src="require('../../../assets/img/fileicon/'+item.iconInfo.name)" />
          <img v-else
               class="general_attach_big_icon"
               :style="general.loadConfigurationSize(26,'w')"
               src="../../../assets/img/fileicon/icon_packge.png" />
          <div class="flex_placeholder">
            <div class="general_attach_big_name text_one2"
                 :style="general.loadConfiguration(-1)">{{item.name}}
            </div>
            <div v-if="pageParam.isDetails"
                 class="flex_box flex_align_center"
                 style="margin-top:0.06rem;">
              <div v-if="item.size"
                   class="general_attach_big_size flex_placeholder"
                   :style="general.loadConfiguration(-4)">{{getFileSize(item.size)}}</div>
              <div v-if="item.state != 2"
                   class="general_attach_state flex_box flex_align_center flex_justify_content"
                   :style="general.loadConfigurationSize([7,7])">
                <van-icon v-if="item.state == 0"
                          class-prefix="iconfont"
                          color="#ccc"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          name="xiazai"></van-icon>
                <van-circle v-else-if="item.state == 1"
                            :size="((appFontSize+3)*0.01)+'rem'"
                            v-model="item.schedule"
                            :rate="item.schedule"
                            stroke-width="150"></van-circle>
                <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                          v-else-if="item.state == 3"
                          color="#ccc"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          name="warning-o"></van-icon>
              </div>
            </div>
          </div>
          <van-switch v-if="management && !pageParam.isDetails"
                      :active-color="appTheme"
                      @click.stop=""
                      @change="editIsAppShow(item)"
                      v-model="item.isShow"
                      :size="((appFontSize+8)*0.01)+'rem'">
          </van-switch>
        </div>
      </div>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
    <template v-if="showEmpty||listData.length == 0">
      <!-- 缺省图 -->
      <vantEmptyGeneral />
    </template>
    <!-- <div v-else
         class="notText"
         :style="general.loadConfiguration(-2)"
         v-html="pageNot.text"
         @click="loadMore()"></div> -->
    <!--返回顶部 需要加一个空白占位 不然返回顶部 就会错位显示 -->
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box">
        {{'&nbsp;'}}
        <div :style="general.loadConfiguration()">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((appFontSize+25)*0.01)+'rem'"
                        name="upgrade"></van-icon>
            </div>
            <div v-if="item.type == 'btn'"
                 class="van-button-box">
              <van-button loading-type="spinner"
                          :loading-size="((appFontSize)*0.01)+'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </ul>
    </transition>
    <!--不为一级页面时 适配底部条-->
    <footer :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import vantEmptyGeneral from '@/views/component/vantEmptyGeneral'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'fileList',
  components: {
    vantEmptyGeneral,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      showEmpty: false,
      pageParam: { isDetails: false },
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      id: route.query.id,
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      scrollTop: 0, // 页面划动距离
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      listData: [
        // {"isShow":true,"fileId":"344003028359053312","url":"http://test.dc.cszysoft.com:21408/lzt/flowAttachment/masterRepository/2021/06/04/343985655451222016.jpg","name":"20160424183425_uiNyv.jpeg.jpg","size":"267068","path":"/storage/emulated/0/UZMap/A6168351706281/fileCache/20160424183425_uiNyv.jpeg.jpg","iconInfo":{"name":"icon_pic.png","type":"image"},"schedule":-1,"state":0}
      ], // 列表数据
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部  btn为按钮

      management: false // 是否资料管理

    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      if (data.id) {
        getDetails()
      } else {
        data.showEmpty = true
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })

    watch(() => data.dataList, (newName, oldName) => {

    })
    const getDetails = async () => {
      var res = []
      var postParam = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        conferenceId: data.id,
        isRelease: 1
      }
      res = await $api.activity.conferencematerialList(postParam) // 详情
      var { data: list } = res
      console.log('🚀 ~ file: fileList.vue ~ line 222 ~ getDetails ~ list', list)
      if (general.isArray(list) && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.id = _eItem.id
          // if (that.pageParam.isDetails) {
          // item.name = _eItem.fileName || ''
          // item.url = _eItem.filePath || ''
          //   that.annexCheck(item);
          // } else {
          item.files = _eItem.files || ''
          item.isShow = _eItem.isRelease === 1// 是否显示
          item.name = _eItem.name || ''
          // }
          data.listData.push(item)
        })
        data.pageNo++
        // data.pageNot.text = data.listData.length == 0 ? "" : data.length >= postParam.pageSize ? T.LOAD_MORE : T.LOAD_ALL;//当前返回的数量 等于 请求的数量 说明可能还有 少于说明没有了
        // data.cleanIosDelay()
        // data.refreshPageSize = Math.ceil(data.listData.length / data.pageSize) * data.pageSize, data.pageNo = Math.ceil(data.listData.length / data.pageSize) + 1;
      }
      // else if (_type == 1) {//加载更多的时候 底部显示文字
      //   data.pageNot.text = ret ? code == 200 ? T.LOAD_ALL : ret.errmsg : T.NET_ERR;
      // }
      // _________________________________________
    }

    const openDetails = (item) => {
      console.log('🚀 ~ file: fileList.vue ~ line 237 ~ openDetails ~ item', item)
      router.push({
        name: 'fileList2', query: { id: item.id, files: item.files, isShow: item.isShow, title: item.name }
      })
    }
    const onRefresh = () => {
    }
    const onLoad = () => {

    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, general, confirm, openDetails }
  }
}
</script>
<style lang="less" scoped>
.fileList {
  background: #f8f8f8;
}
</style>

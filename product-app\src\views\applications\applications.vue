<template>
  <div class="applications">
    <!-- 我的应用 -->
    <div class="allApp">
        <div class="allApp_title">
            我的应用
        </div>
        <van-grid :column-num="4">
            <van-grid-item v-for="item in menuList" :key="item.id"  @click="itemClick(item)">
                <template v-slot:default>
                    <div class="allApp_item">
                        <img :src="item.url" alt="" class="allApp_item_img">
                        <div class="allApp_item_con">{{ item.name }}</div>
                    </div>
                </template>
            </van-grid-item>
        </van-grid>
    </div>
    <!-- 其他应用 -->
    <div class="allApp">
        <div class="allApp_title">
            其他应用
        </div>
        <van-grid :column-num="4">
            <van-grid-item v-for="item in restList" :key="item.id"  @click="itemClick(item)">
                <template v-slot:default>
                    <div class="allApp_item">
                        <img :src="item.url" alt="" class="allApp_item_img">
                        <div class="allApp_item_con">{{ item.name }}</div>
                    </div>
                </template>
            </van-grid-item>
        </van-grid>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Grid, GridItem, Toast } from 'vant'
export default ({
  name: 'applications',
  props: {},
  components: {
    [Grid.name]: Grid,
    [GridItem.name]: GridItem
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')) || {},
      menuList: [], // 我的应用
      restList: [], // 其他应用
      token: sessionStorage.getItem('token') || ''
    })
    onMounted(() => {
      getData()
    })
    // 获取数据
    const getData = async () => {
      var res = await $api.general.appList({ parentId: sessionStorage.getItem('historyIndex'), areaId: data.user.areaId })
      var { data: list } = res
      list = list[0].children || []
      const newData = []
      list.forEach(item => {
        item.url = item.iconUrl// 图标地址
        item.pointType = 'big'// 红点类型
        item.pointNumber = 0// 数量
        if (item.infoUrl2 && item.infoUrl2 !== '#') {
          newData.push(item)
        } else {
          data.restList.push(item)
        }
      })
      data.menuList = newData
    }
    // 栏目点击
    const itemClick = (_item) => {
      switch (_item.name) {
        case '意见征集':
          if (_item.infoUrl2) {
            var routerStr2 = _item.infoUrl2 + (_item.remarks === null ? '' : '?' + _item.remarks)
            var pageData2 = {}
            if (_item.infoUrl2.indexOf('?')) {
              var pageArr2 = _item.infoUrl2.split('?')
              // console.log(pageArr2)
              var pageIndex2 = pageArr2.length - 1
              pageData2[pageArr2[pageIndex2].split('=')[0]] = pageArr2[pageIndex2].split('=')[1]
            }
            if (routerStr2.indexOf('http') === 0) {
              window.location.href = routerStr2
              return false
            }
            router.push({ path: routerStr2, query: pageData2 })
          } else {
            Toast('请配置好H5路由')
          }
          break
        case '群众留言':
          var tok = data.token.split('"')[1]
          window.location.href = `http://123.206.212.39/qingdaord-meet-app/#/crowdReply?token=${tok}`
          break
        case '圈子':
          if (_item.infoUrl2) {
            var routerStr1 = _item.infoUrl2 + (_item.remarks === null ? '' : '?' + _item.remarks)
            var pageData = {}
            if (_item.infoUrl2.indexOf('?')) {
              var pageArr = _item.infoUrl2.split('?')
              var pageIndex = pageArr.length - 1
              pageData[pageArr[pageIndex].split('=')[0]] = pageArr[pageIndex].split('=')[1]
            }
            if (routerStr1.indexOf('http') === 0) {
              window.location.href = routerStr1
              return false
            }
            router.push({ path: routerStr1, query: pageData })
          } else {
            Toast('请配置好H5路由')
          }
          break
        default:
          if (_item.infoUrl2) {
            var routerStr = _item.infoUrl2 + '?' + (_item.remarks === null ? '' : '?' + _item.remarks)
            var myParam = { title: _item.name }
            if (_item.remarks) {
              const a = _item.remarks.split('&')
              a.forEach(element => {
                myParam[element.split('=')[0]] = element.split('=')[1]
              })
            }
            if (routerStr.indexOf('http') === 0) {
              window.location.href = routerStr
              return false
            }
            router.push({ path: routerStr, query: myParam })
          } else {
            Toast('请配置好H5路由')
          }
          break
      }
    }
    return { ...toRefs(data), dayjs, route, router, $api, itemClick }
  }
})
</script>
<style lang='less' scoped>
.applications {
    .allApp {
        width: 100%;
        .allApp_title {
            width: 100%;
            height: 50px;
            border-bottom: 1px solid #e5e4e4;
            box-sizing: border-box;
            line-height: 50px;
            padding: 0 20px;
            font-weight: 700;
        }
        .allApp_item {
            width: 100%;
            height: 80px;
            .allApp_item_img {
                width: 70%;
                height: 70%;
            }
            .allApp_item_con {
                font-size: 12px;
            }
        }
    }
}
</style>

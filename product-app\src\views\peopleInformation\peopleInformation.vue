<template>
  <div class="peopleInformation">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <van-popup v-model="show"
                 position="top">
        <van-cell center
                  :title="item.name"
                  @click="getPopup(item)"
                  v-for="(item,index) in popupData"
                  :key="index"></van-cell>
      </van-popup>
      <div class="top_box_bg"
           :style="'background:'+appTheme"></div>
      <div class="box_one flex_box flex_align_center">
        <div class="box_one_left flex_placeholder">
          <div :style="$general.loadConfiguration(6)">{{keyWord.split('届')[0]}}届</div>
          <div :style="$general.loadConfiguration(6)">{{keyWord.split('届')[1]}}</div>
          <!--<img src="../../../images/sanjiao.png" />-->
        </div>
        <div class="box_one_right flex_box flex_align_center">
          <div class="box_one_right_left">
            <div :style="$general.loadConfiguration(2)"
                 class="box_one_right_left_title">总计{{showName}}</div>
            <div :style="$general.loadConfiguration(-4)"
                 class="box_one_right_left_des">人数共有</div>
          </div>
          <div class="box_one_right_right flex_box flex_align_center flex_justify_content"
               @click="openDetail"
               :style="$general.loadConfiguration(2)+'border: 3px solid '+appTheme+';color: '+appTheme">{{memberAmount}}</div>
        </div>
      </div>
      <div class="box_two">
        <div class="title flex_box flex_align_center"
             :style="$general.loadConfiguration()">
          <div class="cirle"></div>男女比例
        </div>
        <div class="pic_des flex_box flex_align_center">
          <div class="pic_des_item flex_box flex_placeholder"
               @click="openDetail(sex[0],'sex')">
            <img :style="$general.loadConfigurationSize(14,'h')"
                 src="../../assets/img/man.png" />
            <div class="pic_des_title flex_placeholder flex_box"
                 :style="$general.loadConfiguration(2)">男<div class="num inherit">{{sex[0].amount}}</div>名</div>
          </div>
          <div class="pic_des_item flex_box flex_placeholder"
               @click="openDetail(sex[1],'sex')">
            <div class="flex_placeholder"></div>
            <div class="pic_des_title flex_box"
                 :style="$general.loadConfiguration(2)">女<div class="num inherit">{{sex[1].amount}}</div>名</div>
            <img :style="$general.loadConfigurationSize(14,'h')"
                 src="../../assets/img/woman.png" />
          </div>
        </div>
        <div class="progress_box">
          <div class="stroke"
               @click="openDetail(sex[1],'sex')"></div>
          <div class="stroke_on"
               @click="openDetail(sex[0],'sex')"
               :style="'width:'+(sex[0].amount*100/memberAmount).toFixed(0)+'%'"></div>
          <div :style="$general.loadConfiguration(-4)"
               class="stroke_des_right">占比：{{(sex[1].amount*100/memberAmount).toFixed(0)}}%</div>
          <div :style="$general.loadConfiguration(-4)"
               class="stroke_des_left">占比：{{(sex[0].amount*100/memberAmount).toFixed(0)}}%</div>
        </div>
      </div>
      <div class="box_three">
        <div class="title flex_box flex_align_center"
             :style="$general.loadConfiguration()">
          <div class="cirle"></div>年龄占比
        </div>
        <div class="progress_box flex_box flex_align_center"
             v-for="(item,index) in birthday"
             :key="index"
             @click="openDetail(item,'birthday')">
          <div :style="$general.loadConfiguration(-4)"
               class="progress_title">{{item.name}}</div>
          <div class="flex_placeholder">
            <van-progress :show-pivot="false"
                          stroke-width="10"
                          :color="option1.color[index]"
                          :percentage="item.amount*100/memberAmount"></van-progress>
          </div>
          <div class="progress_num"
               :style="$general.loadConfiguration(-4)+'background:'+option1.color[index]">{{(item.amount*100/memberAmount).toFixed(0)}}%</div>
        </div>
        <div style="height:20px;"></div>
      </div>
      <div class="box_three box_four">
        <div class="title flex_box flex_align_center"
             :style="$general.loadConfiguration()">
          <div class="cirle"></div>{{SYS_IF_ZX?"党派":"代表结构"}}
        </div>
        <div id="main1"></div>
        <div style="padding-left: 10px;width:100%;"
             class="flex_box T-flex-flow-row-wrap">
          <div v-for="(item,index) in structure"
               :key="index"
               :style="$general.loadConfiguration(-2)"
               class="item_box_bg flex_box"
               @click="openDetail(item,SYS_IF_ZX?'party':'representerElement')">
            <div class="name_tag"
                 :style="'background:'+option1.color[index]"></div>{{item.name}}<br />{{structure[index].amount+'人/'+(structure[index].amount*100/memberAmount).toFixed(0)+'%'}}
          </div>
        </div>

        <div style="height:20px;"></div>
      </div>
      <div class="box_three box_five">
        <div class="title flex_box flex_align_center"
             :style="$general.loadConfiguration()">
          <div class="cirle"></div>{{SYS_IF_ZX?"界别":"代表团"}}
        </div>
        <div style="padding-left: 10px;width:100%;"
             class="flex_box T-flex-flow-row-wrap">
          <div v-for="(item,index) in listData"
               :key="index"
               class="team_box"
               @click="openDetail(item,SYS_IF_ZX?'deleId':'representerTeam')">
            <div class=" num_box">
              <div class="num"
                   :style="$general.loadConfiguration(2)">{{item.amount}}</div>
            </div>
            <div class="name"
                 :style="$general.loadConfiguration(-4)">{{item.name}}</div>
          </div>
        </div>
        <div style="height:20px;"></div>
      </div>
    </div>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Progress } from 'vant'
export default {
  name: 'peopleInformation',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Progress.name]: Progress,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      keyWord: '十二届一次',
      memberAmount: 1,
      show: false,
      popupData: [{ name: '第十二届一次', value: 0 }, { name: '第十二届二次', value: 1 }, { name: '第十二届三次', value: 2 }, { name: '第十二届四次', value: 3 }, { name: '第十二届五次', value: 4 }],
      birthday: [],
      edu: [],
      nation: [],
      structure: [],
      listData: [],
      showName: $ifzx ? '委员' : '代表',
      sex: [
        { key: '1', name: '男', amount: 0 },
        { key: '2', name: '女', amount: 0 }
      ],
      option1: {
        color: ['#EE905A', '#F06981', '#5A65EE', '#5AC7EE', '#EE5A5A', '#EE905A', '#F06981', '#5A65EE', '#5AC7EE', '#EE5A5A', '#5B92EE'],
        tooltip: {
          show: true
        },
        series: [
          {
            name: $ifzx ? '党派' : '代表结构',
            type: 'pie',
            radius: ['18%', '30%'],
            avoidLabelOverlap: true,
            // label: {show: false,position: 'center'},
            // labelLine: {show: false},
            emphasis: {
              //        label: {show: true,fontSize: '20',fontWeight: 'bold'}
            },
            data: []
          }
        ]
      }

    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const getInfo = async () => {
      const res = await $api.peopleInformation.getPeopleInformationList({ memberType: (data.SYS_IF_ZX ? 1 : 3) })
      var datas = res ? res.data || '' : ''
      if (datas) {
        data.keyWord = datas.circlesName || ''
        data.memberAmount = datas.memberAmount || ''
        data.birthday = datas.birthday || []
        data.edu = datas.edu || []
        data.nation = datas.nation || []
        data.structure = datas[data.SYS_IF_ZX ? 'party' : 'representerElement'] || []
        data.listData = datas[data.SYS_IF_ZX ? 'dele' : 'representerTeam'] || []
        if (datas.sex) {
          for (var i in datas.sex) {
            if (datas.sex[i].name === '男') {
              data.sex[0] = datas.sex[i]
            }
            if (datas.sex[i].name === '女') {
              data.sex[1] = datas.sex[i]
            }
          }
        }
        if (data.structure && data.structure.length !== 0) {
          data.option1.series[0].data = []
          const newData = []
          data.structure.forEach(element => {
            var item = {}
            // var num = (datas.structure[i].amount * 100 / datas.memberAmount).toFixed(0)
            // item.name = num + '%'
            item.name = element.name
            item.value = element.amount
            newData.push(item)
          })
          data.option1.series[0].data = data.option1.series[0].data.concat(newData)
          var myChart = echarts.init(document.getElementById('main1'))
          myChart.setOption(data.option1)
          console.log('===' + myChart)
        }
      }
    }

    const openDetail = (_item, _type) => {
      var myParam = {}
      if (_item) {
        myParam.key = _item.key
        myParam.type = _type
      }
      myParam.title = data.showName + '资料'
      router.push({ path: 'peopleList', query: myParam })
    }

    const onRefresh = () => {
      getInfo()
    }
    const onLoad = () => {

    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, confirm, openDetail }
  }
}
</script>
<style lang="less" scoped>
.peopleInformation {
  background: #f8f8f8;
  position: relative;
  overflow: hidden;
  .top_box_bg {
    width: 200%;
    height: 360px;
    margin-top: -180px;
    margin-left: -100%;
    opacity: 1;
    border-radius: 50%;
  }
  /*总计*/
  .box_one {
    position: absolute;
    top: 40px;
    width: 90%;
    left: 0;
    right: 0;
    margin: auto;
  }
  .box_one_left div {
    margin-right: 0;
    font-size: 24px;
    width: 110px;
    font-weight: 600;
    line-height: 33px;
    color: #ffffff;
    opacity: 1;
  }
  .box_one_left img {
    width: 14px;
    height: 7px;
    margin-top: 15px;
    margin-right: 0;
  }
  .box_one_right {
    padding: 10px;
    background: #ffffff;
    box-shadow: 0px 3px 20px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 8px;
  }
  .box_one_right_left_title {
    font-weight: 600;
    line-height: 1.4;
    color: #384d76;
    opacity: 1;
  }
  .box_one_right_left_des {
    font-weight: 400;
    line-height: 1.4;
    color: #384d76;
    opacity: 1;
  }
  .box_one_right_right {
    margin-left: 10px;
    width: 60px;
    height: 60px;
    background: #ffffff;
    border: 3px solid #ee5a5a;
    border-radius: 50%;
    opacity: 1;
    font-weight: 500;
    color: #ee5a5a;
  }
  /*年龄占比*/
  .box_two {
    width: 90%;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: -40px;
    background: #ffffff;
    box-shadow: 0px 3px 20px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
    padding-bottom: 15px;
  }
  .title {
    padding-top: 20px;
  }
  .cirle {
    width: 11px;
    height: 11px;
    background: #ffffff;
    border: 2px solid #ee5a5a;
    border-radius: 50%;
    opacity: 1;
    margin: 5px 11px;
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
    color: #384d76;
  }
  .pic_des {
    width: 94%;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 10px;
  }
  .pic_des .pic_des_item {
  }
  .pic_des img {
    margin: 0 11px;
  }
  .pic_des .pic_des_title {
    height: 14px;
    font-size: 10px;
    font-weight: 500;
    line-height: 14px;
    color: #384d76;
    margin-top: 10px;
  }
  .stroke {
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, #f06981 0%, #ffa3b4 100%);
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 12px;
  }
  .stroke_on {
    width: 0;
    height: 12px;
    background: linear-gradient(90deg, #5a92ee 0%, #a0c4ff 100%);
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 12px;
    margin-top: -11px;
  }
  .progress_box .stroke_des_left {
    margin-top: 10px;
    height: 11px;
    font-size: 8px;
    font-weight: 500;
    line-height: 11px;
    color: #384d76;
    opacity: 0.8;
  }
  .progress_box .stroke_des_right {
    margin-top: 10px;
    float: right;
    height: 11px;
    font-size: 8px;
    font-weight: 500;
    line-height: 11px;
    color: #384d76;
    opacity: 0.8;
  }
  .box_three {
    height: auto;
    width: 90%;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 10px;
    background: #ffffff;
    box-shadow: 0px 3px 20px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
  }
  .progress_box {
    width: 90%;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 20px;
  }
  .progress_box .progress_title {
    width: 5.5em;
    font-weight: 500;
    color: #384d76;
    opacity: 1;
  }
  .progress_box .van-progress {
    width: 100%;
  }
  .progress_box .progress_num {
    margin-left: 10px;
    min-width: 3em;
    padding: 3px 5px;
    background: #ee5a5a;
    opacity: 1;
    border-radius: 2px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
  }
  /*代表结构*/
  #main1 {
    height: 200px;
  }

  .item_box_bg {
    width: calc(50% - 10px);
    margin-top: 10px;
    padding: 15px 8px;
    background: #ffffff;
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    border-radius: 4px;
    margin-right: 10px;
    font-weight: 500;
    color: #384d76;
  }
  .item_box_bg .name_tag {
    width: 11px;
    height: 9px;
    margin: 0 10px;
    margin-top: 13.5px;
    background: #ee905a;
    opacity: 1;
    border-radius: 2px;
  }
  /*代表团*/
  .box_five .team_box {
    text-align: center;
    width: calc(33.33% - 10px);
    margin-top: 10px;
    margin-right: 10px;
    padding: 10px 5px;
    background: #ffffff;
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
  }
  .box_five .num {
    font-weight: bold;
    color: #384d76;
    opacity: 1;
  }
  .box_five .name {
    margin-top: 5px;
    font-weight: 500;
    color: #384d76;
    opacity: 1;
  }
}
</style>

<template>
  <div class="memberCommunication">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-collapse v-model="activeNames">
        <van-collapse-item :title="item.groupTypeName"
                           :key="item.id"
                           v-for="item in dataList"
                           :name="item.id">
          <div class="communicationText"><span>办理类型：</span>{{item.transactTypName}}</div>
          <div class="communicationText"><span>办理单位：</span>{{item.groupName}}</div>
          <div class="communicationText"><span>联系委员：</span>{{item.contactPerson}}</div>
          <div class="communicationText"><span>沟通类型：</span>{{item.contactTypeView}}</div>
          <div class="communicationText"><span>创建时间：</span>{{item.createDate}}</div>
          <div class="communicationText"><span>沟通详情：</span></div>
          <div v-html="item.content"></div>
          <template v-if="item.attachmentList">
            <div class="proposalText"
                 v-if="item.attachmentList.length">附件</div>
            <div class="attachmentBox">
              <div class="attachmentItem"
                   v-for="item in item.attachmentList"
                   :key="item.id">{{item.fileName}}</div>
            </div>
          </template>
        </van-collapse-item>
      </van-collapse>
    </van-pull-refresh>
  </div>
</template>
<script>
// import { Toast } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'memberCommunication',
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const data = reactive({
      id: route.query.id,
      activeNames: [],
      dataList: [],
      refreshing: false
    })
    onMounted(() => {
      flowContactList()
    })
    const onRefresh = () => {
      setTimeout(() => {
        flowContactList()
      }, 520)
    }
    // 列表请求
    const flowContactList = async () => {
      const res = await $api.suggest.flowContactList({
        suggestId: data.id
        // doNotQueryGroup: 1
      })
      var { data: dataList } = res
      if (!data.activeNames.length) {
        if (dataList[0]) {
          data.activeNames.push(dataList[0].id)
        }
      }
      dataList.forEach(item => {
        if (item.transactType === '1') {
          item.transactTypName = '主办'
        }
        if (item.transactType === '2') {
          item.transactTypName = '协办'
        }
        if (item.transactType === '3') {
          item.transactTypName = '分办'
        }
        item.groupTypeName = `${item.groupName}（${item.transactTypName}）`
      })
      data.dataList = dataList
      data.refreshing = false
    }
    return { ...toRefs(data), onRefresh }
  }
}
</script>
<style lang="less">
.memberCommunication {
  width: 100%;
  min-height: 100%;
  background: #eee;
  .communicationText {
    padding: 6px 0;
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 22px;
    color: #666666;
    span {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 22px;
      color: #333333;
    }
  }
  .proposalText {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 600;
    line-height: 20px;
    color: #333333;
    position: relative;
    padding-left: 6px;
    margin-bottom: 6px;
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 2px;
      height: 15px;
      background: #3088fe;
      opacity: 1;
      border-radius: 10px;
    }
  }
  .attachmentBox {
    width: 100%;
    padding-bottom: 16px;
    .attachmentItem {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 18px;
      color: #3088fe;
      padding: 3px 0;
    }
  }
}
</style>

<template>
  <div class="addGroupBook">
    <!--搜索-->
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="onClickRight">
        <!-- <template #right
                  v-if="businessType !=1">
          <van-icon name="guide-o"
                    size="20" />&nbsp;确定
        </template> -->
      </van-nav-bar>
      <div id="search"
           class="search_box"
           style="background:#fff;">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"
                action="javascript:return true;"><input :style="'font-size:13px;'"
                   :placeholder="seachPlaceholder"
                   maxlength="100"
                   type="search"
                   ref="btnSearch"
                   @keyup.enter="btnSearch()"
                   v-model="keyword" /></form>
        </div>
      </div>
    </van-sticky>
    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <ul v-if="dataList.length != 0">
          <li class="item_body_warp">
            <div class="item_body">
              <van-checkbox-group ref="checkboxGroup"
                                  v-model="listSelect">
                <div class="itemSex_box flex_box T-flex-flow-row-wrap">
                  <div v-for="(nItem,nIndex) in dataList"
                       :key="nIndex"
                       class="itemSex_item">
                    <div :style="'width:93px;height:119px;'+'position: relative;overflow:hidden;'">
                      <img v-if="nItem.txt.bookType == '2'"
                           @click="clickBook(nItem)"
                           class="item_Sound"
                           :style="'width:28px;height:28px;'"
                           :src="icon_hasSound" />
                      <div v-if="nItem.isAvailable == '0'"
                           class="item_takeDown">已下架</div>
                      <img @click="clickBook(nItem)"
                           style="width: 100%;height: 100%;object-fit: cover;border-radius: 2px;"
                           :src="nItem.img.url" />
                      <van-checkbox v-if="hasSelectOk"
                                    :icon-size="21"
                                    :checked-color="appTheme"
                                    class="checkbox_item"
                                    :name="nItem.id"></van-checkbox>
                    </div>
                    <div class="itemSex_name text_one2"
                         :style="'font-size:15px;'"
                         v-html="nItem.name"></div>
                    <div class="itemSex_progress text_one2"
                         :style="'font-size:12px;'"
                         v-html="nItem.author"></div>
                  </div>
                  <div v-if="isGroupAdmin && false"
                       @click="addGroupBook({businessType:2,data:allData,groupId:pageParam.param.groupId,callback:pageParam.param.callback,listCallback:T.frameName(),name:'添加群书籍'})"
                       class="itemSex_item">
                    <div :style="'width:93px;height:119px;'+'margin:auto;border-radius: 2px;'"
                         class="add_box flex_box flex_align_center flex_justify_content">
                      <van-icon :size="16"
                                color="#E1E1E1"
                                name="plus"></van-icon>
                    </div>
                    <div class="itemSex_name text_one2"
                         :style="'font-size:15px;'"
                         v-html="'添加群书籍'"></div>
                  </div>
                </div>
              </van-checkbox-group>
            </div>
          </li>
        </ul>
      </van-list>
    </van-pull-refresh>
    <footer class="footerBtn">
      <div class="footerBtnBox flex_box">
        <!-- <van-button type="primary"
                    block>上传文件</van-button> -->
        <van-button v-if="businessType !=1 && listSelect.length != 0"
                    @click="onClickRight"
                    type="primary"
                    block>保存</van-button>
      </div>
    </footer>
  </div>
  <van-action-sheet v-model:show="show"
                    :actions="actions"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, Sticky, NavBar, ActionSheet } from 'vant'
// import moment from 'moment'
export default {
  name: 'addGroupBook',
  components: {
    [Button.name]: Button,
    [Sticky.name]: Sticky,
    [NavBar.name]: NavBar,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [ActionSheet.name]: ActionSheet,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      icon_upload_delete: require('../../../assets/img/icon_upload_delete.png'),
      icon_default_group: require('../../../assets/img/icon_default_group.png'),
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      title: route.query.title,
      groupId: route.query.groupId,
      listSelect: [],
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部btn为按钮
      businessType: Number(route.query.businessType), // 业务id 1是查看群书箱2是添加群书籍
      hasSelectOk: false,

      isGroupAdmin: false, // 是否群组管理员
      allData: [], // 群书籍ids
      oldSelect: [],
      show: false,
      nowId: '',
      description: '',
      actions: [
        { name: '按钮', color: '#ee0a24' }
      ]
    })
    watch(() => data.keyword, (newName, oldName) => {
      onRefresh()
    })
    onMounted(() => {
      if (data.businessType === 1) {
        data.pageSize = 999
        data.isGroupAdmin = route.query.isGroupAdmin
      } else if (data.businessType === 2) {
        data.hasSelectOk = true
        data.oldSelect = route.query.data
        data.listSelect = data.oldSelect.concat([])
      }
      onRefresh()
    })
    if (data.title) {
      document.title = data.title
    }
    // 点击 书本
    const clickBook = (_item) => {
      if (data.hasSelectOk) {
        var isHave = false
        data.listSelect.forEach((element, index) => {
          if (element === _item.id) {
            isHave = true
            data.listSelect.splice(index, 1)
          }
        })
        if (!isHave) {
          data.listSelect.push(_item.id)
        }
        return
      }
      if (data.isGroupAdmin) {
        data.show = true
        data.nowId = _item.id
        data.description = '对【' + _item.name + '】的操作'
        data.actions = [
          { name: '从群书籍删除', color: '#ee0a24' },
          { name: '群书籍详情' }
        ]
      } else {
        openBookDetails(_item)
      }
    }
    const onSelect = (item) => {
      switch (item.name) {
        case '从群书籍删除':
          editGroupBook('删除')
          break
        case '群书籍详情':
          openBookDetails()
          break
      }
    }
    const editGroupBook = async (_hint) => {
      var sItem = $general.getItemForKey(data.nowId, data.dataList, 'id')
      console.log(sItem)
      if (sItem) {
        data.allData.forEach((element, index) => {
          if (sItem.id === element) {
            data.allData.splice(index, 1)// 删除之前有的
          }
        })
      }
      setTimeout(async () => {
        var datas = {
          groupId: data.groupId,
          bookIds: data.allData.join(',')
        }
        const res = await $api.bookAcademy.editGroupBook(datas)
        if (res.errcode === 200) {
          Toast(_hint + (res.errcode === 200 ? '成功' : '失败，' + res.errmsg || ''))
          onRefresh()
        }
      }, 100)
    }
    const openBookDetails = (_item) => {
      router.push({ name: 'bookDetail', query: { id: (_item ? _item.id : data.nowId) } })
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword
      }
      if (data.businessType === 1) {
        datas.talkGroupId = data.groupId
      }
      const { data: list, total } = await $api.bookAcademy.getBookList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = { img: { url: _eItem.coverImgUrl || '', oldUrl: _eItem.coverImgUrl || '' }, txt: { bookType: _eItem.bookType || '' } }
        item.id = _eItem.id || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.isAvailable = _eItem.isAvailable || ''
        data.allData.push(item.id)
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      router.push({
        name: 'chatRoom',
        query: { id: (sessionStorage.getItem('rongCloudIdPrefix') + row.id), conversationType: 3, name: row.name }
      })
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.allData = []
      data.loading = true
      data.finished = false
      data.show = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }
    const onClickLeft = () => history.back()
    const onClickRight = () => {
      data.allData = data.listSelect
      editGroupBook('保存')
    }
    return { ...toRefs(data), onRefresh, onLoad, openDetails, btnSearch, onClickLeft, onClickRight, clickBook, onSelect }
  }
}
</script>
<style lang="less" scoped>
.addGroupBook {
  width: 100%;
  background: #f5f6f9;
  .footerBtn {
    background: #fff;
    padding: 5px 0;
    position: fixed;
    bottom: 0;
    width: 100%;
    .footerBtnBox {
      width: calc(100% - 20px);
      margin-left: 10px;
      .van-button + .van-button {
        width: calc(100% - 20px);
        margin-left: 10px;
      }
    }
  }
  .item_body .history_item {
    padding: 8px;
  }
  .item_title {
    color: #222;
    font-weight: bold;
    padding: 6px 0;
  }
  .item_title_icon {
    padding: 0 7px;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  .itemSex_box {
    margin: 0 -6px;
  }
  .itemSex_item {
    width: 33.33%;
    padding: 0 6px 5px 6px;
  }
  .itemSex_name {
    color: #222;
    font-weight: 500;
    margin-top: 5px;
    padding-left: 5px;
  }
  .itemSex_progress {
    color: #8b8a8a;
    font-weight: 400;
    margin-top: 1px;
    margin-bottom: 5px;
    padding-left: 5px;
  }
  .checkbox_item {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
  .select_footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    border-top: 1px solid #ccc;
  }
  .select_footer_box {
    height: 40px;
    padding: 0 10px;
  }

  .add_box {
    border: 1px dashed #e1e1e1;
    border-radius: 10px;
  }
}
</style>

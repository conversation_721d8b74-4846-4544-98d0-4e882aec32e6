<template>
  <div class="chatRoom">
    <header>
      <van-sticky>
        <van-nav-bar :title="name || '聊天详情'"
                     left-text=""
                     :left-arrow="isShowHead"
                     @click-left="onClickLeft"
                     @click-right="onClickRight">
          <template #right>
            <van-icon v-if="conversationType != 1"
                      name="ellipsis"
                      size="18" />
          </template>
        </van-nav-bar>
        <!-- <div v-if="groupMore.groupType ==2"
             @click="openMore"
             class="flex_box flex_justify_content"
             style="padding: 0.0rem 0;background:#fff;">
          <van-icon :class="groupMore.is?'arrowClose':'arrowOpen'"
                    :size="14"
                    :color="'#333'"
                    name="play"></van-icon>
        </div> -->
        <div v-if="groupMore.is"
             class=""
             :style="'font-size:14px;background:#fff;'">
          <van-tabs v-model:active="switchs.value"
                    @click="getGroupData()"
                    swipe-threshold="1"
                    :color="appTheme"
                    :title-active-color="appTheme"
                    :ellipsis="false">
            <van-tab v-for="(item, index) in switchs.data"
                     :key="index"
                     :title="item.label"
                     :name="item.value">
              <van-empty v-if="item.data.length == 0"
                         :style="'font-size:14px;padding:15px 0;'"
                         image-size="90"
                         :image="icon_no_data"
                         :description="'暂无' + item.label"></van-empty>
              <template v-else>
                <div v-if="item.value == 'book'"
                     class="itemSex_box">
                  <div v-for="(nItem, nIndex) in item.data"
                       :key="nIndex"
                       class="itemSex_item"
                       @click="openBookDetails(nItem)">
                    <img v-if="nItem.url"
                         :style="'width:65px;height:81px;object-fit:contain;margin:auto;'"
                         :src="nItem.url" />
                    <div v-if="nItem.name"
                         class="itemSex_name text_one"
                         :style="'font-size:12px;'"
                         v-html="nItem.name"></div>
                  </div>
                </div>
                <div v-else
                     class=""
                     style="padding: 15px">
                  <template v-for="(nItem, nIndex) in item.data"
                            :key="nIndex">
                    <div class="flex_box">
                      <van-icon name="volume-o"
                                style="margin-top: 1px" />
                      <div :style="'font-size:14px;margin-left:10px;'"
                           v-html="nItem.text"></div>
                    </div>
                  </template>
                </div>
              </template>
            </van-tab>
          </van-tabs>
        </div>
      </van-sticky>
    </header>
    <!--数据列表-->
    <!-- <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list> -->
    <div class="contert-box"
         ref="contentBody"
         id="tabbarBox">
      <div v-if="finished && dataList.length > 20"
           class="chat_hint"
           style="font-size: 12px">
        已加载全部消息
      </div>
      <div v-for="(item, index) in dataList"
           :key="item.messageId"
           :id="'item_' + item.messageId">
        <!--最新消息提示-->
        <div v-if="item.objectName == 'MC:RcNtf'"
             :style="'font-size:12px;'">
          <van-divider>{{ item.content }}</van-divider>
        </div>
        <template v-else>
          <!--时间提示-->
          <div v-if="calculatingTime(index)"
               class="chat_hint"
               :style="'font-size:14px'">
            <van-tag round>{{ item.sentTimeNew }}</van-tag>
          </div>
          <!--自定义短提示-->
          <div v-if="
              item.objectName == 'RC:RcNtf' || item.objectName == 'RC:RcCmd'
            "
               class="chat_hint"
               :style="'font-size:12px'">
            <van-tag round>{{ getRcNtf(item) }}</van-tag>
          </div>
          <div v-else-if="item.objectName == 'RC:VcMsg'"
               class="chat_hint"
               :style="'font-size:12px'">
            <van-tag round>{{ '语音消息请在app端查看' }}</van-tag>
          </div>
          <ul v-else
              class="chat_msg_box flex_box"
              :class="item.messageDirection == '1' ? 'chat_right' : ''">
            <van-image @click="msgImgTap(item)"
                       v-press:index="() => {msgConPress(index)}"
                       class="chat_msg_img"
                       fit="contain"
                       :style="'width:36px'"
                       :src="item.img"></van-image>
            <div class="chat_msg_add_box flex_placeholder">
              <div v-if="item.messageDirection != '1'"
                   class="flex_box"
                   :class="item.messageDirection == '1' ? 'chat_right' : ''">
                <div class="chat_msg_name"
                     :style="'font-size:12px'">
                  {{ item.name }}
                </div>
              </div>
              <div class="chat_msg_content flex_box"
                   :class="item.messageDirection == '1' ? 'chat_right' : ''">
                <i class="chat_msg_content_i"
                   :style="
                    'border-color: ' +
                    (item.messageDirection == '1' ? appTheme : '#FFF') +
                    ' transparent transparent transparent;'
                  "></i>
                <p class="chat_msg_content_p"
                   :style="
                    'font-size:16px;' +
                    (item.objectName == 'RC:ImgTextMsg' ? 'width:80%;' : '') +
                    (item.messageDirection == 1
                      ? 'background: ' + appTheme + ' !important'
                      : '')
                  "
                   @click="msgConTap(index)"
                   v-press:index="() => {msgConPress(index)}">
                  <!--文字消息-->
                  <template v-if="item.objectName == 'RC:TxtMsg'">
                    <span class="flex_box flex_align_center T-flex-flow-row-wrap inherit"
                          style="word-wrap:break-word;word-break:break-all;"
                          v-press:index="() => {msgConPress(index)}"
                          :conversion="dealEmoticons(item, 'content')"
                          v-html="item.content.content"></span>
                  </template>
                  <!--图片消息-->
                  <template v-else-if="item.objectName == 'RC:ImgMsg'">
                    <van-image @click="imagePreview(item)"
                               :src="item.content.imageUri"
                               v-press:index="() => {msgConPress(index)}"></van-image>
                  </template>
                  <!--语音消息-->
                  <template v-else-if="item.objectName == 'RC:VcMsg'">
                    <span class="flex_box flex_align_center T-flex-flow-row-wrap">
                      <template v-if="item.messageDirection == '1'">
                        <span class="flex_placeholder"></span>
                        {{item.content.duration}}”&nbsp;&nbsp;&nbsp;&nbsp;<span><img :src="item.messageId == recordBtn.playing?msendgif:msendlog"
                               class="chat_voice" /></span>
                      </template>
                      <template v-else>
                        <span><img :src="item.messageId == recordBtn.playing?mrecegif:mrecelog"
                               class="chat_voice" /></span>&nbsp;&nbsp;&nbsp;&nbsp;{{item.content.duration}}”
                        <span class="flex_placeholder"></span>
                      </template>
                    </span>
                    <template v-if="item.content.convertText">
                      <span style="margin-top: 10px">{{
                        '' + item.content.convertText
                      }}</span>
                    </template>
                  </template>
                  <!--位置消息-->
                  <template v-else-if="item.objectName == 'RC:LBSMsg'">
                    <div class="chat_location">
                      <div :style="
                          'font-size:15px' +
                          'margin-bottom:5px;font-weight: 600;'
                        "
                           class="text_two">
                        {{ item.content.poi }}
                      </div>
                      <!--<van-image fit="cover" class="chat_location_img" :src="'http://api.map.baidu.com/staticimage/v2?ak=ga7m5Ajsd0H4juDKQlklqAZtHrp6zz9C&markers='+item.content.longitude+','+item.content.latitude+'&width=300&height=300&zoom=15&copyright=1&&markerStyles=l,,0xff0000'"></van-image>-->
                      <van-image fit="cover"
                                 class="chat_location_img"
                                 :src="
                          'https://restapi.amap.com/v3/staticmap?location=' +
                          item.content.longitude +
                          ',' +
                          item.content.latitude +
                          '&zoom=13&size=300*300&markers=mid,0xff0000,:' +
                          item.content.longitude +
                          ',' +
                          item.content.latitude +
                          '&key=5e2e9231ea0d9fedc723a86b8f8a5ed2'
                        "></van-image>
                    </div>
                  </template>
                  <!--图文消息 一般用于消息推送 分享文章之类的-->
                  <template v-else-if="item.objectName == 'RC:ImgTextMsg'">
                    <template v-if="!checkChinese(item.content.content.split(',')[0])">
                      <div v-if="item.content.title"
                           :style="'color:#262626;'"
                           class="
                          flex_box flex_align_center
                          T-flex-flow-row-wrap
                          text_two
                          inherit
                        "
                           :conversion="dealEmoticons(item, 'title')"
                           v-html="item.content.content"></div>
                      <van-image v-if="item.content.imageUrl"
                                 fit="cover"
                                 :style="'width:100%;height:107px;margin:5px 0;'"
                                 @load="imgLoad"
                                 :src="item.content.imageUrl"></van-image>
                    </template>
                    <template v-if="item.content.content">
                      <!--发送的文件-->
                      <template v-if="item.content.content.split(',')[0] == '[文件]'">
                        <div class="file_box flex_box"
                             :dealWith="
                            dealWithFile(
                              item.content,
                              item.content.content.substring(5)
                            )
                          "
                             v-press:index="() => {msgConPress(index)}">
                          <img v-if="item.messageDirection != '1'"
                               class="content_img"
                               style="margin-right: 10px"
                               :src="item.content.url" />
                          <div class="flex_placeholder">
                            <div class="text_two"
                                 v-html="item.content.fileName"></div>
                            <div :style="'font-size:14px'"
                                 v-html="item.content.fileSize"></div>
                          </div>
                          <img v-if="item.messageDirection == '1'"
                               class="content_img"
                               style="margin-left: 10px"
                               :src="item.content.url" />
                        </div>
                      </template>
                      <!--发送的书籍-->
                      <template v-if="item.content.content.split(',')[0] == '[书籍]'"
                                v-press:index="() => {msgConPress(index)}">
                        <div class="file_box flex_box"
                             :dealWith="
                            dealWithBook(
                              item.content,
                              item.content.content.substring(5)
                            )
                          "
                             v-press:index="() => {msgConPress(index)}">
                          <div :style="'margin-right:10px;width:60px;height:90px;position: relative;'"
                               v-press:index="() => {msgConPress(index)}">
                            <img v-if="item.content.bookType == '2'"
                                 :style="
                                'width:21px;height:21px;' +
                                'position: absolute;left:5px;top:5px;'
                              "
                                 :src="icon_hasSound"
                                 v-press:index="() => {msgConPress(index)}" />
                            <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                                 :src="item.content.url"
                                 v-press:index="() => {msgConPress(index)}" />
                          </div>
                          <div class="flex_placeholder"
                               v-press:index="() => {msgConPress(index)}">
                            <div :style="'font-size:16px'"
                                 class="text_two"
                                 v-html="item.content.name"
                                 v-press:index="() => {msgConPress(index)}"></div>
                            <div :style="'font-size:13px;margin-top:10px;'"
                                 v-html="item.content.author"
                                 v-press:index="() => {msgConPress(index)}"></div>
                          </div>
                        </div>
                      </template>
                      <!--内容为详细样式-->
                      <template v-else-if="
                          item.content.content.split(',')[0] == 'details' ||
                          item.content.content.split(',')[0] == 'immediately'
                        ">
                        <div v-if="item.content.content"
                             class="van-hairline--top flex_box flex_align_center"
                             :style="'padding:9px 0 3px 0;margin-top:5px;'">
                          <div :style="'font-size:14px' + 'color:' + appTheme"
                               class="text_one2 flex_placeholder">
                            {{
                              item.content.content.split(',')[3] || '查看详情'
                            }}
                          </div>
                          <van-icon :color="'#B1AFB2'"
                                    :size="15"
                                    name="arrow"></van-icon>
                        </div>
                      </template>
                      <!--内容为列表样式-->
                      <template v-else-if="item.content.content.split(',')[0] == 'list'">
                        <div @click="msgImgListTap(nItem, item)"
                             v-for="(nItem, nIndex) in (
                            item.content.content.split(',')[2] || ''
                          ).split('||')"
                             :key="nIndex"
                             class="text_two van-hairline--bottom"
                             :style="
                            'font-size:16px' +
                            'padding:5px 0;line-height: 1.5;color:' +
                            appTheme
                          "
                             v-html="nItem.split('|')[0]"></div>
                      </template>
                      <!--内容为人-->
                      <template v-else-if="
                          item.content.content.split(',')[0] == 'member'
                        ">
                        <div @click="msgImgMemberTap(nItem, item)"
                             class="
                            chat_location
                            flex_box flex_align_center
                            van-hairline--bottom
                          "
                             v-for="(nItem, nIndex) in (
                            item.content.content.split(',')[2] || ''
                          ).split('||')"
                             :key="nIndex">
                          <van-image fit="contain"
                                     :style="
                              'width:56px;height:56px;' + 'margin-right:6px;'
                            "
                                     round
                                     :src="nItem.split('|')[2]"></van-image>
                          <div class="flex_placeholder">
                            <div :style="'font-size:16px' + 'padding-bottom:5px;'"
                                 class="text_one2">
                              {{ nItem.split('|')[0] }}
                            </div>
                            <div :style="'font-size:13px'"
                                 class="text_two">
                              {{ nItem.split('|')[3] }}
                            </div>
                          </div>
                        </div>
                      </template>
                    </template>
                  </template>
                  <!--其它未知消息-->
                  <template v-else>
                    <span v-html="item.objectName"></span>
                  </template>
                </p>
                <div v-if="item.sentStatus == 'SENDING'"
                     class="chat_status">
                  <img :style="'width:15px;height:15px;'"
                       :src="loading_more" />
                </div>
                <div v-else-if="item.sentStatus == 'FAILED'"
                     class="chat_status">
                  <img @click="reSend(item, index)"
                       :style="'width:26px;height:26px;'"
                       :src="icon_fail" />
                </div>
              </div>
            </div>
          </ul>
        </template>
        <!--左边框  对方发的-->
        <div style="clear: both"></div>
        <div style="height: 30px"></div>
      </div>
      <div :style="isopenemo || isinputbtn ? 'height:130px;' : ''"></div>
    </div>
    <!-- </van-list>
    </van-pull-refresh> -->
    <van-action-sheet v-model:show="show"
                      :actions="actions"
                      :description="description"
                      cancel-text="取消"
                      @select="onSelect" />
    <footer class="footer-box van-hairline--top">
      <van-cell-group inset>
        <van-field v-model="sendText"
                   center
                   @keyup.enter="sendMessage"
                   rows="1"
                   :autosize=" {maxHeight: 100}"
                   type="textarea"
                   placeholder="请输入内容">
          <template #button>
            <van-button @click="openEmo"
                        class="chatInputBtn">
              <img :src="isopenemo ? key : face" />
            </van-button>
            <van-button v-if="!sendText"
                        @click="openInputBtn"
                        class="chatInputBtn">
              <img :src="add" />
            </van-button>
            <van-button v-if="sendText"
                        class="chatInputBtn"
                        :style="'padding:5px 15px !important;background:' + appTheme"
                        size="small"
                        type="primary"
                        @click="sendMessage">↑</van-button>
          </template>
        </van-field>
        <div class="
            flex_box
            T-flex-flow-row-wrap
            flex_justify_content flex_justify-content_start
            emoBoxBg
          "
             v-if="isopenemo">
          <div v-for="(item, index) in emo"
               class="emoBox"
               :key="index"
               @click="chooseEmo(item)">
            <img :src="getImgUrl(item)" />
          </div>
        </div>
        <div class="
            flex_box
            T-flex-flow-row-wrap
            flex_justify_content flex_justify-content_start
            extrasBtnsBoxBg
          "
             v-if="isinputbtn">
          <div v-for="(item, index) in extrasBtns"
               :key="index"
               class="extrasBtnsBox"
               @click="chooseBtn(item)">
            <img :src="item.normalImg" />
            <div>{{ item.title }}</div>
          </div>
        </div>
      </van-cell-group>
    </footer>
  </div>
  <van-overlay :show="isShowLoading">
    <div style="text-align:center;padding-top:100%;">
      <van-loading size="24px">加载中...</van-loading>
    </div>
  </van-overlay>
  <van-uploader style="display: none"
                v-model="fileList"
                :max-count="1"
                :after-read="afterRead"
                ref="chatImg">
  </van-uploader>
  <van-uploader style="display: none"
                v-model="fileList"
                :max-count="1"
                capture="camera"
                :after-read="afterReadCamera"
                ref="chatImgCamera">
  </van-uploader>
</template>
<script>
import * as RongIMLib from '@rongcloud/imlib-next'
import * as imageConversion from 'image-conversion'
import { onMounted, reactive, watch, nextTick, toRefs, inject } from 'vue'
import { NavBar, Sticky, CellGroup, Button, Field, Toast, Popup, Image as VanImage, ImagePreview, Divider, ActionSheet, Uploader, Loading, Overlay } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { getConversionTime } from '../../../assets/js/date.js'
import emo from '../../../assets/emotion/emotion.json'
import emotion from '../../../components/emotion'
import axios from 'axios'
export default {
  name: 'chatRoom',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Button.name]: Button,
    [CellGroup.name]: CellGroup,
    [Popup.name]: Popup,
    [ActionSheet.name]: ActionSheet,
    [Uploader.name]: Uploader,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Divider.name]: Divider,
    [Field.name]: Field
  },
  setup () {
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const route = useRoute()
    const router = useRouter()
    const data = reactive({
      contentBody: null,
      id: route.query.id,
      name: route.query.name,
      conversationType: Number(route.query.conversationType),
      user: JSON.parse(sessionStorage.getItem('user')),
      userId: JSON.parse(sessionStorage.getItem('user')).id,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      emo: emo,
      isopenemo: false,
      isinputbtn: false,
      key: require('../../../assets/img/key.png'),
      add: require('../../../assets/img/add.png'),
      face: require('../../../assets/img/face.png'),
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      loading_more: require('../../../assets/img/loading_more.gif'),
      icon_fail: require('../../../assets/img/icon_fail.png'),
      icon_notificationStatus: require('../../../assets/img/icon_notificationStatus.png'),
      mrecegif: require('../../../assets/img/mrecegif.gif'),
      mrecelog: require('../../../assets/img/mrecelog.png'),
      msendgif: require('../../../assets/img/msendgif.gif'),
      msendlog: require('../../../assets/img/msendlog.png'),
      recordBtn: { show: false, playing: '', img: require('../../../assets/img/icon_recording.png'), text: '手指上滑，取消发送', textStyle: '' }, // 录音的对象
      seachPlaceholder: '请输入搜索内容',
      extrasBtns: [{
        title: '图片',
        normalImg: require('../../../assets/img/chatBox/album.png'),
        type: 'image'
      }, {
        // title: '相机',
        // normalImg: require('../../../assets/img/chatBox/cam.png'),
        // type: 'camera'
        // }, {
        //   title: '位置',
        //   normalImg: require('../../../assets/img/chatBox/loc.png'),
        //   type: 'location'
        // }, {
        title: '文件',
        normalImg: require('../../../assets/img/chatBox/file.png'),
        type: 'files'
      }],
      fileList: [],
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      startTime: 0, // 可选项）以此时间戳为基准，根据 order 向前或向后查询，当值是 0 时为边界（order 值是 0 时为最大时间戳，order 值是 1 时为最小时间戳）
      count: 10, // （可选项）获取消息的数量，范围: 1-20
      order: 0, // 可选项）查询消息的方向，值为 0或1，0 为以 timestamp 为基准向前查询，1 为以 timestamp 为基准向后查询
      dataList: [],
      permanent: [
        { _ajax: true, key: 'notice', latestMessage: { text: '' }, unreadMessageCount: 0, pointType: 'small', url: require('../../../assets/img/icon_book_notice.png'), userName: '通知', sentTime: '' },
        // { _ajax: true, key: 'readActivity', latestMessage: { text: '' }, unreadMessageCount: 0, url: require('../../../assets/img/icon_book_activity.png'), userName: '活动', sentTime: '' },
        { _ajax: true, key: 'user', latestMessage: { text: '' }, unreadMessageCount: 0, pointType: 'small', url: require('../../../assets/img/icon_book_user.png'), userName: '通讯录', sentTime: '' },
        { _ajax: true, key: 'group', latestMessage: { text: '' }, unreadMessageCount: 0, pointType: 'small', url: require('../../../assets/img/icon_book_group.png'), userName: '群组', sentTime: '' }
      ],
      sendText: '',
      onTouchIndex: '',
      description: '',
      show: false,
      actions: [
        { name: '删除消息' },
        { name: '撤回消息', color: '#ee0a24' }
      ],
      unreadMentioned: [], // 是否有未读@消息

      unreadMsgInfo: { num: 0, id: '', show: false, isShow: false, onscroll: false }, // 未读消息  数量 id 显示 是否显示   是否开始划动监听

      groupMore: { is: false, isAjax: false, groupType: 1 }, // 打开群更多
      switchs: { value: 'book', data: [{ label: '群书籍', value: 'book', data: [] }, { label: '群公告', value: 'notice', data: [] }] },
      pageNot: { url: '../../../images/icon_no_data.png', text: '暂无数据' },
      groupSay: '', // 群公告
      groupName: '', // 群组名
      groupOwner: '', // 群主id
      chatImg: null,
      chatImgCamera: null,
      isShowLoading: false
    })
    document.title = data.name
    const onSelect = (item) => {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      data.show = false

      var touchItem = data.dataList[data.onTouchIndex]
      switch (item.name) {
        case '删除消息':
          deletMessage(touchItem)
          break
        case '撤回消息':
          recallMessage(touchItem)
          break
      }
    }
    // const iniRongCloud = () => {
    //   RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
    //   /** 接收消息监听器 */
    //   const Events = RongIMLib.Events
    //   /**
    //      * 正在链接的事件状态
    //      */
    //   RongIMLib.addEventListener(Events.CONNECTING, () => {
    //     console.log('正在链接...')
    //   })

    //   /**
    //      * 链接到服务器会触发这个事件
    //      */
    //   RongIMLib.addEventListener(Events.CONNECTED, () => {
    //     console.log('连接成功')
    //   })

    //   /**
    //      * 手动调用 disconnect 方法或者用户被踢下线 会触发这个事件
    //      */
    //   RongIMLib.addEventListener(Events.DISCONNECT, () => {
    //     console.log('连接中断，需要业务层进行重连处理')
    //   })

    //   /**
    //      * 链接出问题时，内部进行重新链接，会出发这个事件
    //      */
    //   RongIMLib.addEventListener(Events.SUSPEND, () => {
    //     console.log('链接中断，SDK 会尝试重连，业务层无需关心')
    //   })
    //   const callback = function (messages) {
    //     console.log('收到消息：', messages)
    //     var element = messages.messages[0]
    //     console.log(element.targetId)
    //     console.log(data.id)
    //     if (element.targetId !== data.id) {
    //       return
    //     }
    //     setTimeout(() => {
    //       if (element.messageType === 'RC:RcCmd') {
    //         console.log(element)
    //         data.dataList.forEach((item, index) => {
    //           if (element.content.sentTime === item.sentTime) {
    //             data.dataList.splice(index, 1)
    //           }
    //         })
    //       }
    //       if (element.messageUId !== data.dataList[data.dataList.length - 1].messageUId) { // 自己发消息的时候，收消息的监听有时候也有效，这里做一下判断，不重复加了。
    //         var showDatas = []
    //         getUserMsg(element)
    //         element.sentTimeNew = getConversionTime(new Date(element.sentTime), new Date(), 1)
    //         element.objectName = element.messageType
    //         showDatas.push(element)
    //         setTimeout(() => {
    //           data.dataList = data.dataList.concat(showDatas)
    //           data.dataList.sort((a, b) => {
    //             return a.sentTime - b.sentTime
    //           })// 随便加 然后按发送时间升序排序
    //           goButtom()
    //         }, 500)
    //       }
    //     }, 600)
    //   }
    //   RongIMLib.addEventListener(Events.MESSAGES, callback)
    //   RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
    //     console.log(res)
    //     getImgsPaths(function () {
    //       getHistory(0)
    //     })
    //     clearMessagesUnreadStatus()
    //   })
    // }
    onMounted(() => {
      data.isShowLoading = true
      // eslint-disable-next-line eqeqeq
      if (data.conversationType == 3) {
        getGroupMember()
        getChatInfo()
      } else {
        setTimeout(() => {
          // iniRongCloud()
        }, 500)
      }
      window.addEventListener('scroll', getScollHeight, true)
    })
    // 得到群组成员
    const getGroupMember = async () => {
      var { data: gruopInfo } = await $api.rongCloud.getGroupMember({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      })
      var datalength = gruopInfo ? gruopInfo.length : 0
      if (gruopInfo && datalength !== 0) {
        var fileUserIDs = ''; var ifIn = false
        for (var i in gruopInfo) {
          const userid = gruopInfo[i].id
          if (userid !== data.user.id) {
            fileUserIDs += (fileUserIDs ? ',' : '') + (sessionStorage.getItem('rongCloudIdPrefix') + userid)
          } else {
            ifIn = true
          }
        }
        if (!ifIn) {
          // 不在群组里就返回
          Toast('您已不属于这个群组！')
          onClickLeft()
        }
      }
      // getRongCloudList(gruopInfo)
    }
    // 获取融云群成员
    // const getRongCloudList = async (_data) => {
    //   var oldData = ''
    //   for (var i in _data) { oldData += (oldData ? ',' : '') + (sessionStorage.getItem('rongCloudIdPrefix') + _data[i].id) }
    //   console.error('老数据：' + oldData)
    //   var res = await $api.rongCloud.getToken('push/rongCloud?type=queryGroupUserList&groupId=' + data.id)
    //   var rongdata = res.members
    //   var newData = ''
    //   for (i in rongdata) { newData += (newData ? ',' : '') + rongdata[i].id }
    //   // 查询出融云多余的人 和少的人    多的人退出  少的人 加上
    //   var manyData = ''; var lessData = ''
    //   for (i in rongdata) {
    //     var id = rongdata[i].id
    //     if (oldData.indexOf(id) === -1) {
    //       manyData += (manyData ? ',' : '') + id
    //     }
    //   }
    //   for (i in _data) {
    //     const id = sessionStorage.getItem('rongCloudIdPrefix') + _data[i].id
    //     if (newData.indexOf(id) === -1) {
    //       lessData += (lessData ? ',' : '') + id
    //     }
    //   }
    //   console.error('多的数据：' + manyData)
    //   console.error('少的数据：' + lessData)
    //   if (manyData) { quitGroupBatch(manyData) }
    //   if (lessData) { joinGroupBatch(lessData) }
    //   setTimeout(() => {
    //     iniRongCloud()
    //   }, 500)
    // }
    // 多的人退出
    // const quitGroupBatch = async (_userids) => {
    //   const url = 'push/rongCloud?type=quitGroup&userIds=' + _userids + '&groupId=' + data.id
    //   // const param = { type: 'quitGroup', userIds: _userids, groupId: data.id }
    //   $api.rongCloud.getToken(url)
    // }
    // 少的人加上
    // const joinGroupBatch = async (_userids) => {
    //   const url = 'push/rongCloud?type=joinGroup&userIds=' + _userids + '&groupId=' + data.id + '&groupName=' + data.name
    //   // const param = { type: 'joinGroup', userIds: _userids, groupId: data.id, groupName: data.name }
    //   $api.rongCloud.getToken(url)
    // }
    const getScollHeight = () => {
      if (data.contentBody && (data.contentBody.scrollTop <= 0 && !data.finished)) {
        getHistory(1)
        setTimeout(() => {
          data.contentBody.scrollTop = data.contentBody.scrollHeight / (data.pageNo)
        }, 10)
      }
    }
    const getChatInfo = async () => {
      var res = await $api.rongCloud.getGroupInfo({
        id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      })

      var { data: gruopInfo } = res
      data.groupMore.groupType = gruopInfo.groupType
      data.groupName = gruopInfo.groupName
      data.groupOwner = gruopInfo.groupOwner // 群主id
    }
    watch(() => data.dataList, (newName, oldName) => {
      // console.log(newName, oldName)
      clearMessagesUnreadStatus()
    })
    watch(() => data.sendText, (newName, oldName) => {
      // console.log(newName, oldName)
    })
    const goButtom = () => {
      setTimeout(() => {
        nextTick(() => {
          if (data.contentBody) {
            data.contentBody.scrollTo({
              top: data.contentBody.scrollHeight,
              behavior: 'smooth'
            })
            setTimeout(() => {
              data.contentBody.scrollTo({
                top: data.contentBody.scrollHeight,
                behavior: 'smooth'
              })
              data.isShowLoading = false
            }, 500)
          }
        })
      }, 500)
    }
    const sendMessage = function () {
      if (data.sendText) {
        // 定义消息投送目标会话
        const conversation = { conversationType: data.conversationType, targetId: data.id }
        // 实例化待发送消息，RongIMLib.TextMessage 为内置文本型消息
        const message = new RongIMLib.TextMessage({ content: data.sendText })
        rongSendMsg(conversation, message)
      } else {
        Toast('消息不能为空')
      }
    }

    const rongSendMsg = (conversation, message) => {
      // 发送
      RongIMLib.sendMessage(conversation, message).then(res => {
        console.log('发送消息：', res)
        data.sendText = ''
        var element = res.data
        var showDatas = []
        getUserMsg(element)
        element.sentTimeNew = getConversionTime(new Date(element.sentTime), new Date(), 1)
        element.objectName = element.messageType
        if (element.content.extra) { element.content.extra = JSON.parse(element.content.extra) }
        showDatas.push(element)
        // eslint-disable-next-line eqeqeq
        if (data.conversationType == 3) { // 是书院群
          addCounter(element.messageId, element)
        }
        setTimeout(() => {
          data.dataList = data.dataList.concat(showDatas)
          data.dataList.sort((a, b) => {
            return a.sentTime - b.sentTime
          })// 随便加 然后按发送时间升序排序
          goButtom()
        }, 500)
      })
    }

    // 增加书院统计
    const addCounter = async (msgId, _item) => {
      $api.bookAcademy.addCounter0010({ dataId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1] })
      console.log(JSON.stringify(_item))
      if (_item.objectName === 'RC:TxtMsg' || _item.objectName === 'RC:ImgMsg') {
        $api.rongCloud.addGroupmessage({ groupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1], content: (_item.objectName === 'RC:ImgMsg' ? '图片' : (_item.content.content || '图片')), groupName: data.groupName, messageType: _item.objectName === 'RC:TxtMsg' ? 1 : 2, userId: data.user.id, userName: data.user.name })
      }
    }

    const onClickLeft = () => history.back()
    const onClickRight = () => {
      router.push({ name: 'chatSetting', query: { id: data.id, conversationType: data.conversationType } })
    }
    const msgImgTap = (_item) => {
      console.log('点击头像')
      router.push({ name: 'personData', query: { id: _item.senderUserId.split(sessionStorage.getItem('rongCloudIdPrefix'))[1] } })
    }
    const msgImgPress = () => {
      console.log('常按头像')
    }
    const msgConTap = (_index) => {
      var _item = data.dataList[_index]
      console.log('我单击了正文：' + _index + JSON.stringify(_item))
      if (_item.objectName === 'RC:ImgMsg') {
        // previewImg(_item.content, 0, 'imageUrl')
      } else if (_item.objectName === 'RC:VcMsg') {
        playVoice(_item)
      } else if (_item.objectName === 'RC:LBSMsg') {
        // openLocation(_item)
      } else if (_item.objectName === 'RC:ImgTextMsg') {
        openMsgDetails(_item)
      }
    }
    const playVoice = (_item) => { // 播放录音点击正在播放的就停止否则就播放播放完就停止
      const instance = new Audio()
      instance.crossOrigin = '*'
      instance.oncanplaythrough = () => {
        console.log('播放')
        instance.play()
      }
      instance.onended = () => {
        data.recordBtn.playing = ''
        console.log('播放结束')
        // 播放结束触发，自定义播放按钮重置
      }
      if (data.recordBtn.playing === _item.messageId) {
        data.recordBtn.playing = ''
        instance.pause()
        return
      }
      var blobs = $b64toBlob(_item.content.content)
      instance.src = URL.createObjectURL(new Blob([blobs]))
      instance.pause()
      instance.load()
      data.recordBtn.playing = _item.messageId
      // const elink = document.createElement('a')
      // elink.download = 'test.amr'
      // elink.style.display = 'none'
      // elink.href = URL.createObjectURL(new Blob([blobs]))
      // document.body.appendChild(elink)
      // setTimeout(() => {
      //   elink.click()
      //   document.body.removeChild(elink)
      // }, 1000)
    }
    // base64转音频blob
    const $b64toBlob = (b64Data, contentType, sliceSize) => {
      contentType = contentType || ''
      sliceSize = sliceSize || 512

      const byteCharacters = atob(b64Data)
      const byteArrays = []

      for (
        let offset = 0;
        offset < byteCharacters.length;
        offset += sliceSize
      ) {
        const slice = byteCharacters.slice(offset, offset + sliceSize)

        const byteNumbers = new Array(slice.length)
        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i)
        }

        const byteArray = new Uint8Array(byteNumbers)

        byteArrays.push(byteArray)
      }

      const blob = new Blob(byteArrays, { type: contentType })
      return blob
    }
    const openMsgDetails = (_item) => {
      var description = _item.content.content || ''
      var descriptions = description.split(',')
      var url = _item.content.url || ''
      if (description.split(',')[0] === '[文件]') {
        _item.content.attach.fileSize = _item.content.fileSize
        router.push({ name: 'superFile', query: _item.content.attach })
        // window.open(_item.content.attach.url)
        return
      }
      if (description.split(',')[0] === '[书籍]') {
        router.push({ name: 'bookDetail', query: { id: _item.content.id } })
        return
      }
      // 不为详情类型时  外部点击不触发
      if (descriptions[0] !== 'details' && descriptions[0] !== 'immediately') {
        return
      }
      var openItem = {}
      if (url) {
        openItem.url = url
        // $o.openUrl(openItem)
      } else if (descriptions.length > 2) {
        openItem.relateType = descriptions[1] || ''
        openItem.id = descriptions[2] || ''
        // $o.openDetails(openItem)
      }
    }
    const msgConPress = (index) => {
      data.onTouchIndex = index
      console.log(data.onTouchIndex)
      console.log('常按内容' + data.groupOwner)
      if (sessionStorage.getItem('rongCloudIdPrefix') + data.userId === data.dataList[data.onTouchIndex].senderUserId) {
        data.description = '请选择相应的操作'
        data.actions = [
          { name: '删除消息' },
          { name: '撤回消息', color: '#ee0a24' }
        ]
        data.show = true
        // } else if (data.groupOwner === data.user.id) {
        //   data.description = '群主权限'
        //   data.actions = [
        //     { name: '撤回消息', color: '#ee0a24' }
        //   ]
        //   data.show = true
        //   console.log('群主可以操作别的发言')
      } else {
        console.log('不是自己发的消息')
      }
    }
    // 得到撤回消息信息
    const getRcNtf = (_item) => {
      // eslint-disable-next-line eqeqeq
      const tip = _item.senderUserId != sessionStorage.getItem('rongCloudIdPrefix') + data.userId ? (_item.conversationType == 3 ? _item.name + ' 撤回了一条消息' : '对方撤回了一条消息') : '你撤回了一条消息'
      // console.log(tip)
      return tip
    }
    // 检查是否为自定义消息 即是否有中文
    const checkChinese = (_val) => {
      var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
      return reg.test(_val)
    }
    // 处理书籍数据
    const dealWithBook = (_item, _data) => {
      if (_item._dealWith) {
        return
      }
      _item._dealWith = true
      var nowData = JSON.parse(_data)
      _item.id = nowData.id
      _item.url = nowData.url
      _item.name = nowData.name
      _item.author = nowData.author
      _item.bookType = nowData.bookType || ''
    }
    // 计算时间该不该显示
    const calculatingTime = function (_index) {
      if (_index === 0 || !data.dataList[_index - 1].sentTime) {
        return true
      } else {
        var beginTime = Math.round(new Date(data.dataList[_index - 1].sentTime).getTime() / 1000)
        var endTime = Math.round(new Date(data.dataList[_index].sentTime).getTime() / 1000)
        return endTime - beginTime > 180// 当前时间减上一个时间 大于3分钟 就显示时间
      }
      // return false;
    }
    const getHistory = function (order) {
      data.pageNo = data.pageNo + 1
      const conversation = {
        conversationType: data.conversationType,
        targetId: data.id
      }
      // 从当前时间开始向前查询
      const option = {
        timestamp: order ? data.dataList[0].sentTime : 0,
        count: 10,
        order: 0
      }
      if (order === 0) {
        data.dataList = []
      }
      RongIMLib.getHistoryMessages(conversation, option).then(res => {
        console.log(res)
        if (res.msg === 'NOT_IN_GROUP') { // 不在群组里就返回
          onClickLeft()
        }
        if (res.code === 0) {
          console.log(res.data.hasMore)// 是否还有更多消息
          data.finished = !res.data.hasMore
          const msgData = res.data.list
          const showDatas = []
          msgData.forEach(element => {
            getUserMsg(element)
            element.sentTimeNew = getConversionTime(new Date(element.sentTime), new Date(), 1)
            element.objectName = element.messageType
            if (element.content.extra) { element.content.extra = JSON.parse(element.content.extra) }
            if (order === 1) {
              showDatas.unshift(element)
            } else {
              showDatas.push(element)
            }
          })
          setTimeout(() => {
            data.dataList = data.dataList.concat(showDatas)
            data.dataList.sort((a, b) => {
              return a.sentTime - b.sentTime
            })// 随便加 然后按发送时间升序排序
            console.log(data.dataList)
          }, 500)
          if (order === 0) {
            goButtom()
          }
        } else {
          console.log(res.code, res.msg)
        }
      })
      data.refreshing = false
    }
    const clearMessagesUnreadStatus = () => {
      const conversationType = Number(data.conversationType)
      const targetId = data.id
      console.log('清除未读')
      RongIMLib.clearMessagesUnreadStatus({ conversationType, targetId }).then(res => {
        if (res.code === 0) {
          console.log(res.code)
        } else {
          console.log(res.code, res.msg)
        }
      })
    }
    // 获取当前的用户的信息
    const getUserMsg = async (_item, _index) => {
      var datas = {
        id: _item.senderUserId.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      var { data: userInfo } = await $api.rongCloud.getUserInfo(datas)
      _item.img = userInfo.fullImgUrl || ''
      _item.name = userInfo.userName || ''
      _item.position = userInfo.position || ''
    }
    const emotionData = reactive({})
    const chooseEmo = (_item) => {
      data.sendText = data.sendText + _item.text
    }
    const afterRead = async (file) => {
      // 压缩图片为600kb
      const blob = await imageConversion.compressAccurately(file.file, 80)
      if (blob) {
        blobToBase64(blob).then(res => {
          // 转化后的base64
          getChatImgUrl(file, res)
        })
      }
    }
    const afterReadCamera = async (file) => {
      // 压缩图片为600kb
      const blob = await imageConversion.compressAccurately(file.file, 80)
      if (blob) {
        blobToBase64(blob).then(res => {
          // 转化后的base64
          getChatImgUrl(file, res)
        })
      }
    }
    const getChatImgUrl = async (file, smallPic) => {
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'chatimg')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const { data: msg } = await $api.general.uploadFile(formData)
      if (msg) {
        sendImgMessage(msg[0].filePath, smallPic)
      }
    }
    const sendImgMessage = (uri, smallPic) => {
      data.fileList = []
      // 定义消息投送目标会话
      const conversation = {
        conversationType: data.conversationType,
        targetId: data.id
      }
      const message = new RongIMLib.ImageMessage({
        content: smallPic, // 图片缩略图，应为 Base64 字符串，且不可超过 80KB
        imageUri: uri // 图片的远程访问地址
      })
      rongSendMsg(conversation, message)
    }
    const blobToBase64 = async (blob) => {
      return new Promise((resolve, reject) => {
        const fileReader = new FileReader()
        fileReader.onload = (e) => {
          resolve(e.target.result)
        }
        // readAsDataURL
        fileReader.readAsDataURL(blob)
        fileReader.onerror = () => {
          reject(new Error('文件流异常'))
        }
      })
    }
    const imagePreview = (_item) => {
      ImagePreview({
        images: [_item.content.imageUri],
        closeable: true
      })
    }
    const chooseBtn = (_item) => {
      console.log(_item)
      switch (_item.type) {
        case 'image':
          data.chatImg.chooseFile()
          break
        case 'camera':
          data.chatImgCamera.chooseFile()
          break
        case 'location':
          break
        case 'files':
          router.push({ name: 'chatFile', query: { id: data.id, conversationType: data.conversationType, send: true } })
          break
      }
    }
    const getImgUrl = (_item) => {
      return emotion[_item.name]
    }
    const dealEmoticons = async (_item, _key) => {
      if (_item.content.conversion) {
        return
      }
      _item.content.conversion = true
      var _text = _item.content[_key] || ''
      // 处理正文中的表情信息
      var imgRegx = /\[(.*?)\]/gm
      var _textImg = _text.replace(imgRegx, function (match) {
        var imgSrc = emotionData[match]
        if (!imgSrc) { /* 说明不对应任何表情,直接返回即可. */
          return match
        }
        var imgText = "<img style='width: 30px;' src='" + emotion[imgSrc] + "'/>"
        return imgText
      })
      _item.content[_key] = _textImg
    }
    /* 一个工具方法: 可以获取 所有表情图片的名称和真实url地址, 以JSON对像形式返回; 其中以表情文本为 属性名, 以图片真实路径为属性值. */
    // const getImgsPaths = function (callback) {
    //   var emotionArray = emo
    //   for (var idx in emotionArray) {
    //     var emotionItem = emotionArray[idx]
    //     var emotionText = emotionItem.text
    //     var emotionUrl = emotionItem.name
    //     emotionData[emotionText] = emotionUrl
    //   }
    //   /* 把 emotion对象 回调出去. */
    //   if (typeof (callback) === 'function') {
    //     callback()
    //   }
    // }
    const recallMessage = () => {
      console.log(data.onTouchIndex)
      const item = data.dataList[data.onTouchIndex]
      const conversation = {
        conversationType: data.conversationType,
        targetId: data.id
      }
      RongIMLib.recallMessage(conversation, {
        messageUId: item.messageUId,
        sentTime: item.sentTime
      })
        .then((res) => {
          if (res.code === 0) {
            data.show = false
            data.dataList.splice(data.onTouchIndex, 1)
            var element = res.data
            var showDatas = []
            getUserMsg(element)
            element.sentTimeNew = getConversionTime(new Date(element.sentTime), new Date(), 1)
            element.objectName = element.messageType
            if (element.content.extra) { element.content.extra = JSON.parse(element.content.extra) }

            showDatas.push(element)

            setTimeout(() => {
              data.dataList = data.dataList.concat(showDatas)
              data.dataList.sort((a, b) => {
                return a.sentTime - b.sentTime
              })// 随便加 然后按发送时间升序排序
              console.log(data.dataList)
            }, 1000)
            console.log(res.code, res.data)
          } else {
            console.log(res.code, res.msg)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    }
    const deletMessage = () => {
      console.log(data.onTouchIndex)
      const item = data.dataList[data.onTouchIndex]
      const conversation = {
        conversationType: data.conversationType,
        targetId: data.id
      }
      RongIMLib.deleteMessages(conversation, [
        {
          messageUId: item.messageUId,
          sentTime: item.sentTime,
          messageDirection: RongIMLib.MessageDirection.SEND
        }
      ]).then(res => {
        if (res.code === 0) {
          data.show = false
          data.dataList.splice(data.onTouchIndex, 1)
          console.log('删除成功')
        } else {
          console.log(res.code, res.msg)
        }
      }).catch(error => {
        console.log(error)
      })
    }

    const onRefresh = () => {
      getHistory(1)
    }
    const onLoad = () => {
      // getHistory()
    }
    const openEmo = () => {
      data.isopenemo = !data.isopenemo
      data.isinputbtn = false
      if (data.isopenemo) {
        goButtom()
      }
    }
    const openInputBtn = () => {
      data.isopenemo = false
      data.isinputbtn = !data.isinputbtn
    }
    const openMore = () => {
      data.groupMore.is = !data.groupMore.is
      getGroupData()
    }
    const getGroupData = async () => {
      var nowItem = $general.getItemForKey(data.switchs.value, data.switchs.data, 'value')
      var datas = {
        pageNo: 1, pageSize: 999, talkGroupId: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      if (nowItem.value === 'book') {
        var { data: list } = await $api.bookAcademy.getBookList(datas)
        nowItem.data = []
        const newData = []
        list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.id = _eItem.id || ''// 书本id
          item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.url = _eItem.coverImgUrl || ''// 书图
          newData.push(item)
        })
        nowItem.data = nowItem.data.concat(newData)
      } else {
        nowItem = $general.getItemForKey('notice', data.switchs.data, 'value')
        if (!nowItem) return
        var { data: gruopInfo } = await $api.rongCloud.getGroupInfo({
          id: data.id.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
        })
        nowItem.data = []
        var newData = []
        if (gruopInfo.openSay) {
          var item = {}
          item.name = ''
          item.text = gruopInfo.openSay
          newData.push(item)
        }
        nowItem.data = nowItem.data.concat(newData)
      }
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    // 处理文件数据
    const dealWithFile = (_item, _data) => {
      if (_item._dealWith) {
        return
      }
      _item._dealWith = true
      var nowFile = JSON.parse(_data)
      var nItemPath = encodeURI(axios.defaults.baseURL + '/' + (nowFile.fileName ? 'talkroup' : 'fileinfo') + '/download/' + nowFile.id + '?loginAreaId=' + data.user.areaId + '&loginToken=' + JSON.parse(sessionStorage.getItem('token')))// 附件下载地址
      _item.fileName = nowFile.name
      _item.fileSize = nowFile.fileSizeName
      var fileAttr = $general.getFileTypeAttr(nowFile.fileType)
      _item.url = fileAttr.type === 'image' ? nItemPath : require('../../../assets/img/fileicon/' + fileAttr.name)
      _item.attach = { url: nItemPath, state: 0, schedule: -1, name: nowFile.name + nowFile.fileType }
    }
    return {
      ...toRefs(data),
      onRefresh,
      onLoad,
      openEmo,
      openInputBtn,
      onClickLeft,
      onClickRight,
      onSelect,
      msgImgTap,
      msgImgPress,
      msgConTap,
      msgConPress,
      getRcNtf,
      checkChinese,
      dealWithBook,
      calculatingTime,
      goButtom,
      sendMessage,
      deletMessage,
      recallMessage,
      openMore,
      chooseEmo,
      chooseBtn,
      getImgUrl,
      imagePreview,
      getGroupData,
      openBookDetails,
      dealEmoticons,
      dealWithFile,
      afterRead,
      afterReadCamera
    }
  }
}
</script>

<style lang="less" scoped>
@import "./chatRoom.less";
</style>
<style scoped>
.chatRoom >>> .van-field__control {
  padding: 5px 17px;
  border-radius: 15px;
  background: #eee;
}
</style>

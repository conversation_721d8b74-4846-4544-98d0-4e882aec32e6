<template>
  <van-pull-refresh v-model="loading" @refresh="onRefresh">
    <div class="homePage">
      <div class="homePageHead">
        <div class="homePageNumberBox">
          <div class="homePageNumber">
            <div class="homePageNew"></div>
            <div class="homePageNumberTextBox">
              <div class="homePageNumberName">撰写提案</div>
              <div class="homePageNumberText">已提交提案<span>12件</span></div>
            </div>
          </div>
          <div class="homePageNumber" @click="myClick">
            <div class="homePageMy"></div>
            <div class="homePageNumberTextBox">
              <div class="homePageNumberName">我的提案</div>
              <div class="homePageNumberText">待处理提案<b>12件</b></div>
            </div>
          </div>
        </div>
      </div>
      <div class="homePageBox">
        <div class="homePageNotice" @click="noticeClick">
          <div class="homePageNoticeName">通知<div>公告</div>
          </div>
          <div class="homePageNoticeTitle ellipsis">{{ notice.noticeTitle }}</div>
          <van-icon name="arrow" />
        </div>
        <div class="homePageGeneral">
          <div class="homePageTitle">
            <span class="title">提案关注点</span>
          </div>
          <div class="homePageConcerns">
            <div class="homePageConcernsItem"># 政务信息化</div>
            <div class="homePageConcernsItem"># 乡村振兴</div>
            <div class="homePageConcernsItem"># 医疗保险</div>
            <div class="homePageConcernsItem"># 农业</div>
            <div class="homePageConcernsItem"># 教育产业</div>
            <div class="homePageConcernsItem"># 新冠</div>
            <div class="homePageConcernsItem"># 境外</div>
            <div class="homePageConcernsItem"># 疫苗接种</div>
            <div class="homePageConcernsItem"># 环保</div>
          </div>
        </div>
        <div class="homePageGeneral">
          <div class="homePageTitle">
            <span class="title">所有提案</span>
            <span class="text" @click="allClick">更多
              <van-icon name="arrow" />
            </span>
          </div>
          <dataList v-for="item in dataList" :key="item.id" :title="item.title" @click="dataListClick(item)">
            <div class="state">{{ item.processStateView }}</div>
            <div>{{ item.mainSubmitUserName }}</div>
            <div>{{ item.submitDate }}</div>
          </dataList>
        </div>
      </div>
    </div>
  </van-pull-refresh>
</template>
<script>
import { Toast } from 'vant'
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import dataList from './dataList'
export default {
  name: 'homePage',
  components: {
    dataList
  },
  setup () {
    const $api = inject('$api')
    // const route = useRoute()
    const router = useRouter()
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      notice: {},
      dataList: []
    })
    onMounted(() => {
      noticeList()
    })
    // 通知公告
    const noticeList = async () => {
      const res = await $api.notice.noticeList({ pageNo: 1, pageSize: 1 })
      var { data: list } = res
      data.notice = list[0]
      proposalList()
    }
    // 提案
    const proposalList = async () => {
      const res = await $api.proposal.proposalList({ pageNo: 1, pageSize: 4 })
      var { data: list } = res
      data.dataList = list
      Toast('刷新成功')
      data.loading = false
    }
    const onRefresh = () => {
      noticeList()
    }
    const dataListClick = (row) => {
      console.log(row)
    }
    const myClick = () => {
      router.push({ name: 'myProposalList' })
    }
    const allClick = () => {
      router.push({ name: 'proposalList' })
    }
    const noticeClick = () => {
      router.push({ name: 'noticeList' })
    }
    return { ...toRefs(data), onRefresh, myClick, noticeClick, allClick, dataListClick }
  }
}
</script>
<style lang="less">
.homePage {
  width: 100%;
  padding-bottom: 50px;
  background-color: #f8f8f8;

  .homePageHead {
    width: 375px;
    height: 200px;
    background: url("../../assets/img/homePageZXHeadBg.png") no-repeat;
    background-size: 100% 175px;
    padding: 0 16px;
    padding-top: 130px;

    .homePageNumberBox {
      width: 100%;
      height: 70px;
      display: flex;
      justify-content: space-between;

      .homePageNumber {
        width: 166px;
        height: 70px;
        background: #ffffff;
        box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.08);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .homePageNew {
          width: 40px;
          height: 40px;
          background: url("../../assets/img/homePageNew.png") no-repeat;
          background-size: 100% 100%;
        }

        .homePageMy {
          width: 40px;
          height: 40px;
          background: url("../../assets/img/homePageMy.png") no-repeat;
          background-size: 100% 100%;
        }

        .homePageNumberTextBox {
          margin-left: 10px;

          .homePageNumberName {
            font-family: PingFang SC;
            font-weight: 600;
            line-height: 26px;
            color: #0f2542;
          }

          .homePageNumberText {
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 22px;
            color: #999999;

            span {
              color: #fe7530;
              margin-left: 2px;
            }

            b {
              font-weight: normal;
              color: #3088fe;
              margin-left: 2px;
            }
          }
        }
      }
    }
  }

  .homePageBox {
    padding: 10px 16px 0 16px;

    .homePageNotice {
      width: 100%;
      height: 44px;
      background: #ffffff;
      box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.08);
      opacity: 1;
      border-radius: 4px;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .homePageNoticeName {
        width: 48px;
        font-size: 14px;
        font-family: Fontquan-XinYiGuanHeiTi;
        line-height: 18px;
        padding: 0 10px;
        position: relative;
        letter-spacing: -1px;

        div {
          font-size: 14px;
          color: #3088fe;
          letter-spacing: -1px;
        }

        &::after {
          content: "";
          width: 1px;
          height: 27px;
          background: #999999;
          position: absolute;
          top: 50%;
          right: 0;
          opacity: 0.52;
          transform: translateY(-50%);
        }
      }

      .homePageNoticeTitle {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 600;
        line-height: 17px;
        color: #333333;
        width: 278px;
        padding-left: 6px;
      }

      .van-icon {
        transform: translateY(1px);
        font-size: 12px;
      }
    }

    .homePageGeneral {
      width: 100%;
      padding-bottom: 10px;

      .homePageConcerns {
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.08);
        opacity: 1;
        border-radius: 4px;
        padding: 5px;

        .homePageConcernsItem {
          height: 26px;
          padding: 0 10px;
          background: #f1f5fa;
          border-radius: 13px;
          display: inline-block;
          margin: 5px;
          font-size: 14px;
          font-family: PingFang SC;
          color: #3088fe;
          line-height: 26px;
        }
      }
    }
  }

  .homePageTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    position: relative;
    padding-left: 8px;

    &::after {
      content: "";
      width: 3px;
      height: 15px;
      background: #3088fe;
      border-radius: 2px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .title {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 22px;
      color: #0f2542;
    }

    .text {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 17px;
      color: #666666;

      .van-icon {
        font-size: 12px;
        color: #666666;
      }
    }
  }
}
</style>

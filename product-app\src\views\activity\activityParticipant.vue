<template>
  <div class="activityDetails">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="$general.loadConfiguration(5)+'font-weight:bold;line-height:1.5;'"
           v-html="title"></div>
    </div>
    <div :style="$general.loadConfiguration(1)">
      <van-tabs v-model:active="switchs.value"
                @click="tabClick"
                :color="appTheme"
                :title-active-color="appTheme"
                :ellipsis="false">
        <van-tab v-for="(item,index) in switchs.data"
                 :key="index"
                 :title="item.label"
                 :name="item.value"></van-tab>
      </van-tabs>
    </div>
    <!--搜索-->
    <div id="search"
         class="search_box"
         :style="$general.loadConfiguration()">
      <div class="search_warp flex_box">
        <div @click="btnSearch();"
             class="search_btn flex_box flex_align_center flex_justify_content">
          <van-icon :size="((appFontSize)*0.01)+'px'"
                    :color="'#666'"
                    class-prefix="icon"
                    :name="'sousuo'"></van-icon>
        </div>
        <form class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;"> <input id="searchInput"
                 class="flex_placeholder"
                 :style="$general.loadConfiguration(-1)"
                 :placeholder="seachPlaceholder"
                 maxlength="100"
                 type="text"
                 ref="btnSearch"
                 @keyup.enter="btnSearch()"
                 v-model="seachText" />
          <div v-if="seachText"
               @click="seachText='';btnSearch();"
               class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="((appFontSize)*0.01)+'px'"
                      :color="'#ccc'"
                      :name="'clear'"></van-icon>
          </div>
        </form>
      </div>
    </div>
    <van-grid clickable
              :column-num="4">
      <template v-for="(item,index) in listData"
                :key="index">
        <van-grid-item v-if="showItem(item)"
                       @click="openUser(item)">
          <div style="position: relative;">
            <!-- <zy-photo :style="$general.loadConfigurationSize(23)+'margin:auto;'"
                      :key="item.refresh"
                      :data="item"></zy-photo> -->
            <img :src="item.headImg"
                 :style="$general.loadConfigurationSize(23)+'margin:auto;'"
                 alt=""
                 srcset="">
            <div :style="$general.loadConfiguration(-3)+'margin-top:5px;'"
                 v-html="item.name"></div>
            <div v-if="switchs.value == 'activityLeaves'"
                 class="leave_status"
                 :style="$general.loadConfiguration(-6)">{{item.leaveStatus==2?'请假不通过':item.leaveStatus==1?'请假通过':'待审核'}}</div>
          </div>
        </van-grid-item>
      </template>
    </van-grid>
  </div>
  <div class="notText">{{pageTip}}</div>
  <transition name="van-fade">
    <ul v-if="footerBtnsShow"
        class="footer_btn_box">
      {{'&nbsp;'}}
      <div :style="$general.loadConfiguration()">
        <template v-for="(item,index) in footerBtns"
                  :key="index">
          <div v-if="item.type == 'btn'"
               class="van-button-box">
            <van-button loading-type="spinner"
                        :loading-size="((appFontSize)*0.01)+'px'"
                        :loading="item.loading"
                        :loading-text="item.loadingText"
                        :color="item.color?item.color:appTheme"
                        :disabled="item.disabled"
                        @click="footerBtnClick(item)">{{item.name}}</van-button>
          </div>
        </template>
      </div>
    </ul>
  </transition>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog } from 'vant'
export default {
  name: 'module',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      listData: [],
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部 btn为按钮
      participant: [
        // { id: 1, name: '我的怩' }
      ], // 活动参与人

      switchs: { value: 'activityInviters', data: [{ label: '邀请', value: 'activityInviters' }, { label: '报名', value: 'activitySignUps' }, { label: '签到', value: 'activitySignIns' }, { label: '请假', value: 'activityLeaves' }] },

      activityInviters: [], // 邀请人
      activitySignUps: [], // 报名人
      activitySignIns: [], // 签到人
      activityLeaves: [], // 请假人
      pageTip: ''
    })
    onMounted(() => {
      onRefresh()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      // getList()
    }
    // 列表请求
    const getList = async () => {
      var res = await $api.activity.activityInfo(data.id)
      var { data: info } = res
      var inviterList = info.inviterList || []// 邀请人集合
      var signUpList = info.signUpList || []// 报名人集合
      var signInList = info.signInList || []// 签到人集合
      var leaveList = info.leaves || []// 请假人集合
      if (info) {
        var participants = [inviterList, signUpList, signInList, leaveList]
        // 将人加入到参与人中
        var newData = []
        data.participant = []
        for (var i = 0; i < participants.length; i++) {
          for (var j = 0; j < participants[i].length; j++) {
            var puserId = participants[i][j].userId || ''
            if (!puserId) continue
            var puserName = participants[i][j].name || ''
            var pheadImg = participants[i][j].headImg || ''
            var addItem = $general.getItemForKey(puserId, newData, 'id')
            if (!addItem) { // 没有在参与人中就添加
              newData.push({ id: puserId, name: puserName, url: pheadImg })
            }
          }
        }
        data.activityInviters = participants[0]
        data.activitySignUps = participants[1]
        data.activitySignIns = participants[2]
        data.activityLeaves = participants[3]
        tabClick()
      }
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      // if (data.dataList.length >= total) {
      //   data.finished = true
      // }
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }

    const tabClick = () => {
      data.seachText = ''
      var list = data[data.switchs.value] || []
      if (data.switchs.value === 'activityInviters') {
        list = data.activityInviters
      } else if (data.switchs.value === 'activitySignUps') {
        list = data.activitySignUps
      } else if (data.switchs.value === 'activitySignIns') {
        list = data.activitySignIns
      } else if (data.switchs.value === 'activityLeaves') {
        list = data.activityLeaves
      }
      data.listData = []
      var newData = []
      list.forEach(function (_eItem) {
        _eItem.headImg = _eItem.headImg || require('../../assets/img/icon_default_user.png')
        if (_eItem && _eItem.userId) newData.push(_eItem)
      })
      data.listData = data.listData.concat(newData)
      console.log(data.listData)
      var nItem = $general.getItemForKey(data.switchs.value, data.switchs.data, 'value')
      data.pageTip = nItem.label + data.listData.length + '人'
    }
    const openUser = rows => {
      router.push({ name: 'personData', query: { id: rows.userId } })
    }
    // 显示搜索的人
    const showItem = (_item) => {
      if (data.seachText) {
        if (_item.name.indexOf(data.seachText) === -1) {
          return false
        }
      }
      return true
    }
    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, search, tabClick, openUser, showItem }
  }
}
</script>
<style lang="less" scoped>
.activityDetails {
  background: #fff;
  .search-dropdown-menu {
    margin-right: 5px;
    padding: 10px 0;
  }
  .van-dropdown-menu.van-hairline--top-bottom::after {
    border-width: 0 0;
  }
  .van-grid {
    background: #fff;
  }
  .leave_status {
    position: absolute;
    top: -5px;
    right: -20px;
    color: red;
  }
}
</style>

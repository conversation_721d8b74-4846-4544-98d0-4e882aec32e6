<template>
  <div class="Pie10">
    <div class="container_swipe">
      <div class="pages10">
        <img src="../../../assets/img/timeAxis/g_bg010.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item2">
          <div class="pages_item2_text0"
               :class="{ 'fade-in1': showText1 }">2023年度</div>
          <div class="pages_item2_text1"
               :class="{ 'fade-in1': showText1 }">您的履职排名是<span>{{performanceRankingVal}}</span>位</div>
          <div class="pages_item2_text2"
               :class="{ 'fade-in1': showText2 }">新的一年</div>
          <div class="pages_item2_text3"
               :class="{ 'fade-in1': showText3 }">请继续加油呀</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, onUpdated } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'Page10',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean,
    pageData: Object
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      year6: '', // 第一次参加线上履职学习年
      month6: '', // 第一次参加线上履职学习月
      day6: '', // 第一次参加线上履职学习日
      watchVideoTime: 0, // 观看专题讲座时长
      readBookTime: 0, // 阅读电子数据时长
      showText1: false,
      showText2: false,
      showText3: false,
      performanceRankingVal: 0
    })
    onUpdated(() => {
      data.showText1 = props.showText1
      data.showText2 = props.showText2
      data.showText3 = props.showText3
      data.performanceRankingVal = props.pageData.dutyRank
    })
    onMounted(() => {
      preventScroll()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        setTimeout(() => {
        }, 500) // 延迟500毫秒
      } else {
        // 向下滑动
        console.log('向下滑动')
        setTimeout(() => {
          router.push('/Page8')
        }, 500) // 延迟500毫秒
      }
    }
    return { ...toRefs(data), $general, handleTouchStart, handleTouchMove }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
.Pie10 {
  width: 100%;
  min-height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages_item2_text0,
    .pages_item2_text1,
    .pages_item2_text2,
    .pages_item2_text3 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item2_text0.fade-in,
    .pages_item2_text1.fade-in,
    .pages_item2_text2.fade-in,
    .pages_item2_text3.fade-in {
      opacity: 1;
    }
    .pages10 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages_item2 {
        position: absolute;
        top: 40%;
        z-index: 1;
        .pages_item2_text0 {
          color: #a9894c;
          font-size: 0.9rem;
          font-family: "YouSheBiaoTiHei-2";
          text-align: center;
        }
        .pages_item2_text1 {
          color: #336cb5;
          font-size: 0.45rem;
          margin-top: 0.1rem;
          text-align: center;
          span {
            font-size: 0.8rem;
            color: #a9894c;
            font-family: "YouSheBiaoTiHei-2";
          }
        }
        .pages_item2_text2 {
          color: #336cb5;
          font-size: 0.45rem;
          margin-top: 0.4rem;
          letter-spacing: 3px;
          text-align: center;
        }
        .pages_item2_text3 {
          color: #336cb5;
          font-size: 0.45rem;
          letter-spacing: 2px;
          margin-top: 0.4rem;
          text-align: center;
        }
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
  }
}
</style>

<template>
  <div class="mainModuleDetails">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div class="mainModuleDetailsBox">
        <div class="mainModuleTitle">{{ details.title }}</div>
        <div class="mainModuleInfoBox">
          <div class="mainModuleInfo">
            <div>建议者：{{ details.mainSubmitUserName }}</div>
            <div v-if="details.submitDate">{{ details.submitDate.slice(0, 10) }}</div>
          </div>
          <div class="mainModuleState">{{ details.processStateView }}</div>
        </div>
        <template v-if="details.joinUserList">
          <div class="joinUserBox"
               v-if="details.joinUserList.length">
            <div class="mainModuleText">联名人{{ details.joinUserList.length }}人</div>
            <div class="joinUser ellipsisTwo">
              <span v-for="(item, index) in details.joinUserList"
                    :key="item.userId">{{ index != 0 ? '，' : '' }}{{ item.name }}</span>
            </div>
            <div class="viewMore"
                 @click="detailsClick">点击查看
              <van-icon name="arrow" />
            </div>
          </div>
        </template>
        <div class="mainModuleText">正文</div>
        <div class="mainModuleContent"
             v-html="details.content"></div>
        <template v-if="details.attachmentList">
          <div class="mainModuleText"
               v-if="details.attachmentList.length">附件</div>
          <div class="attachmentBox">
            <div class="attachmentItem"
                 v-for="item in details.attachmentList"
                 @click="download(item)"
                 :key="item.id">{{ item.fileName }}</div>
          </div>
        </template>
        <!-- <div class="situationBox">
          <div class="situation">是否调研：{{ details.ifInvestigate == '1' ? '是' : '' }}{{ details.ifInvestigate == '0' ? '否'
            : '' }}
          </div>
          <div class="situation">是否公开：{{ details.ifAdvicePublic == '1' ? '是' : '' }}{{ details.ifAdvicePublic == '0' ?
            '否' : ''
            }}
          </div>
          <div class="situation">是否涉密：{{ details.ifSecret == '1' ? '是' : '' }}{{ details.ifSecret == '0' ? '否' : '' }}
          </div>
        </div> -->
      </div>
      <div class="mainModuleProcessBox">
        <div class="mainModuleText">建议进程</div>
        <div class="mainModuleProcess">
          <div class="mainModuleProcessItem"
               v-for="(item, index) in process"
               :key="index + 'process'">
            <div class="processIcon"
                 :class="{ processIcone: item.pointFlag && details.processState != '200' }"></div>
            <div class="processName">{{ item.nodeName }}
              <span>{{ item.submitDate || item.auditDate || item.assignDate || item.evaluateDate }}</span>
            </div>
            <template v-if="item.transactDetailVoList">
              <div class="processText"
                   v-for="unit in item.transactDetailVoList"
                   :key="unit.id">
                <template v-if="unit.transactType == '1'">主办</template>
                <template v-if="unit.transactType == '2'">协办</template>
                <template v-if="unit.transactType == '3'">分办</template>单位：{{ unit.groupName }}
                <template v-if="item.showTransactStatusAndDate">（{{ unit.transactStatusView }}）</template>
              </div>
            </template>
            <div class="processText"
                 v-if="item.submitUserName">{{ item.nodeName }}人：{{ item.submitUserName }}</div>
            <div class="processText"
                 v-if="item.auditUserName">{{ item.nodeName }}人：{{ item.auditUserName }}
              <span>{{ item.auditResultView }}</span>
            </div>
            <!-- @click="reviewClick(item)" -->
            <div class="processText"
                 v-if="item.evaluateResultView">{{ item.evaluateResultView }}<span
                    @click="satisfactionClick">满意度测评</span>
            </div>
          </div>
        </div>
        <!-- <div class="mainModuleButton">
          <van-button type="primary"
                      @click="communication"
                      v-if="['170', '180', '190', '200'].includes(details.processState)"
                      block>查看单位与委员沟通情况</van-button>
          <van-button type="primary"
                      @click="reply"
                      block>查看答复信息</van-button>
        </div> -->
      </div>
      <!-- <suggestReview :id="id"></suggestReview> -->
    </van-pull-refresh>
    <van-popup v-model:show="show"
               :style="{ maxHeight: '66%' }"
               position="top">
      <div class="review"
           v-for="item in reviewList"
           :key="item.id">
        <div class="reviewState"
             v-if="item.statusCode == '115'">待专委会审查</div>
        <div class="reviewState"
             v-if="item.statusCode == '120'">待审查</div>
        <div class="reviewState"
             v-if="item.statusCode == '130'">待复审</div>
        <div class="reviewState"
             v-if="item.statusCode == '140'">待审定</div>
        <div class="reviewResults">审查结果：<span>{{ item.acceptResultView }}</span></div>
        <div class="reviewOpinion">审查意见：{{ item.acceptOpinion }}</div>
      </div>
    </van-popup>
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box">
        {{ '&nbsp;' }}
        <div>
          <template v-for="(item, index) in footerBtns"
                    :key="index">
            <div v-if="item.type == 'btn' && item.name != '听一听' && item.name != '暂停' && item.name != '继续播放'"
                 class="van-button-box"><van-button :color="'#3894ff'"
                          @click="footerBtnClick(item)">{{ item.name }}</van-button></div>
          </template>
        </div>
      </ul>
    </transition>

    <van-popup v-model:show="shows"
               round
               closeable
               @click-close-icon="showsicon"
               :style="{ width: '80%' }">
      <div class="popup_transaction">
        <div class="popup_transaction_radio"
             v-for="item, index in flowEvaluateGroupList"
             :key="index"
             v-show="areaId == '370202'">
          <div class="popup_transaction_title">
            {{ item.type == '1' ? '主办单位' : item.type == '2' ? '协办单位' : item.type == '3' ? '分办单位' : '' }}
          </div>
          <label v-for="it, ind in item.children"
                 @click="radioChange(item.type)"
                 :key="ind">
            <van-radio-group v-model="groupName">
              <van-radio :name="it.name"
                         :value="it.name"> {{ it.name }}</van-radio>
            </van-radio-group>
          </label>
        </div>
        <div class="popup_transaction_radio"
             v-show="areaId == '370203'">
          <label @click="radioChange(1)">
            <van-radio-group v-model="groupType">
              <van-radio name="主办"
                         value="主办"> {{ '主办' }}</van-radio>
            </van-radio-group>
          </label>
          <label @click="radioChange(2)">
            <van-radio-group v-model="groupType">
              <van-radio name="协办"
                         value="协办"> {{ '协办' }}</van-radio>
            </van-radio-group>
          </label>
        </div>
        <div class="popup_transaction_but">
          <button style="background: #00000000;border: 1px solid #A0A0A0;"
                  @click="cancel">取消</button>
          <button style="background: #3088FE; color: #fff;"
                  @click="submitForm">确定</button>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
/* eslint-disable */
// import { Toast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
// import suggestReview from './suggestReview/suggestReview'
export default {
  name: 'suggestDetails',
  // components: {
  //   suggestReview
  // },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const data = reactive({
      id: route.query.id,
      details: {},
      refreshing: false,
      process: [],
      reviewList: [],
      areaId: sessionStorage.getItem('areaId') || '',
      footerBtnsShow: true,
      footerBtns: [],
      flowEvaluateGroupList: [],
      groupName: '',
      groupType: '',
      shows: false,
      show: false
    })
    onMounted(() => {
      transactProposalDetail()
      nodeInfo()
    })
    const onRefresh = () => {
      setTimeout(() => {
        transactProposalDetail()
      }, 520)
    }
    // 列表请求
    const transactProposalDetail = async () => {
      data.footerBtns = []
      const res = await $api.suggest.transactSuggestDetail({
        suggestId: data.id
      })
      var { data: details } = res
      data.details = details
      var processState = details.processState || "0"
      var handlingPlanBtn = { name: "办理方案", type: "btn", click: "handlingPlan" }
      // that.delItemForKey(handlingPlanBtn, data.footerBtns, 'click')
      if ((processState == '170' || processState == '180') && data.areaId == '370202') {
        data.footerBtns.push(handlingPlanBtn)
      }
      var flowAnsweBtn = { name: "答复件列表", type: "btn", click: "flowAnsweList" }
      // that.delItemForKey(flowAnsweBtn, data.footerBtns, 'click')
      if (processState == '170' || processState == '180' || processState == '200') {
        data.footerBtns.push(flowAnsweBtn)
      }
      var evaluateBtn = { name: "满意度测评", type: "btn", click: "evaluate" }
      // that.delItemForKey(evaluateBtn, data.footerBtns, 'click')
      if ((processState == '180' || processState == '200') && !['370215', '370213'].includes(data.areaId)) {
        data.footerBtns.push(evaluateBtn)
      }
      var evaluateBtn = { name: "量化测评", type: "btn", click: "quantization" }
      // that.delItemForKey(evaluateBtn, data.footerBtns, 'click')
      if (processState == '180' && data.areaId == '370203') {
        data.footerBtns.push(evaluateBtn)
      }
      var evaluateBtn = { name: "满意度测评", type: "btn", click: "JimoEvaluate" }
      // that.delItemForKey(evaluateBtn, data.footerBtns, 'click')
      if (processState == '180' && data.areaId == '370215') {
        data.footerBtns.push(evaluateBtn)
      }
      var evaluateBtn = { name: "满意度测评", type: "btn", click: "LichangEvaluate" }
      // that.delItemForKey(evaluateBtn, data.footerBtns, 'click')
      if (processState == '180' && data.areaId == '370213') {
        data.footerBtns.push(evaluateBtn)
      }
      if (processState == '200' && data.areaId == '370203') {
        evaluateBtn.disabled = true
        data.footerBtns.push(evaluateBtn)
      }
      data.refreshing = false
    }
    const footerBtnClick = async (_item) => {
      switch (_item.click) {
        case "listen"://听一听
          break;
        case "handlingPlan"://办理方案
          router.push({ name: 'handlingPlan', query: { id: data.id } })
          break;
        case "flowAnsweList"://答复件列表
          router.push({ name: 'suggestReply', query: { id: data.id } })
          // $o.openDetails({ relateType: zyUrl.getSysType("7.1").key }, { recordId: recordId });
          break;
        case "quantization"://量化测评 
          router.push({ name: 'quantificat', query: { recordId: data.id, siteId: data.areaId, disabled: _item.disabled } })
          // $o.openDetails({ relateType: zyUrl.getSysType("7.5").key }, { recordId: recordId, siteId: data.areaId, disabled: _item.disabled });
          break;
        case "JimoEvaluate"://即墨满意度测评
          router.push({ name: 'JimoEvaluate', query: { recordId: data.id, siteId: data.areaId } })
          // $o.openDetails({ relateType: zyUrl.getSysType("7.6").key }, { recordId: recordId, siteId: data.areaId });
          break;
        case "LichangEvaluate"://李沧满意度测评
          router.push({ name: 'LichangEvaluate', query: { recordId: data.id, siteId: data.areaId } })
          // $o.openDetails({ relateType: zyUrl.getSysType("7.6").key }, { recordId: recordId, siteId: data.areaId });
          break;
        case "evaluate"://满意度测评
          data.flowEvaluateGroupList = []
          if (data.areaId == '370202' || data.areaId == '370203') {
            const ret = await $api.suggest.suggestUrl('suggest/flowEvaluateGroupList', { "dataId": data.id })
            var code = ret ? ret.errcode || "" : "";
            if (code == 200) {
              // that.getData();
              // that.flowEvaluateGroupList = ret.data
              var dataArr = ret.data.reduce((acc, cur) => {
                const type = cur.type
                if (!acc[type]) {
                  acc[type] = { type, children: [] }
                }
                const child = acc[type].children.find(item => item.name === cur.name && item.type === cur.type)
                if (child) {
                  child.evaluation = cur.evaluation
                } else {
                  acc[type].children.push({ name: cur.name, type: cur.type, evaluation: cur.evaluation })
                }
                return acc
              }, {})
              for (var key in dataArr) {
                data.flowEvaluateGroupList.push(dataArr[key])
              }
              console.log(data.flowEvaluateGroupList)
              if (data.areaId == '370203' && data.flowEvaluateGroupList[0].type == '3') {
                data.groupName = data.flowEvaluateGroupList[0].children.map(item => item.name).join('、')
                data.groupType = data.flowEvaluateGroupList[0].type == '1' ? '主办' : data.flowEvaluateGroupList[0].type == '2' ? '协办' : data.flowEvaluateGroupList[0].type == '3' ? '分办' : ''
                // $o.openDetails({ relateType: zyUrl.getSysType("7.2").key }, { recordId: recordId, siteId: data.areaId, groupName: data.groupName, groupType: data.groupType });
                router.push({ name: 'suggestSatisfaction', query: { recordId: data.id, siteId: data.areaId, groupName: data.groupName, groupType: data.groupType } })
              } else if (ret.data.length == 1) {
                data.groupType = data.flowEvaluateGroupList[0].type == '1' ? '主办' : data.flowEvaluateGroupList[0].type == '2' ? '协办' : data.flowEvaluateGroupList[0].type == '3' ? '分办' : ''
                data.groupName = data.flowEvaluateGroupList[0].children[0].name || ''
                // $o.openDetails({ relateType: zyUrl.getSysType("7.2").key }, { recordId: recordId, siteId: data.areaId, groupName: data.groupName, groupType: data.groupType });
                router.push({ name: 'suggestSatisfaction', query: { recordId: data.id, siteId: data.areaId, groupName: data.groupName, groupType: data.groupType } })
              } else {
                data.shows = true
              }
            } else {
              // T.toast(ret && code != 200 ? ret.errmsg || ret.data : "加载失败，请重试");
            }
          } else {
            router.push({ name: 'suggestSatisfaction', query: { recordId: data.id, siteId: data.areaId } })
          }
          break;
      }
    }
    // 列表请求
    const nodeInfo = async () => {
      const res = await $api.suggest.nodeInfo({
        id: data.id
      })
      var { data: process } = res
      var processList = []
      var i = true
      process.forEach(item => {
        if (i) {
          processList.push(item)
        }
        if (item.pointFlag) {
          i = false
        }
      })
      data.process = processList
    }
    // 弹框确定
    const submitForm = () => {
      if (data.groupName != '' || data.groupType != '') {
        data.shows = false
        router.push({ name: 'suggestSatisfaction', query: { recordId: data.id, siteId: data.areaId, groupName: data.groupName, groupType: data.groupType } })
        // $o.openDetails({ relateType: zyUrl.getSysType("7.2").key }, { recordId: recordId, siteId: siteId, groupName: data.groupName, groupType: data.groupType });
        data.flowEvaluateGroupList = []
      } else {
        T.toast('至少选一条')
        return
      }
    }
    // 点击单选框
    const radioChange = (_item) => {
      if (data.areaId == '370202') {
        data.groupType = _item == '1' ? '主办' : _item == '2' ? '协办' : _item == '3' ? '分办' : ''
      } else if (data.areaId == '370203') {
        var groupnameList = data.flowEvaluateGroupList.find(item => item.type == _item)
        data.groupName = groupnameList.children.map(item => item.name).join('、')
      }
    }
    // 弹框关闭
    const showsicon = () => {
      data.flowEvaluateGroupList = []
    }
    const cancel = () => {
      data.shows = false
      data.flowEvaluateGroupList = []
    }
    const detailsClick = () => {
      router.push({ name: 'readUserList', query: { id: data.id } })
    }
    const reviewClick = (row) => {
      data.reviewList = row.acceptList
      data.show = true
    }
    const communication = () => {
      router.push({ name: 'memberCommunication', query: { id: data.id } })
    }
    const reply = () => {
      router.push({ name: 'suggestReply', query: { id: data.id } })
    }
    const satisfactionClick = () => {
      router.push({ name: 'suggestSatisfactionDetails', query: { id: data.id } })
    }
    const download = (item) => {
      if (item.fileType === 'pdf') {
        if (window.location.origin === 'http://59.224.134.155') {
          window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + item.filePath)
        } else {
          window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.filePath)
        }
      } else {
        var param = {
          id: item.id,
          url: item.filePath,
          name: item.fileName
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    return { ...toRefs(data), submitForm, radioChange, showsicon, cancel, download, onRefresh, footerBtnClick, detailsClick, reviewClick, communication, reply, satisfactionClick }
  }
}/* eslint-enable */
</script>
<style>
.popup_transaction {
  width: 95%;
  height: 100%;
  margin: 20px auto;
}

.popup_transaction_title {
  width: 90%;
  height: 30px;
  line-height: 30px;
  margin: 10px auto;
  font-weight: 700;
}

.popup_transaction_radio {
  display: flex;
  align-items: left;
  flex-direction: column;
  margin: 10px 0;
}

.popup_transaction_but {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.popup_transaction_but>button {
  width: 48%;
  height: 100%;
  border-radius: 10px;
}
</style>

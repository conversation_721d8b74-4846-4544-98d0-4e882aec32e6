<template>
  <div class="newsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead" :title="title" left-text="" left-arrow @click-left="onClickLeft"
        @click-right="play">
        <!-- <template #right>{{playText}}
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_header_box"
      v-if="(pageType != 'survey' && pageType != 'photograpr') && pageType != 'resumption'">
      <div class="n_details_title" :style="'font-size:20px; '" v-html="politicalData.title"
        v-if="pageType !== 'surveyComment'"> </div>
      <div class="n_details_title" :style="'font-size:20px; '" v-html="title" v-else> </div>
      <div class="n_details_more_box flex_box">
        <!--来源-->
        <div class="n_details_name flex_placeholder" v-if="pageType === 'surveyComment'" :style="'font-size:12px;'">{{
          userName }}</div>
        <!--资讯类型 可点击更多-->
        <span></span>
        <div class=" flex_box flex_align_center flex_justify-content_end">
          <div class="n_details_name" :style="'font-size:12px;' + 'color:' + appTheme + ';'">{{ politicalData.source }}
          </div>
          <div class="n_details_time">
            {{ politicalData.publishDate ? dayjs(politicalData.publishDate).format('YYYY-MM-DD') : '' }}
          </div>
          <div class="n_details_time" v-if="pageType === 'surveyComment'">{{ createDate }}</div>
        </div>
      </div>
      <div class="n_details_content" v-if="pageType !== 'surveyComment'" v-html="politicalData.content"></div>
      <div class="n_details_content" v-else v-html="contents"></div>
      <!-- 展示附件 -->
      <template v-if="attachInfo.data.length != 0">
        <div class="general_attach" style="background-color: #fff;">
          <div v-for="(item, index) in attachInfo.data" :key="index"
            class="general_attach_item flex_box flex_align_center click" @click="download(item, false)">
            <img class="general_attach_icon" :style="$general.loadConfigurationSize([5, 7])"
              src="../../assets/img/pdf.png" />
            <div class="flex_placeholder flex_box flex_align_center">
              <div class="general_attach_name text_one2" style="font-size: 14px;display: -webkit-box;">{{ item.name }}
              </div>
              <div class="general_attach_size" style="font-size: 12px;">{{ $general.getFileSize(item.size) }}</div>
            </div>
            <div v-if="item.state != 2" class="general_attach_state flex_box flex_align_center flex_justify_content"
              :style="$general.loadConfigurationSize([7, 7])">
              <van-icon v-if="item.state == 0" class-prefix="iconfont" color="#ccc"
                :size="((appFontSize + 3) * 0.01) + 'rem'" name="xiazai"></van-icon>
              <van-circle v-else-if="item.state == 1" :size="((appFontSize + 3) * 0.01) + 'rem'" v-model="item.schedule"
                :rate="item.schedule" stroke-width="150"></van-circle>
              <van-icon @click.stop="Toast('缓存异常，请点击标题重试');" v-else-if="item.state == 3" color="#ccc"
                :size="((appFontSize + 3) * 0.01) + 'rem'" name="warning-o"></van-icon>
            </div>
          </div>
        </div>
      </template>
      <!-- <div class="n_details_more_box">
        <div v-if="browerCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">{{browerCount.hint}}{{browerCount.value}}</div>
        <div v-if="shareCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">分享：{{shareCount.value}}</div>
        <div v-if="commentCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">评论：{{commentCount.value}}</div>
        <div v-if="dataTime.show"
             class="n_details_time"
             :style="'font-size:12px;'">{{dataTime.value}}</div>
        <div style="clear: both;"></div>
      </div> -->
    </div>
    <div class="n_details_content" v-if="pageType === 'resumption' || pageType === 'photograpr'">
      <div class="representativeCircle_box_li">
        <div class="representativeCircle_box_top">
          <img :src="representativeDetails.headImg" alt="" class="representativeCircle_box_top_headImg">
          <div class="representativeCircle_box_name">
            <p class="representativeCircle_box_names">{{ representativeDetails.publishName }}</p>
            <p class="representativeCircle_box_congressStr"> {{ representativeDetails.congressStr + '人大代表' }}</p>
          </div>
          <template v-if="pageType === 'resumption'">
            <div v-if="user.id != representativeDetails.publishBy"
              :class="{ 'attention': true, 'attentionDel': isFollow == 1 }" @click="attentionEdit">
              {{ isFollow == 1 ? '已关注' : '+ 关注' }}
            </div>
            <div class="representativeCircle_box_del" @click="committeesayDel(representativeDetails.id)" v-else>
              <van-icon name="delete-o" />
            </div>
          </template>
        </div>
        <div class="representativeCircle_box_center">
          <div class="representativeCircle_box_center_content" v-html="representativeDetails.content">
          </div>
          <div class="representativeCircle_box_center_attachmentList">
            <van-image position="contain" width="2.5rem" fit="cover" height="2rem"
              v-for="it in representativeDetails.attachmentList" :key="it.id" :src="it.filePath"
              @click="previewCalback(representativeDetails.attachmentList)" />
          </div>
        </div>
        <div class="representativeCircle_box_buttom">
          <div class="representativeCircle_box_buttom_time">{{ representativeDetails.publishDate }}</div>
          <div class="representativeCircle_box_buttom_cont">
            <div class="representativeCircle_box_buttom_conmment" @click.stop="openMoreComment()">
              <span>{{ commentCount }}</span>
              <van-icon name="comment-o" />
            </div>
            <div class="representativeCircle_box_buttom_link" @click="downLike(conmmentList)">
              <span>{{ fabulousCount }}</span>
              <van-icon name="good-job-o" :style="ifIike == 1 ? 'color:#fe7e3d' : ''" />
            </div>
          </div>
        </div>
        <div class="likeComment_box">
          <div class="like_box" :style="$general.loadConfiguration(-4)">
            <van-icon name="like-o" v-if="menuList != null && menuList.length" />
            <span :style="$general.loadConfiguration(-4) + 'margin-bottom: 2px;' + 'line-height: 0.2rem;'"
              @click.stop="openUserList(it.id)" v-for="it, ind in menuList" :key="ind">{{ ind > 0 ? ',' : '' }} {{
                it.name
              }} </span>
          </div>
          <div class="comment_box" @click.stop="" v-if="commentList.length != 0">
            <div v-for="items, index in commentList" :key="index">
              <p style="display: flex;align-items: center;">
                <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                  @click.stop="openUserList(items.createBy)">{{ items.name }}: </span>
                <span :style="$general.loadConfiguration(-4) + 'flex:1;'" @click.stop="openMoreComment(items, index)">
                  {{
                    items.content }} </span>
              </p>
              <p v-for="(ite, ind) in items.commentList" :key="ind" :style="$general.loadConfiguration(-4)">
                <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                  @click.stop="openUserList(ite.createBy)">{{ ite.name }}</span> 回复
                <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                  @click.stop="openUserList(items.createBy)">{{ items.name }}: </span>
                <span :style="$general.loadConfiguration(-4)" @click.stop="openMoreComment(items, index)"> {{
                  ite.content }}
                </span>
              </p>
            </div>
          </div>
          <div class="reply_box" @click.stop="">
            <div class="reply_box_item">
              <input type="text" v-model="inputInfo.value" :style="$general.loadConfiguration(-4)" class="reply_box_inp"
                :placeholder="inputInfo.placeholder">
              <button :class="inputInfo.value != '' ? 'reply_box_but' : 'reply_box_buts'"
                :style="$general.loadConfiguration(-4)" @click="mgcHandle()" :disabled="inputInfo.disabled">发送</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--展示评论-->
  <commentList ref="commentOldAllList" v-if="pageType == 'community'" @openInputBoxEvent="openInputBoxEvent"
    @freshState="freshState" :commentData="commentData" :type="type" :pageType="pageType" :id="id" />
  <div style="height:60px;"></div>
  <footer class="footerBox" v-if="pageType == 'community'">
    <inputBox ref="inputBox" :inputData="inputData" @addCommentEvent="addCommentEvent" :pageType="pageType" :type="type"
      :id="id" />
  </footer>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, computed, watch } from 'vue'
import { NavBar, Sticky, ImagePreview, Image as VanImage, Dialog, Toast } from 'vant'
import { useStore } from 'vuex'
import inputBox from '../../components/inputBox/inputBox.vue'
import commentList from '../../components/commentList/commentList.vue'
export default {
  name: 'newsDetails',
  components: {
    inputBox,
    commentList,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const store = useStore()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user') || ''),
      ifIike: JSON.parse(route.query.ifIike || '0'), // eslint-disable-line
      id: route.query.id,
      isFollow: route.query.isFollow || '',
      publishBy: route.query.publishBy || '',
      pageType: route.query.type || '',
      userName: route.query.userName || '',
      contents: route.query.content || '',
      createDate: route.query.createDate || '',
      details: {},
      type: 53,
      refreshing: false,
      show: false,
      browerCount: { show: false, value: '0', hint: '阅读：' }, // 阅读数
      shareCount: { show: false, value: '0' }, // 分享数
      // commentCount: { show: false, value: '0' }, // 评论数
      commentCount: 0,
      fabulousCount: route.query.fabulousCount || 0,
      dataTime: { show: false, value: '' }, // 时间
      IBSName: { show: false, value: '' }, // 资讯类型
      source: '', // 来源
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] }, // 附件对象
      playText: '播放',
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      commentOldAllList: null,
      inputBox: null,
      commentObj: {},
      representativeDetails: {},
      politicalData: {},
      twoInstitutesData: {},
      communityData: {},
      representativeData: {},
      countrysideData: {},
      numberAppData: {},
      surveyComment: [], // 意见征集评论列表
      surveyDetails: {}, // 意见征集详情
      attentionStatus: false,
      conmmentList: {},
      commentList: [],
      menuList: [],
      pingData: '',
      pingIndex: '',
      inputInfo: { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送', disabled: false }
    })
    const getShow = computed(() => {
      // 返回的是ref对象
      return store.state.speechShow
    })
    const getStatus = computed(() => {
      // 返回的是ref对象
      return store.state.speechStauts
    })
    watch(getShow, (newName, oldName) => {
      data.show = newName
      if (!data.show) {
        data.playText = '播放'
      }
    })
    watch(getStatus, (newName, oldName) => {
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '继续'
        } else {
          data.playText = '暂停'
        }
      }
    })
    const play = () => {
      if (data.playText === '播放') {
        data.playText = '暂停'
        store.commit('setSpeechShow', true)
      } else {
        store.commit('setStatus', !store.state.speechStauts)
      }
    }
    onMounted(() => {
      browseSave()
      newsInfo()
      // if (data.pageType === 'resumption') {
      //   data.inputBox.showComment = false
      //   data.inputBox.showLike = false
      // }
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '暂停'
        } else {
          data.playText = '继续'
        }
      }
    })
    const onRefresh = () => {
      newsInfo()
    }
    const browseSave = async () => {
      await $api.general.saveBrowse({
        keyId: data.id,
        type: data.type
      })
    }
    // 详情
    const newsInfo = async () => {
      getDetails()
      getFabulousList()
      if (data.pageType !== 'community') {
        getCommentList()
      }
    }
    // 获取详情
    const getDetails = async () => {
      var pageArr = ['countryside', 'community', 'representative', 'numberApp', 'favorite53']
      if (data.pageType === 'resumption') {
        // resumption 履职圈
        const res = await $api.news.getRepresentativeDetails({
          id: data.id
        })
        var { data: list } = await $api.general.getCommentStats({
          keyId: data.id,
          type: '25',
          areaId: data.user.areaId
        })
        data.conmmentList = list
        data.representativeDetails = res.data
      } else if (data.pageType === 'favorite5' || data.pageType === 'political' || data.pageType === 'twoInstitutes' || data.pageType === 'ZT' || data.pageType === 'columnList' || data.pageType === 'module6') {
        // political 政情快递
        // twoInstitutes 两院咨询
        const res = await $api.news.getNewsDetail({
          id: data.id
        })
        var interlinkage = sessionStorage.getItem('interlinkage') || ''
        if (interlinkage === res.data.externalLinks) {
          router.push({ path: '/newsList5', query: { title: '资讯' } })
          return
        }
        if (res.data.externalLinks !== null) {
          sessionStorage.setItem('interlinkage', res.data.externalLinks)
          window.location.replace(res.data.externalLinks)
          return
        }
        data.politicalData = res.data
        data.attachInfo.data = []
        var attachmentList = res.data.attachmentList || []
        if (attachmentList.length !== 0) {
          for (var k = 0; k < attachmentList.length; k++) {
            var fileId = attachmentList[k].id
            var nItemPath = attachmentList[k].filePath
            var fileName = attachmentList[k].fileName
            var fileType = attachmentList[k].fileType
            data.attachInfo.data.push({
              url: nItemPath,
              state: 0,
              schedule: -1,
              name: fileName,
              fileId: fileId,
              iconInfo: $general.getFileTypeAttr(fileType)
            })
          }
        }
      } else if (pageArr.includes(data.pageType)) {
        // countryside 乡村振兴
        // community 青岛社区民意
        // representative 代表风采
        // numberApp 数字应用
        const res = await $api.news.getSpecialsubjectnewsInfo({
          id: data.id
        })
        data.politicalData = res.data
      } else if (data.pageType === 'announcement') { // 公告栏
        const res = await $api.news.getAnnouncement({ id: data.id })
        data.politicalData = res.data
      } else if (data.pageType === 'photograpr') { // 随手拍详情
        const res = await $api.news.photograprDetails({ id: data.id })
        data.representativeDetails = res.data
      } else if (data.pageType === 'listType') { // 首页专题列表详情
        const res = await $api.news.getSpecialsubjectRelateinfo({ id: data.id, relateType: '53' })
        data.politicalData = res.data
      }
    }
    const download = (item) => {
      if (item.iconInfo.type === 'pdf') {
        if (window.location.origin === 'http://59.224.134.155') {
          window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + item.url)
        } else {
          window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.url)
        }
      } else {
        var param = {
          id: item.id,
          url: item.filePath,
          name: item.fileName
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    // 获取评论列表
    const getCommentList = async () => {
      const res = await $api.general.getCommentList({
        pageNo: 1,
        pageSize: 99,
        keyId: data.id,
        type: '',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      console.log('获取评论列表===>>', res)
      var list = res ? res.data || [] : []
      data.commentCount = res ? res.total : 0
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          var item = {}
          var itemData = _eItem
          item.hasDelete = itemData.createBy === data.user.id
          item.createBy = itemData.createBy
          // id
          item.id = itemData.id || ''
          // id
          item.extend = itemData.extend || ''
          // 来源
          item.url = itemData.userHeadImg || '../../../images/icon_default_user.png'
          // 用户头像
          item.name = itemData.userName || '匿名用户'
          item.content = itemData.content
          // 评论中的图片
          var resultBatchAttach = itemData.filePathList || []
          var resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
          for (var p = 0; p < resultBatchAttachLength; p++) {
            var attachpath = resultBatchAttach[p].fullUrl || ''
            var resultBatchAttachItem = {
              url: attachpath
            }
            item.nAttach.push(resultBatchAttachItem)
          }
          item.commentList = []
          var Commentlist = itemData.children || []
          var CommentlistLength = Commentlist ? Commentlist.length : 0
          for (var j = 0; j < CommentlistLength; j++) {
            var item2 = {}
            var itemData2 = Commentlist[j]
            item2.hasDelete = itemData2.createBy === data.user.id
            item.createBy = itemData2.createBy
            // id
            item2.id = itemData2.id || ''
            // id
            item2.extend = itemData2.extend || ''
            // 来源
            item2.url = itemData2.userHeadImg || ''
            // 用户头像
            item2.name = itemData2.userName || '匿名用户'
            // 用户名
            item2.createTime = itemData2.dateDetail || ''
            item2.content = itemData.content
            // 是否审核,0待审核，1审核通过，2审核不通过
            item2.nAttach = []
            // 评论中的图片
            const resultBatchAttach = itemData2.filePathList || []
            const resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
            for (var k = 0; k < resultBatchAttachLength; k++) {
              const attachpaths = resultBatchAttach[k].fullUrl || ''
              const resultBatchAttachItem = {
                url: attachpaths
              }
              item2.nAttach.push(resultBatchAttachItem)
            }
            item.commentList.push(item2)
          }
          data.commentList.push(item)
        })
      }
    }
    // 获取点赞列表
    const getFabulousList = async () => {
      const res = await $api.general.getFabulousList({
        pageNo: 1,
        pageSize: 100,
        keyId: data.id,
        type: '25',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      if (res) {
        var list = res ? res.data || [] : []
        var dataLength = list ? list.length : 0
        if (list) {
          data.menuList = []
          for (var i = 0; i < dataLength; i++) {
            var item = {}
            var itemData = list[i]
            item.id = itemData.createBy // id
            item.name = itemData.userName // 标题
            item.url = itemData.headImg // 图标地址
            data.menuList.push(item)
          }
        }
      }
      console.log('获取点赞列表===>>', res)
    }
    // 点击评论中的评论框
    const openMoreComment = (_item, _index) => {
      data.pingData = _item || ''
      data.pingIndex = _index || ''
      if (_item) {
        data.inputInfo = { value: '', placeholder: '回复' + _item.name, name: '评论', btn: '回复' }
      } else {
        data.inputInfo = { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送' }
      }
      // that.inputFocus = true
    }
    // 发送
    const mgcHandle = async () => {
      if (!data.inputInfo.value) {
        return
      }
      var url = 'comment/save'
      var params = {
        content: data.inputInfo.value,
        createBy: data.user.id,
        commentPid: data.pingData ? data.pingData.id : '0',
        keyId: data.id,
        attach: '',
        extend: '1',
        type: '25',
        areaId: '370200'
      }
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        data.commentList = []
        getCommentList()
        data.inputInfo.value = ''
      } else {
        Toast('请求失败。')
      }
    }

    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    const committeesayDel = (id) => {
      console.log(id)
      Dialog.confirm({
        title: '温馨提示',
        message: '确定删除吗'
      })
        .then(async () => {
          var { errcode } = await $api.news.committeesayDels({ ids: id })
          if (errcode === 200) {
            router.push({ path: '/newsMore', query: { type: 'resumption' } })
          }
          // on confirm
        })
        .catch(() => {
          // on cancel
        })
    }
    const downLike = (_item) => {
      if (data.ifIike == 1 || data.ifIike == true) { // eslint-disable-line
        data.fabulousCount--
        data.menuList = data.menuList.filter(item => item.id !== data.user.id)
        data.ifIike = 0
      } else {
        data.fabulousCount++
        data.menuList.push({ id: data.user.id, name: data.user.userName })
        data.ifIike = 1
      }
      fabulousInfo(data.ifIike, data.id)
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '25'
      }
      await $api.general.fabulous({ url, params })
      getDetails()
    }
    // 点关注
    const attentionEdit = () => {
      var type = ''
      if (data.isFollow === 1) {
        type = 'del'
        data.isFollow = 0
      } else {
        type = 'add'
        data.isFollow = 1
      }
      $api.news.attention({ params: { followId: data.publishBy, type: '25' }, type })
      getDetails()
    }

    const addCommentEvent = (value) => {
      data.commentOldAllList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      data.inputBox.getStats()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), download, $general, openMoreComment, committeesayDel, route, dayjs, previewCalback, attentionEdit, downLike, onRefresh, onClickLeft, play, addCommentEvent, openInputBoxEvent, freshState, annexClick, mgcHandle }
  }
}
</script>
<style lang="less">
.newsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;

  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }

  .n_details_survey {
    width: 100%;

    .n_details_survey_bg {
      width: 100%;
      height: 350px;
      background: #000;
      background-size: 100% 100% !important;
      overflow: hidden;
      position: relative;

      .n_details_survey_bgooo {
        overflow: hidden;
        width: 100%;
        height: 100%;
        background: #0000005b;
      }

      .n_details_survey_content {
        width: 100%;
        height: 30%;
        background: #fff;
        border-radius: 20px 20px 0 0;
        position: absolute;
        bottom: 0;
        padding: 10px;
        box-sizing: border-box;
      }

      .n_details_survey_top {
        width: 100%;
        height: 30px;
        color: #a8a8a8;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .n_details_survey_top_time {
          font-size: 14px;
        }

        .n_details_survey_top_text {
          font-size: 14px;
        }
      }

      .n_details_survey_title {
        width: 100%;
        height: 30px;
        font-size: 20px;
        font-weight: 700;
        color: #fff;
        margin: 40px 10px 0;
      }
    }
  }

  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }

  .n_details_content {
    margin: 10px;

    img {
      width: 100%;
    }

    >p {
      margin: 15px 0;
      font-size: 16px !important;
      line-height: 28px !important;

      span {
        font-size: 16px !important;
        line-height: 28px !important;
      }
    }
  }

  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }

  .n_details_more_box {
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }

  .n_details_more_box>.flex_box {
    width: 40%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    text-align: right !important;
    align-items: right;
  }

  .n_details_time {
    font-size: 12px;
    color: #666;
    width: 100%;
    margin-top: 10px;
  }

  .n_details_name {
    color: #666;
    width: 100%;
  }

  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }

  .n_details_time {
    color: #666;
    float: right;
  }

  .n_details_nextImg {}

  .representativeCircle_box_li {
    width: 100%;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e5e5;

    .representativeCircle_box_top {
      width: 100%;
      height: 35px;
      margin: 5px 0;
      display: flex;
      align-items: center;
      position: relative;

      .attention {
        text-align: center;
        position: absolute;
        top: 0;
        right: 10px;
        width: 80px;
        height: 80%;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40px;
        color: #3894ff;
        border: 1px solid #3894ff;
      }

      .attentionDel {
        color: #666;
        border: 1px solid #666;
      }

      .representativeCircle_box_top_headImg {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin: 5px;
      }

      .representativeCircle_box_name {
        font-size: 16px;

        .representativeCircle_box_congressStr {
          font-size: 14px;
          color: #4c4c4c;
        }
      }
    }

    .representativeCircle_box_center {
      box-sizing: border-box;

      .representativeCircle_box_center_content {
        padding-left: 13px;
        margin: 5px 0;
      }

      .representativeCircle_box_center_attachmentList {
        width: 95%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;

        // justify-content: space-between;
        .van-image {
          margin: 5px;
        }
      }
    }
  }
}

.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    padding-left: 10px;
    color: #a8a8a8;
  }

  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .representativeCircle_box_buttom_conmment {
      display: flex;
      align-items: center;
      justify-content: space-between;

      >span {
        font-size: 14px;
        margin-right: 4px;
      }

      >img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }

    .representativeCircle_box_buttom_link {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;
      padding-right: 10px;

      >span {
        margin-right: 10px;
        font-size: 14px;
      }

      >img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }

  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;

    img {
      margin: 15px 0;
      width: 100% !important;
    }
  }

  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
}

.likeComment_box {
  background: #f7f7f7;
  margin: 0 5px 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;

  .comment_box {
    margin: 0 5px 0px;
  }

  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }

  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;

    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;

      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }

      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }

      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}

.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>

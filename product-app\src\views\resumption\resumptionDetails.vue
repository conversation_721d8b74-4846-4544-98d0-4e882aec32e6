<template>
  <div class="newsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="play">
        <!-- <template #right>{{playText}}
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_content">
      <div class="representativeCircle_box_li">
        <div class="representativeCircle_box_top">
          <img :src="representativeDetails.headImg"
               alt=""
               class="representativeCircle_box_top_headImg">
          <div class="representativeCircle_box_name">
            <p class="representativeCircle_box_names">{{ representativeDetails.publishName }}</p>
            <p class="representativeCircle_box_congressStr"> {{ representativeDetails.congressStr+'人大代表' }}</p>
          </div>
          <template>
            <div v-if="user.id!=representativeDetails.publishBy"
                 :class="{'attention':true,'attentionDel':isFollow==1 }"
                 @click="attentionEdit">
              {{ isFollow==1?'已关注':'+ 关注' }}
            </div>
            <div class="representativeCircle_box_del"
                 @click="committeesayDel(representativeDetails.id)"
                 v-else>
              <van-icon name="delete-o" />
            </div>
          </template>
        </div>
        <div class="representativeCircle_box_center">
          <div class="representativeCircle_box_center_content"
               v-html="representativeDetails.content">
          </div>
          <div class="representativeCircle_box_center_attachmentList">
            <van-image position="contain"
                       width="2.5rem"
                       fit="cover"
                       height="2rem"
                       v-for="it in representativeDetails.attachmentList"
                       :key="it.id"
                       :src="it.filePath"
                       @click="previewCalback(representativeDetails.attachmentList)" />
          </div>
        </div>
        <div class="representativeCircle_box_buttom">
          <div class="representativeCircle_box_buttom_time">{{ representativeDetails.publishDate }}</div>
          <div class="representativeCircle_box_buttom_cont">
            <div class="representativeCircle_box_buttom_conmment">
              <span>{{ conmmentList.commentCount }}</span>
              <van-icon name="comment-o" />
            </div>
            <div class="representativeCircle_box_buttom_link"
                 @click="downLike(conmmentList)">
              <span>{{ conmmentList.fabulousCount }}</span>
              <van-icon name="good-job-o"
                        :style="conmmentList.isFabulous == '1'?'color:#3088fe':''" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--展示评论-->
  <commentList ref="commentList"
               @openInputBoxEvent="openInputBoxEvent"
               @freshState="freshState"
               :commentData="commentData"
               :type="type"
               :pageType="pageType"
               :id="id" />
  <div style="height:60px;"></div>
  <footer class="footerBox">
    <inputBox ref="inputBox"
              :inputData="inputData"
              @addCommentEvent="addCommentEvent"
              :pageType="pageType"
              :type="type"
              :id="id" />
  </footer>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, computed, watch } from 'vue'
import { NavBar, Sticky, ImagePreview, Image as VanImage, Dialog } from 'vant'
import { useStore } from 'vuex'
import inputBox from '../../components/inputBox/inputBox.vue'
import commentList from '../../components/commentList/commentList.vue'
export default {
  name: 'newsDetails',
  components: {
    inputBox,
    commentList,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const store = useStore()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      isFollow: route.query.isFollow || '',
      publishBy: route.query.publishBy || '',
      pageType: route.query.type || '',
      userName: route.query.userName || '',
      contents: route.query.content || '',
      createDate: route.query.createDate || '',
      details: {},
      type: 25,
      refreshing: false,
      show: false,
      browerCount: { show: false, value: '0', hint: '阅读：' }, // 阅读数
      shareCount: { show: false, value: '0' }, // 分享数
      commentCount: { show: false, value: '0' }, // 评论数
      dataTime: { show: false, value: '' }, // 时间
      IBSName: { show: false, value: '' }, // 资讯类型
      source: '', // 来源
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] }, // 附件对象
      playText: '播放',
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      commentList: null,
      inputBox: null,
      commentObj: {},
      representativeDetails: {},
      politicalData: {},
      twoInstitutesData: {},
      communityData: {},
      representativeData: {},
      countrysideData: {},
      numberAppData: {},
      surveyComment: [], // 意见征集评论列表
      surveyDetails: {}, // 意见征集详情
      attentionStatus: false,
      conmmentList: []
    })
    const getShow = computed(() => {
      // 返回的是ref对象
      return store.state.speechShow
    })
    const getStatus = computed(() => {
      // 返回的是ref对象
      return store.state.speechStauts
    })
    watch(getShow, (newName, oldName) => {
      data.show = newName
      if (!data.show) {
        data.playText = '播放'
      }
    })
    watch(getStatus, (newName, oldName) => {
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '继续'
        } else {
          data.playText = '暂停'
        }
      }
    })
    const play = () => {
      if (data.playText === '播放') {
        data.playText = '暂停'
        store.commit('setSpeechShow', true)
      } else {
        store.commit('setStatus', !store.state.speechStauts)
      }
    }
    onMounted(() => {
      browseSave()
      newsInfo()
      data.inputBox.showComment = false
      data.inputBox.showLike = false
      console.log(route)
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '暂停'
        } else {
          data.playText = '继续'
        }
      }
    })
    const onRefresh = () => {
      newsInfo()
    }
    const browseSave = async () => {
      await $api.general.saveBrowse({
        keyId: data.id,
        type: data.type
      })
    }
    const getDetails = async () => {
      const res = await $api.news.getRepresentativeDetails({
        id: data.id
      })
      var { data: list } = await $api.general.getCommentStats({
        keyId: data.id,
        type: data.type,
        areaId: data.user.areaId
      })
      data.conmmentList = list
      console.log(data.conmmentList)
      data.representativeDetails = res.data
      // console.log(res.data)
      // data.commentObj = ret.data
    }
    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    const committeesayDel = (id) => {
      console.log(id)
      Dialog.confirm({
        title: '温馨提示',
        message: '确定删除吗'
      })
        .then(async () => {
          var { errcode } = await $api.news.committeesayDels({ ids: id })
          if (errcode === 200) {
            router.push({ path: '/newsMore', query: { type: 'resumption' } })
          }
          // on confirm
        })
        .catch(() => {
          // on cancel
        })
    }
    const downLike = (_item) => {
      fabulousInfo(_item.isFabulous, data.id)
    }
    const newsInfo = async () => {
      getDetails()
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status === '0' ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '25'
      }
      await $api.general.fabulous({ url, params })
      getDetails()
    }
    // 点关注
    const attentionEdit = () => {
      var type = ''
      if (data.isFollow === 1) {
        type = 'del'
        data.isFollow = 0
      } else {
        type = 'add'
        data.isFollow = 1
      }
      $api.news.attention({ params: { followId: data.publishBy, type: '25' }, type })
      getDetails()
    }
    const addCommentEvent = (value) => {
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      data.inputBox.getStats()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), committeesayDel, route, dayjs, previewCalback, attentionEdit, downLike, onRefresh, onClickLeft, play, addCommentEvent, openInputBoxEvent, freshState, annexClick }
  }
}
</script>
<style lang="less">
.newsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }
  .n_details_survey {
    width: 100%;
    .n_details_survey_bg {
      width: 100%;
      height: 350px;
      background: #000;
      background-size: 100% 100% !important;
      overflow: hidden;
      position: relative;
      .n_details_survey_bgooo {
        overflow: hidden;
        width: 100%;
        height: 100%;
        background: #0000005b;
      }
      .n_details_survey_content {
        width: 100%;
        height: 30%;
        background: #fff;
        border-radius: 20px 20px 0 0;
        position: absolute;
        bottom: 0;
        padding: 10px;
        box-sizing: border-box;
      }
      .n_details_survey_top {
        width: 100%;
        height: 30px;
        color: #a8a8a8;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .n_details_survey_top_time {
          font-size: 14px;
        }
        .n_details_survey_top_text {
          font-size: 14px;
        }
      }
      .n_details_survey_title {
        width: 100%;
        height: 30px;
        font-size: 20px;
        font-weight: 700;
        color: #fff;
        margin: 40px 10px 0;
      }
    }
  }
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }
  .n_details_content {
    img {
      width: 100%;
    }
    > p {
      margin: 15px 0;
      font-size: 16px !important;
      line-height: 28px !important;
      span {
        font-size: 16px !important;
        line-height: 28px !important;
      }
    }
  }
  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }
  .n_details_more_box > .flex_box {
    width: 40%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    text-align: right !important;
    align-items: right;
  }
  .n_details_time {
    font-size: 12px;
    color: #666;
    width: 100%;
    margin-top: 10px;
  }
  .n_details_name {
    color: #666;
    width: 100%;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .n_details_nextImg {
  }
  .representativeCircle_box_li {
    width: 100%;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e5e5;
    .representativeCircle_box_top {
      width: 100%;
      height: 35px;
      margin: 5px 0;
      display: flex;
      align-items: center;
      position: relative;
      .attention {
        text-align: center;
        position: absolute;
        top: 0;
        right: 10px;
        width: 80px;
        height: 80%;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40px;
        color: #3894ff;
        border: 1px solid #3894ff;
      }
      .attentionDel {
        color: #666;
        border: 1px solid #666;
      }
      .representativeCircle_box_top_headImg {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin: 5px;
      }
      .representativeCircle_box_name {
        font-size: 16px;
        .representativeCircle_box_congressStr {
          font-size: 14px;
          color: #4c4c4c;
        }
      }
    }
    .representativeCircle_box_center {
      box-sizing: border-box;
      .representativeCircle_box_center_content {
        padding-left: 13px;
        margin: 5px 0;
      }
      .representativeCircle_box_center_attachmentList {
        width: 95%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        .van-image {
          margin: 5px;
        }
      }
    }
  }
}
.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    padding-left: 10px;
    color: #a8a8a8;
  }
  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .representativeCircle_box_buttom_conmment {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > span {
        font-size: 14px;
        margin-right: 10px;
      }
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
    .representativeCircle_box_buttom_link {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;
      padding-right: 10px;
      > span {
        margin-right: 10px;
        font-size: 14px;
      }
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
    img {
      margin: 15px 0;
      width: 100% !important;
    }
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
}
.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>

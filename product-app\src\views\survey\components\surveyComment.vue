<template>
  <div class="survey_comment">
    <div class="survey_comment_title">
      <div class="survey_comment_bg"></div>
      <div class="survey_comment_fw">
        请您建言
      </div>
    </div>
    <div class="survey_comment_list"
         v-for="item in surveyComment"
         :key="item.id"
         @click="skipDetails('surveyComment', item)">
      <div class="survey_comment_list_top">
        <div class="survey_comment_list_top_img">
          <img :src="item.headImg"
               alt="">
        </div>
        <div class="survey_comment_list_top_cen">
          <div class="survey_comment_list_top_name">{{ item.name }}</div>
          <div class="survey_comment_list_top_time">
            <div class="survey_comment_list_top_time_left">{{ item.createDate }}</div>
            <div class="survey_comment_list_top_time_right">
              <div :class="{survey_comment_list_top_time_right_like:true}"
                   @click.stop="downLike(item)">
                <van-icon name="good-job-o"
                          :class="{bg:item.fabulousNumber>0}" />
                {{ item.fabulousNumber }}
              </div>
              <div class="survey_comment_list_top_time_right_commentNumber">
                <van-icon name="comment-o"
                          @click.stop="commtentClick(item)" />
                {{ item.commentNumber }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="survey_comment_list_but">
        <div class="survey_comment_list_but_title">
          {{ item.title }}
        </div>
        <div class="survey_comment_list_but_detail">
          {{ item.detail }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
export default ({
  name: 'schedule',
  props: ['id'],
  components: {},
  setup (props, { emit }) {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      id: '',
      inputData: {
        input_placeholder: '我要留言'
      },
      inputBox: null,
      surveyComment: []
    })
    onMounted(() => {
      data.id = props.id
      getList(data.id)
    })
    const getList = async (_id) => {
      const res = await $api.news.getAurveyComment({ surveyId: _id, pageNo: '1', pageSize: '199', auditSituation: '' })
      data.surveyComment = res.data
    }
    const skipDetails = (type, { name, detail, createDate, id, title }) => {
      router.push({ name: 'surveyComentInfo', query: { id, type, userName: name, content: detail, createDate, title } })
    }
    // 点击点赞
    const downLike = (_item) => {
      _item.isFabulous = !_item.isFabulous
      if (_item.isFabulous) {
        _item.fabulousNumber++
      } else {
        _item.fabulousNumber--
      }
      fabulousInfo(_item.isFabulous, _item.id)
    }

    const fabulousInfo = async (_status, _id) => {
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '60'
      }
      await $api.general.fabulous({ url, params })
      emit('fabulousInfo')
    }

    return { ...toRefs(data), skipDetails, dayjs, route, router, $api, downLike, props, commtentClick }
  }
})
</script>
<style lang='less' scoped>
.survey_comment {
  width: 100%;
  background: #fff;
  margin-bottom: 80px;
  .survey_comment_title {
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
  }
  .survey_comment_fw {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-weight: 700;
  }
  .survey_comment_bg {
    width: 5px;
    height: 16px;
    background: #3894ff;
    margin: 0 10px;
  }
  .survey_comment_list {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border-bottom: 1px solid #e8e8e8;
    .survey_comment_list_but {
      box-sizing: border-box;
      width: 100%;
      .survey_comment_list_but_title {
        font-weight: 700;
        margin: 10px 0;
        font-size: 18px;
      }
      .survey_comment_list_but_detail {
        font-size: 14px;
      }
    }
    .survey_comment_list_top {
      display: flex;
      align-items: center;
      width: 100%;
      height: 50px;
      .survey_comment_list_top_img {
        width: 40px;
        height: 90%;
        border-radius: 3px;
        overflow: hidden;
        margin: 0 5px;
        > img {
          width: 100%;
          height: 100%;
        }
      }
      .survey_comment_list_top_cen {
        height: 100%;
        width: calc(100% - 30px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .survey_comment_list_top_name {
        }
        .survey_comment_list_top_time {
          width: 100%;
          color: #c2c2c2;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .survey_comment_list_top_time_left {
            font-size: 14px;
          }
          .survey_comment_list_top_time_right {
            width: 20%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0 10px;
            .survey_comment_list_top_time_right_like {
              font-size: 14px;
              .bg {
                color: #3894ff;
              }
            }
            .survey_comment_list_top_time_right_commentNumber {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style>

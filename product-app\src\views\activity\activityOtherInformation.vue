<template>
  <div class="activityOtherInformationDetails">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">

      <!--搜索-->
      <van-pull-refresh v-model="refreshing"
                        @refresh="onRefresh">
        <van-list v-model:loading="loading"
                  :finished="finished"
                  finished-text="没有更多了"
                  offset="52"
                  @load="onLoad">
          <div v-if="showSearch"
               id="search"
               class="search_box"
               :style="$general.loadConfiguration()">
            <div class="search_warp flex_box">
              <div @click="search();"
                   class="search_btn flex_box flex_align_center flex_justify_content">
                <van-icon :size="((appFontSize)*0.01)+'px'"
                          :color="'#666'"
                          :name="'search'"></van-icon>
              </div>
              <form class="flex_placeholder flex_box flex_align_center search_input"
                    action="javascript:return true;"> <input id="searchInput"
                       class="flex_placeholder"
                       :style="$general.loadConfiguration(-1)"
                       :placeholder="seachPlaceholder"
                       maxlength="100"
                       type="text"
                       ref="btnSearch"
                       @keyup.enter="search()"
                       v-model="seachText" />
                <div v-if="seachText"
                     @click="seachText='';search();"
                     class="search_btn flex_box flex_align_center flex_justify_content">
                  <van-icon :size="((appFontSize)*0.01)+'px'"
                            :color="'#999'"
                            :name="'clear'"></van-icon>
                </div>
              </form>
            </div>
          </div>
          <!--数据列表-->
          <ul class="vue_newslist_box">
            <van-swipe-cell v-for="(item,index) in dataList"
                            :key="index"
                            class="van-hairline--bottom">
              <van-cell clickable
                        class="vue_newslist_item "
                        @click="openDetails(item)">
                <div class="flex_box">
                  <img class="vue_newslist_img"
                       v-if="item.url"
                       :src="item.url" />
                  <div class="flex_placeholder vue_newslist_warp">
                    <div class="vue_newslist_title text_two"
                         :style="$general.loadConfiguration(1)">
                      <span v-if="item.isTop == '1'"
                            class="vue_newslist_top"
                            :style="$general.loadConfiguration(-4)">
                        <van-tag plain
                                 :color="appTheme">置顶</van-tag>
                      </span>
                      {{item.title}}
                    </div>
                    <div v-if="item.url"
                         class="vue_newslist_time"
                         :style="$general.loadConfiguration(-3) + 'margin-bottom:9px;'">{{item.time.split(' ')[0]}}</div>
                    <div v-else
                         class="vue_newslist_summary text_one2"
                         :style="$general.loadConfiguration(-3)"
                         v-html="item.content.replace(/<[^>]+>/g, '').replace(/\s*/g,'')"></div>
                    <div class="flex_box flex_align_center">
                      <div v-if="!item.url"
                           class="vue_newslist_time"
                           :style="$general.loadConfiguration(-3)">{{item.time.split(' ')[0]}}</div>
                      <div class="vue_newslist_source"
                           :style="$general.loadConfiguration(-3)">{{item.source || item.createBy}}</div>
                      <div class="flex_placeholder"></div>
                    </div>
                  </div>
                </div>
              </van-cell>
            </van-swipe-cell>
          </ul>

          <!--加载中提示 首次为骨架屏-->
          <div v-if="showSkeleton"
               class="notText">
            <van-skeleton v-for="(item,index) in 3"
                          :key="index"
                          title
                          :row="3"></van-skeleton>
          </div>
          <template v-else-if="dataList.length == 0">
            <van-empty :style="$general.loadConfiguration(-2)">
              <!-- <template #description>
                    <div class="van-empty__description_text"
                         :style="$general.loadConfiguration(-1)"
                         v-html="'暂无数据'"></div>
                  </template> -->
            </van-empty>
          </template>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog } from 'vant'
export default {
  name: 'activityOtherInformationDetails',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id || '',
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      onRefresh()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const search = () => {
      onRefresh()
    }
    // 列表请求
    const getList = async () => {
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      const param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        meetId: data.id,
        isAppShow: '1'
      }
      var res = {}
      if (data.relateType === 'activityMateria') {
        res = await $api.activity.getMaterialList(param)
      } else if (data.relateType === 'activitySchedule') {
        res = await $api.activity.getScheduleList(param)
      } else if (data.relateType === 'activityReport') {
        res = await $api.activity.getReportList(param)
      }
      var { data: list, total } = res
      const newData = []
      list.forEach(item => {
        const itemData = item
        item.id = itemData.id || ''// id
        item.title = itemData.title || itemData.materialName || ''// 标题
        var photo = itemData.photo || []
        // eslint-disable-next-line eqeqeq
        item.url = photo.length != 0 ? photo[0].filePath : ''
        item.content = itemData.content || ''//
        item.source = itemData.createName || ''// 部门
        item.time = itemData.dateTime || itemData.createDate || ''// 时间
        item.type = data.title || ''// 标签
        item.isTop = itemData.isTop || '0'// 置顶 1是0否
        item.relateType = data.relateType
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }

    const listBtnClick = (_nItem, _item) => {
      if (!_nItem.isClick) return
      switch (_nItem.click) {
        case 'details':
          openDetails(_item)
          break
      }
    }
    const openDetails = rows => {
      router.push({ path: 'activityOtherInformationDetails', query: { id: rows.id, title: rows.title, relateType: data.relateType } })
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, search, openDetails, listBtnClick }
  }
}
</script>
<style lang="less" scoped>
.activityOtherInformationDetails {
  background: #fff;
  .search-dropdown-menu {
    margin-right: 5px;
    padding: 10px 0;
  }
  .van-dropdown-menu.van-hairline--top-bottom::after {
    border-width: 0 0;
  }
}
</style>

<template>
  <div class="screening">
    <div class="screeningName">{{title}}</div>
    <div class="screeningBox">
      <div class="screeningItem"
           :key="item[props.id]"
           v-for="item in data"
           @click="dataClick(item)"
           :class="{screeningItemA : modelValue == item[props.id]}">
        {{item[props.label]}}<slot></slot>
      </div>
      <div v-if="more"
           class="screeningItem"
           @click="moreClick">更早</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'screening',
  props: {
    title: {
      type: String,
      default: ''
    },
    modelValue: [String, Number, Array, Object],
    data: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          id: 'id'
        }
      }
    },
    // 是否再次点击取消选中
    cancel: {
      type: Boolean,
      default: false
    },
    // 更多
    more: {
      type: <PERSON>olean,
      default: false
    }
  },
  setup (props, { emit }) {
    const dataClick = (row) => {
      if (props.cancel && props.modelValue === row[props.props.id]) {
        emit('update:modelValue', '')
        return
      }
      emit('update:modelValue', row[props.props.id])
    }
    const moreClick = (row) => {
      emit('more-click')
    }
    return { dataClick, moreClick }
  }
}
</script>
<style lang="less">
.screening {
  width: 100%;
  padding-top: 6px;
  .screeningName {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
    padding: 0 16px;
  }
  .screeningBox {
    display: flex;
    flex-wrap: wrap;
    padding: 5px 11px;
    .screeningItem {
      width: 76px;
      height: 25px;
      background: #f6f6f6;
      border-radius: 2px;
      margin: 5px;
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 25px;
      color: #666666;
      text-align: center;
    }
    .screeningItemA {
      color: #BC1D1D;
      background-color: rgba(188,29,29, 0.1);
    }
  }
}
</style>

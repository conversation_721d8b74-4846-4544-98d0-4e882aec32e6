<template>
  <div class="ConferenceAffairsManagement">
    <van-dialog v-if="qrcode.show"
                v-model="qrcode.show"
                title="签到二维码"
                :style="general.loadConfiguration()"
                show-cancel-button
                :confirm-button-color="appTheme"
                confirm-button-text="保存到相册"
                @confirm="btnSave()">
      <div class="qrcode_img">
        <van-image fit="cover"
                   :src="qrcode.src"></van-image>
      </div>
    </van-dialog>
    <!--子会筛选-->
    <van-dropdown-menu v-if="allMeeting"
                       class="van-hairline--bottom"
                       :active-color="appTheme"
                       :style="general.loadConfiguration(-1)">
      <van-dropdown-item v-model="allMeetingChild.value"
                         @change="allMeetingChange"
                         :options="allMeetingChild.data"></van-dropdown-item>
    </van-dropdown-menu>
    <div class="manage_btn_box flex_box T-flex-flow-row-wrap">
      <van-cell v-for="(item,index) in listData"
                :key="index"
                clickable
                class=""
                @click="openDetails(item)">
        <div class="flex_box flex_align_center">
          <van-icon :size="((appFontSize+2)*0.01)+'rem'"
                    color="#777"
                    :name="item.icon"></van-icon>
          <div :style="general.loadConfiguration(-1)+'color: #333;margin-left:0.1rem;flex-shrink:0;'">{{item.name}}
          </div>
          <div class="flex_placeholder"></div>
          <div v-if="item.value"
               :style="general.loadConfiguration(-2)+'color: #666;margin-right:0.1rem;'">
            {{item.value}}</div>
          <van-icon v-if="item.click"
                    :size="((appFontSize+2)*0.01)+'rem'"
                    color="#aaa"
                    name="arrow">
          </van-icon>
        </div>
      </van-cell>
    </div>
    <!--反馈情况统计图表-->
    <div v-if="signUpStatisticsHas"
         class="item_warp">
      <div class="statistics_box flex_box flex_align_center flex_justify_content">
        <!-- <v-chart style="width:3.32rem;height:2.16rem;"
                 :options="signUpStatistics"></v-chart> -->

        <div class="Chart"
             ref="myEchartLine"
             :style="{ height: '420px' }"></div>

      </div>
    </div>
    <!--接收统计图表-->
    <div v-if="attendanceStatisticsHas"
         class="item_warp">
      <div class="statistics_box flex_box flex_align_center flex_justify_content">
        <!-- <v-chart ref="attendanceStatistics"
                 style="width:3.32rem;height:2.16rem;"
                 :options="attendanceStatistics"></v-chart> -->

        <div class="Chart"
             ref="myEchartLines"
             :style="{ height: '330px' }"></div>

      </div>
    </div>
    <!--不为一级页面时 适配底部条-->
    <footer v-if="pageType=='page'"
            :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer>
  </div>

</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, ref, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
import * as echartst from 'echarts'
export default {
  name: 'ConferenceAffairsManagement',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const myEchartLine = ref(null)
    const myEchartLines = ref(null)
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      myParam: JSON.parse(route.query.myParam) || {},

      scrollTop: 0, // 页面划动距离
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      listData: [
        { name: '考勤管理', icon: 'points', click: 'attendance' },
        { name: '会议材料', icon: 'notes-o', click: 'matera' },
        { name: '签到二维码', icon: 'qr', click: 'signQR' },
        { key: 'signInCommand', name: '签到口令', icon: 'orders-o', value: '2345' },
        { name: '扫码签到', icon: 'scan', value: '扫描他人二维码帮助签到', click: 'scanSign' }
      ], // 列表数据
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部 btn为按钮

      qrcode: { show: false, src: '' }, // 展示二维码

      signUpStatisticsHas: true,
      signUpStatistics: { // 报名数据统计
        title: { text: '出勤率统计', textStyle: { fontSize: 16, color: '#333' }, padding: [13, 14] },
        // tooltip: {trigger: 'item',formatter: "{b}: {c} ({d}%)"},// {b}:数据名； {c}：数据值； {d}：百分比，可以自定义显示内容，
        legend: {
          top: '20%',
          orient: 'vertical',
          left: '3%',
          data: []
        },
        color: ['#FFC649', '#8E49FF', '#0271E3', '#F8B287', '#51E8CD', '#E34C4C', '#F9EA3E'],
        series: [{
          type: 'pie',
          radius: ['35%', '55%'],
          center: ['70%', '50%'],
          avoidLabelOverlap: false,
          label: {
            normal: { show: false, position: 'center', formatter: '{b}\n{c} ({d}%)' },
            emphasis: { show: true, textStyle: { fontSize: '13', color: '#6670AB' } } // 文字至于中间时，这里需为true
          },
          data: []
        }]
      },

      attendanceStatisticsHas: true,
      attendanceStatistics: { // 出勤率统计
        title: { text: '考勤数据统计', textStyle: { fontSize: 16, color: '#333' }, padding: [13, 14] },
        color: ['#007BFF'], // 线条颜色
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: { show: true, textStyle: { color: '#666666', fontSize: 11 } },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLabel: { show: true, textStyle: { color: '#666666', fontSize: 11 } },
          axisTick: { show: false }
        },
        series: [{
          type: 'bar',
          name: '人数',
          label: { normal: { show: true, position: 'top' } },
          barWidth: 24, // 柱图宽度
          data: []
        }]
      },
      allMeeting: false, // 是否全会管理
      allMeetingChild: { value: '1', data: [{ text: '子会', value: '1' }] },
      meetLeavePercentage: [],
      conferenceAllList: []

    })
    onMounted(() => {
      console.log(JSON.parse(route.query.myParam))
      setTimeout(() => {
        getConferenceGetAttendanceNum()
        GetConferenceGetSignUpNum()
      }, 500)

      const barChart = echartst.init(myEchartLine.value)
      const barCharts = echartst.init(myEchartLines.value)
      barChart.setOption(barChartOptions())
      barCharts.setOption(barChartst())
      window.onresize = function () {
        barChart.resize()
        barCharts.resize()
      }
      if (data.title) {
        document.title = data.title
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const onRefresh = () => {
    }
    const onLoad = () => {

    }

    const onClickLeft = () => history.back()

    // 报名数据统计
    const getConferenceGetAttendanceNum = async () => {
      var res = []
      var datas = {
        conferenceId: data.myParam.meetId || ''
      }
      res = await $api.activity.conferenceGetAttendanceNum(datas)
      var { data: list } = res
      console.log('🚀 ~ file: ConferenceAffairsManagement.vue ~ line 208 ~ getConferenceGetAttendanceNum ~ list', list)
      data.meetLeavePercentage = list
      const barChart = echartst.init(myEchartLine.value)
      barChart.setOption(barChartOptions(list.meetLeavePercentage))
    }

    const barChartOptions = (item) => {
      return {
        // tooltip: {
        //   trigger: 'item',
        //   formatter: '{a} <br/>{b} : {c}人 <br/>出勤率:({d}%)'
        // },
        // legend: {
        //   top: 'bottom',
        //   left: 'center'
        // },
        // series: [{
        //   name: '统计',
        //   type: 'pie',
        //   radius: ['10%', '40%'],
        //   left: 'center',
        //   width: 350,
        //   height: 300,
        //   itemStyle: {
        //     borderColor: '#fff',
        //     borderWidth: 1
        //   },
        //   label: {
        //     alignTo: 'edge',
        //     formatter: '{name|{b}}\n{time|{c}}人',
        //     minMargin: 10,
        //     edgeDistance: 16,
        //     lineHeight: 25,
        //     rich: {
        //       time: {
        //         fontSize: 16,
        //         color: '#999'
        //       }
        //     }
        //   },
        //   data: item
        // }]
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} <br/>出勤率:({d}%)'
        },
        legend: {
          top: 'bottom',
          left: 'center'
        },
        series: [
          {
            name: '',
            type: 'pie',
            label: {
              normal: {
                show: false
              }
            },
            radius: ['30%', '60%'],
            data: item
          }
        ]
      }
    }

    const barChartst = (num, name) => {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          left: '2%',
          right: '4%',
          bottom: '14%',
          top: '16%',
          containLabel: true
        },
        legend: {
        },
        xAxis: {
          type: 'category',
          data: name,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },

        yAxis: {
          type: 'value'
          // axisLine: {
          //   show: false,
          //   lineStyle: {
          //     color: 'white'
          //   }
          // },
          // splitLine: {
          //   show: false,
          //   lineStyle: {
          //     color: 'white'
          //   }
          // }
        },
        series: {
          name: '',
          type: 'bar',
          barWidth: '8%',
          itemStyle: {
            normal: {
              color: new echartst.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: '#248ff7'
              }]),
              barBorderRadius: 11
            }
          },
          data: num,
          markPoint: {
            data: [
              { type: 'max', name: 'Max' },
              { type: 'min', name: 'Min' }
            ]
          }
        }
      }
    }
    // 出勤率统计
    const GetConferenceGetSignUpNum = async () => {
      var res = []
      var datas = {
        conferenceId: data.myParam.meetId || ''
      }
      res = await $api.activity.conferenceGetSignUpNum(datas)
      var { data: list } = res
      console.log('🚀 ~ file: ConferenceAffairsManagement.vue ~ line 218 ~ GetConferenceGetSignUpNum ~ list', list)
      var arrNum = []
      var arrName = []
      list.map(item => {
        arrNum.push(item.count)
        arrName.push(item.state)
      })

      const barCharts = echartst.init(myEchartLines.value)
      barCharts.setOption(barChartst(arrNum, arrName))
    }

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, general, confirm, getConferenceGetAttendanceNum, GetConferenceGetSignUpNum }
  }
}
</script>
<style lang="less" scoped>
.ConferenceAffairsManagement {
  background: #f9f9f9;

  .manage_btn_box {
    border-bottom: 0.1rem solid #f4f4f4;
    background: #fff;
  }

  .van-cell {
    padding: 0.16rem 0.16rem;
  }

  .qrcode_img {
    width: 2rem;
    height: 2rem;
    margin: auto;
    padding: 0.1rem;
  }

  .item_warp {
    padding: 0.14rem 0.14rem 0 0.14rem;
    background: #fff;
  }

  .van-dropdown-menu__bar {
    background: #ffffff;
    padding: 0.14rem;
  }

  .van-dropdown-menu .van-dropdown-menu__title {
    width: 100%;
  }

  .van-dropdown-menu__title::after {
    right: 0.04rem;
    border: 0.04rem solid;
    border-color: transparent transparent #323232 #323232;
    margin-top: -0.08rem;
  }

  .van-dropdown-menu__title--active::after {
    margin-top: -0.04rem;
  }

  .van-dropdown-menu .van-ellipsis {
    padding-right: 0.1rem;
  }
}
</style>

<template>
  <div class="speechContent"
       v-if="show">
    <div class="speech-box flex_box"
         :style="'background:'+appTheme">
      <div class="speech-text"
           @click="playSpeech">
        <van-icon :name="status?'play-circle-o':'pause-circle-o'" />
      </div>
      <div class="flex_placeholder"></div>
      <div class="speech-close"
           @click="closeBtn">
        <van-icon name="cross" />
      </div>
    </div>
  </div>
</template>
<script>
import { onMounted, onUnmounted, reactive, toRefs, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { Icon } from 'vant'
export default {
  name: 'speechContent',
  components: {
    [Icon.name]: Icon
  },
  emit: ['play'],
  setup (props) {
    const store = useStore()
    const data = reactive({
      show: store.state.speechShow,
      status: store.state.speechStatus,
      content: store.state.content,
      appTheme: '#3A77FF',
      playText: '播放'
    })
    const getShow = computed(() => {
      // 返回的是ref对象
      return store.state.speechShow
    })
    const getStatus = computed(() => {
      // 返回的是ref对象
      return store.state.speechStauts
    })
    const getContent = computed(() => {
      // 返回的是ref对象
      return store.state.speechContent
    })
    watch(getShow, (newName, oldName) => {
      data.show = newName
      console.log('显示播放组件', data.show)
      if (data.show) {
        handleSpeak(data.content)
        data.status = false
        data.playText = '暂停'
      }
    })
    watch(getStatus, (newName, oldName) => {
      data.status = newName
      if (data.show) {
        if (!store.state.speechStauts) {
          handleResume()
          data.playText = '继续'
          console.log('继续播放')
        } else {
          handlePause()
          data.playText = '暂停'
          console.log('暂停播放')
        }
      }
    })
    watch(getContent, (newName, oldName) => {
      data.content = newName
    })

    onMounted(() => {
    })

    // const synth = window.speechSynthesis
    // const msg = new SpeechSynthesisUtterance()
    const playSpeech = () => {
      store.commit('setStatus', !store.state.speechStauts)
    }
    // SpeechSynthesis.cancel()// 移除所有语⾳谈话队列中的谈话。
    // SpeechSynthesis.getVoices()// 返回当前设备所有可⽤声⾳的 SpeechSynthesisVoice列表。
    // SpeechSynthesis.pause()// 把 SpeechSynthesis 对象置为暂停状态。
    // SpeechSynthesis.resume()// 把 SpeechSynthesis 对象置为⼀个⾮暂停状态：如果已经暂停了则继续。
    // SpeechSynthesis.speak()// 添加⼀个utterance到语⾳谈话队列；它将会在其他语⾳谈话播放完之后播放。
    // SpeechSynthesisUtterance.lang：设置话语的语⾔。 例如：“zh-cn”表⽰中⽂
    // SpeechSynthesisUtterance.pitch：设置说话的⾳调(⾳⾼)。范围从0（最⼩）到2（最⼤）。默认值为1
    // SpeechSynthesisUtterance.rate：设置说话的速度。默认值是1，范围是0.1到10，表⽰语速的倍数，例如2表⽰正常语速的两倍
    // SpeechSynthesisUtterance.text：设置在说话时将合成的⽂本内容。
    // SpeechSynthesisUtterance.voice：设置⽤于说话的声⾳。
    // SpeechSynthesisUtterance.volume：设置将在其中发⾔的⾳量。区间范围是0到1，默认是1
    const handleSpeak = (text) => {
      // msg.text = text // ⽂字内容: ⼩朋友，你是否有很多问号
      // msg.lang = 'zh-CN' // 使⽤的语⾔:中⽂
      // msg.volume = 1 // 声⾳⾳量：1
      // msg.rate = 1 // 语速：1
      // msg.pitch = 1 // ⾳⾼：1
      // msg.addEventListener('end', function () {
      //   console.log('播放结束')
      //   closeBtn()
      // })
      // synth.speak(msg) // 播放
    }

    // 语⾳继续
    const handleResume = (e) => {
      // msg.text = e
      // msg.lang = 'zh-CN'
      // synth.resume(msg)
    }
    // 语⾳暂停
    const handlePause = (e) => {
      // msg.text = e
      // msg.lang = 'zh-CN'
      // synth.pause(msg)
    }

    // 语⾳停⽌
    const handleStop = (e) => {
      // msg.text = e
      // msg.lang = 'zh-CN'
      // synth.cancel(msg)
    }
    onUnmounted(() => {
      handleStop()
    })
    const closeBtn = () => {
      handleStop()
      store.commit('setStatus', false)
      setTimeout(() => {
        store.commit('setSpeechShow', false)
      }, 100)
    }
    return { ...toRefs(data), ...toRefs(props), closeBtn, playSpeech }
  }
}
</script>
<style lang="less" scoped>
.speechContent {
  width: 100%;
  position: fixed;
  bottom: 30px;
  left: 30px;
  z-index: 9999;
  .speech-box {
    border-radius: 5px;
    width: 70px;
    height: 30px;
    line-height: 30px;
    color: #fff;
  }
  .speech-text {
    margin-left: 10px;
  }
  .speech-close {
    width: 25px;
  }
}
</style>

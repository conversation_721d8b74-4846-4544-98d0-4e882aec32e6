<template>
  <div class="activityDetails">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="$general.loadConfiguration(5)+'font-weight:bold;line-height:1.5;'"
           v-html="title"></div>
    </div>
    <div v-if="otherCon.length != 0"
         class="n_details_other">
      <div class="state"
           v-if="state"
           :style="$general.loadConfiguration(-6)">
        <van-tag round
                 :color="$general.getColorStatus(state,'color')"
                 style="padding:2px 10px;">{{state}}</van-tag>
      </div>
      <div v-for="(item,index) in otherCon"
           :key="index"
           class="flex_box">
        <div :style="$general.loadConfiguration(1)+'font-weight:500;'"
             v-html="item.label"></div>
        <div class="flex_placeholder flex_box">
          <span :style="$general.loadConfiguration()"
                v-html="item.value"></span>
          <span v-if="item.click"
                :style="$general.loadConfiguration(-3)"
                @click="footerBtnClick(item)">
            <!--<van-tag :color="appTheme">{{'详情'}}</van-tag>-->
            <img :style="$general.loadConfigurationSize(-2,'h')"
                 style="margin-top:3px;margin-left:10px;"
                 :src="SYS_IF_ZX?require('../../assets/img/icon_time_zx.png'):require('../../assets/img/icon_time_rd.png')" />
          </span>
        </div>
      </div>
    </div>
    <div class="n_details_content"
         :style="$general.loadConfiguration()"
         v-html="content"></div>
    <template v-if="isShowZiliaoBtn">
      <div class="flex_box flex_align_center">
        <div class="readTitleImg"
             style="visibility:hidden;"></div>
        <div :style="$general.loadConfiguration(-2)"
             class="readTitle">相关资料<span :style="$general.loadConfiguration(-6)"
                style="color: #CCCCCC;margin-left:10px;">点击查看</span></div>
      </div>
      <div class="flex_box userBox T-flex-flow-row-wrap text_two">
        <div :style="$general.loadConfiguration(-4)"
             class="btn_box flex_box flex_align_center flex_justify_content"
             v-for="(item,index) in ziliaoBtns"
             :key="index"
             @click="footerBtnClick(item)">{{item.name}}</div>
      </div>
    </template>
    <!--已读人-->
    <template v-if="participant.length != 0">
      <div style="width:100%;height:20px;background: #f8f8f8;margin-bottom:20px;"></div>
      <div style="width:calc(100% - 32px);margin-left:16px;">
        <div class="flex_box flex_align_center">
          <div class="readTitleImg"
               :style="$general.loadConfigurationSize(0,'h') + 'background:'+appTheme"></div>
          <div :style="$general.loadConfiguration(-2)"
               class="readTitle">{{'参与人员'}}</div>
        </div>
        <div class="flex_box userBox T-flex-flow-row-wrap text_two">
          <font v-for="(uItem,uIndex) in participant"
                :key="uIndex"
                class=""
                @click="openUser(uItem)"
                :style="$general.loadConfiguration(-2)">
            {{uItem.name+(uIndex != (participant.length-1)?'、':'')}}
          </font>
          <div v-if="participant.length>9">
            ...
          </div>
          <div class="lookMoreBtn flex_box flex_align_center"
               @click="openParticipant"
               :style="$general.loadConfiguration(-2)+'color:'+appTheme">点击查看<van-icon :size="((appFontSize-4))+'px'"
                      :color="appTheme"
                      name="arrow"></van-icon>
          </div>
        </div>
      </div>
      <div style="height:40px;"></div>
    </template>
  </div>
  <transition name="van-fade">
    <ul v-if="footerBtnsShow && !dialogShow"
        class="footer_btn_box">
      {{'&nbsp;'}}
      <div :style="$general.loadConfiguration()">
        <template v-for="(item,index) in footerBtns"
                  :key="index">
          <div v-if="item.type == 'btn'"
               class="van-button-box">
            <van-button loading-type="spinner"
                        :loading-size="((appFontSize))+'px'"
                        :loading="item.loading"
                        :loading-text="item.loadingText"
                        :color="item.color?item.color:appTheme"
                        :disabled="item.disabled"
                        @click="footerBtnClick(item)">{{item.name}}</van-button>
          </div>
        </template>
      </div>
    </ul>
  </transition>
  <commentList ref="commentList"
               @openInputBoxEvent="openInputBoxEvent"
               @freshState="freshState"
               :commentData="commentData"
               :type="type"
               :id="id" />
  <div style="height:60px;"></div>
  <footer class="footerBox">
    <inputBox ref="inputBox"
              :inputData="inputData"
              @addCommentEvent="addCommentEvent"
              :type="type"
              :id="id" />
  </footer>
  <van-overlay :show="dialogShow"
               @click="show = false">
    <van-dialog v-model:show="dialogShow"
                :title="dialogTitle"
                :width="'288px'"
                :overlay="false"
                @confirm="confirm"
                show-cancel-button>
      <div class="inherit"
           style="padding: 20px 0 20px 0;">
        <!-- 密码输入框 -->
        <van-password-input :value="value"
                            :mask="false"
                            :length="signlength"
                            :focused="showKeyboard"
                            @focus="showKeyboard = true" />
      </div>

    </van-dialog>
    <!-- 数字键盘 -->
    <van-number-keyboard v-model="value"
                         :show="showKeyboard"
                         :z-index="'99'"
                         @blur="showKeyboard = false" />
  </van-overlay>
  <van-action-sheet v-model:show="show"
                    :actions="actions"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, Toast, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
import inputBox from '../../components/inputBox/inputBox.vue'
import commentList from '../../components/commentList/commentList.vue'
export default {
  name: 'activityDetails',
  components: {
    inputBox,
    commentList,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const data = reactive({
      appFontSize: 16,
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      detailsData: '',
      time: '', // 时间 开始时间
      times: [], // 储存的时间集合
      state: '', // 活动状态
      org: '', // 发布部门
      meetType: '', //
      type: '49', // 类型
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部 btn为按钮
      isShowZiliaoBtn: false, // 按钮是否隐藏
      ziliaoBtns: [{ name: '活动资料', type: 'btn', click: 'activityMateria' }, { name: '日程行程', type: 'btn', click: 'activitySchedule' }, { name: '活动报告', type: 'btn', click: 'activityReport' }],
      otherCon: [
        // { label: '组织部门：', value: '办公厅' },
        // { label: '地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;点：', value: '01101121', click: 'time' }
      ], // 其它内容

      // isInviter: false,//是否邀请人
      // isSignUp: false,//是否报名
      // isSignIn: false,//是否签到
      // isLeave: false,//是否请假
      // leaveStatus: "0",//请假状态 0未审核 1审核通过 2审核未通过

      participant: [
        // { id: 1, name: '我的怩' }
      ], // 活动参与人

      floatModule: { state: '', id: '' }, // 播报的状态播报的id 0没有任何状态 1播放中2已暂停3已结束
      ifSignInPostion: false, // 是否开启定位签到
      longitude: '', // 经度
      latitude: '', // 纬度
      rangeDist: 0, // 范围距离
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      commentList: null,
      inputBox: null,
      description: '',
      show: false,
      actions: [],
      dialogShow: false,
      dialogTitle: '',
      showKeyboard: true,
      value: '',
      signlength: 4
    })
    onMounted(() => {
      onRefresh()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    watch(() => data.value, (newVal) => {
      console.log(newVal)
      if (newVal.length > data.signlength) {
        Toast(`签到口令为${data.signlength}位`)
        data.value = data.value.slice(0, 4)
      }
    })

    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    // 列表请求
    const getList = async () => {
      var res = await $api.activity.activityInfo(data.id)
      var { data: info } = res
      var isPublish = info.isPublish || '0'// 是否公开 1是0否
      var isPublishbm = info.isPublishbm || '0'// 是否公开报名 1是0否
      // var meetApplyCount = info.meetApplyCount || '0'// 会议报名限制人数
      var inviterList = info.inviterList || []// 邀请人集合
      var signUpList = info.signUpList || []// 报名人集合
      var signInList = info.signInList || []// 签到人集合
      var leaveList = info.leaves || []// 请假人集合

      // data.pageNot.text = '抱歉，找不到页面内容了~'
      data.detailsData = info
      if (info) {
        var inviters = info.inviters || []
        // eslint-disable-next-line eqeqeq
        if (isPublish != '1' && inviters.length && !$general.getItemForKey(data.user.id, inviters, 'userId')) { // 不是公开 并且 邀请人里没有 就是没权限
          data.detailsData = false
          data.pageNot.text = '抱歉，您没有访问权限~'
          data.footerBtns = []
          return
        }
        data.title = info.meetName || ''// 标题
        data.org = info.organizer || ''// 部门
        data.state = info.state || ''// 标签
        data.meetType = info.meetTypeName || ''// 标签
        // eslint-disable-next-line eqeqeq
        data.ifSignInPostion = (info.ifSignInPostion || '') == '1'// 是否开启定位签到
        data.longitude = info.longitude || ''// 经度
        data.latitude = info.latitude || ''// 纬度
        data.rangeDist = info.rangeDist || ''// 范围距离
        data.content = $general.dealWithCon(info.content || '')// 内容
        data.otherCon = []
        var signBeginTime = info.signBeginTime || ''
        var signEndTime = info.signEndTime || ''
        var meetSignBeginTime = info.meetSignBeginTime || ''
        var meetSignEndTime = info.meetSignEndTime || ''
        var meetStartTime = info.meetStartTime || ''
        var meetEndTime = info.meetEndTime || ''
        var address = info.address || ''
        data.otherCon.push({ label: '组织部门：', value: data.org })
        data.otherCon.push({ label: '开始时间：', value: meetStartTime ? dayjs(meetStartTime).format('YYYY-MM-DD HH:mm') : '未设置活动开始时间' })
        data.otherCon.push({ label: '结束时间：', value: meetEndTime ? dayjs(meetEndTime).format('YYYY-MM-DD HH:mm') : '未设置活动结束时间', click: 'time' })
        data.otherCon.push({ label: '地点：', value: address })
        data.times = [signBeginTime, signEndTime, meetSignBeginTime, meetSignEndTime, meetStartTime, meetEndTime]
        setTimeout(() => { // 显示数据之后重新 处理一下点击延时 不然不生效了
          // 图片正文中的图片缓存
          // processContentImg();
          // cleanIosDelay();
        }, 100)
        var activityMateria = $general.getItemForKey('activityMateria', data.footerBtns, 'click')// 查询会议资料按钮是否存在
        var activitySchedule = $general.getItemForKey('activitySchedule', data.footerBtns, 'click')// 查询日程行程按钮是否存在
        var activityReport = $general.getItemForKey('activityReport', data.footerBtns, 'click')// 查询活动报告按钮是否存在
        var signUpBtn = $general.getItemForKey('signUp', data.footerBtns, 'click')// 报名
        var signInBtn = $general.getItemForKey('signIn', data.footerBtns, 'click')// 签到
        var leaveBtn = $general.getItemForKey('leave', data.footerBtns, 'click')// 请假
        // 先把按钮都删掉
        $general.delItemForKey(activityMateria, data.footerBtns, 'click')
        $general.delItemForKey(activitySchedule, data.footerBtns, 'click')
        $general.delItemForKey(activityReport, data.footerBtns, 'click')
        $general.delItemForKey(signUpBtn, data.footerBtns, 'click')
        $general.delItemForKey(signInBtn, data.footerBtns, 'click')
        $general.delItemForKey(leaveBtn, data.footerBtns, 'click')

        var participants = [inviterList, signUpList, signInList]
        // 将人加入到参与人中
        var newData = []
        data.participant = []
        for (var i = 0; i < participants.length; i++) {
          for (var j = 0; j < participants[i].length; j++) {
            var puserId = participants[i][j].userId || ''
            if (!puserId) continue
            var puserName = participants[i][j].name || ''
            var pheadImg = participants[i][j].headImg || ''
            var addItem = $general.getItemForKey(puserId, newData, 'id')
            if (!addItem) { // 没有在参与人中就添加
              newData.push({ id: puserId, name: puserName, url: pheadImg })
            }
          }
        }
        data.participant = data.participant.concat(newData)
        console.log(data.participant)
        var arrNum = 9
        if (data.participant.length > arrNum) {
          data.participant.splice(arrNum, data.participant.length - arrNum)
        }
        var isInviter = $general.getItemForKey(data.user.id, inviterList, 'userId')// 找出当前是否被邀请
        var isSignUp = $general.getItemForKey(data.user.id, signUpList, 'userId')// 找出当前是否报名
        var isSignIn = $general.getItemForKey(data.user.id, signInList, 'userId')// 找出当前是否签到
        var isLeave = $general.getItemForKey(data.user.id, leaveList, 'userId')// 找出当前是否请假
        var leaveStatus = isLeave ? isLeave.leaveStatus || '0' : ''
        // eslint-disable-next-line eqeqeq
        if (isInviter || isPublishbm == '1') { // 邀请 或 公开报名就显示按钮
          var formatNowTime = Math.round(new Date().getTime() * 0.001)// 当前时间的时间戳
          var formatSignBeginTime = Math.round(new Date(signBeginTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化报名开始成时间戳
          var formatSignEndTime = Math.round(new Date(signEndTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化报名截止成时间戳
          var formatMeetSignBeginTime = Math.round(new Date(meetSignBeginTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化签到开始成时间戳
          var formatMeetSignEndTime = Math.round(new Date(meetSignEndTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化签到结束成时间戳
          var formatMeetStartTime = Math.round(new Date(meetStartTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化开始成时间戳
          var formatMeetEndTime = Math.round(new Date(meetEndTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化结束成时间戳

          if (formatSignBeginTime > formatNowTime) { // 报名没开始
            data.footerBtns.push({ name: isSignUp ? '已报名' : '报名', type: 'btn', click: 'signUp', loading: false, color: '#ccc', disabled: true })
            if (isInviter) {
              // eslint-disable-next-line eqeqeq
              data.footerBtns.push({ name: isLeave ? (leaveStatus == 0 ? '请假审核中' : leaveStatus == 2 ? '请假未通过' : '请假通过') : '请假', type: 'btn', click: 'leave', loading: false, color: '#ccc', disabled: true })
            }
          } else if (formatSignEndTime > formatNowTime) { // 报名没结束
            // eslint-disable-next-line eqeqeq
            data.footerBtns.push({ name: isSignUp ? '已报名' : '报名', type: 'btn', click: 'signUp', loading: false, color: (isLeave ? leaveStatus != 2 : false) || isSignUp ? '#ccc' : data.appTheme, disabled: (isLeave ? leaveStatus != 2 : false) || isSignUp })
            if (isInviter) {
              // eslint-disable-next-line eqeqeq
              data.footerBtns.push({ name: isLeave && !isSignUp ? (leaveStatus == 0 ? '请假审核中' : leaveStatus == 2 ? '请假未通过' : '请假通过') : '请假', type: 'btn', click: 'leave', loading: false, color: isLeave || isSignUp ? '#ccc' : data.appTheme, disabled: isLeave || isSignUp })
            }
          } else if (formatMeetStartTime > formatMeetSignBeginTime && formatMeetSignEndTime > formatNowTime) { // 签到时间大于开始时间 并且没有结束签到
            showSignInBtn()
          } else if (formatMeetStartTime > formatNowTime) { // 没到活动开始时间
            showSignInBtn()
          } else if (formatMeetSignBeginTime > formatNowTime) { // 没到签到时间
            showSignInBtn()
          } else if (formatMeetSignEndTime > formatNowTime) { // 签到没结束
            showSignInBtn()
          } else if (formatMeetEndTime > formatNowTime) { // 活动没结束
            showSignInBtn()
          }
          // 很多状态都是一个判断
          // eslint-disable-next-line no-inner-declarations
          function showSignInBtn () {
            if (isSignUp) {
              data.footerBtns.push({ name: isSignIn ? '已签到' : '签到', type: 'btn', click: 'signIn', loading: false, color: formatNowTime < formatMeetSignBeginTime || isSignIn ? '#ccc' : data.appTheme, disabled: formatNowTime < formatMeetSignBeginTime || formatNowTime > formatMeetSignEndTime || isSignIn })
            } else if (isLeave) {
              // eslint-disable-next-line eqeqeq
              data.footerBtns.push({ name: leaveStatus == 0 ? '请假审核中' : leaveStatus == 2 ? '请假未通过' : '请假通过', type: 'btn', click: 'leave', loading: false, color: '#ccc', disabled: true })
            } else {
              data.footerBtns.push({ name: '未报名', type: 'btn', click: 'signUp', loading: false, color: '#ccc', disabled: true })
            }
          }
        }
        data.isShowZiliaoBtn = isInviter || isSignUp
      }

      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      // if (data.dataList.length >= total) {
      //   data.finished = true
      // }
    }
    const onSelect = (item) => {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      data.show = false
      // var touchItem = data.dataList[data.onTouchIndex]
      switch (item.name) {
        case '签到口令':
          data.dialogTitle = '活动签到码'
          data.dialogShow = true
          data.showKeyboard = true
          break
      }
    }
    const confirm = async () => {
      console.log(data.value)
      if (!data.value || data.value.length !== 4) {
        Toast('签到口令不正确')
        return
      }
      const res = await $api.activity.addSignInUser({ userId: data.user.id, dataId: data.id, type: 'signIn', signInCommand: data.value })
      var code = res.errcode || 0
      if (code === 200) {
        Toast('签到成功')
        onRefresh()
      } else {
        Toast(res.errmsg || res.data)
      }
    }
    // 底部按钮事件
    const footerBtnClick = (_item) => {
      console.log(JSON.stringify(_item))
      switch (_item.click) {
        case 'signUp':// 报名
          _item.loading = true
          _item.loadingText = '报名'
          Toast({ type: 'loading', message: '报名中', duration: 0 })
          Dialog.confirm({
            message:
              '确定要报名吗？'
          }).then(async () => {
            const res = await $api.activity.addSignInUser({ userId: data.user.id, dataId: data.id, type: 'signUp' })
            var code = res.errcode || 0
            if (code === 200) {
              _item.loading = false
              onRefresh()
              Toast({ type: 'success', message: '报名成功', duration: 1500 })
            } else {
              Toast({ type: 'fail', message: res.errmsg, duration: 1500 })
            }
          }).catch(() => {
            _item.loading = false
            Toast({ type: 'fail', message: '取消报名', duration: 1500 })
            // on cancel
          })
          break
        case 'signIn':// 签到
          data.description = '签到方式'
          data.actions = [{ name: '签到口令', color: '#ee0a24' }]
          data.show = true
          break
        case 'leave':// 请假
          router.push({ name: 'activityLeave', query: { title: '请假', id: data.id, paramType: 'addLeave' } })
          break
        case 'activityMateria':// 活动资料
        case 'activitySchedule':// 日程行程
        case 'activityReport':// 活动报告
          router.push({ name: 'activityOtherInformation', query: { relateType: _item.click, id: data.id, title: _item.name } })
          break
        case 'time':// 点击更多时间
          var dialogParam = {
            message: '<div style="' + $general.loadConfiguration() + 'margin-bottom:0.05rem;padding-bottom:0.05rem;border-bottom:1px dashed #999">时间安排表</div>' +
              '<div style="' + $general.loadConfiguration(-2) + 'text-align:left;" class="flex_box"><div class="inherit">报名时间：</div><div style="color:' + data.appTheme + ';" class="inherit flex_placeholder">' + (data.times[0] ? dayjs(data.times[0]).format('YYYY-MM-DD HH:mm') : '') + (data.times[0] && data.times[1] ? '<span style="color:#000;" class="inherit">至</span><br/>' : '') + (data.times[1] ? dayjs(data.times[1]).format('YYYY-MM-DD HH:mm') : '') + '</div></div>' +
              '<div style="' + $general.loadConfiguration(-2) + 'text-align:left;" class="flex_box"><div class="inherit">签到时间：</div><div style="color:' + data.appTheme + ';" class="inherit flex_placeholder">' + (data.times[2] ? dayjs(data.times[2]).format('YYYY-MM-DD HH:mm') : '') + (data.times[2] && data.times[3] ? '<span style="color:#000;" class="inherit">至</span><br/>' : '') + (data.times[3] ? dayjs(data.times[3]).format('YYYY-MM-DD HH:mm') : '') + '</div></div>' +
              '<div style="' + $general.loadConfiguration(-2) + 'text-align:left;" class="flex_box"><div class="inherit">活动时间：</div><div style="color:' + data.appTheme + ';" class="inherit flex_placeholder">' + (data.times[4] ? dayjs(data.times[4]).format('YYYY-MM-DD HH:mm') : '') + (data.times[4] && data.times[5] ? '<span style="color:#000;" class="inherit">至</span><br/>' : '') + (data.times[5] ? dayjs(data.times[5]).format('YYYY-MM-DD HH:mm') : '') + '</div></div>' +
              '',
            confirmButtonColor: data.appTheme,
            allowHtml: true,
            confirmButtonText: '知道了'
          }
          Dialog(dialogParam)
          break
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    const openParticipant = () => {
      router.push({ name: 'activityParticipant', query: { id: data.id } })
    }
    const tabClick = () => {
      onRefresh()
    }
    const addCommentEvent = (value) => {
      console.log(value)
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      console.log(value)
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      console.log(value)
      data.inputBox.getStats()
    }
    const openUser = rows => {
      router.push({ name: 'personData', query: { id: rows.id } })
    }
    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, search, tabClick, openParticipant, footerBtnClick, addCommentEvent, openInputBoxEvent, freshState, openUser, onSelect, confirm }
  }
}
</script>
<style lang="less" scoped>
.activityDetails {
  background: #fff;
  .vue_newslist_li:active {
    background: rgba(0, 0, 0, 0);
  }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
  }
  .n_details_other {
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
    padding-bottom: 0;
    position: relative;
  }
  .n_details_other div {
    padding: 2px 0;
  }

  .list_item {
    margin-top: 15px;
  }
  .add_warp {
    padding: 10px 10px;
    background: #fff;
  }
  .participant_box {
    padding: 10px;
    box-sizing: border-box;
  }
  .participant_box span {
    font-size: inherit;
    font-family: inherit;
  }

  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 25px 10px;
    box-sizing: border-box;
    position: relative;
  }
  .n_details_title {
    font-weight: 500;
    line-height: 1.5;
  }

  .errMsg {
    width: 140px;
    left: 0;
    right: 0;
    margin: auto;
    padding-top: 30%;
  }
  .errText {
    text-align: center;
    color: #999;
    margin-top: 20px;
  }
  .errBtn {
    line-height: 30px;
    text-align: center;
    width: 120px;
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 20px;
    color: #fff;
    border-radius: 5px;
  }

  .readTitleImg {
    width: 2px;
    height: 14px;
    background: #3088fe;
    opacity: 1;
    border-radius: 10px;
    margin: 0 10px;
  }
  .readTitle {
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
    color: #333333;
  }
  .userBox {
    position: relative;
    width: calc(100% - 20px);
    left: 0;
    right: 0;
    margin: auto;
    font-weight: 400;
    color: #666666;
    opacity: 1;
    margin: 10px auto;
  }
  .lookMoreBtn {
    position: relative;
    font-weight: 400;
    color: #3088fe;
    opacity: 1;
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;
  }
  .canReturn {
    width: 44px;
    height: 44px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    opacity: 1;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    line-height: 44px;
    color: #ffffff;
    left: 0;
    right: 0;
    margin: auto;
    margin-bottom: 10px;
  }
  .canReturnTitle {
    height: 40px;
    font-size: 14px;
    font-weight: 600;
    line-height: 40px;
    text-align: center;
    color: #333333;
    opacity: 1;
    padding: 10px 0;
  }
  .lookMoreBtn .van-icon {
    margin-left: 5px;
  }
  .btn_box {
    background: #8c96a2;
    opacity: 1;
    border-radius: 14px;
    font-weight: 400;
    text-align: center;
    margin-left: 10px;
    color: #ffffff;
    padding: 5px 15px;
  }
  .state {
    position: absolute;
    top: -25px;
    right: 16px;
  }
}
</style>

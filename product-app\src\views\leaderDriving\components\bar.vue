<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'bar',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: <PERSON><PERSON>
  },
  props: {
    color: String,
    id: String,
    list: Array
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      nextTick(() => {
        var chartDom = document.getElementById(props.id)
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        myChart = echarts.init(chartDom)
        setOptions()
      })
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
    })
    const setOptions = () => {
      console.log(parseInt(data.viewportWidth * 0.03))
      var options = {
        tooltip: {
          trigger: 'axis', // 触发类型——坐标轴
          axisPointer: {
            type: 'line',
            z: 0,
            lineStyle: {
              type: 'solid',
              color: 'rgba(225,225,225,.3)',
              width: 18
            }
          },
          borderColor: 'rgba(255, 255, 255, 1)'
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          axisLabel: {
            interval: 0, // 设置刻度标签全部显示
            fontSize: parseInt(data.viewportWidth * 0.025), // 设置字体大小
            rotate: 45 // 设置刻度标签旋转角度
          },
          axisTick: {
            show: false // 隐藏 x 轴的刻度
          },
          axisLine: {
            show: false, // 隐藏 x 轴线
            lineStyle: {
              type: 'solid',
              color: '#bfbfbf',
              width: '1'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#bfbfbf',
              width: '1'
            },
            show: true // 显示 y 轴线
          },
          axisLabel: {
            interval: 0, // 设置刻度标签全部显示
            color: '#bfbfbf',
            fontSize: parseInt(data.viewportWidth * 0.025) // 设置字体大小
          },
          splitLine: {
            show: false // 隐藏 y 轴的刻度背景线
          }
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130, 200, 150, 80, 70, 110, 130],
            type: 'bar',
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            barWidth: parseInt(data.viewportWidth * 0.02),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1, // 渐变的起点和终点坐标
                [
                  { offset: 0, color: props.color }, // 渐变起点颜色
                  { offset: 1, color: '#fff' } // 渐变终点颜色
                ]
              )
            },
            label: {
              show: true,
              color: 'rgba(177, 177, 177, 1)',
              fontSize: parseInt(data.viewportWidth * 0.025),
              align: 'center',
              position: 'top' // 设置标签在顶部显示
            }
          }
        ],
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          show: false, // 隐藏背景线
          bottom: '0%',
          containLabel: true // 是否包含坐标轴标签
        }
      }
      var options2 = {
        tooltip: {
          trigger: 'axis', // 触发类型——坐标轴
          axisPointer: {
            type: 'line',
            z: 0,
            lineStyle: {
              type: 'solid',
              color: 'rgba(225,225,225,.3)',
              width: 18
            }
          },
          borderColor: 'rgba(255, 255, 255, 1)'
        },
        yAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Thu', 'Fri'],
          axisLabel: {
            interval: 0, // 设置刻度标签全部显示
            fontSize: parseInt(data.viewportWidth * 0.025) // 设置字体大小
          },
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#bfbfbf',
              width: '1'
            },
            show: true
          }
        },
        xAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#bfbfbf',
              width: '1'
            },
            show: false
          },
          axisLabel: {
            interval: 0,
            fontSize: parseInt(data.viewportWidth * 0.025)
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 80, 70],
            type: 'bar',
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            barWidth: parseInt(data.viewportWidth * 0.02),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                1, 0, 0, 0, // 渐变的起点和终点坐标
                [
                  { offset: 0, color: props.color }, // 渐变起点颜色
                  { offset: 1, color: '#fff' } // 渐变终点颜色
                ]
              )
            },
            label: {
              show: true,
              color: 'rgba(177, 177, 177, 1)',
              fontSize: parseInt(data.viewportWidth * 0.025),
              // align: 'center',
              position: 'right' // 设置标签在顶部显示
            }
          }
        ],
        grid: {
          left: '10%',
          right: '10%',
          top: '10%',
          show: false, // 隐藏背景线
          bottom: '0%',
          containLabel: true // 是否包含坐标轴标签
        }
      }
      nextTick(() => {
        if (props.id === 'bar3') {
          options2.yAxis.data = props.list.map(item => item.name)
          options2.series[0].data = props.list.map(item => item.value)
          myChart.setOption(options2)
        } else if (props.id === 'bar4') {
          options.xAxis.data = props.list.map(item => item.name)
          options.series[0].data = props.list.map(item => item.value)
          myChart.setOption(options)
        } else if (props.id === 'bar1') {
          options.xAxis.data = props.list.map(item => item.name)
          options.series[0].data = props.list.map(item => item.value)
          myChart.setOption(options)
        } else if (props.id === 'bar2') {
          options.xAxis.data = props.list.map(item => item.name)
          options.series[0].data = props.list.map(item => item.value)
          myChart.setOption(options)
        }
      })
    }
    return { ...toRefs(data), general }
  }
}
</script>
<style lang="less" scoped>
#bar1 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
}

#bar2 {
  background: #fff;
  width: 100%;
  margin: 10px 0;
  box-sizing: border-box;
  height: 180px;
}

#bar3 {
  background: #fff;
  width: 100%;
  margin: 10px 0;
  box-sizing: border-box;
  height: 180px;
}

#bar4 {
  background: #fff;
  width: 100%;
  margin: 10px 0;
  box-sizing: border-box;
  height: 180px;
}
</style>

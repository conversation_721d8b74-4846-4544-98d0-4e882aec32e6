<template>
  <div class="library">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <div style="padding: 11px 16px;">
      <div class="search_warp flex_box flex_align_center">
        <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
          <van-icon :size="18"
                    :color="'#A5A5A5'"
                    name="search"></van-icon>
        </div>
        <form @click="openSearch()"
              class="flex_placeholder flex_box flex_align_center search_input"
              action="javascript:return true;"><input disabled=""
                 id="searchInput"
                 :style="'font-size:15px;'"
                 :placeholder="seachSuggest"
                 maxlength="100"
                 type="search"
                 ref="btnSearch"
                 @keyup.enter="btnSearch()"
                 v-model="seachText" /></form>
      </div>
    </div>
    <!--书库banner-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div v-if="carouselList.length != 0"
             style="padding: 10px 16px;">
          <van-swipe class="carouselMap"
                     :autoplay="3000"
                     :indicator-color="appTheme"
                     :show-indicators="true">
            <van-swipe-item @click="openBookDetails(item)"
                            v-for="(item) in carouselList"
                            :key="item.id">
              <img class="carousel_img"
                   :src="item.url" />
              <div class="carousel_elBox flex_box flex_align_center">
                <div class="carousel_title text_one2"
                     :style="'font-size:15px;'"
                     v-html="item.title"></div>
              </div>
            </van-swipe-item>
          </van-swipe>
        </div>
        <!--书库分类-->
        <div style="padding: 11px 16px;">
          <div v-for="(item) in classification"
               :key="item.id">
            <div class="card_item"
                 v-if="item.data.length != 0">
              <div class="flex_box flex_align_center"
                   style="padding: 10px;">
                <div class="flex_placeholder"
                     :style="'font-size:17px;font-weight: bold;'">{{item.name}}</div>
                <div @click="openBookStoreDetails(item)"
                     class="flex_box flex_align_center">
                  <div :style="'font-size:12px;font-weight: bold;color:#999'">{{'查看更多'}}</div>
                  <van-icon style="margin-left: 4px;"
                            :size="12"
                            :color="'#999'"
                            :name="'arrow'"></van-icon>
                </div>
              </div>
              <van-empty v-if="!item.data || item.data.length == 0"
                         :style="'font-size:14px;'"
                         :image="pageNot.url"
                         :description="'暂无数据'"></van-empty>
              <div v-else
                   class="itemSex_box flex_box T-flex-flow-row-wrap">
                <div v-for="(nItem) in item.data"
                     :key="nItem.id"
                     @click="openBookDetails(nItem)"
                     class="itemSex_item click">
                  <div :style="'width:60px;height:81px;margin:auto;position: relative;'">
                    <img v-if="nItem.txt.bookType == '2'"
                         class="item_Sound"
                         :src="icon_hasSound" />
                    <img v-if="nItem.txt.isAvailable == '0'"
                         class="item_overdue"
                         src="../../../assets/img/overdue.png" />
                    <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                         :src="nItem.img.url" />
                  </div>
                  <div v-if="nItem.name"
                       class="itemSex_name text_one2"
                       :style="'font-size:12px;'"
                       v-html="nItem.name"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--卡片分类-->
        <div style="padding: 11px 16px;">
          <div v-for="(item) in card"
               :key="item.id">
            <div class="card_item"
                 v-if="item.data.length != 0">
              <div class="flex_box flex_align_center"
                   style="padding: 10px;">
                <van-icon style="margin-right: 4px;"
                          :size="18"
                          :color="item.color"
                          :name="item.icon"></van-icon>
                <div class="flex_placeholder"
                     :style="'font-size:14px;'">{{item.name}}</div>
                <div @click="getTypesData(item,1)"
                     class="flex_box flex_align_center">
                  <div :style="'font-size:14px;font-weight: bold;color:'+appTheme">{{'换一批'}}</div>
                  <van-icon style="margin-left: 4px;"
                            :size="18"
                            :color="appTheme"
                            :name="'replay'"></van-icon>
                </div>
              </div>
              <van-empty v-if="!item.data || item.data.length == 0"
                         :style="'font-size:14px;'"
                         :image="icon_no_data"
                         :description="'暂无数据'"></van-empty>
              <div v-else
                   class="itemSex_box flex_box T-flex-flow-row-wrap">
                <div v-for="(nItem) in item.data"
                     :key="nItem.id"
                     @click="openBookDetails(nItem)"
                     class="itemSex_item click">
                  <div :style="'width:60px;height:81px;margin:auto;position: relative;'">
                    <img v-if="nItem.txt.bookType == '2'"
                         class="item_Sound"
                         :src="icon_hasSound" />
                    <img v-if="nItem.txt.isAvailable == '0'"
                         class="item_overdue"
                         src="../../../assets/img/overdue.png" />
                    <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                         :src="nItem.img.url" />
                  </div>
                  <div v-if="nItem.name"
                       class="itemSex_name text_one2"
                       :style="'font-size:14px;'"
                       v-html="nItem.name"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'bookDetail',
  components: {
    [NavBar.name]: NavBar, [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const onClickLeft = () => history.back()
    // const $general = inject('$general')
    const data = reactive({
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      title: route.query.title || '书库',
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      dataList: [
      ], // 列表数据

      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部   btn为按钮

      seachSuggest: '', // 建议搜索词
      carouselList: [
        // { url: 'https://org.modao.cc/uploads4/images/5793/57937892/v2_qksnm0.jpg', title: '看看主席看什么书?' },
        // { url: 'https://org.modao.cc/uploads4/images/5793/57937956/v2_qksnmv.jpg', title: '看看主席看什么书?' },
        // { url: 'https://org.modao.cc/uploads4/images/5793/57938046/v2_qksnnz.jpg', title: '看看主席看什么书?' }
      ],

      classification: [
        // { id: '', name: '最美国学' }, { id: '', name: '党政热点' }, { id: '', name: '西方经典' }, { id: '', name: '戏说历史' },
        // { id: '', name: '红色经典' }, { id: '', name: '推理悬疑' }, { id: '', name: '浪漫文学' }, { id: '', name: '散文诗歌' }
      ],

      card: [
        { name: '热门图书', icon: 'fire-o', color: '#FF4029', pageNo: 1, pageSize: 4, lisType: 'hotest​', data: [] },
        { name: '最新图书', icon: 'new-arrival-o', color: '#067DFF', pageNo: 1, pageSize: 4, lisType: 'newest​', data: [] },
        { name: '猜你喜欢', icon: 'like-o', color: '#F960D0', pageNo: 1, pageSize: 4, lisType: 'readest', data: [] }
      ]

    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      onRefresh()
    })
    // 获取数据
    const getList = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getTypefourList(datas)
      var dataListNew = []
      list.forEach((_nItem, _nIndex, _nArr) => { // item index 原数组对象
        var nItem = { id: _nItem.firstTypeId || '', name: _nItem.firstTypeName || '', data: [] }
        var books = _nItem.books || []
        books.forEach((_nItem, _nIndex, _nArr) => {
          var item = { img: { url: _nItem.coverImgUrl || '' }, txt: { isAvailable: _nItem.isAvailable, url: _nItem.bookContentUrl || '', state: 0, schedule: -1, name: _nItem.bookName || '', bookType: _nItem.bookType || '' } }
          item.id = _nItem.id || ''// 书本id
          item.name = (_nItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_nItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_nItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          // data.annexCheck(item.txt)// 附件检测 拿到附件缓存 信息
          nItem.data.push(item)
        })
        dataListNew.push(nItem)
      })
      data.classification = dataListNew

      var RollBookListNew = []
      var { data: RollBookList } = await $api.bookAcademy.getRollBookList({ pageNo: 1, pageSize: 10, isIssue: 1 })
      RollBookList.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = {}
        item.id = _eItem.bookId || ''
        item.url = _eItem.coverImgUrl || ''
        item.title = _eItem.recommendedWord || ''
        RollBookListNew.push(item)
      })
      data.carouselList = RollBookListNew

      data.card.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        getTypesData(_eItem)
      })
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
      // if (list.length < data.pageSize) {
      //   data.finished = true
      // }
    }
    const getTypesData = async (_item, type) => {
      if (type) {
        data.pageNo = data.pageNo + 1
      } else {
        data.pageNo = 1
      }
      var { data: BookList } = await $api.bookAcademy.getBookList({ pageNo: data.pageNo, pageSize: _item.pageSize || data.pageSize, lisType: _item.lisType })
      if (BookList && BookList.length !== 0) {
        _item.data = []
        BookList.forEach((_nItem, _nIndex, _nArr) => { // item index 原数组对象
          var item = { img: { url: _nItem.coverImgUrl || '' }, txt: { isAvailable: _nItem.isAvailable, url: _nItem.bookContentUrl || '', state: 0, schedule: -1, name: _nItem.bookName || '', bookType: _nItem.bookType || '' } }
          item.id = _nItem.id || ''// 书本id
          item.name = (_nItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
          item.author = (_nItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
          item.summary = (_nItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
          _item.data.push(item)
        })
      } else {
        data.pageNo = 0
        // getTypesData(_item)
      }
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      // data.pageNo = data.pageNo + 1
      // getList()
    }
    const openSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    // 跳转分类详情
    const openBookStoreDetails = (item) => {
      console.log(item)
      router.push({ name: 'libraryDetails', query: { id: item.id, title: item.name } })
    }
    return { ...toRefs(data), onClickLeft, openSearch, openBookDetails, getList, onRefresh, onLoad, getTypesData, openBookStoreDetails }
  }
}
</script>
<style lang="less" scoped>
@import "./library.less";
</style>

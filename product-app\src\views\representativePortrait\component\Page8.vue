<template>
  <div class="Pie8">
    <div class="container_swipe">
      <div class="pages8">
        <img src="../../../assets/img/timeAxis/g_bg08.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages8_item_top">
          <div class="pages8_item_top_text1">2023年</div>
          <div class="pages8_item_top_text2">履职能力评估</div>
        </div>
        <div v-if="tenShow"
             class="pages8_item_almighty">
          <img src="../../../assets/img/timeAxis/g_almighty.png"
               alt=""
               style="width: 100%;height: 100%;">
        </div>
        <div class="pages8_item_center_box"
             v-else-if="oneShow"
             style="left: 25%;">
          <div class="pages8_item_center"
               v-if="assessmentNum.jdnl === 10">
            <div class="bubbling_text">监督型代表</div>
          </div>
          <!-- <div class="pages8_item_center"
               v-if="assessmentNum.lzzs === 10">
            <div class="bubbling_text">发圈型代表</div>
          </div> -->
          <div class="pages8_item_center"
               v-if="assessmentNum.wmfw === 10">
            <div class="bubbling_text">为民型代表</div>
          </div>
          <div class="pages8_item_center"
               v-if="assessmentNum.xxnl === 10">
            <div class="bubbling_text">学习型代表</div>
          </div>
          <div class="pages8_item_center"
               v-if="assessmentNum.dcyj === 10">
            <div class="bubbling_text">调研型代表</div>
          </div>
        </div>
        <div class="pages8_item_center_box"
             v-else-if="maxShow"
             :style="maxLength>'1'?'':'left: 25%;'">
          <div class="pages8_item_center"
               v-if="maxKeyjdnl=='jdnl'||maxFlag">
            <div class="bubbling_text">监督型代表</div>
          </div>
          <!-- <div class="pages8_item_center"
               v-if="assessmentNum.lzzs === 10">
            <div class="bubbling_text">发圈型代表</div>
          </div> -->
          <div class="pages8_item_center"
               v-if="maxKeywmfw=='wmfw'||maxFlag">
            <div class="bubbling_text">为民型代表</div>
          </div>
          <div class="pages8_item_center"
               v-if="maxKeyxxnl=='xxnl'||maxFlag">
            <div class="bubbling_text">学习型代表</div>
          </div>
          <div class="pages8_item_center"
               v-if="maxKeydcyj=='dcyj'||maxFlag">
            <div class="bubbling_text">调研型代表</div>
          </div>
        </div>
        <div class="pages8_item_center_box"
             v-else>
          <div class="pages8_item_center"
               v-if="assessmentNum.jdnl === 10">
            <div class="bubbling_text">监督型代表</div>
          </div>
          <!-- <div class="pages8_item_center"
               v-if="assessmentNum.lzzs === 10">
            <div class="bubbling_text">发圈型代表</div>
          </div> -->
          <div class="pages8_item_center"
               v-if="assessmentNum.wmfw === 10">
            <div class="bubbling_text">为民型代表</div>
          </div>
          <div class="pages8_item_center"
               v-if="assessmentNum.xxnl === 10">
            <div class="bubbling_text">学习型代表</div>
          </div>
          <div class="pages8_item_center"
               v-if="assessmentNum.dcyj === 10">
            <div class="bubbling_text">调研型代表</div>
          </div>
        </div>
        <div class="pages8_item_radar"
             :style="topShow?'margin-top: 42%;':'margin-top: 55%;'">
          <radarPages v-if="radarShow"
                      id="radars"
                      :listData="radarListData"
                      style="width:350px;height:350px;"></radarPages>
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, onUpdated, toRef, watchEffect } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
import radarPages from './radarPages.vue'
export default {
  name: 'Page8',
  components: {
    radarPages,
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean,
    pageData: Object
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      showText1: false,
      showText2: false,
      showText3: false,
      radarShow: false,
      radarListData: [],
      assessmentNum: {
        wmfw: '',
        lzzs: '',
        xxnl: '',
        jdnl: '',
        dcyj: ''
      },
      tenShow: false,
      oneShow: false,
      topShow: false,
      maxShow: false,
      maxKeywmfw: '',
      maxKeyxxnl: '',
      maxKeyjdnl: '',
      maxKeydcyj: '',
      maxLength: 0
    })
    onUpdated(() => {
    })
    watchEffect(() => {
      data.showText1 = toRef(props, 'showText1').value
      data.showText2 = toRef(props, 'showText2').value
      data.showText3 = toRef(props, 'showText3').value
      console.log('user----->>', data.user.id)
      if (data.user.id === '492626685447897088') {
        data.assessmentNum.wmfw = 8
        data.assessmentNum.lzzs = 8
        data.assessmentNum.xxnl = 8
        data.assessmentNum.jdnl = 8
        data.assessmentNum.dcyj = 10
      } else {
        data.assessmentNum.wmfw = toRef(props.pageData, 'wmfw').value
        data.assessmentNum.lzzs = toRef(props.pageData, 'lzzs').value
        data.assessmentNum.xxnl = toRef(props.pageData, 'xxnl').value
        data.assessmentNum.jdnl = toRef(props.pageData, 'jdnl').value
        data.assessmentNum.dcyj = toRef(props.pageData, 'dcyj').value
      }
      // 判断如果全部等于满分10分的话 就显示全能型代表
      const allAreTen = Object.values(data.assessmentNum).every(val => val === 10)
      data.tenShow = allAreTen
      // 如果只有一个10分的话并且要排除lzzs这一项，那么就为true（标识oneShow）
      const allAreOne = Object.entries(data.assessmentNum).filter(([key, val]) => key !== 'lzzs' && val === 10).length === 1
      data.oneShow = allAreOne
      // 如果五个值都一致并且都在2分以上那么也显示全能型代表。
      const allAreSame = Object.values(data.assessmentNum).every(val => val > 2 && val === data.assessmentNum.wmfw)
      data.tenShow = allAreSame
      // 判断如果这五个值只有履职指数（lzzs）为10分或者这五个都没有10分的话 那么就把雷达图上移（标识topShow）
      const topShow = data.assessmentNum.lzzs === 10 || Object.values(data.assessmentNum).every(val => val !== 10)
      console.log('topShow------------', topShow)
      data.topShow = topShow
      // 判断如果这五个值来比较出来的最高那个值 也要显示相对应的关键词（要把lzzs排除）
      const maxScore = Math.max(...Object.values(data.assessmentNum))
      // const maxKeys = Object.keys(data.assessmentNum).filter(key => data.assessmentNum[key] === maxScore)
      const maxKeys = Object.keys(data.assessmentNum).filter(key => data.assessmentNum[key] === maxScore && data.assessmentNum[key] > 2)
      console.log('maxKeys===>', maxKeys)
      console.log('maxKeys.length===>', maxKeys.length)
      if (maxKeys.length > 1) {
        data.maxLength = maxKeys.length
        data.topShow = false
        data.maxShow = true
      }
      if (maxKeys.includes('wmfw') && maxKeys.includes('xxnl') && maxKeys.includes('jdnl') && maxKeys.includes('dcyj')) {
        data.maxFlag = true
      }
      if (maxKeys.includes('wmfw')) {
        data.maxKeywmfw = 'wmfw'
      }
      if (maxKeys.includes('xxnl')) {
        data.maxKeyxxnl = 'xxnl'
      }
      if (maxKeys.includes('jdnl')) {
        data.maxKeyjdnl = 'jdnl'
      }
      if (maxKeys.includes('dcyj')) {
        data.maxKeydcyj = 'dcyj'
      }
      console.log('data.maxKey---------', data.maxKey)
      setTimeout(() => {
        getCapabilityAssessment()
      }, 1000)
    })
    onMounted(() => {
      preventScroll()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    // 获取能力评估
    const getCapabilityAssessment = async () => {
      data.radarShow = true
      data.radarListData = [
        { name: '履职指数', max: 10, num: data.assessmentNum.lzzs },
        { name: '为民服务', max: 10, num: data.assessmentNum.wmfw },
        { name: '学习能力', max: 10, num: data.assessmentNum.xxnl },
        { name: '调查研究', max: 10, num: data.assessmentNum.dcyj },
        { name: '监督能力', max: 10, num: data.assessmentNum.jdnl }
      ]
    }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        setTimeout(() => {
          router.push('/Page9')
        }, 500) // 延迟500毫秒
      } else {
        // 向下滑动
        console.log('向下滑动')
        setTimeout(() => {
          router.push('/Page7')
        }, 500) // 延迟500毫秒
      }
    }
    return { ...toRefs(data), $general, handleTouchStart, handleTouchMove }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
.Pie8 {
  width: 100%;
  min-height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages_item2_text0,
    .pages_item2_text1,
    .pages_item2_text2,
    .pages_item2_text3 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item2_text0.fade-in,
    .pages_item2_text1.fade-in,
    .pages_item2_text2.fade-in,
    .pages_item2_text3.fade-in {
      opacity: 1;
    }
    .pages8 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages8_item_top {
        position: absolute;
        top: 18%;
        left: 10%;
        .pages8_item_top_text1 {
          color: #204a86;
          font-size: 0.75rem;
          font-family: "YouSheBiaoTiHei-2";
        }
        .pages8_item_top_text2 {
          color: #204a86;
          font-size: 0.75rem;
          font-family: "YouSheBiaoTiHei-2";
        }
      }
      .pages8_item_center_box {
        position: absolute;
        top: 34%;
        width: 100%;
        padding: 0 20px;
        // display: flex;
        // align-content: center;
        // justify-content: space-around;
        // flex-wrap: wrap;
        .pages8_item_center {
          float: left;
          width: 140px;
          height: 0.96rem;
          position: relative;
          img {
            width: 89px;
            height: 36px;
          }
        }
        .pages8_item_center:nth-child(1) .bubbling_text {
          float: left;
          padding: 5px 18px 5px 18px;
          height: 1.2rem;
          color: #fff;
          font-size: 0.4rem;
          position: absolute;
          top: 8%;
          left: 12%;
          background-image: url(../../../assets/img/timeAxis/g_blue_right.png);
          background-size: cover;
        }
        .pages8_item_center:nth-child(2) .bubbling_text {
          float: left;
          padding: 5px 18px 5px 18px;
          height: 1.2rem;
          color: #3367cb;
          font-size: 0.4rem;
          position: absolute;
          top: 8%;
          left: 12%;
          background-image: url(../../../assets/img/timeAxis/g_write_left.png);
          background-size: cover;
        }
        .pages8_item_center:nth-child(3) {
          margin-left: 40px;
          margin-top: 20px;
          .bubbling_text {
            float: left;
            padding: 5px 18px 5px 18px;
            height: 1.2rem;
            color: #3367cb;
            font-size: 0.4rem;
            position: absolute;
            top: 8%;
            left: 12%;
            background-image: url(../../../assets/img/timeAxis/g_write_right.png);
            background-size: cover;
          }
        }
        .pages8_item_center:nth-child(4) {
          margin-top: 20px;
          .bubbling_text {
            float: left;
            padding: 5px 18px 5px 18px;
            height: 1.2rem;
            color: #fff;
            font-size: 0.4rem;
            position: absolute;
            top: 8%;
            left: 12%;
            background-image: url(../../../assets/img/timeAxis/g_blue_left.png);
            background-size: cover;
          }
        }
        .pages8_item_center:nth-child(n + 3) {
          text-align: right;
        }
      }
      .pages8_item_almighty {
        position: absolute;
        top: 34%;
        margin: 0 45px;
      }
      .pages8_item_radar {
        position: absolute;
        top: 20%;
        margin-top: 55%;
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    .more {
      position: absolute;
      bottom: 1rem;
      left: 4.5rem;
      .drop {
        font-size: 30px;
        animation: drop 1s linear infinite;
      }
      // .text {
      //   color: #454545;
      // }
    }
    @keyframes drop {
      0% {
        opacity: 0;
        margin-top: 0px;
      }

      25% {
        opacity: 0.5;
        margin-top: -10px;
      }

      50% {
        opacity: 1;
        margin-top: -20px;
      }

      75% {
        opacity: 0.5;
        margin-top: -30px;
      }

      100% {
        opacity: 0;
        margin-top: -40px;
      }
    }
  }
}
</style>

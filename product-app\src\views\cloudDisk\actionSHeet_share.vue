<template>
  <div class="actionSHeet_share">
    <div class="box_warp T-flexbox-vertical">
      <div class="header_box flex_box flex_align_center">
        <div @click="nowLevel.length>1?itemCancel():''"
             class="header_cancel"
             :style="$general.loadConfiguration(-3)">{{nowLevel.length>1?'返回':'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'}}</div>
        <div class="header_hint flex_placeholder"
             :style="$general.loadConfiguration(-1)">{{type=="share"?'共享到':type=="copy"?'复制到':'移动到'}}{{addTitle?('“'+addTitle+'”'):''}}</div>
        <div @click="cancelActionSheet"
             class="header_ok"
             :style="$general.loadConfiguration(-3)+'color:'+appTheme">关闭</div>
      </div>
      <div style="margin: 0 15px;"
           class="clouddisk_after van-hairline--top"></div>
      <template v-if="showPermission()">
        <div class="permission_box flex_box flex_align_center"
             :style="$general.loadConfiguration(-1)+'color:#333;'">
          <div class="inherit"
               style="line-height: 1.4;">操作权限：</div>
          <div style="padding: 0 19px;"
               class="inherit">
            <van-checkbox disabled
                          v-model="permissionPreview"
                          :checked-color="appTheme"
                          :icon-size="((appFontSize+3))+'px'"
                          label-position="left">预览</van-checkbox>
          </div>
          <div style="padding: 0 19px;"
               class="inherit">
            <van-checkbox v-model="permissionDownload"
                          :checked-color="appTheme"
                          :icon-size="((appFontSize+3))+'px'"
                          label-position="left">下载</van-checkbox>
          </div>
        </div>
        <div style="margin: 0 15px;"
             class="clouddisk_after van-hairline--top"></div>
      </template>
      <template v-if="showNewFolder()">
        <div class="newFolder_box">
          <div @click="newFolder()"
               class="newFolder_warp T-flexbox-vertical flex_align_center flex_justify_content">
            <img :src="newFolderIcon"
                 :style="$general.loadConfigurationSize(22)"
                 class="newFolder_icon" />
            <div :style="$general.loadConfiguration(-3)+'color:#333;margin-top:10px;'">{{newFolderText()}}</div>
          </div>
        </div>
        <div style="margin: 0 15px;"
             class="clouddisk_after van-hairline--top"></div>
      </template>
      <div class="flex_placeholder">
        <ul class="main_box">
          <li v-for="(item,index) in listData"
              :key="index"
              @click="openDetails(item)"
              class="flex_box flex_align_center">
            <img :src="require('../../assets/img/fileicon/icon_folder.png')"
                 :style="$general.loadConfigurationSize(13)"
                 class="clouddisk_icon" />
            <div class="flex_placeholder">
              <div class="clouddisk_warp flex_box flex_align_center">
                <div :style="$general.loadConfiguration(-1)"
                     class="flex_placeholder clouddisk_name text_one2">{{item.name}}</div>
                <div class="clouddisk_more flex_box flex_align_center flex_justify_content"></div>
              </div>
              <div style="margin-right: 15px;"
                   class="clouddisk_after van-hairline--top"></div>
            </div>
          </li>
        </ul>
      </div>
      <footer>
        <div class="flex_box flex_justify_content"
             :style="$general.loadConfiguration()">
          <van-button style="padding: 7px 48px;margin:10px;"
                      :color="hasSelect()?appTheme:'#ccc'"
                      @click="hasSelect()?itemSelect():''">{{"确定"}}</van-button>
        </div>
      </footer>
    </div>
    <div class="footer_btn_box">
      <van-popup v-model:show="showAddFile"
                 position="bottom"
                 :style="{ height: '40%' }"
                 round>
        <div class="flex_box flex_align_center flex_justify_content file_options add_file">
          <div :style="$general.loadConfiguration(-3)+'color:#333'"
               @click="showAddFile=false">取消</div>
          <div style="font-weight:bold;">新建个人文件夹</div>
          <div :style="$general.loadConfiguration(-3)+'color:'+appTheme"
               @click="createBucket(newFileName)">确定</div>
        </div>
        <div class="flex_box flex_align_center flex_justify_content flex_flex_direction_column"
             style="height:250px;">
          <img :src="require('../../assets/img/icon_folder_add.png')"
               :style="$general.loadConfigurationSize(13)"
               class="clouddisk_icon" />
          <van-cell-group inset>
            <van-field v-model="newFileName"
                       placeholder="新建文件夹"
                       style="background:#ede8e8;"
                       input-align="center" />
          </van-cell-group>
        </div>
      </van-popup>
    </div>
  </div>

</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Toast } from 'vant'
export default {
  name: 'actionSHeet_share',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: {
    type: {
      type: String,
      default: 'share'
    },
    operateIds: {
      type: String,
      default: ''
    },
    baseTitle: {
      type: String,
      default: ''
    },
    baseType: {
      type: String,
      default: ''
    },
    baseData: {
      type: Array,
      default: () => []
    }
  },
  emits: ['cancelActionSheet', 'fileShare', 'fileMove', 'fileCopy'],
  setup (props, { emit }) {
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      show: false,
      type: props.type, // share 共享  copy 复制  move 移动
      baseType: props.baseType, // 是在个人 还是处室点击的
      baseData: props.baseData, // 复制到的数据源
      baseTitle: props.baseTitle,
      operateIds: props.operateIds,
      addTitle: '',

      permissionPreview: true,
      permissionDownload: true,
      newFolderIcon: require('../../assets/img/icon_folder_add.png'),

      listSelect: [],
      listData: [
      ],

      nowLevel: [// 当前页面层级 默认一级  key代表某个页面 name页面标题 id或其它参数自定义
      ],
      showAddFile: false,
      newFileName: ''

    })
    onMounted(() => {
      open()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    const cancelActionSheet = () => {
      emit('cancelActionSheet', data.type)
    }
    // 是否显示操作权限
    const showPermission = () => {
      var isShow = false
      if (data.type === 'share') {
        isShow = true
      }
      return isShow
    }
    // 是否可以点击确定
    const hasSelect = () => {
      var isShow = false
      if (data.type === 'share' && data.nowLevel.length > 1) {
        isShow = true
      } else if (data.type === 'move') { // 第一级就可以移动
        isShow = true
      } else if (data.type === 'copy' && data.nowLevel.length > 1) {
        isShow = true
      }
      return isShow
    }
    // 是否显示新建文件夹
    const showNewFolder = () => {
      var isShow = false
      if (data.type === 'share') {
        isShow = true
      }
      return isShow
    }
    // 显示新建文件夹文字
    const newFolderText = () => {
      var isShow = '新建文件夹'
      if (data.type === 'share') {
        isShow = '新建共享文件夹'
      }
      return isShow
    }
    // 打开页面
    const open = () => {
      data.show = true
      window.operateIds = data.operateIds || ''

      data.nowLevel = []
      if (data.type === 'share') {
        data.nowLevel.push({ key: 'basePage', name: '', id: '' })
      } else if (data.type === 'move') {
        data.nowLevel.push({ key: 'basePage', name: data.baseTitle, id: '' })
      } else if (data.type === 'copy') {
        data.nowLevel.push({ key: 'basePage', name: '', id: '' })
      }
      setTitle()
      getData()
    }
    // 点击 item
    const itemSelect = () => {
      var param = {
        ids: data.operateIds,
        level: data.nowLevel[data.nowLevel.length - 1],
        download: data.permissionDownload ? 1 : 0
      }
      if (data.type === 'share') {
        emit('fileShare', param)
      } else if (data.type === 'move') {
        emit('fileMove', param)
      } else if (data.type === 'copy') {
        emit('fileCopy', param)
      }
      data.show = false
    }

    // 点击 取消
    const itemCancel = () => {
      data.nowLevel.pop()
      setTitle()
      getData()
    }
    // 设置当前页面标题
    const setTitle = (_name) => {
      console.log(data.nowLevel)
      data.addTitle = _name || (data.nowLevel.length !== 0 ? data.nowLevel[data.nowLevel.length - 1].name : '')
    }
    // 点击进入详情 是选择还是打开文件夹 预览附件
    const openDetails = (_item) => {
      data.nowLevel.push({ key: 'fileList', name: _item.name, id: _item.id, baseId: _item.baseId || '' })
      setTitle()
      getData()
    }
    // 获取列表数据
    const getData = async () => {
      var url = ''
      if (data.type === 'share') {
        url = sessionStorage.getItem('fileStoreUrl') + '/share/listAllObjects?'
      } else if (data.type === 'move') {
        url = sessionStorage.getItem('fileStoreUrl') + '/' + data.baseType + '/listAllObjects?'
      }
      var postParam = {
      }
      var lastLevel = data.nowLevel.length !== 0 ? data.nowLevel[data.nowLevel.length - 1] : []
      if (lastLevel.length === 0) {
        return
      }
      switch (lastLevel.key) {
        case 'basePage':// 在首页
          if (data.type === 'share') {
            postParam.folderId = '/'
            url = url + 'folderId=/'
          } else if (data.type === 'copy') { // 复制显示第一个
            data.showSkeleton = false
            data.listData = []
            data.baseData.forEach(function (_eItem) {
              if (_eItem.show && _eItem.id !== 'share') {
                data.listData.push({ name: _eItem.title, id: '', baseId: _eItem.id })
              }
            })
            return
          }
          break
        case 'fileList':// 在详细列表页
          if (data.type === 'share') {
            postParam.folderId = lastLevel.id
            url = url + 'folderId=' + lastLevel.id
          } else {
            postParam.bucketId = lastLevel.id
            url = url + 'bucketId=' + lastLevel.id
          }
          if (data.type === 'copy') {
            url = sessionStorage.getItem('fileStoreUrl') + '/' + lastLevel.baseId + '/listAllObjects?'
          }
          break
      }
      const ret = await $api.cloudDisk.generalGet(url, postParam)
      data.showSkeleton = false
      data.listData = []
      var attribute = ret ? ret.attribute || {} : {}
      var showList = []
      var result = $general.isParameters(attribute.result) ? attribute.result : ''
      if ($general.isArray(result)) { // 直接是数组
        showList = [result]
      } else {
        var bucketList = result.bucketList || []// 所有文件夹
        // var fileList = result.fileList || []// 所有文件
        var folderList = result.folderList || []// 共享文件夹
        if (data.type === 'share') {
          showList = [folderList]
        } else {
          showList = [bucketList]
        }
      }
      showList.forEach(function (_nItem) {
        _nItem.forEach(function (_eItem) {
          if (_eItem.fileObject) {
            _eItem = _eItem.fileObject
          }
          _eItem.baseId = lastLevel.baseId || ''
          _eItem.name = _eItem.name || _eItem.foldName || ''
          _eItem.time = _eItem.lastModifyDate || _eItem.modifyDate || ''
          if (_eItem.bucketId) {
            _eItem.iconInfo = $general.getFileTypeAttr(_eItem.name)
          } else {
            _eItem.iconInfo = { name: 'icon_folder.png', type: 'folder' }// 是文件夹
          }
          if (data.type === 'move' && $general.getItemForKey(_eItem.id, window.operateIds.split(',,'))) { // 是移动时 不能选择自己
            return
          }
          data.listData.push(_eItem)
        })
      })
    }
    // 点击新建文件夹
    const newFolder = () => {
      data.showAddFile = true
    }
    // 新建文件夹
    const createBucket = async (_name) => {
      if (!_name) {
        Toast('请输入文件夹名字')
        return
      }
      var url = sessionStorage.getItem('fileStoreUrl') + '/' + data.baseType + '/createBucket?'
      var postParam = {
        bucketName: _name
      }
      if (data.type === 'share') {
        url = sessionStorage.getItem('fileStoreUrl') + '/share/createFolder?'
        delete postParam.bucketName
        postParam.folderName = _name
      }
      var lastLevel = data.nowLevel[data.nowLevel.length - 1]
      switch (lastLevel.key) {
        case 'basePage':// 在首页
          break
        case 'fileList':// 在详细列表页
          if (data.type === 'share') {
            postParam.parentId = lastLevel.id
          } else {
            postParam.parentBucketId = lastLevel.id
          }
          break
      }
      const ret = await $api.cloudDisk.generalPost(url, postParam)
      if (ret && ret.code === 1) {
        Toast('操作成功')
        getData()
        data.showAddFile = false
      } else {
        Toast(ret ? (ret.message || ret.prompt || '操作失败') : ('网络错误'))
      }
    }
    return { ...toRefs(data), $general, confirm, cancelActionSheet, showPermission, newFolder, createBucket, itemCancel, openDetails, itemSelect, open, newFolderText, showNewFolder, hasSelect }
  }
}
</script>
<style lang="less" scoped>
.actionSHeet_share {
  // background: rgba(0, 0, 0, 0.25);
  #app {
    height: 100%;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  #app .van-overlay {
    background-color: rgba(0, 0, 0, 0);
  }
  #app .van-action-sheet {
    height: 80%;
  }
  .header_box {
    background-color: #fff;
    min-height: 45px;
  }
  .header_cancel {
    padding: 10px 15px;
    color: #666666;
  }
  .header_hint {
    font-weight: 600;
    color: #333333;
    text-align: center;
  }
  .header_ok {
    padding: 10px 15px;
    font-weight: 600;
  }
  .box_warp {
    background: #fff;
    border-radius: 10px 10px 0px 0px;
    width: 100%;
    height: 84%;
  }
  .main_box {
    overflow-y: auto;
    height: 500px !important;
  }
  .permission_box {
    background-color: #fff;
    min-height: 50px;
    padding: 5px 15px;
  }
  body .van-checkbox__label--left {
    margin-right: 5px;
    margin-left: 0;
  }
  .newFolder_box {
    padding: 19px 15px;
  }
  .newFolder_warp {
    background-color: #f8f8f8;
    border-radius: 4px;
    padding: 10px;
  }

  .clouddisk_icon {
    margin: 16px 15px;
    object-fit: cover;
  }
  .clouddisk_warp {
    padding: 10px 0;
  }
  .clouddisk_more {
    padding: 15px;
    min-height: 41px;
  }
  .clouddisk_name {
    color: #333333;
  }
  .footer_btn_box {
    bottom: 130px;
    right: 10px;

    .van-button-box {
      border-radius: 50%;
    }
    .file_options {
      justify-content: space-around;
      height: 100%;
      .cloudDisk_icon_file {
        text-align: center;
      }
    }
    .add_file {
      height: 50px;
      border-bottom: 1px solid #ede8e8;
    }
  }
}
</style>

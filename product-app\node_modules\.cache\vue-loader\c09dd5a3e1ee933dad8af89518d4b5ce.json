{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue?vue&type=style&index=0&id=53054bfa&lang=less&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue", "mtime": 1756438117302}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue"], "names": [], "mappings": ";AA+zCA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACZ;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEP,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACpB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACpB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACf;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACf;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnB,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAElB,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEf,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEd,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEd,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC5B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxD;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7C;EACF;AACF", "file": "D:/zy/xm/h5/qdrd_h5/product-app/src/views/leaderDriving/leaderDriving.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"leaderDriving\">\r\n    <div class=\"leaderDriving_top\">\r\n    </div>\r\n    <div class=\"leaderDriving_tab\">\r\n      <div @click=\"tabClick(item)\"\r\n        :class=\"{ leaderDriving_tab_item: true, leaderDriving_tab_item_active: active == item.value }\"\r\n        v-for=\"item in tabList\" :key=\"item.value\">{{ item.name }}</div>\r\n    </div>\r\n    <div v-if=\"active == 1\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市概括\r\n      </div>\r\n      <leader-driving-box title=\"组织概括\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in generalize\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总用户量\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar1\" v-if=\"representativeVos.length\" :list=\"representativeVos\"></Bar>\r\n          <Bar :color=\"'rgba(255, 123, 49)'\" id=\"bar2\" v-if=\"officeVos.length\" :list=\"officeVos\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"市代表变动情况\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in representative\" :key=\"index\"\r\n              @click=\"representativeClick(item)\">\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                <span class=\"leaderDriving_generalize_item_title_span\">{{ item.title }}</span>\r\n                <span v-if=\"index == 0\">\r\n                  <el-icon style=\"color: #41ce81;\">\r\n                    <Bottom style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n                <span v-else>\r\n                  <el-icon style=\"color: #ff6da2;\">\r\n                    <Top style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_num\" :style=\"{ color: index == 0 ? '#41ce81' : '#ff6da2' }\">\r\n                {{ index == 0 ? '-' : '+' }}{{ item.num }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各代表团人数\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(255, 110, 110)'\" id=\"bar3\" v-if=\"representerTeam.length\" :list=\"representerTeam\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表学历分析\">\r\n        <template v-slot:content>\r\n          <Radar id=\"radar1\" v-if=\"memberEducationData.length\" :list=\"memberEducationData\"></Radar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表年龄分析\">\r\n        <template v-slot:content>\r\n          <Pie :id=\"'pie1'\" :list=\"birthday\" v-if=\"birthday.length\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表性别分析\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie\">\r\n            <div class=\"box_left\">\r\n              <Pie :id=\"'pie2'\" v-if=\"sex.length\" :list=\"sex\"></Pie>\r\n            </div>\r\n            <div class=\"box_right\" v-if=\"sex.length\">\r\n              <div class=\"top\">\r\n                <div><img :src=\"require('../../assets/img/man.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[0].name }}性{{ sex[0].value }}名</span>\r\n                <span style=\"color: #3894ff;\">{{ parseInt(sex[0].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <div><img :src=\"require('../../assets/img/woman.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[1].name }}性{{ sex[1].value }}名</span>\r\n                <span style=\"color: #ff8197;\">{{ parseInt(sex[1].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表结构分析\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar4\" v-if=\"representerElement.length\" :list=\"representerElement\">\r\n          </Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 2\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              今日提交总额\r\n            </div>\r\n            <div class=\"suggest_num\">\r\n              <span style=\"font-weight: 700;\">{{ AdviceByToday }}</span>\r\n              件\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议总数\r\n          </div>\r\n          <div class=\"suggest_box\">\r\n            <div :class=\"{ suggest_meet: index == 0, suggest_flat: index == 1 }\" v-for=\"item, index in AdviceByDomain\"\r\n              :key=\"index\">\r\n              <div class=\"meet_num\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1')\">\r\n                <span>{{ item.adviceCount }}</span>\r\n                件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1020')\">\r\n                正在办理<span>{{ item.transacting }}</span>件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1100')\">\r\n                已办结<span>{{ item.transactAccomplish }}</span>件\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"suggest_title\">\r\n            类别占比\r\n          </div>\r\n          <Pie :id=\"'pie3'\" v-if=\"currentCategoryData.length\" :list=\"currentCategoryData\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in keywords\" :key=\"index\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              满意度\r\n            </div>\r\n            <div class=\"suggest_satisfaction\">\r\n              <div class=\"satisfaction_item\" v-for=\"item, index in ['满意', '基本满意', '不满意']\" :key=\"index\">\r\n                <span :style=\"{ 'background': index == 0 ? '#40cd80' : index == 1 ? '#ffd055' : '#ff6d6d' }\"></span>\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_title\">\r\n            <p>建议满意度</p>\r\n            <template v-if=\"BySatisfaction.length\">\r\n              <memory-bar v-for=\"item, index in BySatisfaction\" :key=\"index\" :item=\"item\"></memory-bar>\r\n            </template>\r\n          </div>\r\n          <div class=\"satisfaction_title\" style=\"border: 0;\">\r\n            <p>类别满意度</p>\r\n            <div class=\"satisfaction_item\" v-for=\"item, index in SatisfactionByData\" :key=\"index\">\r\n              <!-- <span>{{ item.name }}</span> -->\r\n              <memory-bar :item=\"item\"></memory-bar>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_all\">\r\n            <p v-if=\"satisfactionStatus\" @click=\"satisfactionAll(false)\"><van-icon name=\"arrow-up\" />\r\n              收起</p>\r\n            <p v-if=\"!satisfactionStatus\" @click=\"satisfactionAll(true)\"><van-icon name=\"arrow-down\" />\r\n              点击展开查看更多</p>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByRepresentative\" :click=\"true\"\r\n            :title=\"['排行', '姓名', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByDelegation\" :click=\"true\"\r\n            :title=\"['排行', '代表团', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 3\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"message_box\">\r\n            <img :src=\"require('../../assets/img/ldjsc_message.png')\" alt=\"\">\r\n            <template v-if=\"findWygzsTitleData && findWygzsTitleData.length != 0\">\r\n              <div class=\"message\">\r\n                <div v-for=\"(item, index) in findWygzsTitleData\" :key=\"index\" @click=\"MessagePage(item)\">\r\n                  <div v-if=\"index < 2\" class=\"news_text_box_item\">{{ item.title }}</div>\r\n                </div>\r\n              </div>\r\n              <div style=\"color: #7e7d7d;padding: 0.1rem;position: absolute;right: 10px;top: 0;\"\r\n                v-if=\"findWygzsTitleData.length >= 2 && (areaId == '370215' || areaId == '370200')\"\r\n                @click=\"massMessagesClick\"> >\r\n              </div>\r\n            </template>\r\n            <!-- <div class=\"message\"\r\n                 v-if=\"findWygzsTitleData.length\">\r\n              <p v-for=\"item,index in findWygzsTitleData\"\r\n                 :key=\"index\"\r\n                 v-show=\"index < 2\"><span>{{ item.title }}</span><span v-if=\"index == 0\">></span></p>\r\n            </div> -->\r\n            <template v-else>\r\n              <div class=\"messageNull\">\r\n                暂无数据\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            数据概括\r\n          </div>\r\n          <div class=\"interface_location_box\">\r\n            <div class=\"interface_location_left\">\r\n              <div class=\"interface_location_left_title\">\r\n                联络站总数量\r\n              </div>\r\n              <div class=\"interface_location_left_bot\">\r\n                <span>{{ findStudioCountByCityData.studioCount }}</span>个\r\n              </div>\r\n            </div>\r\n            <div class=\"interface_location_right\">\r\n              <Pie2 v-if=\"findWygzsTitlesCountShow\"\r\n                :datas=\"{ percentage: findWygzsTitlesCountData.responseRate, num: findWygzsTitlesCountData.num, text: '回复率', }\"\r\n                id=\"pie2\"></Pie2>\r\n            </div>\r\n          </div>\r\n          <div class=\"interface_location_box_bot\">\r\n            <div>\r\n              <p>总留言数</p>\r\n              <p>{{ (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) ?\r\n                (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>已回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.repliedCount ? findWygzsTitlesCountData.repliedCount : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>未回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.noRreplyCount ? findWygzsTitlesCountData.noRreplyCount : '暂无数据' }}</p>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            联络站分布\r\n          </div>\r\n          <Map v-if=\"mapListShow\" :list=\"mapList\" id=\"maplist\"></Map>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in findHotspotKeywordsData\" :key=\"index\" v-show=\"index < 5\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            区市联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsTitlesRankingData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsStudioTitlesCountData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 4\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            市代表标兵\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear == years }\"\r\n              @click=\"representativeTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear != years }\"\r\n              @click=\"representativeTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"medal\" type=\"resumption\" :dataList=\"dutynumList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"router.push('/performanceFilesList')\">\r\n            查看更多\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团排行\r\n            <van-icon name=\"question-o\" color=\"#d5d5d5\" size=\"24\" @click=\"show = true\" />\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear == years }\"\r\n              @click=\"dumplingTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear != years }\"\r\n              @click=\"dumplingTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"trophy\" :dataList=\"delegationScore\" :title=\"['排行', '代表团', '得分']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区市代表标兵\r\n            <div>\r\n              <van-popover v-model:show=\"showPopover\" placemen=\"bottom-end\" :actions=\"areas\" @select=\"onSelect\">\r\n                <template #reference>\r\n                  <p>{{ actionsText }} <van-icon name=\"play\" style=\"transform: rotate(90deg);\" /></p>\r\n                </template>\r\n              </van-popover>\r\n            </div>\r\n          </div>\r\n          <ranking-list type=\"resumption\" urlType=\"medal\" :dataList=\"dutynumCityList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"notText\" style=\"font-size:14px;color: #ccc;\" v-html=\"pageNot.text\" @click=\"loadMore()\"></div>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 5\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市\r\n      </div>\r\n      <leader-driving-box title=\"总安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie1\">\r\n            <div class=\"box_left\">\r\n              <pie-2 v-if=\"appToday.num\" :datas=\"{ num: appToday.num, text: '总安装率' }\" id=\"pie1\"></pie-2>\r\n            </div>\r\n            <div class=\"box_right\">\r\n              <div class=\"top\">\r\n                <p>今日登录人数\r\n                  <span :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\">{{ appToday.todayLoginNum\r\n                  }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\"> <van-icon name=\"down\"\r\n                    style=\"transform: rotate(-90deg)\" /> 较昨日{{ appToday.rorfNum === 1 ? '增加' : '下降' }}{{\r\n                      appToday.riseOrFallNum }}\r\n                </p>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <p>今日登录人次\r\n                  <span :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\">{{\r\n                    appToday.todayLoginTimes }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\"><van-icon name=\"down\" />\r\n                  较昨日{{ appToday.rorfTime === 1 ? '增加' : '下降' }}{{ appToday.riseOrFallTimes }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: dynamicId == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '1')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line1\" :list=\"appLoginActivation\" v-if=\"appLoginActivation.length\" :status=\"dynamicId\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in install\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团安装率排行\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded2 ? memberCMemTeamInstallount : memberCMemTeamInstallount.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll2\">\r\n            {{ isExpanded2 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"青岛市本级活跃度分析\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: subactive == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '2')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            登录人数/人次\r\n          </div>\r\n          <line-2 id=\"lines1\" v-if=\"appLoginActivationByNumTim.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByNumTim\"></line-2>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            活跃度\r\n          </div>\r\n          <Line id=\"line2\" v-if=\"appLoginActivationCity.length\" :status=\"subactive\" :list=\"appLoginActivationCity\">\r\n          </Line>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            机关、代表活跃度\r\n          </div>\r\n          <Line id=\"line3\" v-if=\"appLoginActivationByMemOff.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByMemOff\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团活跃度排行\" color=\"#e2eeff\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: groupActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '3')\"> 本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\"\r\n            :dataList=\"isExpanded1 ? appLoginActivationByTeam : appLoginActivationByTeam.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '活跃度']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll1\">\r\n            {{ isExpanded1 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市各区\r\n      </div>\r\n      <leader-driving-box title=\"总安装率排名\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded ? areaInstall : areaInstall.slice(0, showCount)\"\r\n            :title=\"['排行', '区市', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll\">\r\n            {{ isExpanded ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市登录情况\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: istrictEntry == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '4')\">本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <line-2 id=\"lines2\" v-if=\"appLoginByArea.length\" :status=\"istrictEntry\" :list=\"appLoginByArea\"></line-2>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: districtActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '5')\">\r\n              <div>本{{ item.name }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line4\" v-if=\"appLoginActivationByArea.length\" :status=\"districtActivity\"\r\n            :list=\"appLoginActivationByArea\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 6\">\r\n      <iframe src=\"http://120.221.72.187:9003/cockpit/#/\" frameborder=\"0\"\r\n        style=\"width: 100%;height: 680px;z-index: 99999;-webkit-overflow-scrolling: touch; overflow: scroll;\"></iframe>\r\n    </div>\r\n    <demo></demo>\r\n    <van-popup close-icon=\"close\" round v-model:show=\"show\" closeable :style=\"{ height: '13%', width: '90%' }\">\r\n      <div class=\"popup_con\">\r\n        <div class=\"popup_con_title\">\r\n          提示\r\n        </div>\r\n        <div class=\"info\">代表团积分=代表团中代表之和/总人数</div>\r\n      </div>\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'\r\nimport LeaderDrivingBox from './components/leaderDrivingBox.vue'\r\nimport Bar from './components/bar.vue'\r\nimport Pie from './components/pie.vue'\r\nimport RankingList from './components/rankingList.vue'\r\nimport Map from './components/map.vue'\r\nimport Line from './components/line.vue'\r\nimport Pie2 from './components/pie2.vue'\r\nimport Line2 from './components/line2.vue'\r\nimport Radar from './components/radar.vue'\r\nimport MemoryBar from './components/memoryBar.vue'\r\nimport Demo from './components/demo.vue'\r\nexport default {\r\n  name: 'leaderDriving',\r\n  components: {\r\n    LeaderDrivingBox,\r\n    Bar,\r\n    Pie,\r\n    RankingList,\r\n    Map,\r\n    Line,\r\n    Pie2,\r\n    Line2,\r\n    Radar,\r\n    MemoryBar,\r\n    Demo,\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Circle.name]: Circle\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      pageNot: { text: '' },\r\n      pageNo: 1,\r\n      pageSize: 5,\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId')),\r\n      areas: [],\r\n      areaIdStatus: '',\r\n      years: new Date().getFullYear(),\r\n      showPopover: false,\r\n      actionsText: '',\r\n      active: sessionStorage.getItem('leaderActive') || '1',\r\n      tabList: [{\r\n        name: '组织情况',\r\n        value: '1'\r\n      }, {\r\n        name: '建议情况',\r\n        value: '2'\r\n      }, {\r\n        name: '联络站',\r\n        value: '3'\r\n      }, {\r\n        name: '履职报表',\r\n        value: '4'\r\n      }, {\r\n        name: '运行情况',\r\n        value: '5'\r\n      }, {\r\n        name: '信访情况',\r\n        value: '6'\r\n      }],\r\n      generalize: [\r\n        {\r\n          num: '',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      install: [\r\n        {\r\n          num: '0',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      representative: [\r\n        {\r\n          num: 1,\r\n          title: '出缺代表',\r\n          key: '1',\r\n          type: 'hasVacant'\r\n        },\r\n        {\r\n          num: 1,\r\n          title: '新增代表',\r\n          key: '2',\r\n          type: ''\r\n        }\r\n      ],\r\n      keywordsList: ['教育', '产业链'],\r\n      keywords: ['教育', '产业链', '农业'],\r\n      mapList: [],\r\n      mapListShow: false,\r\n      rate: 50,\r\n      cityYear: new Date().getFullYear(),\r\n      dumplingYear: 2025,\r\n      show: false,\r\n      dynamicTab: [{\r\n        name: '日',\r\n        id: '1'\r\n      }, {\r\n        name: '月',\r\n        id: '2'\r\n      }],\r\n      dynamicId: '1',\r\n      subactive: '1',\r\n      groupActivity: '1',\r\n      istrictEntry: '1',\r\n      districtActivity: '1',\r\n      representativeText: '点击查看更多',\r\n      satisfactionStatus: false,\r\n      sex: [],\r\n      birthday: [],\r\n      party: [],\r\n      representerElement: [],\r\n      representerTeam: [],\r\n      memberEducationData: [],\r\n      officeVos: [],\r\n      representativeVos: [],\r\n      AdviceByToday: '0',\r\n      AdviceByDomain: [],\r\n      currentCategoryData: [],\r\n      BySatisfaction: [],\r\n      SatisfactionBy: [],\r\n      SatisfactionByData: [],\r\n      ByRepresentative: [],\r\n      ByDelegation: [],\r\n      findWygzsTitleData: [],\r\n      findStudioCountByCityData: {},\r\n      findWygzsTitlesCountData: {},\r\n      findWygzsTitlesCountShow: false,\r\n      findHotspotKeywordsData: {},\r\n      findWygzsTitlesRankingData: [],\r\n      findWygzsStudioTitlesCountData: [],\r\n      dutynumList: [],\r\n      delegationScore: [],\r\n      dutynumCityList: [],\r\n      appToday: {\r\n        todayLoginNum: '', // 今日登录人数\r\n        rorfNum: '', // 较昨日上升或下降\r\n        riseOrFallNum: '', // 上升或下降数量\r\n        todayLoginTimes: '', // 今日登陆人次\r\n        rorfTime: '', // 较昨日上升或下降\r\n        riseOrFallTimes: '', // 上升或下降数量\r\n        num: 0\r\n      },\r\n      appLoginActivation: [],\r\n      appInstall: [],\r\n      areaInstall: [],\r\n      showCount: 5, // 控制展示的数据数量，默认为5\r\n      isExpanded: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded1: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded2: false, // 控制是否展开全部数据，默认为false\r\n      memberCMemTeamInstallount: [],\r\n      appLoginActivationByNumTim: [],\r\n      appLoginActivationCity: [],\r\n      appLoginActivationByMemOff: [],\r\n      appLoginActivationByTeam: [],\r\n      appLoginByArea: [],\r\n      appLoginActivationByArea: []\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      const areaList = JSON.parse(sessionStorage.getItem('areas'))\r\n      data.areas = areaList.map(item => {\r\n        return {\r\n          text: item.name,\r\n          id: item.id,\r\n          name: item.name\r\n        }\r\n      })\r\n      // data.areas.splice(0, 1)\r\n      data.actionsText = data.areas[0].name\r\n      data.areaIdStatus = data.areas[0].id\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    })\r\n    // 组织情况\r\n    const organization = () => {\r\n      getMemberCount()\r\n      memberEducation()\r\n      getOrganization()\r\n      memberChange()\r\n    }\r\n    // 建议情况\r\n    const recommendation = () => {\r\n      getAdviceByToday()\r\n      getAdviceByDomain()\r\n      currentCategory()\r\n      keywords()\r\n      getAdviceBySatisfaction()\r\n      getNumberByRepresentative()\r\n      getNumberByDelegation()\r\n    }\r\n    // 联络站\r\n    const interfaceLocation = () => {\r\n      getMapList()\r\n      findWygzsTitleList()\r\n      findStudioCountByCity()\r\n      findWygzsTitlesCount()\r\n      findHotspotKeywords()\r\n      findWygzsTitlesRanking()\r\n      findWygzsStudioTitlesCount()\r\n    }\r\n    // 履职报表\r\n    const PerformanceReport = () => {\r\n      dutynumList(2025)\r\n      delegationScore()\r\n      dutynumCityList()\r\n    }\r\n    // 运行情况\r\n    const runningCondition = () => {\r\n      appTodayLogin()\r\n      appAllInstall()\r\n      appLoginActivation()\r\n      appInstall()\r\n      memberCMemTeamInstallount()\r\n      appLoginActivationByNumTim()\r\n      appLoginActivationCity()\r\n      appLoginActivationByMemOff()\r\n      appLoginActivationByTeam()\r\n      areaInstall()\r\n      appLoginByArea()\r\n      appLoginActivationByArea()\r\n    }\r\n    const getMapList = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByDistrict({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.mapList = res.data\r\n      data.mapListShow = true\r\n    }\r\n    const representativeTab = (y) => {\r\n      data.cityYear = y\r\n      dutynumList(y)\r\n    }\r\n    const dumplingTab = (y) => {\r\n      data.dumplingYear = y\r\n      delegationScore(y)\r\n    }\r\n    const tabClick = (item) => {\r\n      sessionStorage.setItem('leaderActive', item.value)\r\n      data.active = sessionStorage.getItem('leaderActive')\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    }\r\n    const onSelect = (item) => {\r\n      data.actionsText = item.text\r\n      data.areaIdStatus = item.id\r\n      dutynumCityList()\r\n    }\r\n    const dynamic = (id, type) => {\r\n      switch (type) {\r\n        case '1':\r\n          data.dynamicId = id\r\n          appLoginActivation(id)\r\n          break\r\n        case '2':\r\n          data.subactive = id\r\n          appLoginActivationByNumTim(id)\r\n          appLoginActivationCity(id)\r\n          appLoginActivationByMemOff(id)\r\n          break\r\n        case '3':\r\n          data.groupActivity = id\r\n          appLoginActivationByTeam(id)\r\n          break\r\n        case '4':\r\n          data.istrictEntry = id\r\n          appLoginByArea(id)\r\n          break\r\n        case '5':\r\n          data.districtActivity = id\r\n          appLoginActivationByArea(id)\r\n          break\r\n      }\r\n    }\r\n    const satisfactionAll = (type) => {\r\n      data.satisfactionStatus = type\r\n      if (data.satisfactionStatus) {\r\n        data.SatisfactionByData = data.SatisfactionBy\r\n      } else {\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getMemberCount = async () => {\r\n      var res = await $api.leaderDriving.memberCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      if (res.data) {\r\n        data.sex = res.data.sex.map((item, index) => {\r\n          return {\r\n            name: item.name,\r\n            value: item.amount,\r\n            key: item.key,\r\n            itemStyle: { color: index === 0 ? '#3da2ff' : '#ff738c' }\r\n          }\r\n        })\r\n        data.birthday = res.data.birthday.map(item => {\r\n          return {\r\n            key: item.key,\r\n            name: item.name,\r\n            value: item.amount\r\n          }\r\n        })\r\n        data.party = res.data.party.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        })\r\n        data.representerElement = res.data.representerElement.map(item => {\r\n          return {\r\n            value: item.amount,\r\n            key: item.key,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n        data.representerTeam = res.data.representerTeam.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        }).reverse()\r\n      }\r\n      // console.log(data.birthday)\r\n      // console.log('getMemberCount', res.data)\r\n    }\r\n    const memberEducation = async () => {\r\n      var res = await $api.leaderDriving.memberEducation({})\r\n      if (res.data) {\r\n        data.memberEducationData = res.data.map(item => {\r\n          return {\r\n            value: item.value,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const getOrganization = async () => {\r\n      var res = await $api.leaderDriving.getOrganization({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.officeVos = res.data.officeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.representativeVos = res.data.representativeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.generalize[0].num = res.data.totalNumber\r\n      data.generalize[1].num = res.data.representativeNumber\r\n      data.generalize[2].num = res.data.officeNumber\r\n    }\r\n    const memberChange = async () => {\r\n      var res = await $api.leaderDriving.memberChange({})\r\n      data.representative[0].num = res.data.vacantNum\r\n      data.representative[1].num = res.data.repairNum\r\n    }\r\n    const getAdviceByToday = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByToday({ personCode: '' })\r\n      if (res.result) {\r\n        data.AdviceByToday = res.result\r\n      }\r\n    }\r\n    const getAdviceByDomain = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByDomain({})\r\n      if (res.result) {\r\n        data.AdviceByDomain = res.result.reverse()\r\n      }\r\n    }\r\n    const suggestGoLink = (type, mType) => {\r\n      // if (mType) {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&manageType=${mType}&token={{token}}`\r\n      // } else {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&token={{token}}`\r\n      // }\r\n    }\r\n    const currentCategory = async () => {\r\n      var res = await $api.leaderDriving.currentCategory({ type: '' })\r\n      if (res.result) {\r\n        data.currentCategoryData = res.result.map(item => {\r\n          return {\r\n            name: item.name,\r\n            proportion: item.proportion,\r\n            value: item.value,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_cate?type=${item.code}&token={{token}}`\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const keywords = async () => {\r\n      var res = await $api.leaderDriving.keywords({})\r\n      if (res) {\r\n        data.keywordsList = res.data.filter(item => item !== '青岛市')\r\n      }\r\n    }\r\n    const getAdviceBySatisfaction = async () => {\r\n      var res = await $api.leaderDriving.getAdviceBySatisfaction({ type: '' })\r\n      var ress = await $api.leaderDriving.getSatisfactionByCategory({ type: '' })\r\n      if (res) {\r\n        data.BySatisfaction = [res.result].map(item => {\r\n          return {\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionBy = ress.result.map(item => {\r\n          return {\r\n            name: item.suggestName,\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getNumberByRepresentative = async () => {\r\n      var res = await $api.leaderDriving.getNumberByRepresentative({ type: '' })\r\n      if (res) {\r\n        data.ByRepresentative = res.result.map(item => {\r\n          return {\r\n            num: item.issueCount,\r\n            name: item.name,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_mylist?personCode=${item.userCode}&token={{token}}`\r\n          }\r\n        }).slice(0, 5)\r\n      }\r\n    }\r\n    const getNumberByDelegation = async () => {\r\n      var res = await $api.leaderDriving.getNumberByDelegation({ type: '' })\r\n      if (res) {\r\n        data.ByDelegation = res.result.map(item => {\r\n          if (item.delegationName !== '解放军代表团') {\r\n            return {\r\n              num: item.adviceTotal,\r\n              name: item.delegationName,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_group?groupId=${item.delegationCode}&token={{token}}`\r\n            }\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const findWygzsTitleList = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })\r\n      data.findWygzsTitleData = res.data\r\n    }\r\n    const findStudioCountByCity = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByCity({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findStudioCountByCityData = res.data[0]\r\n    }\r\n    const findWygzsTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesCountData = res.data\r\n      data.findWygzsTitlesCountData.num = parseFloat(data.findWygzsTitlesCountData.responseRate.replace('%', ''))\r\n      data.findWygzsTitlesCountShow = true\r\n    }\r\n    const findHotspotKeywords = async () => {\r\n      var res = await $api.leaderDriving.findHotspotKeywords({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findHotspotKeywordsData = res.data.filter(item => item !== '测试')\r\n      // console.log('findHotspotKeywords', res.data)\r\n    }\r\n    const findWygzsTitlesRanking = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesRanking({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesRankingData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      })\r\n    }\r\n    const findWygzsStudioTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsStudioTitlesCount({})\r\n      data.findWygzsStudioTitlesCountData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      }).splice(0, 10)\r\n    }\r\n    const dutynumList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: '1',\r\n        pageSize: '5',\r\n        year: y,\r\n        areaId: data.areaId\r\n      })\r\n      data.dutynumList = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username,\r\n          year: y,\r\n          userid: item.userid\r\n        }\r\n      }).splice(0, 5)\r\n    }\r\n    const delegationScore = async (y) => {\r\n      var res = await $api.leaderDriving.delegationScore({\r\n        pageNo: '1',\r\n        pageSize: '10',\r\n        year: data.years\r\n      })\r\n      data.delegationScore = res.data.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.delegationview\r\n        }\r\n      })\r\n    }\r\n    const dutynumCityList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        year: new Date().getFullYear(),\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.pageNot.text = res && res.errcode !== 200 ? res.errmsg || res.data : ''\r\n      var a = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username\r\n        }\r\n      })\r\n      data.dutynumCityList = data.dutynumCityList.concat(a)\r\n      var LOAD_MORE = '点击加载更多'\r\n      var LOAD_ALL = '已加载完'\r\n      data.pageNot.text = data.dutynumCityList.length === 0 ? '' : res.data.dutyNumListVos.length >= data.pageSize ? LOAD_MORE : LOAD_ALL\r\n    }\r\n    const appTodayLogin = async (y) => {\r\n      var res = await $api.leaderDriving.appTodayLogin({\r\n        areaId: data.areaId\r\n      })\r\n      data.appToday.todayLoginNum = Number(res.data.todayLoginNum) // 今日登录人数\r\n      data.appToday.rorfNum = Number(res.data.rorfNum) // 较昨日上升或下降\r\n      data.appToday.riseOrFallNum = Number(res.data.riseOrFallNum) // 上升或下降数量\r\n      data.appToday.todayLoginTimes = Number(res.data.todayLoginTimes) // 今日登陆人次\r\n      data.appToday.rorfTime = Number(res.data.rorfTime) // 较昨日上升或下降\r\n      data.appToday.riseOrFallTimes = Number(res.data.riseOrFallTimes) // 上升或下降数量\r\n    }\r\n    const appAllInstall = async (y) => {\r\n      var res = await $api.leaderDriving.appAllInstall({\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.appToday.num = Number(res.data.rate.replace('%', ''))\r\n    }\r\n    const appLoginActivation = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId == '370215' ? data.areaId : '' }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivation = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appInstall = async () => {\r\n      var res = await $api.leaderDriving.appInstall({ areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.install[0].num = res.data.totalInstall\r\n        data.install[1].num = res.data.memberInstall\r\n        data.install[2].num = res.data.officeInstall\r\n      }\r\n    }\r\n    const areaInstall = async () => {\r\n      var res = await $api.leaderDriving.areaInstall({ areaId: data.areaId })\r\n      if (res) {\r\n        data.areaInstall = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const memberCMemTeamInstallount = async () => {\r\n      var res = await $api.leaderDriving.memberCMemTeamInstallount({ areaId: data.areaId })\r\n      if (res) {\r\n        data.memberCMemTeamInstallount = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByNumTim = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByNumTim({ type: t, areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivationByNumTim = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            nums: item.times\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationCity = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationCity = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByMemOff = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByMemOff({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationByMemOff = res.data.map(item => {\r\n          return {\r\n            numMem: item.numMem,\r\n            numOff: item.numOff,\r\n            name: item.time,\r\n            activationOff: item.activationOff,\r\n            activationMem: item.activationMem\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByTeam = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByTeam({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByTeam = res.data.map(item => {\r\n          return {\r\n            num: item.activation,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            times: item.times\r\n          }\r\n        })\r\n        console.log('data.appLoginByArea===>', data.appLoginByArea)\r\n      }\r\n    }\r\n    const appLoginActivationByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const MessagePage = async (_item) => {\r\n      window.location.href = `http://120.221.72.187:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`\r\n    }\r\n    const massMessagesClick = async () => {\r\n      router.push({ name: 'messageMorePage' })\r\n    }\r\n    const loadMore = async () => {\r\n      var LOAD_MORE = '点击加载更多'\r\n      var NET_ERR = '网络不小心断开了'\r\n      var LOAD_ING = '加载中，请稍候...'\r\n      if ((data.pageNot.text === LOAD_MORE || data.pageNot.text === NET_ERR) && data.pageNo !== 1) {\r\n        data.pageNot.text = LOAD_ING\r\n        data.pageNo++\r\n        dutynumCityList()\r\n      } else {\r\n        data.pageNo = data.pageNo + 1\r\n        dutynumCityList()\r\n      }\r\n    }\r\n    const representativeAll = () => {\r\n      data.isExpanded = !data.isExpanded\r\n    }\r\n    const representativeAll1 = () => {\r\n      data.isExpanded1 = !data.isExpanded1\r\n    }\r\n    const representativeAll2 = () => {\r\n      data.isExpanded2 = !data.isExpanded2\r\n    }\r\n    const representativeClick = (_item) => {\r\n      router.push({ name: 'peopleList', query: { key: _item.key, type: _item.type } })\r\n    }\r\n    return { ...toRefs(data), loadMore, MessagePage, massMessagesClick, representativeAll, representativeAll1, representativeAll2, suggestGoLink, general, confirm, tabClick, representativeTab, dumplingTab, onSelect, dynamic, router, satisfactionAll, representativeClick }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.leaderDriving {\r\n  background: #f8f8f8;\r\n  box-sizing: border-box;\r\n  padding: 15px 10px 10px 10px;\r\n  height: 100%;\r\n\r\n  .satisfaction_title {\r\n    width: 95%;\r\n    margin: 10px 10px 0 10px;\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid #d8d8d8;\r\n  }\r\n\r\n  .satisfaction_item {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    >span {\r\n      font-size: 14px;\r\n      display: inline-block;\r\n      width: 25%;\r\n    }\r\n  }\r\n\r\n  .satisfaction_all {\r\n    text-align: center;\r\n    color: #3894ff;\r\n    margin: 15px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .dynamic_tab {\r\n    width: 100%;\r\n    height: 100%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n\r\n    .dynamic_tab_item {\r\n      width: 50%;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .dynamic_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .sex_pie1 {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 50%;\r\n      height: 120px;\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // justify-content: space-around;\r\n      font-size: 16px;\r\n\r\n      .top {\r\n        display: flex;\r\n        // align-items: center;\r\n        flex-direction: column;\r\n        margin-bottom: 10px;\r\n        font-size: 16px;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        // align-items: center;\r\n        font-size: 16px;\r\n        flex-direction: column;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .popup_con {\r\n    margin: 0px 10px;\r\n\r\n    .popup_con_title {\r\n      text-align: center;\r\n      font-size: 20px;\r\n      margin: 10px 0;\r\n      font-weight: 700;\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .representative_all {\r\n    width: 100%;\r\n    text-align: center;\r\n    color: #a2a2a2;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .representative_tab {\r\n    width: 94%;\r\n    height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n    margin: 0 10px;\r\n\r\n    .representative_tab_item {\r\n      flex: 1;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      color: #3894ff;\r\n      text-align: center;\r\n    }\r\n\r\n    .representative_tab_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .interface_location_box_bot {\r\n    width: 100%;\r\n    height: 80px;\r\n    background: #f8fbfe;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    text-align: center;\r\n\r\n    >div {\r\n      flex: 1;\r\n\r\n      >p {\r\n        margin: 10px 0;\r\n      }\r\n\r\n      p:nth-child(1) {\r\n        color: #8c9fb7;\r\n      }\r\n\r\n      p:nth-child(2) {\r\n        font-weight: 700;\r\n      }\r\n    }\r\n  }\r\n\r\n  .interface_location_box {\r\n    width: 100%;\r\n    height: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 10px;\r\n\r\n    .interface_location_left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      height: 100%;\r\n\r\n      .interface_location_left_title {\r\n        color: #747474;\r\n      }\r\n\r\n      .interface_location_left_bot {\r\n        >span {\r\n          font-weight: 700;\r\n          color: #3894ff;\r\n          font-size: 45px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .interface_location_right {\r\n      width: 37%;\r\n      height: 90px;\r\n      position: relative;\r\n      margin-right: 30px;\r\n\r\n      .text {\r\n        position: absolute;\r\n        top: 26px;\r\n        left: 22px;\r\n        text-align: center;\r\n\r\n        >p:nth-child(1) {\r\n          font-weight: 700;\r\n          font-size: 20px;\r\n        }\r\n\r\n        >p:nth-child(2) {\r\n          font-size: 12px;\r\n          color: #a2a2a2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_satisfaction {\r\n    width: 65%;\r\n    margin: 0 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .satisfaction_item {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 14px;\r\n\r\n      >span {\r\n        width: 14px;\r\n        height: 14px;\r\n        display: inline-block;\r\n        margin: 0 5px;\r\n        border-radius: 7px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message_box {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    >img {\r\n      height: 50px;\r\n      width: 50px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .message {\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    margin: 0 10px;\r\n\r\n    .news_text_box_item {\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 1;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 2px 0;\r\n      font-size: 15px;\r\n    }\r\n\r\n    p:nth-child(1) {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n\r\n  .messageNull {\r\n    text-align: center;\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #aeaeae;\r\n  }\r\n\r\n  .content_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .hotWord {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    height: 35px;\r\n    padding: 5px 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    .hotWord_item {\r\n      display: flex;\r\n      width: 70%;\r\n      align-items: center;\r\n\r\n      .index {\r\n        margin: 0 10px 0 0;\r\n      }\r\n    }\r\n\r\n    .hotWord_right {\r\n      color: #fff;\r\n      // padding: 3px;\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 3px;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .suggest_box {\r\n    display: flex;\r\n    color: #fff;\r\n\r\n    .suggest_transaction {\r\n      margin-left: 10px;\r\n      margin-bottom: 5px;\r\n\r\n      >span {\r\n        font-size: 22px;\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .suggest_meet {\r\n      flex: 1;\r\n      margin: 0 5px;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg1.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .suggest_flat {\r\n      margin: 0 5px;\r\n      flex: 1;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg2.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_title {\r\n    height: 24px;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    margin: 5px 10px 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    >div {\r\n      width: 29%;\r\n      color: #3894ff;\r\n    }\r\n  }\r\n\r\n  .suggest_num {\r\n    color: #3894ff;\r\n    margin-right: 10px;\r\n\r\n    >span {\r\n      font-size: 28px;\r\n    }\r\n  }\r\n\r\n  .sex_pie {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 60%;\r\n      height: 120px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      font-size: 18px;\r\n\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .leaderDriving_top {\r\n    width: 100%;\r\n    height: 100px;\r\n    // margin: 15px 10px 0;\r\n    background: url(\"../../assets/img/ldjsc_head_bg.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  .leaderDriving_tab {\r\n    margin-top: 10px;\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_tab_item {\r\n      width: 60px;\r\n      height: 60px;\r\n      background: #fff;\r\n      border-radius: 30px;\r\n      text-align: center;\r\n      box-sizing: border-box;\r\n      padding: 10px;\r\n      box-shadow: 0px 5px 15px -3px rgba(138, 138, 138, 0.1);\r\n    }\r\n\r\n    .leaderDriving_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .leaderDriving_title {\r\n    width: 100%;\r\n    height: 30px;\r\n    margin: 10px 0;\r\n    color: #3894ff;\r\n    padding-left: 10px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    position: relative;\r\n  }\r\n\r\n  .leaderDriving_title::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    height: 18px;\r\n    width: 4px;\r\n    top: 4px;\r\n    left: 0px;\r\n    background: #3894ff;\r\n    border-radius: 1px;\r\n  }\r\n\r\n  .leaderDriving_generalize {\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_generalize_item {\r\n      // width: 32%;\r\n      flex: 1;\r\n      height: 100%;\r\n      // padding-left: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .leaderDriving_generalize_item_num {\r\n        font-size: 28px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title {\r\n        color: #8196af;\r\n        // display: flex;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title_span {}\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div class="searchCriteria">
    <div class="handle_item">
      <van-collapse v-model="activeNameobj.activeNames">
        <van-collapse-item title="状态"
                           name="1">
          <div class="select_box">
            <div :class="{ 'select_box_item': true, 'select_box_items': searchList.seachStatus == item.value }"
                 @click="searchList.seachStatus ? searchList.seachStatus = '' : searchList.seachStatus = item.value"
                 v-for="item in stateLists"
                 :key="item.value">{{ item.name }}</div>
          </div>
        </van-collapse-item>
      </van-collapse>
      <van-collapse v-model="activeNameobj.activeNames1">
        <van-collapse-item title="问题类别"
                           name="1">
          <div class="select_box">
            <div :class="{ 'select_box_item': true, 'select_box_items': searchList.typeStatus == item.id }"
                 @click="searchList.typeStatus ? searchList.typeStatus = '' : searchList.typeStatus = item.id"
                 v-for="item in typeLists"
                 :key="item.id">{{ item.text }}</div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>
    <div class="button_box">
      <button class="clear"
              @click="clear">清除</button>
      <button class="determine"
              @click="determine">确定</button>
    </div>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { Calendar, Checkbox, CheckboxGroup, Form, Field, CellGroup, NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Collapse, CollapseItem } from 'vant'
export default {
  name: 'searchCriteria',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [CellGroup.name]: CellGroup,
    [Field.name]: Field,
    [Form.name]: Form,
    [Calendar.name]: Calendar,
    [Collapse.name]: Collapse,
    [CollapseItem.name]: CollapseItem,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup
  },
  props: ['rowId', 'dateText'],
  setup (props, { emit }) {
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const store = useStore()
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      areaId: JSON.parse(sessionStorage.getItem('areaId')).toString(),
      user: JSON.parse(sessionStorage.getItem('user')),
      handleRemarks: '',
      activeNameobj: {
        activeNames: ['1'],
        activeNames1: ['1'],
        activeNames2: [],
        activeNames3: [],
        activeNames4: [],
        activeNames5: [],
        activeNames6: [],
        activeNames7: []
      },
      searchList: {
        seachStatus: '',
        typeStatus: ''
      },
      stateLists: [
        { name: '待办理', value: '1' },
        { name: '已转办', value: '2' },
        { name: '已办结', value: '3' },
        // { name: '退回', value: '4' },
        { name: '二次交办', value: '5' },
        { name: '已评价', value: '6' },
        { name: '正在办理', value: '11' }
      ],
      typeLists: [
      ]
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      getpubkvs()
    })
    const getpubkvs = async () => {
      const res = await $api.general.pubkvs({
        types: 'photograph_message_type'
      })
      var { data: list } = res
      data.typeLists = list.photograph_message_type.map(item => {
        return {
          text: item.value,
          id: item.id
        }
      })
      seInfo()
    }
    const seInfo = () => {
      data.searchList = store.state.searchList
    }
    const determine = async () => {
      store.commit('setsearchList', data.searchList)
      emit('callback', false)
    }
    const clear = () => {
      data.searchList = {
        seachStatus: '',
        typeStatus: ''
      }
      store.commit('setsearchList', data.searchList)
      emit('callback', false)
    }
    const tagClick = (val) => {
      data.handleRemarks = data.handleRemarks ? data.handleRemarks + ',' + val : val
    }
    return { ...toRefs(data), tagClick, $general, confirm, clear, determine, dayjs }
  }
}
</script>
<style lang="less"
       scoped>
      .searchCriteria {
        background: #fff;
        box-sizing: border-box;

        .search_tiem {
          display: flex;
          align-items: center;

          .search_tiem_item {
            width: 40%;
            height: 35px;
            margin: 5px auto;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            border: 1px solid #f1f1f1;
            box-sizing: border-box;
            font-size: 14px;

            .notes {
              display: flex;
              align-items: center;
              justify-content: center;
              margin: 2px 5px 0 5px;
              font-size: 16px;
            }
          }
        }

        .keyword_inp {
          width: 90%;
          background: #f9f9f9;
          border-radius: 20px;
          height: 35px;
          margin: 5px auto;
          padding-left: 10px;
          font-size: 16px;
        }

        .name_inp {
          width: 100%;
          background: #f9f9f9;
          border-radius: 20px;
          height: 30px;
          padding-left: 10px;
          font-size: 16px;
        }

        .select_box {
          display: flex;
          flex-wrap: wrap;

          // justify-content: space-between;
          .select_box_item {
            width: 30%;
            height: 35px;
            margin: 5px 5px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            border: 1px solid #f1f1f1;
            box-sizing: border-box;
            font-size: 14px;
          }

          .select_box_items {
            border: 1px solid #59a0f3;
            color: #59a0f3;
            background: #e3f0ff;
          }
        }

        ::v-deep.van-cell:after {
          display: none;
          border: 0px !important;
        }

        ::v-deep.van-collapse-item__title:after {
          display: none;
        }

        .button_box {
          width: 100%;
          position: fixed;
          bottom: 10px;
          left: 0%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 10px;
        }

        .opinion_inputs {
          display: flex;
          margin-top: 10px;
          flex-wrap: wrap;

          >span {
            margin: 0 10px 5px 0px;
          }
        }

        .determine {
          width: 65%;
          border-radius: 3px;
          height: 40px;
          display: block;
          background: #006fef;
          color: #fff;
          font-size: 16px;
          margin: 10px 0;
        }

        .clear {
          width: 30%;
          border-radius: 3px;
          height: 40px;
          display: block;
          background: #f6f6f6;
          color: #7a7a7a;
          font-size: 16px;
          margin: 10px 0;
        }

        .handle_item {
          border-bottom: 1px solid #f7f7f7;

          ::v-deep .van-cell__title {
            display: flex;
            align-items: center;
          }

          .label_boxs {
            margin-top: 10px;
            box-sizing: border-box;
            width: 100%;

            ::v-deep .van-checkbox {
              font-size: 12px;
              margin: 3px;
              width: 48%;
            }

            ::v-deep .van-checkbox__label {
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 12px !important;
            }

            .time_box {
              width: 120px;
              height: 20px;
              font-size: 12px !important;
              border: 1px solid #c0c0c0;
              display: flex;
              align-items: center;
              // justify-content: space-around;
              border-radius: 3px;
            }
          }

          .label_box {
            margin-top: 10px;
            box-sizing: border-box;
            width: 100%;

            ::v-deep .van-checkbox {
              font-size: 12px;
              margin: 3px;
            }

            ::v-deep .van-checkbox__label {
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 12px !important;
            }

            .time_box {
              width: 120px;
              height: 20px;
              font-size: 12px !important;
              border: 1px solid #c0c0c0;
              display: flex;
              align-items: center;
              justify-content: space-around;
              border-radius: 3px;
            }
          }

          .label_text {
            margin: 15px 15px;
            box-sizing: border-box;
          }

          .opinion_input {
            width: 100%;
            height: 100px;
            background: #f8f8f8;
            margin-top: 10px;
            border-radius: 8px;
            box-sizing: border-box;

            textarea {
              width: 100%;
              height: 100%;
              resize: none;
              padding: 10px;
              box-sizing: border-box;
            }
          }
        }
      }
    </style>

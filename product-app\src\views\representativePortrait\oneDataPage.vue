<template>
  <div class="annualReport"
       @touchstart="handleTouchStart"
       @touchmove="handleTouchMove"
       @touchend="handleTouchEnd">
    <div style="position: absolute;top: 10px;right: 10px;z-index: 2;">
      <audio ref="audioPlayer"
             id="play1"
             loop>
        <source src="../../assets/img/audio/yekongdejijing.mp3"
                type="audio/mpeg">
      </audio>
      <div class="flex_box flex_align_center">
        <div v-if="!isPlaying"
             style="color:#fff;font-size: 12px;margin-right: 10px;">打开音乐，体验更佳哦！</div>
        <div>
          <img v-if="!isPlaying"
               src="../../assets/img/audio/g_musicOff.png"
               alt=""
               style="width: 42px;height: 40px;"
               @click.stop="playMusic"
               :class="{ rotate: !isPlaying }">
          <img v-else
               src="../../assets/img/audio/g_playMusic.png"
               alt=""
               style="width: 34px;height: 34px;"
               @click.stop="stopMusic"
               :class="{ rotate: isPlaying }">
        </div>
      </div>
    </div>
    <transition name="fade"
                mode="out-in"
                ref="transition">
      <div class="container_swipe">
        <Page9 class="page"
               :showText1="page9ShowText1"
               :showText2="page9ShowText2"
               :showText3="page9ShowText3"
               :class="{ 'active': currentPage === 1 }"></Page9>
      </div>
    </transition>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
import Page9 from './component/Page9.vue'
export default {
  name: 'annualReport',
  components: {
    Page9,
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  setup () {
    // const router = useRouter()
    const $api = inject('$api')
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const startX = ref(0)
    const startY = ref(0)
    const startTime = ref(0)
    const isScrolling = ref(false)
    const currentPage = ref(1)
    const isPlaying = ref(false)
    const audioPlayer = ref(null)
    const transition = ref(null)
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      typePlay: route.query.typePlay || false,
      userName: '',
      page9ShowText1: false,
      page9ShowText2: false,
      page9ShowText3: false,
      pageData: {}
    })
    if (data.title) {
      document.title = data.title
    }
    watch(currentPage, (newPage, oldPage) => {
      switch (newPage) {
        case 1:
          executePage9Code()
          break
        // 其他页面的逻辑...
        default:
          break
      }
    })
    onMounted(() => {
      // data.userName = data.user.userName
      preventScroll()
      executePage9Code()
      memberPortraitDutyTime()
      if (data.typePlay) {
        audioPlayer.value.play()
        isPlaying.value = true
      }
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const handleOpenPage = () => {
      console.log('父组件打印的')
      currentPage.value = 1
      audioPlayer.value.play()
      isPlaying.value = true
    }
    const playMusic = () => {
      audioPlayer.value.play()
      isPlaying.value = true
    }
    const stopMusic = () => {
      audioPlayer.value.pause()
      isPlaying.value = false
    }
    const executePage9Code = () => {
      setTimeout(() => {
        data.page9ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page9ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page9ShowText3 = true
      }, 3000)
    }

    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
      const touch = event.touches[0]
      startX.value = touch.clientX
      startY.value = touch.clientY
      startTime.value = Date.now()
      isScrolling.value = false
    }
    const handleTouchMove = (event) => {
      const touch = event.touches[0]
      const deltaX = Math.abs(touch.clientX - startX.value)
      const deltaY = Math.abs(touch.clientY - startY.value)
      if (deltaY > deltaX) {
        isScrolling.value = true
      }
    }
    const handleTouchEnd = (event) => {
      const touch = event.changedTouches[0]
      const endX = touch.clientX
      const endY = touch.clientY
      const endTime = Date.now()
      const deltaX = Math.abs(endX - startX.value)
      const deltaY = Math.abs(endY - startY.value)
      const duration = endTime - startTime.value
      touchMoveY.value = event.changedTouches[0].clientY
      if (!isScrolling.value && deltaX < 10 && deltaY < 10 && duration < 300) {
        // 触发点击事件
        console.log('触发点击事件')
      } else if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        if (currentPage.value < 1) {
          currentPage.value++
        }
      } else {
        // 向下滑动
        console.log('向下滑动')
        if (currentPage.value > 1) {
          currentPage.value--
        }
      }
    }
    // 获取数据
    const memberPortraitDutyTime = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyTime()
      data.pageData = res.data
    }
    return { ...toRefs(data), $general, handleTouchStart, currentPage, handleTouchMove, handleTouchEnd, isPlaying, audioPlayer, playMusic, stopMusic, handleOpenPage, transition }
  }
}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiYuan-2";
  src: url("../../assets/img/timeAxis/font/YouSheBiaoTiYuan-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}

.annualReport {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
  }
  .page {
    opacity: 0;
    transition: opacity 3s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    pointer-events: auto;
  }

  .active {
    opacity: 1;
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 3s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }
  .rotate {
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>

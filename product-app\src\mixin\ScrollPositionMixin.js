// export default {
//   data () {
//     return {
//       scrollPosition: 0
//     }
//   },
//   mounted () {
//     this.$nextTick(() => {
//       this.scrollPosition = sessionStorage.getItem('scrollPosition') || 0
//       window.scrollTo(0, this.scrollPosition)
//     })
//   },
//   beforeRouteLeave (to, from, next) {
//     this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop
//     sessionStorage.setItem('scrollPosition', this.scrollPosition)
//     next()
//   }
// }

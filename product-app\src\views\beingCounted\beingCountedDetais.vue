<template>
  <div class="beingCountedDetais">
    <div class="n_details_content">
      <div class="representativeCircle_box_li">
        <div class="representativeCircle_box_top">
          <img :src="representativeDetails.headImg"
               alt=""
               class="representativeCircle_box_top_headImg">
          <div class="representativeCircle_box_name">
            <p class="representativeCircle_box_names">{{ representativeDetails.publishName }}</p>
            <p class="representativeCircle_box_congressStr"> {{ representativeDetails.congressStr+'人大代表' }}</p>
          </div>
          <template>
            <div v-if="user.id!=representativeDetails.publishBy"
                 :class="{'attention':true,'attentionDel':isFollow==1 }"
                 @click="attentionEdit">
              {{ isFollow==1?'已关注':'+ 关注' }}
            </div>
            <div class="representativeCircle_box_del"
                 @click="committeesayDel(representativeDetails.id)"
                 v-else>
              <van-icon name="delete-o" />
            </div>
          </template>
        </div>
        <div class="representativeCircle_box_center">
          <div class="representativeCircle_box_center_content"
               v-html="representativeDetails.content">
          </div>
          <div class="representativeCircle_box_center_attachmentList">
            <van-image position="contain"
                       width="2.5rem"
                       fit="cover"
                       height="2rem"
                       v-for="it in representativeDetails.attachmentList"
                       :key="it.id"
                       :src="it.filePath"
                       @click="previewCalback(representativeDetails.attachmentList)" />
          </div>
        </div>
        <div class="representativeCircle_box_buttom">
          <div class="representativeCircle_box_buttom_time">{{ representativeDetails.publishDate }}</div>
          <div class="representativeCircle_box_buttom_cont">
            <div class="representativeCircle_box_buttom_conmment"
                 @click.stop="openMoreComment()">
              <span>{{ commentCount }}</span>
              <van-icon name="comment-o" />
            </div>
            <div class="representativeCircle_box_buttom_link"
                 @click="downLike(conmmentList)">
              <span>{{ conmmentList.fabulousCount }}</span>
              <van-icon name="good-job-o"
                        :style="conmmentList.isFabulous==='1' ?'color:#3088fe':''" />
            </div>
          </div>
        </div>
        <div class="likeComment_box">
          <div class="like_box"
               :style="$general.loadConfiguration(-4)">
            <van-icon name="like-o"
                      v-if="menuList != null && menuList.length" />
            <span :style="$general.loadConfiguration(-4)+'margin-bottom: 2px;' + 'line-height: 0.2rem;'"
                  @click.stop="openUserList(it.id)"
                  v-for="it,ind in menuList"
                  :key="ind">{{ind>0?',':''}} {{it.name}} </span>
          </div>
          <div class="comment_box"
               @click.stop=""
               v-if="commentList.length!=0 ">
            <div v-for="items,index in commentList"
                 :key="index">
              <p style="display: flex;align-items: center;">
                <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'"
                      @click.stop="openUserList(items.createBy)">{{items.name}}: </span>
                <span :style="$general.loadConfiguration(-4)+'flex:1;'"
                      @click.stop="openMoreComment(items,index)"> {{items.content}} </span>
              </p>
              <p v-for="(ite,ind) in items.commentList"
                 :key="ind"
                 :style="$general.loadConfiguration(-4)">
                <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'"
                      @click.stop="openUserList(ite.createBy)">{{ite.name}}</span> 回复
                <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'"
                      @click.stop="openUserList(items.createBy)">{{items.name}}: </span>
                <span :style="$general.loadConfiguration(-4)"
                      @click.stop="openMoreComment(items,index)"> {{ite.content}} </span>
              </p>
            </div>
          </div>
          <div class="reply_box"
               @click.stop="">
            <div class="reply_box_item">
              <input type="text"
                     v-model="inputInfo.value"
                     :style="$general.loadConfiguration(-4)"
                     class="reply_box_inp"
                     :placeholder="inputInfo.placeholder">
              <button :class="inputInfo.value!='' ? 'reply_box_but' : 'reply_box_buts'"
                      :style="$general.loadConfiguration(-4)"
                      @click="mgcHandle()"
                      :disabled="inputInfo.disabled">发送</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { ImagePreview, Image as VanImage, Dialog, Grid, Toast } from 'vant'
export default {
  name: 'beingCountedDetais',
  components: {
    [Grid.name]: Grid,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      details: {},
      content: '', // 正文内容
      type: route.query.types,
      inputBox: null,
      ifIike: route.query.ifIike || '',
      commentCount: 0,
      fabulousCount: route.query.fabulousCount || 0,
      representativeDetails: {},
      conmmentList: {},
      commentList: [],
      menuList: [],
      pingData: '',
      pingIndex: '',
      inputInfo: { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送', disabled: false }
    })
    onMounted(() => {
      committeesayAppInfo()
      getCommentList()
      getFabulousList()
    })
    const onRefresh = () => {
      committeesayAppInfo()
      getCommentList()
      getFabulousList()
    }
    // 获取点赞列表
    const getFabulousList = async () => {
      const res = await $api.general.getFabulousList({
        pageNo: 1,
        pageSize: 100,
        keyId: data.id,
        type: '25',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      if (res) {
        var list = res ? res.data || [] : []
        var dataLength = list ? list.length : 0
        if (list) {
          data.menuList = []
          for (var i = 0; i < dataLength; i++) {
            var item = {}
            var itemData = list[i]
            item.id = itemData.createBy // id
            item.name = itemData.userName // 标题
            item.url = itemData.headImg // 图标地址
            data.menuList.push(item)
          }
        }
      }
    }
    // 点赞
    const downLike = (_item) => {
      if (data.conmmentList.isFabulous === '1') {
        data.conmmentList.fabulousCount--
        data.menuList = data.menuList.filter(item => item.id !== data.user.id)
      } else {
        data.conmmentList.fabulousCount++
        data.menuList.push({ id: data.user.id, name: data.user.userName })
      }
      fabulousInfo(data.conmmentList.isFabulous, data.id)
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status === '0' ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '25'
      }
      await $api.general.fabulous({ url, params })
      committeesayAppInfo()
    }
    // 发送
    const mgcHandle = async () => {
      if (!data.inputInfo.value) {
        return
      }
      var url = 'comment/save'
      var params = {
        content: data.inputInfo.value,
        createBy: data.user.id,
        commentPid: data.pingData ? data.pingData.id : '',
        keyId: data.id,
        attach: '',
        extend: '1',
        type: '25',
        isCheck: '1',
        areaId: '370200'
      }
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        data.commentList = []
        getCommentList()
        data.inputInfo.value = ''
      } else {
        Toast('请求失败。')
      }
    }
    // 获取评论列表
    const getCommentList = async () => {
      const res = await $api.general.getCommentList({
        pageNo: 1,
        pageSize: 99,
        keyId: data.id,
        type: '',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      console.log('获取评论列表===>>', res)
      var list = res ? res.data || [] : []
      data.commentCount = res ? res.total : 0
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          var item = {}
          var itemData = _eItem
          item.hasDelete = itemData.createBy === data.user.id
          item.createBy = itemData.createBy
          // id
          item.id = itemData.id || ''
          // id
          item.extend = itemData.extend || ''
          // 来源
          item.url = itemData.userHeadImg || '../../../images/icon_default_user.png'
          // 用户头像
          item.name = itemData.userName || '匿名用户'
          item.content = itemData.content
          // 评论中的图片
          var resultBatchAttach = itemData.filePathList || []
          var resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
          for (var p = 0; p < resultBatchAttachLength; p++) {
            var attachpath = resultBatchAttach[p].fullUrl || ''
            var resultBatchAttachItem = {
              url: attachpath
            }
            item.nAttach.push(resultBatchAttachItem)
          }
          item.commentList = []
          var Commentlist = itemData.children || []
          var CommentlistLength = Commentlist ? Commentlist.length : 0
          for (var j = 0; j < CommentlistLength; j++) {
            var item2 = {}
            var itemData2 = Commentlist[j]
            item2.hasDelete = itemData2.createBy === data.user.id
            item.createBy = itemData2.createBy
            // id
            item2.id = itemData2.id || ''
            // id
            item2.extend = itemData2.extend || ''
            // 来源
            item2.url = itemData2.userHeadImg || ''
            // 用户头像
            item2.name = itemData2.userName || '匿名用户'
            // 用户名
            item2.createTime = itemData2.dateDetail || ''
            item2.content = itemData.content
            // 是否审核,0待审核，1审核通过，2审核不通过
            item2.nAttach = []
            // 评论中的图片
            const resultBatchAttach = itemData2.filePathList || []
            const resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
            for (var k = 0; k < resultBatchAttachLength; k++) {
              const attachpaths = resultBatchAttach[k].fullUrl || ''
              const resultBatchAttachItem = {
                url: attachpaths
              }
              item2.nAttach.push(resultBatchAttachItem)
            }
            item.commentList.push(item2)
          }
          data.commentList.push(item)
        })
      }
    }
    // 点关注
    const attentionEdit = async () => {
      var type = ''
      if (data.isFollow === 1) {
        type = 'del'
        data.isFollow = 0
      } else {
        type = 'add'
        data.isFollow = 1
      }
      const res = await $api.beingCounted.attention({ params: { followId: data.publishBy, type: '25' }, type })
      if (res.errcode === 200) {
        onRefresh()
      }
    }
    // 预览大图
    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    // 获取详情
    const committeesayAppInfo = async () => {
      const res = await $api.beingCounted.getRepresentativeDetails({
        id: data.id
      })
      var { data: list } = await $api.general.getCommentStats({
        keyId: data.id,
        type: '25',
        areaId: data.user.areaId
      })
      data.conmmentList = list
      data.representativeDetails = res.data
    }
    // 删除
    const committeesayDel = (id) => {
      Dialog.confirm({
        title: '温馨提示',
        message: '您将删除本条数据！'
      }).then(async () => {
        // on confirm
        const res = await await $api.beingCounted.committeesayAppDels(id)
        if (res) {
          onRefresh()
          router.push('/beingCountedList')
        }
      }).catch(function () {
        // on cancel
      })
    }
    // 打开附件
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    // 点击评论中的评论框
    const openMoreComment = (_item, _index) => {
      data.pingData = _item || ''
      data.pingIndex = _index || ''
      if (_item) {
        data.inputInfo = { value: '', placeholder: '回复' + _item.name, name: '评论', btn: '回复' }
      } else {
        data.inputInfo = { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送' }
      }
    }
    // 打开用户列表
    const openUserList = (_item) => {
      router.push({ path: '/committeesayUserList', query: { uId: _item } })
    }
    return { ...toRefs(data), previewCalback, mgcHandle, openMoreComment, downLike, committeesayDel, attentionEdit, onRefresh, annexClick, $general, openUserList }
  }
}
</script>
<style lang="less">
.beingCountedDetais {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .n_details_content {
    margin: 10px;
    .representativeCircle_box_li {
      width: 100%;
      padding-bottom: 5px;
      border-bottom: 1px solid #e5e5e5;
      .representativeCircle_box_top {
        width: 100%;
        height: 35px;
        margin: 5px 0;
        display: flex;
        align-items: center;
        position: relative;
        .attention {
          text-align: center;
          position: absolute;
          top: 0;
          right: 10px;
          width: 80px;
          height: 80%;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 40px;
          color: #3894ff;
          border: 1px solid #3894ff;
        }
        .attentionDel {
          color: #666;
          border: 1px solid #666;
        }
        .representativeCircle_box_top_headImg {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          margin: 5px;
        }
        .representativeCircle_box_name {
          font-size: 16px;
          .representativeCircle_box_congressStr {
            font-size: 14px;
            color: #4c4c4c;
          }
        }
        .representativeCircle_box_del {
          position: absolute;
          top: 0;
          right: 10px;
        }
      }
      .representativeCircle_box_center {
        box-sizing: border-box;
        .representativeCircle_box_center_content {
          padding-left: 13px;
          margin: 5px 0;
        }
        .representativeCircle_box_center_attachmentList {
          width: 95%;
          margin: auto;
          display: flex;
          flex-wrap: wrap;
          // justify-content: space-between;
          .van-image {
            margin: 5px;
          }
        }
      }
      .representativeCircle_box_buttom {
        width: 100%;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .representativeCircle_box_buttom_time {
          width: 70%;
          font-size: 14px;
          padding-left: 10px;
          color: #a8a8a8;
        }
        .representativeCircle_box_buttom_cont {
          width: 25% !important;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .representativeCircle_box_buttom_conmment {
            display: flex;
            align-items: center;
            justify-content: space-between;
            > span {
              font-size: 14px;
              margin-right: 4px;
            }
            > img {
              width: 16px;
              height: 16px;
              margin-right: 5px;
            }
          }
          .representativeCircle_box_buttom_link {
            // display: flex;
            // align-items: center;
            // justify-content: space-between;
            line-height: 100%;
            padding-right: 10px;
            > span {
              margin-right: 10px;
              font-size: 14px;
            }
            > img {
              width: 16px;
              height: 16px;
              margin-right: 5px;
            }
          }
        }
        /*内容的样式 处理内容的样式*/
        .n_details_content {
          width: 100%;
          padding: 10px;
          box-sizing: border-box;
          padding-top: 0;
          img {
            margin: 15px 0;
            width: 100% !important;
          }
        }
        .n_details_content * {
          font-size: inherit;
          font-family: inherit;
          word-break: normal !important;
          text-align: justify;
        }
      }
      .likeComment_box {
        background: #f7f7f7;
        margin: 0 0 10px;
        overflow: hidden;
        box-sizing: border-box;
        border-radius: 5px;
        .comment_box {
          margin: 0 5px 0px;
        }
        .like_box {
          color: #6e7fa3;
          margin: 5px 5px;
        }
        .reply_box {
          background: #f7f7f7;
          margin: 5px 5px 0;
          padding: 5px 0 0 0;
          border-top: 1px solid #e8e8e8;
          height: 50px;
          .reply_box_item {
            width: 100%;
            background: #fff;
            height: 100%;
            border-radius: 5px;
            border: 1px solid #3895ff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-sizing: border-box;
            padding: 0 5px;
            .reply_box_but {
              width: 60px;
              border-radius: 5px;
              height: 80%;
              color: #fff;
              background: #3895ff;
            }
            .reply_box_buts {
              color: rgb(112, 112, 112);
              background: #bdbdbd;
              width: 60px;
              border-radius: 5px;
              height: 80%;
            }
            .reply_box_inp {
              height: 80%;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="beingCountedList">
    <van-tabs v-model:active="switchs.value" :color="appTheme" sticky :offset-top="isShowHead ? '46px' : '0'"
      :title-active-color="appTheme" :ellipsis="false" @click-tab="onClickTab">
      <van-tab v-for="(item, index) in switchs.data" :key="index" :title="item.label" :name="item.value">
        <template #title>
          <van-badge :content="unread[item.unread] != 0 ? unread[item.unread] : ''">
            {{ item.label }}
          </van-badge>
        </template>
        <van-search v-model="keyword" @search="search" @clear="search" placeholder="请输入搜索关键词" />
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad"
            :immediate-check="false">
            <van-cell v-for="item in dataList" :key="item.id">
              <!-- 履职圈列表 -->
              <!-- @click="skipDetails(item)" -->
              <!--  @click="skipDetails(item.id, item.publishBy,item.isFabulous,item.fabulousCount)"  -->
              <div class="representativeCircle_box_li" @click.stop="openUserList(item.publishBy)">
                <div class="representativeCircle_box_top">
                  <img :src="item.headImg" alt="" class="representativeCircle_box_top_headImg">
                  <div class="representativeCircle_box">
                    <div class="representativeCircle_box_name">{{ item.publishName }}</div>
                    <p class="representativeCircle_box_congressStr" v-if="item.congressStr.length > 0"> {{
                      item.congressStr.join(' | ') + '人大代表' }}
                      {{ item.representerTeam ? '(' + item.representerTeam + ')' : '' }}
                    </p>
                    <div v-if="user.id != item.publishBy"
                      :class="{ 'attention': true, 'attentionDel': item.isFollow == '1' }"
                      @click.stop="attentionEdit(item.publishBy, item.isFollow)">
                      {{ item.isFollow == '1' ? '已关注' : '+ 关注' }}
                    </div>
                    <div class="representativeCircle_box_del" @click.stop="committeesayDel(item.id)" v-else><van-icon
                        name="delete-o" />
                    </div>
                  </div>
                </div>
                <div class="representativeCircle_box_center">
                  <div class="representativeCircle_box_center_content" v-html="item.content">
                  </div>
                </div>
                <div v-if="item.attachmentList && item.attachmentList.length != 0">
                  <van-grid :border="false" :gutter="10" :column-num="3" class="house_content_img_bg">
                    <van-grid-item class="house_content_img" v-for="(nItem, nIndex) in item.attachmentList"
                      :key="nItem.id" @click.stop="previewCalback(item.attachmentList, nIndex)">
                      <van-image fit="cover" width="100%" height="100%" :src="nItem.filePath"
                        style="margin: 5px 5px;"></van-image>
                    </van-grid-item>
                  </van-grid>
                </div>
                <div class="representativeCircle_box_buttom">
                  <div class="representativeCircle_box_buttom_time flex_placeholder">{{ item.publishDate }}</div>
                  <div class="representativeCircle_box_buttom_cont">
                    <div class="representativeCircle_box_buttom_comment" @click.stop="replyClick(item)">
                      <img :src="require('../../assets/img/icon_comments.png')" alt="">
                      <span>{{ item.commentCount }}</span>
                    </div>
                    <div class="representativeCircle_box_buttom_like" @click.stop="downLike(item)">
                      <img
                        :src="require(!item.isFabulous ? '../../assets/img/icon_likes.png' : '../../assets/img/icon_likes_on.png')"
                        alt="">
                      <span>{{ item.fabulousCount }}</span>
                    </div>
                  </div>
                </div>
                <div class="likeComment_box">
                  <div class="like_box" :style="$general.loadConfiguration(-4)"
                    v-if="item.fabulousList != null && item.fabulousList.length">
                    <van-icon name="like-o" v-if="item.fabulousList != null && item.fabulousList.length" />
                    <span :style="$general.loadConfiguration(-4) + 'margin-bottom: 2px;' + 'line-height: 0.2rem;'"
                      @click.stop="openUserList(it.id)" v-for="(it, ind) in item.fabulousList" :key="ind">
                      {{ ind > 0 ? ',' : '' }} {{ it.userName }} </span>
                  </div>
                  <div class="comment_box" @click.stop="" v-if="item.children.length != 0">
                    <div v-for="(items, indexs) in item.children" :key="indexs">
                      <p style="display: flex;align-items: center;">
                        <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                          @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType
                          }}:
                        </span>
                        <span :style="$general.loadConfiguration(-4) + 'flex:1;'"
                          @click.stop="replyClick(item, items, indexs)">
                          {{ items.content }} </span>
                      </p>
                      <p v-for="(ite, inde) in items.children" :key="inde" :style="$general.loadConfiguration(-4)">
                        <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                          @click.stop="openUserList(ite.createBy)">{{ ite.userName }}</span> 回复
                        <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                          @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType
                          }}:
                        </span>
                        <span :style="$general.loadConfiguration(-4)" @click.stop="replyClick(item, items, inde)"> {{
                          ite.content }} </span>
                      </p>
                    </div>
                  </div>
                  <div class="reply_box" @click.stop="" v-show="item.inputObj.replyShow">
                    <div class="reply_box_item">
                      <input type="text" v-model="item.commentObj.content" :style="$general.loadConfiguration(-4)"
                        class="reply_box_inp" :placeholder="item.inputObj.replyName">
                      <button :class="item.commentObj.content != '' ? 'reply_box_but' : 'reply_box_buts'"
                        :style="$general.loadConfiguration(-4)"
                        @click="transmitClick(item.commentObj, item.inputObj)">发送</button>
                    </div>
                  </div>
                </div>
              </div>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <!-- 发布 -->
    <div class="issue" @click="issueAdd">发布</div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog, Toast, Grid, Badge } from 'vant'
export default {
  name: 'beingCountedList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Grid.name]: Grid,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component,
    [Badge.name]: Badge
  },
  setup () {
    const router = useRouter()
    // const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      identification: 0,
      unread: {
        allUnreadNum: '0',
        followUnreadNum: '0',
        myUnreadNum: '0'
      },
      committeesayUnread: '',
      switchs: { value: '', data: [{ label: '全部', value: '0', unread: 'allUnreadNum' }, { label: '已关注', value: '1', unread: 'followUnreadNum' }, { label: '我的', value: '2', unread: 'myUnreadNum' }] }
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      onRefresh()
      saveBrowseQingdao()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList(data.identification)
      getUnread()
    }
    // committeesayUnread
    const getUnread = async () => {
      const res = await $api.general.committeesayUnread({
        isFollow: 1,
        publishBy: 1
      })
      data.unread = res.data
      data.committeesayUnread = Object.values(res.data).reduce(function (accumulator, currentValue) {
        return accumulator + currentValue
      }, 0)
      sessionStorage.setItem('committeesayUnread', data.committeesayUnread)
    }
    const saveBrowseQingdao = async () => {
      const res = await $api.general.saveBrowseQingdao({
        keyIdList: '',
        type: 25,
        areaId: data.areaId
      })
      console.log(res)
    }
    // 打开用户列表
    const openUserList = (_item) => {
      router.push({ path: '/committeesayUserList', query: { uId: _item } })
    }
    // 删除
    const committeesayDel = (id) => {
      Dialog.confirm({
        title: '温馨提示',
        message: '您将删除本条数据！'
      }).then(async () => {
        // on confirm
        const res = await $api.beingCounted.committeesayDels({ ids: id })
        if (res) {
          Toast('删除成功')
          onRefresh()
        }
      }).catch(function () {
        // on cancel
      })
    }
    // 搜索
    const search = async () => {
      onRefresh()
    }
    // 跳详情
    const skipDetails = (_id, publishBy, isFabulous, fabulousCount) => {
      router.push({ path: '/beingCountedDetais', query: { id: _id, types: 25, publishBy, ifIike: isFabulous, fabulousCount: fabulousCount } })
    }
    // 发布
    const issueAdd = () => {
      router.push({ name: 'add', query: { paramType: 'resumption' } })
    }
    // 点击tab
    const onClickTab = async ({ title }) => {
      data.keyword = ''
      if (title === '全部') {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        data.identification = 0
        getList(0)
      } else if (title === '已关注') {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        data.identification = 1
        getList(1)
      } else if (title === '我的') {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        data.identification = 2
        getList(2)
      }
    }
    // 预览图片
    const previewCalback = (item) => {
      console.log(item)
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    // 点击点赞
    const downLike = (_item) => {
      _item.isFabulous = !_item.isFabulous
      if (_item.isFabulous) {
        _item.fabulousCount++
        _item.likes = _item.likes + (_item.likes ? ',' : '') + data.user.userName
        _item.fabulousList.push({ id: data.user.id, userName: data.user.userName })
      } else {
        _item.fabulousCount--
        _item.fabulousList = _item.fabulousList.filter(item => item.id !== data.user.id)
      }
      fabulousInfo(_item.isFabulous, _item.id, '25')
    }
    // 回复
    const replyClick = (_item, _items) => {
      _item.inputObj.replyShow = true
      _item.inputObj.replyName = '发送评论'
      if (_items) {
        _item.commentObj.commentPid = _items.id
        _item.inputObj.replyName = _items.userName ? '回复' + _items.userName : '回复'
      }
    }
    // 发送
    const transmitClick = async (_item, _items) => {
      if (_item.content === '') {
        return false
      }
      _items.replyShow = false
      var url = 'comment/save'
      var params = _item
      const ret = await $api.general.fabulous({ url, params })
      if (ret.errcode === 200) {
        getList(data.identification)
      } else {
        Toast('请求失败。')
      }
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id, type = '25') => {
      var relateType = type
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: relateType
      }
      await $api.general.fabulous({ url, params })
    }
    // 关注或取关
    const attentionEdit = async (followId, state) => {
      var type = ''
      if (state === 1) {
        type = 'del'
      } else {
        type = 'add'
      }
      const res = await $api.beingCounted.attention({ params: { followId, type: 25 }, type })
      if (res.errcode === 200) {
        onRefresh()
        if (state === 1) {
          Toast('取消关注成功')
        } else {
          Toast('关注成功')
        }
      }
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList(data.identification)
    }
    // 获取列表
    const getList = async (type) => {
      console.log('type==>', type)
      if (type === 1) {
        const { data: yiguanzhuLists, total: yiguanzhuTotal } = await $api.beingCounted.committeesayAppList({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          isFollow: '1',
          keyword: data.keyword
        })
        data.identification = 1
        yiguanzhuLists.forEach(item => {
          // 点赞人名称
          item.likes = item.fabulous || ''
          // 评论集合
          item.commentObj = {
            content: '',
            createBy: data.user.id,
            commentPid: '',
            keyId: item.id,
            attach: '',
            extend: '1',
            type: '25',
            areaId: '370200'
          }
          item.inputObj = {
            replyShow: false,
            replyName: '发送评论'
          }
          if (item.fabulousUser != null) {
            item.fabulousList = item.fabulousUser.map(items => {
              return {
                userName: items.userName,
                id: items.id
              }
            })
          } else {
            item.fabulousList = []
          }
        })
        data.dataList = data.dataList.concat(yiguanzhuLists)
        // data.dataList = yiguanzhuLists
        data.total = yiguanzhuTotal
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        if (data.dataList.length >= yiguanzhuTotal) {
          data.finished = true
        }
      }
      if (type === 2) {
        const { data: lista, total: totala } = await $api.beingCounted.committeesayAppList({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          publishBy: data.user.id,
          keyword: data.keyword
        })
        lista.forEach(item => {
          // 点赞人名称
          item.likes = item.fabulous || ''
          // 评论集合
          item.commentObj = {
            content: '',
            createBy: data.user.id,
            commentPid: '',
            keyId: item.id,
            attach: '',
            extend: '1',
            type: '25',
            areaId: '370200'
          }
          item.inputObj = {
            replyShow: false,
            replyName: '发送评论'
          }
          if (item.fabulousUser != null) {
            item.fabulousList = item.fabulousUser.map(items => {
              return {
                userName: items.userName,
                id: items.id
              }
            })
          } else {
            item.fabulousList = []
          }
        })
        data.identification = 2
        data.dataList = data.dataList.concat(lista)
        // data.dataList = lista
        data.total = totala
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        if (data.dataList.length >= totala) {
          data.finished = true
        }
      }
      if (type === 0) {
        var { data: list, total } = await $api.beingCounted.committeesayAppList({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          keyword: data.keyword
        })
        list.forEach(item => {
          // 点赞人名称
          item.likes = item.fabulous || ''
          // 评论集合
          item.commentObj = {
            content: '',
            createBy: data.user.id,
            commentPid: '',
            keyId: item.id,
            attach: '',
            extend: '1',
            type: '25',
            areaId: '370200'
          }
          item.inputObj = {
            replyShow: false,
            replyName: '发送评论'
          }
          if (item.fabulousUser != null) {
            item.fabulousList = item.fabulousUser.map(items => {
              return {
                userName: items.userName,
                id: items.id
              }
            })
          } else {
            item.fabulousList = []
          }
        })
        data.identification = 0
        data.dataList = data.dataList.concat(list)
        // data.dataList = list
        data.total = total
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        if (data.dataList.length >= total) {
          data.finished = true
        }
      }
    }
    return { ...toRefs(data), $general, onRefresh, committeesayDel, attentionEdit, onClickTab, search, previewCalback, onLoad, issueAdd, downLike, dayjs, skipDetails, transmitClick, replyClick, openUserList }
  }
}
</script>

<style lang="less" scoped>
.beingCountedList {
  width: 100%;
  background: #f8f8f8;

  .representativeCircle_box_li {
    width: 100%;
    padding-bottom: 5px;

    .representativeCircle_box_top {
      width: 100%;
      height: 35px;
      margin: 5px 0;
      display: flex;
      align-items: center;
      position: relative;

      .representativeCircle_box_del {
        position: absolute;
        top: 0;
        right: 10px;
      }

      .attention {
        text-align: center;
        position: absolute;
        top: 0;
        right: 10px;
        padding: 2px 8px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40px;
        color: #3894ff;
        border: 1px solid #3894ff;
      }

      .attentionDel {
        color: #666;
        border: 1px solid #666;
      }

      .representativeCircle_box_top_headImg {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin: 5px;
      }

      .representativeCircle_box {
        padding: 0 8px;

        .representativeCircle_box_name {
          font-size: 16px;
          color: #333;
          font-weight: 600;
          line-height: 1.4;
        }

        .representativeCircle_box_congressStr {
          line-height: 1.4;
          width: 230px;
          white-space: nowrap;
          /* 防止文本换行 */
          overflow: hidden;
          /* 隐藏溢出的内容 */
          text-overflow: ellipsis;
          /* 使用省略号表示溢出的文本 */
        }

        .representativeCircle_box_tag {
          font-size: 13px;
          color: #a5a5a5;
          margin-top: 3px;
        }
      }
    }

    .representativeCircle_box_center {
      background: #ffffff;
      // margin: 5px 10px 0px 30px;
      color: #333;
      font-weight: 400;
      line-height: 1.4;

      .representativeCircle_box_center_content {
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
        margin: 10px 0 0 10px;
      }
    }

    .house_content_img_bg {
      width: calc(100% - 10px);
      left: 0;
      right: 0;

      .house_content_img {
        height: 70px;
        width: 100px;
        overflow: hidden;
      }

      .house_content_img img {
        border-radius: 2px;
      }
    }

    .representativeCircle_box_buttom {
      width: 100%;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .representativeCircle_box_buttom_time {
        width: 70%;
        font-size: 14px;
        color: #a8a8a8;
      }

      .representativeCircle_box_buttom_cont {
        width: 25% !important;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .representativeCircle_box_buttom_comment {
          display: flex;
          align-items: center;
          justify-content: space-between;

          >img {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }

        .representativeCircle_box_buttom_like {
          line-height: 100%;

          >img {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }
      }
    }
  }

  .issue {
    width: 50px;
    height: 50px;
    color: #fff;
    text-align: center;
    line-height: 50px;
    background: #3894ff;
    position: fixed;
    border-radius: 50%;
    z-index: 999;
    bottom: 30px;
    left: 50%;
    transform: translate(-50%, 0);
  }

  .likeComment_box {
    background: #f7f7f7;
    margin: 0 0 10px;
    overflow: hidden;
    box-sizing: border-box;
    border-radius: 5px;

    .comment_box {
      margin: 0 5px 0px;
    }

    .like_box {
      color: #6e7fa3;
      margin: 5px 5px;
    }

    .reply_box {
      background: #f7f7f7;
      margin: 5px 5px 0;
      padding: 5px 0 0 0;
      border-top: 1px solid #e8e8e8;
      height: 50px;

      .reply_box_item {
        width: 100%;
        background: #fff;
        height: 100%;
        border-radius: 5px;
        border: 1px solid #3895ff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0 5px;

        .reply_box_but {
          width: 60px;
          border-radius: 5px;
          height: 80%;
          color: #fff;
          background: #3895ff;
        }

        .reply_box_buts {
          color: rgb(112, 112, 112);
          background: #bdbdbd;
          width: 60px;
          border-radius: 5px;
          height: 80%;
        }

        .reply_box_inp {
          height: 80%;
          flex: 1;
        }
      }
    }
  }
}
</style>

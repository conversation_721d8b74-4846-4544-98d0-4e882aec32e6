<template>
  <div class="activityOtherInformationDetails">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="$general.loadConfiguration(4)"
           v-html="title"></div>
      <div class="n_details_more_box flex_box">
        <!--来源-->
        <div class="n_details_name flex_placeholder"
             :style="$general.loadConfiguration(-4)">{{source?('来源：'+source):''}}</div>
        <!--资讯类型 可点击更多-->
        <div v-if="IBSName.show && IBSName.value"
             class=" flex_box flex_align_center flex_justify-content_end">
          <div class="n_details_name"
               :style="$general.loadConfiguration(-4)+'margin-right: 6px;color:'+appTheme">{{IBSName.value}}</div>
        </div>
      </div>
      <div class="n_details_more_box">
        <div v-if="browerCount.show"
             class="n_details_item"
             :style="$general.loadConfiguration(-4)">{{browerCount.hint}}{{browerCount.value}}</div>
        <div v-if="shareCount.show"
             class="n_details_item"
             :style="$general.loadConfiguration(-4)">分享：{{shareCount.value}}</div>
        <div v-if="commentCount.show"
             class="n_details_item"
             :style="$general.loadConfiguration(-4)">评论：{{commentCount.value}}</div>
        <div v-if="dataTime.show"
             class="n_details_time"
             :style="$general.loadConfiguration(-4)">{{dataTime.value}}</div>
        <div style="clear: both;"></div>
      </div>
    </div>
    <div class="n_details_content"
         :style="$general.loadConfiguration()"
         v-html="content">
    </div>
    <!--展示附件-->
    <template v-if="attachInfo.data.length != 0">
      <van-cell class="list_item"
                :style="$general.loadConfiguration()"
                center
                :title="attachInfo.name + '('+attachInfo.data.length+')'"></van-cell>
      <div class="add_warp attach_warp"
           :style="$general.loadConfiguration()">
        <van-swipe-cell v-for="(nItem,nIndex) in attachInfo.data"
                        :key="nIndex">
          <van-cell :border="false"
                    :class="nItem.state==2?'cache':''"
                    @click="annexClick(nItem)"
                    :title="nItem.name"></van-cell>
        </van-swipe-cell>
      </div>
    </template>
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box">
        {{'&nbsp;'}}
        <div :style="$general.loadConfiguration()">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="item.type == 'btn'"
                 class="van-button-box">
              <van-button loading-type="spinner"
                          :loading-size="((appFontSize))+'px'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </ul>
    </transition>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog } from 'vant'
export default {
  name: 'activityOtherInformationDetails',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      browerCount: { show: false, value: '0', hint: '阅读：' }, // 阅读数
      shareCount: { show: false, value: '0' }, // 分享数
      commentCount: { show: false, value: '0' }, // 评论数
      dataTime: { show: false, value: '' }, // 时间
      IBSName: { show: false, value: '' }, // 资讯类型
      source: '', // 来源
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      footerBtnsShow: true, // 按钮是否隐藏
      type: '49', // 类型
      footerBtns: [], // 底部按钮集合 top为返回顶部 btn为按钮
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] } // 附件对象

    })
    onMounted(() => {
      onRefresh()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    // 列表请求
    const getList = async () => {
      var res = {}
      if (data.relateType === 'activityMateria') {
        res = await $api.activity.getMaterialInfo(data.id)
      } else if (data.relateType === 'activitySchedule') {
        res = await $api.activity.getScheduleInfo(data.id)
      } else if (data.relateType === 'activityReport') {
        res = await $api.activity.getReportInfo(data.id)
      }
      data.dataTime.show = true
      var info = res.data || {}
      data.title = info.materialName || info.title || ''
      data.dataTime.value = info.dateTime || info.createDate || ''
      data.content = $general.dealWithCon(info.content || '')// 内容
      data.picInfo.data = []
      data.attachInfo.data = []
      var attachmentList = info.attachId || []
      if (attachmentList.length !== 0) {
        for (var k = 0; k < attachmentList.length; k++) {
          var nItemName = attachmentList[k].fileName
          var nItemPath = attachmentList[k].filePath
          data.attachInfo.data.push({ url: nItemPath, state: 0, schedule: -1, name: nItemName })
        }
      }
      var photoList = info.photo || []
      if (photoList.length !== 0) {
        photoList.forEach(element => {
          var nItemPath = element.filePath
          data.picInfo.data.push({ url: nItemPath })
        })
      }
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
    }
    // 底部按钮事件
    const footerBtnClick = (_item) => {
      console.error(JSON.stringify(_item))
      switch (_item.click) {

      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    const openParticipant = () => {
      router.push({ name: 'activityParticipant', query: { id: data.id } })
    }
    const tabClick = () => {
      onRefresh()
    }
    const addCommentEvent = (value) => {
      console.log(value)
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      console.log(value)
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      console.log(value)
      data.inputBox.getStats()
    }
    const openUser = rows => {
      router.push({ name: 'personData', query: { id: rows.id } })
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, search, tabClick, openParticipant, footerBtnClick, addCommentEvent, openInputBoxEvent, freshState, openUser, annexClick }
  }
}
</script>
<style lang="less" scoped>
.activityOtherInformationDetails {
  background: #fff;
  .vue_newslist_li:active {
    background: rgba(0, 0, 0, 0);
  }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
  }
  .n_details_other {
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
    padding-bottom: 0;
    position: relative;
  }
  .n_details_other div {
    padding: 2px 0;
  }

  .list_item {
    margin-top: 15px;
  }
  .add_warp {
    padding: 10px 10px;
    background: #fff;
  }
  .participant_box {
    padding: 10px;
    box-sizing: border-box;
  }
  .participant_box span {
    font-size: inherit;
    font-family: inherit;
  }

  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 25px 10px;
    box-sizing: border-box;
    position: relative;
  }
  .n_details_title {
    font-weight: 500;
    line-height: 1.5;
  }

  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }

  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
  }
  .n_details_name {
    color: #666;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .n_details_nextImg {
  }

  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
  .n_details_content img {
    margin: 15px 0;
  }
}
</style>

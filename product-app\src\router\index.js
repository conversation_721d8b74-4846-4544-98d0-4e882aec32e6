import {
  createRouter,
  createWebHashHistory
} from 'vue-router'
import store from '../store'
import api from '../api'
import general from '../assets/js/general'

const files = require.context('./module', false, /\.js$/)
var pages = []
files.keys().forEach(key => {
  pages = pages.concat(files(key).default)
})

export const routes = [{
  path: '/login',
  name: 'login',
  component: () => import('@/views/main/login.vue')
},
{
  path: '/',
  name: 'home',
  component: () => import('@/views/main/home.vue'),
  children: [
    {
      path: '/module',
      name: 'module',
      component: () => import('@/views/module/module.vue')
    },
    {
      path: '/zxBookmain',
      name: 'zxBookmain',
      component: () => import('@/views/zxBookmain/zxBookmain.vue')
    },
    {
      path: '/msgListHome',
      name: 'msgListHome',
      component: () => import('@/views/rongCloud/msgList/msgList.vue')
    },
    {
      path: '/bookDeskHome',
      name: 'bookDeskHome',
      component: () => import('@/views/bookAcademy/bookDesk/books.vue')
    },
    {
      path: '/homePageZX',
      name: 'homePageZX',
      component: () => import('@/views/homePage/homePageZX.vue')
    },
    {
      path: '/myUser',
      name: 'myUser',
      component: () => import('@/views/myUser/myUser.vue')
    }
  ]
},
...pages
]
const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      const position = {}
      if (to.hash) {
        position.selector = to.hash
      }
      if (sessionStorage.getItem('scrollPosition')) {
        position.x = 0
        position.y = sessionStorage.getItem('scrollPosition')
      }
      return position
    }
  }
})
router.afterEach((to, from) => {
  if (from.path === '/module101') {
    sessionStorage.setItem('scrollPosition', window.scrollY)
  }
})

router.beforeEach(async (to, from, next) => {
  document.title = to.query.title || '青岛人大'
  const token = sessionStorage.getItem('token') || ''
  const userid = to.query.userId || ''
  const pcToken = to.query.token || ''
  const areaId = to.query.areaId || ''
  // const userToken = to.query.userToken
  const userToken = ''// 先用空的，保证请求不到接口还可以跳转到登录页手机动登录
  if (areaId) {
    sessionStorage.setItem('areaId', areaId)
  }
  if (userToken) {
    sessionStorage.setItem('otherToken', 'Bearer ' + userToken)
    getPersonal(to, from, next)
  } else if (pcToken) {
    sessionStorage.setItem('token', JSON.stringify(pcToken))
    sessionStorage.setItem('Sys_token', JSON.stringify(pcToken))
    changearea(to, from, next)
    next()
  } else if (userid) {
    getOtherToken(userid, to, from, next)
  } else {
    if (token) {
      if (to.name === 'login') {
        next({
          name: 'home'
        })
      } else {
        const tabbarList = store.getters.tabbarList
        const isHasRoute = hasRoute.isPath(to.path)
        if (!tabbarList.length) {
          await store.dispatch('getAppList')
          if (!isHasRoute) {
            next({ ...to })
          } else {
            next()
          }
        } else {
          next()
        }
      }
    } else {
      if (to.name !== 'login') {
        if (to.name === 'RandomClapping' || to.name === 'userFeedbackAdd') {
          next()
        } else {
          next({
            name: 'login'
          })
        }
      } else {
        next()
      }
    }
  }
})

const hasRoute = (k, val) => {
  const routes = router.getRoutes()
  return routes.findIndex(item => item[k] === val) > -1
}
['name', 'path'].forEach(k => {
  const name = 'is' + k.charAt(0).toLocaleUpperCase() + k.substring(1)
  hasRoute[name] = (val) => {
    return hasRoute(k, val)
  }
})

// 新增
export const handleAdd = (item) => {
  router.options.routes.forEach(element => {
    // eslint-disable-next-line eqeqeq
    if (element.name == item.infoUrl2) {
      const newRouter = {}
      newRouter.path = item.infoUrl2 + item.type
      newRouter.name = item.infoUrl2 + item.type
      newRouter.meta = { title: item.name }
      newRouter.component = element.component
      router.addRoute('home', newRouter)
    }
  })
}

const getPersonal = async (to, from, next) => {
  const {
    custom: user
  } = await api.general.getPersonal({
    params: {}
  })
  const useridN = user.userguid
  getOtherToken(useridN, to, from, next)
}
const getOtherToken = async (useridN, to, from, next) => {
  sessionStorage.setItem('useridN', useridN)
  sessionStorage.setItem('token', '')
  sessionStorage.setItem('Sys_token', '')
  const { data: token } = await api.general.calogin({ certId: useridN })
  sessionStorage.setItem('token', JSON.stringify(token))
  sessionStorage.setItem('Sys_token', JSON.stringify(token))
  changearea(to, from, next)
}

// 切换系统登录
const changearea = async (to, from, next) => {
  const res = await api.general.changearea()
  var {
    data: {
      token,
      user,
      menus,
      areas
    }
  } = res
  sessionStorage.setItem('menus', JSON.stringify(menus))
  sessionStorage.setItem('token', JSON.stringify(token))
  sessionStorage.setItem('Sys_token', JSON.stringify(token))
  sessionStorage.setItem('user', JSON.stringify(user))
  sessionStorage.setItem('areas', JSON.stringify(areas))
  sessionStorage.setItem('areaId', user.areaId)

  areas.forEach(item => {
    if (item.id === user.areaId) {
      sessionStorage.setItem('areaName', JSON.stringify(item.value))
    }
  })
  nologin(to, from, next)
  findRolesByUserId()
  await store.dispatch('getAppList')
}
// 获取用户角色
const findRolesByUserId = async () => {
  const { data: roleList } = await api.general.findRolesByUserId()
  sessionStorage.setItem('roleList', roleList)
}
// 切换系统登录
const nologin = async (to, from, next) => {
  const res = await api.general.nologin({ codes: 'logo,AcademyAppId,AcademyRentId,rongCloudIdPrefix,appGrayscale,setWaterMark,fileStoreUrl,fileVisitUrl,BigDataUser,cjhjssp' })
  var { data: { logo, AcademyAppId, AcademyRentId, rongCloudIdPrefix, appGrayscale, setWaterMark, fileStoreUrl, fileVisitUrl, BigDataUser, cjhjssp } } = res
  sessionStorage.setItem('tomcatAddress', 'http://www.cszysoft.com:9090/')
  sessionStorage.setItem('logo', logo)
  sessionStorage.setItem('AcademyAppId', AcademyAppId)
  sessionStorage.setItem('AcademyRentId', AcademyRentId)
  sessionStorage.setItem('rongCloudIdPrefix', rongCloudIdPrefix)
  sessionStorage.setItem('appGrayscale', appGrayscale)
  sessionStorage.setItem('setWaterMark', setWaterMark)
  sessionStorage.setItem('fileStoreUrl', fileStoreUrl)
  sessionStorage.setItem('fileVisitUrl', fileVisitUrl)
  sessionStorage.setItem('BigDataUser', BigDataUser)
  sessionStorage.setItem('cjhjssp', cjhjssp)
  general.setWaterMark()
  general.appGrayscale()
}

export async function setupRouter (app) {
  app.use(router)
  await router.isReady()
}

export default router

<template>
  <div class="addNew">
    <van-nav-bar v-if="isShowHead" :title="title" fixed placeholder safe-area-inset-top left-text="" left-arrow
      @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <van-cell-group :style="$general.loadConfiguration(1)">
        <template v-for="(item, index) in items" :key="index">
          <div v-if="!item.hide">
            <div v-if="item.caveat" :style="$general.loadConfiguration(-1)" class="caveat" v-html="item.caveat"></div>
            <!--普通的输入框-->
            <template v-if="item.type == 'text'">
              <div class="list_item  van-hairline--bottom" :style="item.style">
                <van-field size="large" :label="item.label" v-model="item.value" :required="!item.noRequired"
                  :type="item.type" clearable :maxlength="item.maxlength"
                  :placeholder="item.hint ? item.hint : !item.readonly ? ('请输入' + item.label) : ''"
                  :readonly="item.readonly || ifDisabled" :ref="item.key" :show-word-limit="item.showWordLimit"
                  :input-align="item.inputAlign || ''"></van-field>
                <div v-if="item.btns && item.btns.length" class="list_addbtn flex_box">
                  <div class="flex_placeholder"></div>
                  <van-button :style="$general.loadConfiguration(-2)" v-for="(nItem, nIndex) in item.btns" :key="nIndex"
                    :color="appTheme" @click="footerBtnClick(nItem)">{{ nItem.name }}</van-button>
                </div>
              </div>
            </template>
            <!--文本输入框-->
            <template v-else-if="item.type == 'textarea'">
              <div class="list_item">
                <van-field size="large" :label="item.label" v-model="item.value"
                  :readonly="item.readonly || ifDisabled || item.disabled" :required="!item.noRequired"
                  :type="item.type" rows="9" autosize :maxlength="item.maxlength" show-word-limit
                  :placeholder="item.hint || ('请输入' + item.label)" :ref="item.key" @blur="textareaBlur"></van-field>
                <div v-if="!item.readonly && !ifDisabled && !item.disabled" style="background: #FFF;padding: 0.1rem;"
                  class="flex_box flex_align_center">
                  <!-- <div @click="identifyPic(item)"
                       :style="$general.loadConfiguration(-1)"
                       class="Identify_btn flex_box flex_align_center flex_justify_content">
                    <van-icon name="photo-o"
                              style="margin-right:0.1rem;"></van-icon>图片识别
                  </div>
                  <div @click="identifyAudio(item)"
                       :style="$general.loadConfiguration(-1)"
                       class="Identify_btn flex_box flex_align_center flex_justify_content">
                    <van-icon name="bullhorn-o"
                              style="margin-right:0.1rem;"></van-icon>语音输入
                  </div> -->
                </div>
              </div>
            </template>
            <!--时间选择框-->
            <template v-else-if="item.type == 'time'">
              <div :style="item.style">
                <van-field class="list_item" size="large" input-align="right" :label="item.label" v-model="item.value"
                  readonly :required="!item.noRequired" :placeholder="`请选择${item.label}`" @click="openTimeFn(item)"
                  right-icon="clock-o" :ref="item.key"></van-field>
                <van-popup v-model:show="item.show" position="bottom" :style="{ height: 'auto' }">
                  <van-datetime-picker v-model="item.timeValue" :title="`选择${item.label}`" :formatter="formatter"
                    :type="item.nType ? item.nType : 'datetime'" @confirm="confirmFn(item)"
                    @cancel="cancelFn(item)"></van-datetime-picker>
                </van-popup>
              </div>
            </template>
            <!--日历，可以选择区间-->
            <template v-else-if="item.type == 'calendar'">
              <van-field class="list_item" size="large" input-align="right" :label="item.label" v-model="item.value"
                readonly :required="!item.noRequired" :placeholder="`请选择${item.label}`" @click="openTimeFn(item)"
                right-icon="clock-o" :ref="item.key"></van-field>
              <van-calendar v-model="item.show" :default-date="item.defaultDate" :allow-same-day="true"
                :type="item.nType" :color="appTheme" :min-date="calendarMinDate()" :max-date="calendarMaxDate()"
                @confirm="onConfirmCalendar($event, item)"></van-calendar>
            </template>
            <!--选择人-->
            <template v-else-if="item.type == 'user'">
              <div class="list_item" :class="item.tStyle == 'user' ? 'colorGray' : ''" :style="item.style">
                <van-cell :required="!item.noRequired" class="" center @click="openUserFn(item)">
                  <div class="flex_box flex_align_center user_warp">
                    <span class="flex_placeholder text_one2" :style="$general.loadConfiguration(1)">{{ item.label +
                      (item.data.length >
                        0 ? ('(' + item.data.length + ')') : '') }}</span>
                    <template v-if="item.tStyle == 'label'">
                      <div class="flex_box flex_align_center right">
                        <div class="flex_placeholder"></div>
                        <div :style="$general.loadConfiguration(-1)">请选择</div>
                        <van-icon :size="((appFontSize + 2)) + 'px'" name="arrow"></van-icon>
                      </div>
                    </template>
                    <van-icon v-else :size="((appFontSize + 2)) + 'px'" name="add"></van-icon>
                  </div>
                </van-cell>
                <div v-if="item.data.length != 0" class="add_warp flex_box flex_align_center">
                  <div class="flex_placeholder" :class="item.tStyle == 'user' ? 'user_box' : ''">
                    <template v-if="item.tStyle == 'user'">
                      <div v-for="(uItem, uIndex) in item.data" :key="uIndex" class="user_item"
                        @click="clickUser(uItem)">
                        <van-image class="delUser" fit="cover" round
                          :src="require('../../assets/img/icon_upload_delete.png')"
                          :style="$general.loadConfigurationSize(4)"
                          @click.stop="$general.delItemForKey(uIndex, item.data)"></van-image>
                        <van-image class="userImg" :style="$general.loadConfigurationSize(29)" fit="contain" round
                          :src="uItem.url"></van-image>
                        <div class="user_name text_one2" :style="$general.loadConfiguration(-4)">{{ uItem.name }}</div>
                      </div>
                    </template>
                    <template v-else-if="item.tStyle == 'label'">
                      <div class="flex_box T-flex-flow-row-wrap">
                        <div v-for="(uItem, uIndex) in item.data" :key="uIndex"
                          class="user2_item flex_box flex_align_center">
                          <div class="flex_placeholder text_one2" :style="$general.loadConfiguration(1)">{{ uItem.name
                          }}
                          </div>
                          <van-image fit="cover" round :src="require('../../assets/img/icon_upload_delete.png')"
                            :style="$general.loadConfigurationSize(4)"
                            @click.stop="$general.delItemForKey(uIndex, item.value)"></van-image>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </template>
            <!--switch开关-->
            <template v-else-if="item.type == 'switch'">
              <van-cell :required="!item.noRequired || item.showRed" class="list_item" :style="item.style" center
                :title="item.label">
                <template #right-icon>
                  <van-switch @change="switchChange(item)" :disabled="item.readonly || ifDisabled || item.disabled"
                    v-model="item.value" :size="((appFontSize + 8)) + 'px'" :active-color="appTheme"></van-switch>
                </template>
              </van-cell>
            </template>
            <!--NNN 选择主题词 交办单位-->
            <template v-else-if="item.type == 'selectUnit'">
              <div>
                <van-field :class="!item.noRequired ? 'listNoRequired' : ''" :label="item.label"
                  :model-value="item.value.length + '个单位'" readonly @click="openUnitDialog(item)"
                  :style="$general.loadConfiguration()"></van-field>
              </div>
            </template>
            <!--select选择框-->
            <template v-else-if="item.type == 'select'">
              <div class="list_item setting_item" :style="item.style">
                <van-cell :required="!item.dotRequired && !item.noRequired" :data="getSelData(item)" center>
                  <div class="flex_box flex_align_center" :style="$general.loadConfiguration(1)">
                    <span class="text_one2 inherit">{{ item.label }}</span>
                    <div class="flex_placeholder"></div>
                    <div class="flex_placeholder"></div>
                    <div class="flex_placeholder flex_box inherit">
                      <van-dropdown-menu class="add_select" :active-color="appTheme">
                        <van-dropdown-item style="width:100%;" :disabled="item.readonly || ifDisabled || item.disabled"
                          @change="selChange(item)" v-model="item.value" :options="item.data"></van-dropdown-item>
                      </van-dropdown-menu>
                    </div>
                  </div>
                </van-cell>
              </div>
            </template>
            <!--radio 单选框  超过3个建议使用 select-->
            <template v-else-if="item.type == 'radio'">
              <div class="list_item" :style="item.style">
                <van-cell :required="!item.dotRequired && !item.noRequired" :data="getSelData(item)" center>
                  <van-radio-group v-model="item.value" @change="selChange(item)"
                    :disabled="item.readonly || ifDisabled || item.disabled" direction="horizontal"
                    class="flex_box flex_align_center">
                    <span class="flex_placeholder text_one2" :style="$general.loadConfiguration(1)">{{ item.label
                    }}</span>
                    <van-radio v-for="(nItem, nIndex) in item.data" :key="nIndex" :icon-size="(appFontSize + 4) + 'px'"
                      :checked-color="appTheme" :name="nItem.value">{{ nItem.text }}</van-radio>
                  </van-radio-group>
                </van-cell>
              </div>
            </template>
            <!-- 上传图片 -->
            <template v-else-if="item.type == 'pic'">
              <!-- {{item}} -->
              <div class="picUploader">
                <div class="picUploader_title">
                  上传图片(最多上传{{ item.max }}张)
                </div>
                <div class="imgloager">
                  <div class="img_box" v-for="(nItem, nIndex) in item.data" :key="nIndex">
                    <van-icon name="clear" class="clear" @click.stop="$general.delItemForKey(nIndex, item.data)" />
                    <van-image @click.stop="ImagePreview([nItem.url])" width="2rem" height="2rem" fit="cover"
                      :src="nItem.url" />
                  </div>
                  <van-uploader :preview-full-image="true" ref="vanUploader" accept="image/*" capture="camera" multiple
                    :disabled="item.data.length == item.max" :after-read="imgUploader" :max-count="item.max">
                  </van-uploader>
                  <van-uploader :preview-full-image="true" ref="vanUploader" accept="image/*" multiple
                    :disabled="item.data.length == item.max" :after-read="imgUploader" :max-count="item.max">
                    <div class="photo">
                      <van-icon name="photo" />
                    </div>
                  </van-uploader>
                </div>
              </div>
            </template>
            <!-- 上传附件 -->
            <template v-else-if="item.type == 'attach'">
              <div class="list_item enclosure" :style="item.style">
                <van-cell :required="!item.noRequired" center>
                  <div class="flex_box flex_align_center" :style="$general.loadConfiguration(1)">
                    <span class="text_one2 inherit flex_placeholder">上传附件</span>
                    <span class="text_one2" :style="$general.loadConfiguration(-1) + 'color:#aaa;'">{{ item.data.length
                      !=
                      0 ? '向左滑动可删除' : '' }}</span>
                  </div>
                </van-cell>
                <div class="add_warp" :style="$general.loadConfiguration()">
                  <div style="max-height:150px;overflow:auto;">
                    <van-swipe-cell v-for="(nItem, nIndex) in item.data" :key="nIndex">
                      <van-cell :border="false">
                        <div class="flex_box flex_align_center" style="height: 100%;width:100%;padding:0.1rem 0;">
                          <div class="flex_placeholder text_one2 fileName" v-html="nItem.name" @click="test(nItem)">
                          </div>
                          <img v-if="nItem.state > 0"
                            :src="nItem.state == 1 ? require('../../assets/img/icon_doubt.png') : nItem.state == 2 ? require('../../assets/img/icon_upload_success.png') : require('../../assets/img/icon_upload_fail.png')"
                            :style="$general.loadConfigurationSize(4)" />
                        </div>
                      </van-cell>
                      <template #right>
                        <van-button square text="删除" type="danger" style="height: 100%;"
                          @click="$general.delItemForKey(nIndex, item.data)"></van-button>
                      </template>
                    </van-swipe-cell>
                  </div>
                  <div class="flex_box flex_align_center">
                    <van-button @click="openAttach(1)" icon="bars" size="large" v-if="paramType !== 'resumption'"
                      :color="appTheme">{{
                        '添加文件' }}</van-button>
                    <van-button @click="openAttach(2)" icon="bars" size="large" :color="appTheme">{{ '添加图片'
                    }}</van-button>
                  </div>
                  <van-uploader accept=".pdf,.doc.,rar,.zip,.docx" v-model="fileList" :after-read="afterReadFile"
                    style="display: none;" ref="file">
                  </van-uploader>
                  <van-uploader accept="image/*,.png,.jpg" v-model="fileList" :after-read="afterReadImg"
                    style="display: none;" ref="image">
                  </van-uploader>
                </div>
              </div>
            </template>
          </div>
        </template>
      </van-cell-group>
    </div>
    <!--不为一级页面时 适配底部条-->
    <footer v-if="hasFooter" :style="$general.loadConfiguration()">
      <van-button loading-type="spinner" :loading-size="(appFontSize - 4) + 'px'" size="large" :disabled="ifDisabled"
        :color="appTheme" @click="submit(false)" :loading="ifLoading" loading-text="提交中..."> {{ submitText
        }}</van-button>
    </footer>
  </div>
  <van-popup v-model:show="showUnit" round position="bottom" :style="{ height: '90%' }">
    <selectUnit v-if="showUnit" ref="unitComponts" :selectUnitParams="selectUnitParams"></selectUnit>
    <footer class="footerBtn" style="position:fixed;bottom:0;width:100%;">
      <div class="footerBtnBox flex_box">
        <van-button @click="getUnit" type="primary" block>确定</van-button>
      </div>
    </footer>
  </van-popup>
  <van-popup v-model:show="show" round position="bottom" :style="{ height: '90%' }">
    <addressBook ref="userComponts" :data="selectUser" :max="max" :isSelectUser="true" />
    <footer class="footerBtn" style="position:fixed;bottom:0;width:100%;">
      <div class="footerBtnBox flex_box">
        <van-button @click="getUser" type="primary" block>确定</van-button>
      </div>
    </footer>
  </van-popup>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch, ref } from 'vue'
import { NavBar, ImagePreview, Sticky, SwipeCell, Uploader, Field, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Switch, Toast, DatetimePicker, Calendar, Popup } from 'vant'
import addressBook from '../rongCloud/addressBook/addressBook'
import selectUnit from '../../components/selectUnit/selectUnit'
export default {
  name: 'addNew',
  components: {
    addressBook,
    selectUnit,
    [Dialog.Component.name]: Dialog.Component,
    [Field.name]: Field,
    [Popup.name]: Popup,
    [Calendar.name]: Calendar,
    [DatetimePicker.name]: DatetimePicker,
    [Switch.name]: Switch,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Uploader.name]: Uploader,
    [SwipeCell.name]: SwipeCell
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const vanUploader = ref(null)
    const data = reactive({
      safeAreaTop: 0,
      appFontSize: $general.data.appFontSize,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id || '',
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',

      max: 0,
      show: false,
      userComponts: null,
      selectUser: [
        // { id: 1, name: '超级管理员', url: '', notDel: true }
      ],
      ifReturn: false, // 是否返回人员
      ifReturnNull: false, // 是否能选择返回空 默认不能

      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      nImgs: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      hasFooter: true, // 是否有按钮
      ifDisabled: false, // 是否禁用提交
      ifLoading: false, // 是否正在提交
      submitText: '提交',
      module: '', // 上传附件的module

      // noRequired不必填   dotRequired必填但不显示红点
      items: [
        // { type: 'text', value: '', key: 'title', hint: '标题' },
        // { type: 'user', tStyle: 'user', value: '', data: [], key: 'jointSubmitUserIds', label: '添加联名者', hintText: '请确保联名者同意', noRequired: true, onlySel: '0' },
        // { type: 'user', tStyle: 'label', show: false, value: [], showItem: [], searchItem: {}, activeIndex: 0, max: 5, caveat: '(最多选择5个)', data: [], key: 'topicWordIds', label: '主题词', noRequired: true },
        // { type: 'user', tStyle: 'label', show: false, value: [], showItem: [], searchItem: {}, activeIndex: 0, max: 4, caveat: '(最多选择4个)', data: [], key: 'hopeGroupIds', label: '希望送交办单位(供参考)', noRequired: true },
        // { type: 'switch', value: false, key: 'ifInvestigate', label: '是否调研', noRequired: true },
        // { type: 'switch', value: false, key: 'ifAdvicePublic', label: '是否公开', noRequired: true },
        // { type: 'switch', value: false, key: 'ifSecret', label: '是否涉密', noRequired: true },
        // { type: 'textarea', value: '', key: 'content', hint: '正文', maxlength: 2000, caveat: '(事实清楚，建议明确，不超过2000字)' },
        // { type: 'number', value: '', show: false, key: 'selfScore', hint: '提案自评' },
        // { type: 'text', value: '', key: 'contactUser', label: '提案联系人', caveat: '(负责联系该提案的工作人员)', noRequired: true, readonly: true, style: 'margin-bottom: 0.0rem;' },
        // { type: 'text', value: '', key: 'contactUserName', hint: '姓名', noRequired: true, style: 'margin-bottom: 0.0rem;' },
        // { type: 'text', value: '', key: 'contactUserAddress', hint: '通讯地址', noRequired: true, style: 'margin-bottom: 0.0rem;' },
        // { type: 'text', value: '', key: 'contactUserPhone', hint: '联系电话', noRequired: true },
        // { type: 'radio', value: '', key: 'draftsFlag', label: '提交状态', hint: '', data: [{ text: '草稿', value: '1' }, { text: '正式', value: '0' }] }
      ],
      largeSmallClass: [], // 社情民意大小类别集合   建议主小分类
      paramType: route.query.paramType || '',
      topicId: route.query.topicId || '',
      officeId: route.query.officeId || '',
      showUnit: false,
      unitComponts: null,
      selectUnitParams: {},
      selectUnit: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      attachmentIds: [],
      imgshow: false
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      init()
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    const imgUploader = async (file) => {
      console.log(file)
      console.log(data.images)
      const item = { url: file.content, uploadUrl: '', name: '', uploadId: '', status: 'uploading', module: data.module }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', data.module)
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.status = 'done'
        item.url = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.status = 'failed'
        item.error = ret.errmsg || ''
      }
      var attachmentIds = $general.getItemForKey('attachmentIds', data.items)// 得到大类item
      attachmentIds.data.push(item)
      localStorage.setItem('PerformanceCircleFileImg', JSON.stringify(attachmentIds.data))
    }
    const clickUploader = () => {
      console.log(vanUploader.value)
      vanUploader.value[0].chooseFile()
    }
    const submit = async () => {
      for (var i = 0; i < data.items.length; i++) {
        var nItem = data.items[i]
        if (!nItem.noRequired && !nItem.hide) { // 没有不必填 就是必填  并且没有隐藏
          if (nItem.type === 'pic' || nItem.type === 'attach') {
            if (nItem.data.length === 0) {
              Toast('请添加' + (nItem.hint || nItem.label))
              return
            }
          } else if (nItem.type === 'user') {
            if (nItem.data.length === 0) {
              Toast('请添加' + (nItem.hint || nItem.label))
              return
            }
          } else if (nItem.type === 'table') { // table先放过 后面判断

          } else {
            if (!nItem.value) {
              Toast((nItem.type === 'text' || nItem.type === 'textarea' || nItem.type === 'number' ? '请输入' : '请选择') + (nItem.hint || nItem.label))
              return
            }
          }
          if (!nItem.hide && (nItem.type === 'pic' || nItem.type === 'attach')) {
            for (var k = 0; k < nItem.data.length; k++) {
              if (nItem.data[k].state === 1) {
                Toast('有' + (nItem.type === 'pic' ? '图片' : '附件') + '正在上传中，请稍候')
                return
              } else if (nItem.data[k].state === 3) {
                Toast('有' + (nItem.type === 'pic' ? '图片' : '附件') + '上传失败，请删除重试')
                return
              }
            }
          }
        }
        var postData = {}
        var hintWord = '提交'
        var isReview = false
        // var postFile = {}
        switch (data.paramType) {
          case 'social':// 社情民意
            postData = {
              socialDate: $general.getTime(),
              phoneNumber: data.user.mobile,
              reportType: '1',
              reflectType: '2',
              firstReflecterType: '1',
              firstReflecterId: data.user.id,
              firstReflecterName: data.user.userName
            }
            var attachments = data.attachments || ''
            postData.attachmentIds = ''
            if (attachments && attachments.length !== 0) {
              attachments.forEach(function (item, index) {
                postData.attachmentIds += (postData.attachmentIds ? ',' : '') + item.id
              })
            }
            if (data.id) {
              // postUrl = zyUrl.getAppUrl() + "socialinfo/edit";
              postData.id = data.id
              hintWord = '操作'
            }
            break
          case 'keyWork':// 重点工作
            postData = {
            }
            if (data.id) {
              // postUrl = zyUrl.getAppUrl() + "socialinfo/edit";
              postData.id = data.id
              hintWord = '操作'
            }
            break
          case 'lzbl':// 履职补录
            postData = {
            }
            if (data.id) {
              // postUrl = zyUrl.getAppUrl() + "socialinfo/edit";
              postData.id = data.id
              hintWord = '操作'
            }
            break
          case 'wyzb':// 委员值班
            console.log(data, 'id123')
            postData = {
              source: '1',
              userId: data.user.id,
              officeId: data.officeId,
              topicId: data.topicId
            }
            if (data.id) {
              postData.id = data.id
              hintWord = '留言'
            }
            break
          case 'resumption':// 代表履职圈
            console.log(data, 'id123')
            postData = {
              publishBy: data.user.id,
              publishDate: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
            }
            break
          case 'photograpr':// 随手拍添加
            postData = {
              publishBy: data.user.id,
              publishDate: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
            }
            break
        }
      }
      // eslint-disable-next-line no-redeclare
      for (var i = 0; i < data.items.length; i++) {
        // eslint-disable-next-line no-redeclare
        var nItem = data.items[i]
        if (nItem.notUpdate || nItem.hide) { // 不增加这个字段  或者隐藏的
          continue
        }
        if (nItem.type === 'pic' || nItem.type === 'attach') {
          var postFiles = postData[nItem.key] || ''
          for (var j = 0; j < nItem.data.length; j++) {
            postFiles += (postFiles ? ',' : '') + nItem.data[j].uploadId
          }
          postData[nItem.key] = postFiles
        } else if (nItem.type === 'selectUnit') { // 有多级文本的
          postData[nItem.key] = nItem.value.toString()
        } else if (nItem.type === 'calendar' && nItem.nType === 'range') { // 是日期选择区间
          postData[nItem.key.split(',')[0]] = nItem.value.split('至')[0]
          postData[nItem.key.split(',')[1]] = nItem.value.split('至')[1]
        } else if (nItem.type === 'user') {
          console.log(nItem)
          // var datas = postData[nItem.key] || "";//发起人存在时会出现重复数据 不知道会不会有影响
          var datas = ''
          // eslint-disable-next-line no-redeclare
          for (var j = 0; j < nItem.data.length; j++) {
            datas += (datas ? ',' : '') + nItem.data[j].id
          }
          postData[nItem.key] = datas
        } else {
          postData[nItem.key] = nItem.value
        }
      }
      var change = $general.getItemForKey('content', data.items)
      if (change) { // 将内容转换
        postData[change.key] = $general.conversionRichText(change.value)
      }
      data.ifLoading = true
      var res = {}
      switch (data.paramType) {
        case 'social':// 社情民意
          if (data.id) {
            res = await $api.social.editSocial(postData)
          } else {
            res = await $api.social.addSocial(postData)
          }
          break
        case 'keyWork':// 重点工作
          if (data.id) {
            res = await $api.keyWork.saveKeyWork(postData)
          } else {
            res = await $api.keyWork.saveKeyWork(postData)
          }
          break
        case 'lzbl':// 履职补录
          if (data.id) {
            res = await $api.performanceFiles.editLzbl(postData)
          } else {
            res = await $api.performanceFiles.addLzbl(postData)
          }
          break
        case 'wyzb':// 委员值班
          if (data.id) {
            res = await $api.committeeLivingRoom.addCommittee(postData)
          } else {
            res = await $api.committeeLivingRoom.addCommittee(postData)
          }
          break
        case 'resumption':// 代表履职圈
          if (data.id) {
            res = await $api.news.addCommittee(postData)
          } else {
            res = await $api.news.addCommittee(postData)
          }
          break
        case 'photograpr':// 代表履职圈
          if (data.id) {
            res = await $api.news.photograprAdd(postData)
          } else {
            res = await $api.news.photograprAdd(postData)
          }
          break
      }
      data.ifLoading = false
      if (res) {
        var code = res.errcode || ''
        Toast((code === 200 ? (hintWord + '成功') + (isReview ? '，请等待审核，成功后将予以显示' : '') : res.errmsg))
        if (code === 200) {
          localStorage.removeItem('PerformanceCircleContent')
          localStorage.removeItem('PerformanceCircleFileImg')
          data.ifDisabled = true
          onClickLeft()
        }
      } else {
        Toast('提交失败')
      }
    }
    const init = async () => {
      switch (data.paramType) {
        case 'social':// 社情民意
          data.module = 'socialinfo'
          data.items = [
            { type: 'text', value: '', key: 'titile', hint: '标题' },
            { type: 'user', tStyle: 'user', value: '', data: [], key: 'otherMemberJoiners', label: '其他反映人(委员)', noRequired: true },
            { type: 'text', value: '', key: 'otherUserJoiners', hint: '其他反映人(非委员)', noRequired: true },
            { type: 'select', value: '', key: 'firstCategory', label: '稿件类别', data: [{ text: '请选择', value: '' }], style: 'margin-bottom: 0.0rem;' },
            { type: 'select', value: '', key: 'secondCategory', label: '', hint: '稿件小类别', data: [{ text: '请选择', value: '' }], dotRequired: true, noRequired: true },
            //            { type: "select", value: "", key: "largeClass", label: "稿件类别", data: [{ text: '请选择', value: "" }], style: "margin-bottom: 0.0rem;" },
            //            { type: "select", value: "", key: "smallClass", label: "", hint: "稿件小类别", data: [{ text: '请选择', value: "" }], dotRequired: true },
            { type: 'select', value: '1', key: 'worryDegree', label: '紧急程度', data: [{ text: '平急', value: '1' }, { text: '加急', value: '2' }, { text: '特急1', value: '3' }], noRequired: true },
            //            { type: "text", value: "", key: "mainOrg", hint: "建议报送的领导同志和主管单位（单位）" },
            { type: 'textarea', value: '', key: 'content', hint: '正文', maxlength: 1000 },
            { type: 'radio', value: '', key: 'processStatus', label: '提交状态', hint: '', data: [{ text: '草稿', value: '20' }, { text: '正式', value: '30' }] }
          ]
          if (data.id) { // 如果有id 就是可编辑
            const res = await $api.social.getSocialDetails(data.id)
            var info = res.data || {}
            data.attachments = info.attachments || []
            showInfoData(info)
            var firstCategory = $general.getItemForKey('firstCategory', data.items)// 得到大类item
            selChange(firstCategory, true)
          }
          break
        case 'keyWork':// 重点工作
          data.module = 'focusWork'
          data.items = [
            { type: 'text', value: '', key: 'title', label: '主题' },
            { type: 'select', value: '', key: 'structureId', label: '事项类型', hint: '', data: [{ text: '请选择', value: '' }] },
            { type: 'time', nType: 'date', value: '', key: 'expectCompleteDate', label: '预计完成时间', show: false },
            { type: 'time', nType: 'date', value: '', key: 'completeDate', label: '实际完成时间', show: false, noRequired: true },
            { type: 'user', tStyle: 'user', value: '', data: [], key: 'responsibilityUser', recommendKey: 'user', label: '责任领导', max: 1, recommend: '', recommendData: [] },
            { type: 'selectUnit', params: { module: 'focusWork', isEnable: 1 }, value: [], max: 1, caveat: '（最多选择1个）', data: [], key: 'responsibilityOrg', label: '责任部门', hint: '选择部门', selectStyle: 1 },
            { type: 'selectUnit', value: [], max: 1, caveat: '（最多选择1个）', data: [], key: 'assistingOrg', label: '协办部门', hint: '选择部门', noRequired: true, selectStyle: 1 },
            { type: 'textarea', value: '', key: 'content', label: '正文', hint: '输入文本内容...' },
            { type: 'attach', uploadType: 'uploadFile', value: '', key: 'attachmentIds', label: '附件', max: 1, data: [], noRequired: true, module: data.module }
          ]
          break
        case 'lzbl':// 履职补录
          data.module = 'lzbl'
          data.items = [
            { type: 'text', value: (data.user ? data.user.userName : ''), key: 'createName', label: '补录人', readonly: true, notUpdate: true, inputAlign: 'right', noRequired: true, style: 'padding-bottom:0.3rem' },
            { type: 'time', nType: 'date', value: '', key: 'dataTime', label: '补录时间', show: false, style: 'padding-bottom:0.3rem' },
            { type: 'select', value: '', key: 'bigType', label: '选择履职项', hint: '', data: [{ text: '请选择', value: '' }] },
            { type: 'select', value: '', key: 'smallType', label: '', hint: '履职项小类', data: [{ text: '请选择', value: '' }], dotRequired: true, noRequired: true, style: 'padding-bottom:0.3rem;' },
            { type: 'text', value: '', key: 'name', label: '履职标题', inputAlign: 'right', style: 'padding-bottom:0.3rem' },
            { type: 'textarea', value: '', key: 'content', hint: '履职详情', noRequired: true },
            { type: 'pic', uploadType: 'uploadFile', value: '', key: 'attachmentIds', noRequired: true, label: '图片', max: 9, data: [], module: data.module }
          ]
          // that.getData(0)
          break
        case 'wyzb':// 委员值班
          data.module = 'wyzb'
          data.items = [
            { type: 'text', value: data.user.userName, key: 'name', hint: '来信人', readonly: true, label: '' },
            { type: 'text', value: data.user.mobile, key: 'phone', hint: '联系方式', readonly: true, label: '' },
            { type: 'text', value: '', key: 'email', hint: '电子邮件', noRequired: true },
            { type: 'text', value: '', key: 'title', hint: '请输入标题', label: '标题' },
            { type: 'textarea', value: '', key: 'content', hint: '请输入内容', maxlength: 1000, label: '内容' }
          ]
          break
        case 'resumption':
          data.module = 'committeesay'
          data.items = [
            { type: 'textarea', value: localStorage.getItem('PerformanceCircleContent'), key: 'content', hint: '请输入内容', maxlength: 1000, label: '内容' },
            { type: 'pic', uploadType: 'uploadFile', value: '', key: 'attachmentIds', noRequired: true, label: '图片', max: 6, data: [], module: 'lzbl' }
          ]
          var attachmentList = JSON.parse(localStorage.getItem('PerformanceCircleFileImg')) || []
          var showItem = $general.getItemForKey('attachmentIds', data.items)
          if (showItem) {
            showItem.data = []
            for (var i = 0; i < attachmentList.length; i++) {
              var nItem = attachmentList[i]
              var id = nItem.uploadId || ''
              // var name = nItem.fileName || ''
              var url = nItem.url || ''
              showItem.data.push({ url: url, uploadUrl: url, uploadId: id, state: 2, module: showItem.module || '', _fileAjax: true })
            }
          }
          break
        case 'photograpr':
          data.module = 'showyourself'
          data.items = [
            { type: 'textarea', value: '', key: 'content', hint: '请输入内容', maxlength: 1000, label: '内容' },
            { type: 'pic', uploadType: 'uploadFile', value: '', key: 'attachmentIds', noRequired: true, label: '图片', max: 6, data: [], module: 'lzbl' }
          ]
          break
      }
    }

    // 代表履职圈输入正文失去焦点
    const textareaBlur = (event) => {
      localStorage.setItem('PerformanceCircleContent', event.target.value)
    }

    watch(() => data.dataList, (newName, oldName) => {

    })

    const openUnitDialog = (_item) => {
      var myParam = {}
      myParam.title = '选择' + _item.label
      myParam.selectMax = _item.max || 0//
      myParam.selectStyle = _item.selectStyle || 0
      myParam.selectData = _item.value || []
      myParam.data = _item.data || []
      myParam.caveat = _item.caveat || ''
      myParam.chooseUnitItemList = _item.value || ''
      myParam.params = _item.params || {}
      data.selectUnitParams = myParam
      data.nowItem = _item
      data.showUnit = true
    }

    const getUnit = () => {
      $general.getItemForKey(data.nowItem.key, data.items, 'key').value = data.unitComponts.chooseUnitItemList || []
      console.log($general.getItemForKey(data.nowItem.key, data.items, 'key'))
      data.showUnit = false
    }

    const openUserFn = (_item, _bItem, _agree) => {
      var maxLength = _item.max || -1// 最多选择人数
      data.max = maxLength
      if (maxLength > 0 && _item.data.length >= maxLength) {
        Toast('最多只能选择' + maxLength + (_item.type === 'user' ? '人' : '个'))
        return
      }
      if (_item.hintText && !_agree) {
        Dialog.confirm({
          title: '提示',
          message: _item.hintText
        }).then(function () {
          openUserFn(_item, _bItem, true)
        }).catch(function () {
        })
        return
      }
      data.nowItem = _item
      data.show = true
    }
    const getUser = () => {
      $general.getItemForKey(data.nowItem.key, data.items, 'key').data = data.userComponts.selectUser || []
      data.show = false
    }
    const clickUser = (_item) => {
      router.push({ name: 'personData', query: { id: _item.id } })
    }
    const getSelData = async (_item, _key) => {
      // 是否能网络   已请求 则不再请求
      if (_item[_key ? ('ajax_' + _key) : '_selAjax']) { return }
      _item[_key ? ('ajax_' + _key) : '_selAjax'] = true// 是否请求过  有就不再请求
      var comparedKey = _key || _item.onlyKey || _item.key
      // var comparedData = 'data'
      var namekey = 'value'; var idkey = 'id'
      var res = {}
      var newData = []
      switch (comparedKey) {
        case 'firstCategory':// 社情民意类别
          res = await $api.activity.treelist({ treeType: 6 })
          break
        case 'bigType':// 活动大类
          res = await $api.activity.treelist({ treeType: 3 })
          break
        case 'structureId':// 重点工作类别
          res = await $api.keyWork.keyWorkTree({ module: data.module, isEnable: 1 })
          namekey = 'name'
          idkey = 'id'
          break
        case 'worryDegree':// 社情民意紧急程度
          res = await $api.activity.dictionaryPubkvs({ types: 'opinion_worry_degree' })
          break
        default:
          console.log((_item.label || _item.hint) + '没调接口')
          return
      }
      if (res) {
        var datas = res ? res.data || [] : []
        var dataLength = datas ? datas.length : 0
        // eslint-disable-next-line no-redeclare
        var newData = []
        if (comparedKey === 'typeId') { // 服务在线 服务类型
          newData = []
          datas.forEach((_eItem, _eIndex, _eArr) => {
            var item = { text: _eItem.name || '', value: _eItem.id || '', sort: 0, level: 1, defaultIndex: 0 }
            newData.push(item)
          })
          $general.getItemForKey(comparedKey, data.items, 'key').data = newData
          // that.pickerEcho(_item)
        } else if (comparedKey === 'firstCategory') { // 是社情民意分类
          newData = [{ text: '请选择', value: '' }]
          if (datas && dataLength !== 0) {
            for (var i = 0; i < dataLength; i++) {
              var text = datas[i].label || ''
              var value = datas[i].id || ''
              newData.push({ text: text, value: value })
            }
            $general.getItemForKey(comparedKey, data.items, 'key').data = newData
            if (comparedKey === 'firstCategory') { // 大类别就保存下来 小类别需要
              data.largeSmallClass = datas
              if (data.id) { // 避免 详情先获取的情况   这里再展示一下小类
                var firstCategory = $general.getItemForKey('firstCategory', data.items, 'key')// 得到大类item
                selChange(firstCategory, true)
              }
            }
          }
        } else if (comparedKey === 'bigType') { // 选择履职项大类
          _item.data = [{ text: '请选择', value: '' }]
          if (datas && dataLength !== 0) {
            for (i = 0; i < dataLength; i++) {
              text = datas[i].label || ''
              value = datas[i].id || ''
              _item.data.push({ text: text, value: value })
            }
            if (comparedKey === 'bigType') { // 大类别就保存下来 小类别需要
              data.largeSmallClass = datas
              if (data.id) { // 避免 详情先获取的情况 这里再展示一下小类
                var bigType = $general.getItemForKey('bigType', data.items)// 得到大类item
                selChange(bigType, true)
              }
            }
          }
        } else if (comparedKey === 'worryDegree') { // 社情民意紧急程度
          datas = res.data || {}
          // eslint-disable-next-line camelcase
          const opinion_worry_degree = datas.opinion_worry_degree || []
          var worryDegree = $general.getItemForKey('worryDegree', data.items, 'key')
          worryDegree.data = [{ text: '请选择', value: '' }]
          // eslint-disable-next-line no-redeclare
          for (var i = 0; i < opinion_worry_degree.length; i++) {
            worryDegree.data.push({ text: opinion_worry_degree[i].value, value: opinion_worry_degree[i].id })
          }
        } else {
          _item.data = [{ text: '请选择', value: '' }]
          if (datas && dataLength !== 0) {
            // eslint-disable-next-line no-redeclare
            for (var i = 0; i < dataLength; i++) {
              text = datas[i][namekey] || ''
              value = datas[i][idkey] || ''
              _item.data.push({ text: text, value: value })
            }
          }
        }
      }
    }
    const switchChange = (_item) => {
      switch (_item.key) {
        case 'ifSecret':// 是否涉密监听
          if (_item.value) {
            Dialog.alert({
              message: '按照提案工作条例，泄密事项不得作为提案提交！'
            }).then(function () {
              _item.value = false
            })
          }
          break
        case 'hasLetter':// 接待审批  有无公函
          var attachmentIds = $general.getItemForKey('attachmentIds', data.items)
          var letterText = $general.getItemForKey('letterText', data.items);
          (attachmentIds || {}).hide = !_item.value;
          (letterText || {}).hide = _item.value
          break
      }
    }
    const selChange = (_item, _Unchangeable) => {
      switch (_item.key) {
        case 'firstCategory':// 选择大类的时候  展示小类
        case 'contentMainType':// 选择大类的时候  展示小类
          var findKey = ''
          if (_item.key === 'firstCategory') {
            findKey = 'secondCategory'
          } else if (_item.key === 'contentMainType') {
            findKey = 'contentDetailType'
          }
          var secondCategory = $general.getItemForKey(findKey, data.items, 'key')
          if (!_Unchangeable) { secondCategory.value = '' }
          secondCategory.data = [{ text: '请选择', value: '' }]
          // eslint-disable-next-line no-unused-vars
          var datas = []
          for (var i = 0; i < data.largeSmallClass.length; i++) {
            if (data.largeSmallClass[i].id === _item.value) {
              datas = data.largeSmallClass[i].children || []
            }
          }
          var dataLength = data ? datas.length : 0
          // eslint-disable-next-line no-redeclare
          for (var i = 0; i < dataLength; i++) {
            var text = datas[i].label || ''
            var value = datas[i].id || ''
            secondCategory.data.push({ text: text, value: value })
          }
          break
        case 'bigType':// 选择大类的时候展示小类
        case 'smallType':// 选择大类的时候展示小类
          findKey = ''
          if (_item.key === 'bigType') {
            findKey = 'smallType'
          }
          var smallType = $general.getItemForKey(findKey, data.items)
          if (!_Unchangeable) { smallType.value = '' }
          smallType.data = [{ text: '请选择', value: '' }]
          datas = []
          for (i = 0; i < data.largeSmallClass.length; i++) {
            if (data.largeSmallClass[i].id === _item.value) {
              datas = data.largeSmallClass[i].children || []
            }
          }
          dataLength = data ? datas.length : 0
          for (i = 0; i < dataLength; i++) {
            text = datas[i].label || ''
            value = datas[i].id || ''
            smallType.data.push({ text: text, value: value })
          }
          break
      }
    }

    // 展示历史数据
    const showInfoData = (_data) => {
      for (var i = 0; i < data.items.length; i++) {
        try {
          var nItem = data.items[i]
          if (nItem.notUpdate) { // 不处理内容  不提交时  没有key时
            continue
          }
          var nowKey = nItem.showKey || nItem.key
          var nValue = _data[nowKey] ? _data[nowKey] : ''// 当前字段是否有值
          if (nItem.type === 'switch') { // 处理 1是0否
            nItem.value = nValue === '1'
            switchChange(nItem, true)
          } else if (nItem.type === 'calendar' && nItem.nType === 'range') { // 日历区间
            var startTime = (_data[nItem.key.split(',')[0]] || '').split(' ')[0]
            var endTime = (_data[nItem.key.split(',')[1]] || '').split(' ')[0]
            if (startTime && endTime) {
              nItem.value = startTime + '至' + endTime
              nItem.defaultDate = [new Date(startTime), new Date(endTime)]
            }
          } else if (nItem.type === 'user' && nItem.tStyle === 'label') { // 处理
            nItem.value = nValue ? nValue.split(',') : []
          } else if (nItem.type === 'user' && nItem.key === 'otherMemberJoiners') { // 处理
            nItem.value = ''
            nItem.data = [];
            (nValue || []).forEach(function (item, index) {
              var param = {}
              nItem.value += (nItem.value ? ',' : '') + item.userId
              param.id = item.userId
              param.name = item.userName
              param.url = item.headImg || '../../assets/img/icon_default_user.png'
              nItem.data.push(param)
              data.selectUser.push(param)
            })
          } else if (nItem.type === 'user' && nItem.key === 'applyUserId') { // 处理 报账回显发起人
            nItem.value = ''
            nItem.data = []
            nItem.value = _data.applyUserId
            var param = {}
            param.id = _data.applyUserId
            param.name = _data.applyUserName
            param.url = _data.applyUserHeadImg || '../../assets/img/icon_default_user.png'
            nItem.data.push(param)
          } else if (nItem.type === 'pic' || nItem.type === 'attach') { // 是附件相关样式
            nItem.data = [];
            (nValue || []).forEach(function (_eItem, _eIndex, _eArr) {
              var id = _eItem.id || ''
              // var name = _eItem.fileName || ''
              var url = _eItem.filePath || ''
              nItem.data.push({ url: url, uploadUrl: url, uploadId: id, state: 2, module: nItem.module || data.module, _fileAjax: true })
            })
          } else {
            if (nItem.key === 'content') {
              nItem.value = $general.clearRichText(nValue, false, true, true, true)
            } else if (nItem.key === 'otherUserJoiners') {
              nItem.value = '';
              (nValue || []).forEach(function (item, index) {
                nItem.value += (nItem.value ? ',' : '') + item.userName
              })
            } else if (nItem.key === 'firstCategory' || nItem.key === 'worryDegree') {
              nItem.value = $general.getItemForKey(nValue, nItem.data, 'text').value
            } else if (nItem.key === 'secondCategory') {
              var firstCategoryId = $general.getItemForKey('firstCategory', data.items, 'key').value
              var secondCategoryData = $general.getItemForKey(firstCategoryId, data.largeSmallClass, 'id').children
              nItem.data = [{ text: '请选择', value: '' }]
              secondCategoryData.forEach(function (item, index) {
                var param = {}
                param.text = item.name
                param.value = item.id
                nItem.data.push(param)
              })
              nItem.value = $general.getItemForKey(nValue, nItem.data, 'text').value
            } else {
              nItem.value = nValue.toString()
              if (nItem.type === 'select') { // 选择 处理
                selChange(nItem, true)
              } else if (nItem.type === 'time' && nItem.value) { // 时间处理回显
                nItem.value = dayjs(nItem.value).format(nItem.nType === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm')
              }
            }
          }
          if (data.modificationSubmit) { // 提交必须修改时 保存一下当前值
            nItem.oldValue = ($general.isArray(nItem.value) || $general.isObject(nItem.value)) ? JSON.parse(JSON.stringify(nItem.value)) : nItem.value
          }
        } catch (e) {
          console.log(e)
        }
      }
    }

    // 点击打开时间没有初始时间时 设置当前时间
    const openTimeFn = (_item) => {
      if (!_item.value) {
        _item.timeValue = new Date()
      } else {
        _item.timeValue = new Date(_item.value.replace(/-/g, '/'))
      }
      _item.show = true
    }
    // 点击时间取消
    const cancelFn = (_item) => {
      _item.show = false
    }
    // 时间点击确定 并设置格式显示
    const confirmFn = (_item) => {
      _item.value = $general.getTime(_item.timeValue)
      if (_item.nType === 'date') { // 只选年月日
        _item.value = _item.value.split(' ')[0]
      } else {
        _item.value = _item.value.substring(0, _item.value.lastIndexOf(':'))
      }
      _item.show = false
    }
    // 格式化时间
    const formatter = (type, value) => {
      if (type === 'year') {
        return value + '年'
      } else if (type === 'month') {
        return value + '月'
      } else if (type === 'day') {
        return value + '日'
      } else if (type === 'hour') {
        return value + '时'
      } else if (type === 'minute') {
        return value + '分'
      }
      return value
    }
    // 日历选择完成
    const onConfirmCalendar = (_date, _item) => {
      _item.show = false
      _item.value = $general.getTime(_date[0]).split(' ')[0] + '至' + $general.getTime(_date[1]).split(' ')[0]
    }
    // 可选择最小时间
    const calendarMinDate = () => {
      var nowDate = new Date()
      nowDate.setMonth(nowDate.getMonth() - (2))
      return nowDate
    }
    // 可选择最大时间
    const calendarMaxDate = () => {
      var nowDate = new Date()
      nowDate.setMonth(nowDate.getMonth() + (10))
      return nowDate
    }

    const onRefresh = () => {
    }
    const onLoad = () => {

    }
    const test = (val) => {
      console.log(val)
    }
    const afterReadFile = async (file) => {
      const item = { url: file.content, name: '', uploadUrl: '', uploadId: '', state: 0, module: data.module }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', data.module)
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.state = 2
        item.name = info.fileName || ''
        item.uploadUrl = info.filePath || ''
        item.uploadId = info.id || ''
      } else {
        item.state = 3
        item.error = ret.errmsg || ''
      }
      var attachmentIds = $general.getItemForKey('attachmentIds', data.items)// 得到大类item
      attachmentIds.data.push(item)
    }
    const afterReadImg = async (file) => {
      const item = { url: file.content, uploadUrl: '', name: '', uploadId: '', state: 0, module: data.module }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', data.module)
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.state = 2
        item.uploadUrl = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.state = 3
        item.error = ret.errmsg || ''
      }
      var attachmentIds = $general.getItemForKey('attachmentIds', data.items)// 得到大类item
      attachmentIds.data.push(item)
    }

    const openAttach = (type) => {
      if (data.nImgs.length >= 9) {
        Toast('最多上传9张图片')
        return
      }
      switch (type) {
        case 1:
          data.file[0].chooseFile()
          break
        case 2:
          data.image[0].chooseFile()
          break
      }
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), textareaBlur, ImagePreview, imgUploader, clickUploader, vanUploader, onClickLeft, test, afterReadImg, afterReadFile, openAttach, onRefresh, onLoad, $general, confirm, getSelData, selChange, openUserFn, openUnitDialog, getUser, getUnit, clickUser, switchChange, submit, openTimeFn, cancelFn, confirmFn, formatter, onConfirmCalendar, calendarMinDate, calendarMaxDate }
  }
}
</script>
<style lang="less" scoped>
.addNew {
  background: #f8f8f8;

  .van-cell-group {
    background-color: rgba(0, 0, 0, 0);
  }

  .imgloager {
    display: flex;
    flex-wrap: wrap;

    .img_box {
      margin-right: 10px;
      position: relative;

      .clear {
        position: absolute;
        top: 0;
        right: 0;
        font-size: 16px;
        z-index: 999;
      }
    }

    .photo {
      width: 2.13333rem;
      height: 2.13333rem;
      margin: 0 0.21333rem 0.21333rem 0;
      border-radius: 0.10667rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f7f8fa;
      color: #dcdee0;
      font-size: 24px;
    }
  }

  .list_item {
    // margin-bottom: 14px;
    font-size: inherit;
    font-family: inherit;
  }

  [class*="van-hairline"]::after {
    border: 0 solid #fff;
  }

  .van-field__label {
    width: auto;
    max-width: 150px;
    padding: 0 5px;
  }

  .enclosure {
    background: #fff;
    padding: 0.1rem;
    height: 300px;
    width: 100%;
  }

  footer {
    background: #fff;
    padding: 12px 10px;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    width: 100%;
  }

  #app footer .van-button {
    padding: 13px 21px;
  }

  .van-button+.van-button {
    margin-left: 5px;
  }

  .colorGray .van-cell__title span {
    color: #828181;
  }

  .colorGray .van-cell__value {
    display: box;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-flex-flow: column;
    flex-flow: column;
    height: 100%;
    -webkit-flex-direction: column;
    flex-direction: column;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
  }

  .add_warp {
    padding: 5px;
    background: #fff;
    box-sizing: border-box;
  }

  .user_item {
    width: 70px;
    text-align: center;
    display: inline-block;
    position: relative;
    margin: 10px 5px;
    box-sizing: border-box;
  }

  .picUploader {
    background: #fff;
    overflow: hidden;
    margin-top: 10px;
    padding: 10px;

    .picUploader_title {
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }

  .user_item .userImg {}

  .user_item .user_name {
    color: #555;
    margin-top: 5px;
  }

  .user_box {
    white-space: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .delUser {
    position: absolute;
    top: -5px;
    right: 2px;
    z-index: 99;
  }

  .caveat {
    color: #e83b29;
    background: #fff;
    padding-left: 12px;
    padding-top: 5px;
  }

  .user2_item {
    width: 33.333%;
    padding: 4px 5px;
    box-sizing: border-box;
  }

  .list_item .van-radio+.van-radio {
    margin-left: 10px;
  }

  .user_warp .van-icon {
    color: #919191;
  }

  .user_warp .right * {
    color: #919191;
  }

  .add_select {
    margin: 0 5px;
    width: 100%;
  }

  .add_select .van-dropdown-menu__item {
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    -moz-box-pack: flex-end;
    -webkit--moz-box-pack: flex-end;
    box-pack: flex-end;
  }

  .process_item {
    position: relative;
    min-height: 82px;
    padding-left: 50px;
  }

  .process_point {
    position: absolute;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #e6e5e8;
    left: 20px;
  }

  .process_line_top {
    position: absolute;
    width: 1px;
    height: 50%;
    background: #e6e5e8;
    left: 26px;
    top: 0;
  }

  .process_line_bottom {
    position: absolute;
    width: 1px;
    height: 50%;
    background: #e6e5e8;
    left: 26px;
    bottom: 0;
  }

  .process_item_hint {
    color: #606060;
    padding-bottom: 10px;
  }

  .process_item .user_box {
    margin: 0;
    min-height: 82px;
  }

  .process_user_name {
    color: #555;
    margin-top: 5px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    position: absolute;
    bottom: -12px;
    width: 100%;
    text-align: center;
  }

  .table_hint {
    border-top: 1px solid #e6e5e8;
    border-right: 1px solid #e6e5e8;
    border-left: 1px solid #e6e5e8;
    padding: 10px 0;
    position: relative;
  }

  .table_header_add {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
  }

  .table_item {
    border: 1px solid #e6e5e8;
    border-bottom: 0px solid #e6e5e8;
    min-height: 35px;
    background: #fff;
    position: relative;
  }

  .table_item:last-child {
    border-bottom: 1px solid #e6e5e8;
  }

  .table_label {
    padding: 3px 3px;
    border-left: 1px solid #e6e5e8;
  }

  .table_item .table_label:first-child {
    border-left: 0px solid #e6e5e8;
  }

  .table_label input {
    width: 1px;
  }

  .table_label .van-icon+.van-icon {
    margin-left: 5px;
  }

  .Identify_btn {
    text-align: center;
    color: #333;
    width: 100%;
  }

  .Identify_btn+.Identify_btn {
    border-left: 1px solid #eee;
  }

  .readTitleImg {
    width: 2px;
    height: 14px;
    background: #3088fe;
    opacity: 1;
    border-radius: 10px;
    margin: 0 10px;
  }

  .readTitle {
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
    color: #333333;
  }

  .list_item .van-cell::after {
    border-bottom: 0;
  }

  .list_addbtn {
    background: #fff;
    padding: 5px 10px;
  }

  #app .list_addbtn .van-button {
    padding: 5px 22px;
  }

  .fileName {
    font-size: 0.1rem;
  }

  .footerBtn {
    background: #fff;
    padding: 5px 0;
    position: fixed;
    bottom: 0;
    width: 100%;

    .footerBtnBox {
      width: calc(100% - 20px);
      margin-left: 10px;

      .van-button+.van-button {
        width: calc(100% - 20px);
        margin-left: 10px;
      }
    }
  }
}
</style>

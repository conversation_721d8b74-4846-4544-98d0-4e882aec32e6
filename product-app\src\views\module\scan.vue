<template>
  <div class="scan">
    <div class="reader-box">
      <div class="reader" id="reader"></div>
      <div class="area flex_placeholder">
        <div class="scanArea_prompt">将二维码/条码放入框内，即可自动扫描</div>
      </div>
    </div>
    <footer class="flex_box footer_box">
      <div class="footer_body">
        <svg t="1650876409672" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="2058">
          <path
            d="M213.333333 213.333333v128H128V128h213.333333v85.333333h-128z m0 597.333334h128v85.333333H128V682.666667h85.333333v128z m597.333334-597.333334h-128V128h213.333333v213.333333h-85.333333v-128z m0 597.333334v-128h85.333333v213.333333H682.666667v-85.333333h128zM128 469.333333h768v85.333334H128v-85.333334z"
            :fill="appTheme" p-id="2059"></path>
        </svg>
        <div :style="'margin-top:0.03rem;font-size:14px;color:' + appTheme" @click="scanClick">扫码</div>
      </div>
    </footer>
  </div>
</template>
<script>
// import { Html5Qrcode } from 'html5-qrcode'
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { Image as VanImage, ImagePreview, Dialog, Toast } from 'vant'
import wx from '../../static/wx'
// import CryptoJS from '../../static/crypto'
export default {
  name: 'scan',
  components: {
    // Html5Qrcode,
    [Dialog.Component.name]: Dialog.Component,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  setup () {
    const $appTheme = inject('$appTheme')
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const CryptoJS = require('crypto-js')
    const data = reactive({
      appTheme: $appTheme,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      html5Qrcode: null,
      newTime: dayjs().unix(),
      noncestr: 'shandongoffice',
      ticket: ''
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      signature()
    })
    const scanClick = () => {
      signature()
    }
    // 先通过接口，获取签名信息
    const signature = async () => {
      var res = await $api.scan.signature()
      data.ticket = ''
      if (res.errcode === 200) {
        data.ticket = res.data.ticket // 保存接口返回的签名信息
        // 获取到签名信息后，就要开始调用山东通的SDK，首先需要完成权限验证
        ticketHandle(data.ticket)
      }
    }
    const ticketHandle = async (num) => {
      var data = await wx
      var nonceStr = uuid() // 随机字符串
      var timestamp = data.newTime // 时间戳
      var url = window.location.href.split('#')[0] // 当前页面的url
      var jsApiList = ['scanQRCode']
      var appId = 'ww2389532372dc6805'
      var str = ''
      data.ready(function (e) { })
      data.error(function (res) { })
      str = 'jsapi_ticket=' + num + '&noncestr=' + nonceStr + '&timestamp=' + timestamp + '&url=' + url
      data.config({
        beta: true, // 调用wx.invoke形式的接口值时，该值必须为true。
        // debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: appId, // 必填，山东通的cropID
        timestamp: timestamp, // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，生成签名的随机串
        signature: CryptoJS.SHA1(str).toString(), // 必填，签名，见附录1
        jsApiList: jsApiList // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
      })
      data.scanQRCode({
        desc: 'scanQRCode desc',
        needResult: 1, // 默认为0，扫描结果由山东通处理，1则直接返回扫描结果，
        scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
        success: function (res) {
          // 回调
          console.log(res, '回调')
          var result = res.resultStr
          startScan(result)
        },
        error: function (res) {
          if (res.errMsg.indexOf('function_not_exist') > 0) {
            alert('版本过低请升级')
          }
        }
      })
    }
    const uuid = () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0
        var v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    }

    const startScan = async (decodeText) => {
      var params = decodeText.split('|')
      switch (params[1]) {
        case 'activitySignIn':
        case 'meetingSignIn':
          var id = params[2]
          if (params[1] === 'meetingSignIn') {
            try {
              const res = await $api.conferenceActivitiesFile.conferenceAddMySignIn({
                activityId: id,
                conferenceId: id,
                signInType: 'qrCode',
                userId: data.user.id,
                dataId: id,
                type: 'signIn'
              })
              if (!res) {
                Toast('请检查网络!')
                return
              }
              if (res) {
                var code = res.errcode || 0
                if (code === 200) {
                  Dialog.alert({
                    title: '提示',
                    message: res.errmsg || res.data,
                    confirmButtonText: '我知道了'
                  }).then(async () => {
                    setTimeout(() => {
                      router.go(-1)
                    }, 1000)
                  }).catch(() => {
                    // on cancel
                  })
                }
              }
            } catch (error) {
              Dialog.alert({
                title: '提示',
                message: error.data.errmsg || error.data.data,
                confirmButtonText: '我知道了'
              }).then(async () => {
                setTimeout(() => {
                  router.go(-1)
                }, 1000)
              }).catch(() => {
                // on cancel
              })
              // return
            }
          } else if (params[1] === 'activitySignIn') {
            try {
              const res = await $api.conferenceActivitiesFile.activityaddMySignIn({
                activityId: id,
                conferenceId: id,
                signInType: 'qrCode',
                userId: data.user.id,
                dataId: id,
                type: 'signIn'
              })
              if (!res) {
                Toast('请检查网络!')
                return
              }
              if (res) {
                var code1 = res.errcode || 0
                if (code1 === 200) {
                  Dialog.alert({
                    title: '提示',
                    message: res.errmsg || res.data,
                    confirmButtonText: '我知道了'
                  }).then(async () => {
                    setTimeout(() => {
                      router.go(-1)
                    }, 1000)
                  }).catch(() => {
                    // on cancel
                  })
                }
              }
            } catch (error) {
              Dialog.alert({
                title: '提示',
                message: error.data.errmsg || error.data.data,
                confirmButtonText: '我知道了'
              }).then(async () => {
                setTimeout(() => {
                  router.go(-1)
                }, 1000)
              }).catch(() => {
                // on cancel
              })
              // return
            }
          }
          break
        case 'login':
          var qrCodeId = params[2]
          var postParams = {
            qrCodeId: qrCodeId
          }
          try {
            const res3 = await $api.conferenceActivitiesFile.apptoken(postParams)
            if (!res3) {
              Toast('请检查网络!')
              return
            }
            if (res3) {
              var code3 = res3.errcode || 0
              if (code3 === 200) {
                Dialog.alert({
                  title: '提示',
                  message: res3.errmsg || res3.data || '登录成功',
                  confirmButtonText: '我知道了'
                }).then(async () => {
                  setTimeout(() => {
                    router.go(-1)
                  }, 1000)
                }).catch(() => {
                  // on cancel
                })
              }
            }
          } catch (error) {
            Dialog.alert({
              title: '提示',
              message: error.data.errmsg || error.data.data,
              confirmButtonText: '我知道了'
            }).then(async () => {
              setTimeout(() => {
                router.go(-1)
              }, 1000)
            }).catch(() => {
              // on cancel
            })
            // return
          }
          break
        default:
          break
      }
    }
    // const startScan = () => {
    // apptoken?"qrCodeId": params[2]
    //   Html5Qrcode.getCameras().then(devices => {
    //     if (devices && devices.length) {
    //       data.html5Qrcode = new Html5Qrcode('reader')
    //       data.html5Qrcode.start({
    //         facingMode: 'environment'
    //       }, {
    //         fps: 24,
    //         qrbox: 280
    //       }, async (decodeText, decodeResult) => {
    //         if (decodeText) {
    //           var params = decodeText.split('|')
    //           switch (params[1]) {
    //             case 'activitySignIn':
    //             case 'meetingSignIn':
    //               var id = params[2]
    //               if (params[1] === 'meetingSignIn') {
    //                 try {
    //                   const res = await $api.conferenceActivitiesFile.conferenceAddMySignIn({
    //                     activityId: id,
    //                     conferenceId: id,
    //                     signInType: 'qrCode',
    //                     userId: data.user.id,
    //                     dataId: id,
    //                     type: 'signIn'
    //                   })
    //                   if (!res) {
    //                     Toast('请检查网络!')
    //                     return
    //                   }
    //                   if (res) {
    //                     var code = res.errcode || 0
    //                     if (code === 200) {
    //                       Dialog.alert({
    //                         title: '提示',
    //                         message: res.errmsg || res.data,
    //                         confirmButtonText: '我知道了'
    //                       }).then(async () => {
    //                         setTimeout(() => {
    //                           router.go(-1)
    //                         }, 1000)
    //                       }).catch(() => {
    //                         // on cancel
    //                       })
    //                     }
    //                   }
    //                 } catch (error) {
    //                   Dialog.alert({
    //                     title: '提示',
    //                     message: error.data.errmsg || error.data.data,
    //                     confirmButtonText: '我知道了'
    //                   }).then(async () => {
    //                     setTimeout(() => {
    //                       router.go(-1)
    //                     }, 1000)
    //                   }).catch(() => {
    //                     // on cancel
    //                   })
    //                   return
    //                 }
    //               } else if (params[1] === 'activitySignIn') {
    //                 try {
    //                   const res = await $api.conferenceActivitiesFile.activityaddMySignIn({
    //                     activityId: id,
    //                     conferenceId: id,
    //                     signInType: 'qrCode',
    //                     userId: data.user.id,
    //                     dataId: id,
    //                     type: 'signIn'
    //                   })
    //                   if (!res) {
    //                     Toast('请检查网络!')
    //                     return
    //                   }
    //                   if (res) {
    //                     var code1 = res.errcode || 0
    //                     if (code1 === 200) {
    //                       Dialog.alert({
    //                         title: '提示',
    //                         message: res.errmsg || res.data,
    //                         confirmButtonText: '我知道了'
    //                       }).then(async () => {
    //                         setTimeout(() => {
    //                           router.go(-1)
    //                         }, 1000)
    //                       }).catch(() => {
    //                         // on cancel
    //                       })
    //                     }
    //                   }
    //                 } catch (error) {
    //                   Dialog.alert({
    //                     title: '提示',
    //                     message: error.data.errmsg || error.data.data,
    //                     confirmButtonText: '我知道了'
    //                   }).then(async () => {
    //                     setTimeout(() => {
    //                       router.go(-1)
    //                     }, 1000)
    //                   }).catch(() => {
    //                     // on cancel
    //                   })
    //                   return
    //                 }
    //               }
    //               break
    //             default:
    //               break
    //           }
    //           stopScan()
    //         }
    //       }, (err) => {
    //         console.log('err', err)
    //       })
    //     }
    //   })
    // }
    // const stopScan = () => {
    //   // data.html5Qrcode.stop()
    //   setTimeout(() => {
    //     router.go(-1)
    //   }, 1000)
    // }

    return { ...toRefs(data), dayjs, router, $general, scanClick }
  }
}
</script>
<style lang="less" scoped>
.scan {
  box-sizing: border-box;
  padding-bottom: 50px;

  .container {
    height: 100%;
  }

  .reader-box {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .reader {
    width: 540rpx;
    height: 540rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .area {
    // background: rgba(0, 0, 0, 0.4);
    position: relative;
    top: 33%;
  }

  .scanArea2_2 {
    width: 2.1rem;
    position: relative;
  }

  .scanArea2_2 img {
    width: 100%;
  }

  .scanArea_prompt {
    padding-top: 20px;
    width: 100%;
    color: #bcbcbc;
    text-align: center;
  }

  .footer_box {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;

    .footer_body {
      color: #b7b7b7;
      text-align: center;
    }
  }
}
</style>

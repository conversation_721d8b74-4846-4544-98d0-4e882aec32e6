<template>
  <div class="electronicreadingItemDetails">
    <header v-if="scrollTop>150"
            class="header flex_box"
            :style="'padding-top:'+safeAreaTop+'px;background: #3f7ed5' + ';opacity:'+(scrollTop/380)">
      <div class="flex_placeholder flex_box flex_align_center flex_justify_content new_title">
        <span style="font-size: 0.4rem;color: #fff;text-align: center;margin: 0.2rem;"
              v-html="name"></span>
      </div>
    </header>
    <!-- 竖屏 -->
    <div v-if="Verticalscreen==='1'">
      <div class="bg_one">
        <div class="top_big">
          <div class="top_name">{{name}}</div>
          <div class="top_time"
               v-if="time">{{time}}</div>
          <div class="top_address">{{address}}</div>
        </div>
      </div>
      <div class="fiveBtn">
        <div class="fiveBtn_item"
             v-for="item in fiveBtn"
             :key="item.id"
             @click="fiveBtnClick(item)">
          <!-- <div> -->
          <img :src="item.imgBtn"
               alt=""
               style="width: 60%;">
          <!-- </div> -->
          <div style="color: #333333;margin-top:8px;font-size: 0.3rem;">{{item.name}}</div>
        </div>
      </div>
      <!-- 会议通知 -->
      <!-- <div v-if="fiveId === 1"> -->
      <div class="content_title_box">
        <div class="content_border"></div>
        <div style="margin-left: 6px;color:#0D6AD8;font-weight: 800;">会议通知</div>
      </div>
      <div :class="meetContent?'content':'nullContent'"
           v-html="meetContent?meetContent:''"></div>
      <!--展示附件-->
      <div v-if="attachInfo.data.length != 0"
           style="background:#fff;">
        <div class="general_attach">
          <div v-for="(item,index) in attachInfo.data"
               :key="index"
               class="general_attach_item flex_box flex_align_center click"
               @click="annexClick(item,false)">
            <img class="general_attach_icon"
                 style="width: 0.4rem;height: 0.5rem;"
                 :src="require('../../assets/img/fileicon/icon_pdf.png')" />
            <div class="flex_placeholder flex_box flex_align_center">
              <div class="general_attach_name text_one2"
                   style="font-size: 0.34rem;">{{item.name}}
              </div>
              <div class="general_attach_size"
                   style="font-size: 0.22rem;">{{general.getFileSize(item.size)}}
              </div>
            </div>
            <div v-if="item.state != 2"
                 class="general_attach_state flex_box flex_align_center flex_justify_content"
                 :style="general.loadConfigurationSize([7,7])">
              <van-icon v-if="item.state == 0"
                        class-prefix="iconfont"
                        color="#ccc"
                        :size="((appFontSize+3)*0.01)+'rem'"
                        name="xiazai"></van-icon>
              <van-circle v-else-if="item.state == 1"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          v-model="item.schedule"
                          :rate="item.schedule"
                          stroke-width="150"></van-circle>
              <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                        v-else-if="item.state == 3"
                        color="#ccc"
                        :size="((appFontSize+3)*0.01)+'rem'"
                        name="warning-o"></van-icon>
            </div>
          </div>
        </div>
      </div>
      <!-- </div> -->
      <!-- 会议议程 -->
      <!-- <div v-if="fiveId === 2"> -->
      <!-- <div class="content_title_box">
          <div class="content_border"></div>
          <div style="margin-left: 6px;color:#0D6AD8;font-weight: 800;">会议议程</div>
        </div>
        <div :class="meetContent?'content':'nullContent'"
             v-html="meetContent?meetContent:'暂无内容'"></div> -->
      <!--展示附件-->
      <!-- <div v-if="attachInfo.data.length != 0"
             style="background:#fff;">
          <div class="general_attach">
            <div v-for="(item,index) in attachInfo.data"
                 :key="index"
                 class="general_attach_item flex_box flex_align_center click"
                 @click="annexClick(item,false)">
              <img class="general_attach_icon"
                   style="width: 0.4rem;height: 0.5rem;"
                   :src="require('../../assets/img/fileicon/icon_pdf.png')" />
              <div class="flex_placeholder flex_box flex_align_center">
                <div class="general_attach_name text_one2"
                     style="font-size: 0.34rem;">{{item.name}}
                </div>
                <div class="general_attach_size"
                     style="font-size: 0.22rem;">{{general.getFileSize(item.size)}}
                </div>
              </div>
              <div v-if="item.state != 2"
                   class="general_attach_state flex_box flex_align_center flex_justify_content"
                   :style="general.loadConfigurationSize([7,7])">
                <van-icon v-if="item.state == 0"
                          class-prefix="iconfont"
                          color="#ccc"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          name="xiazai"></van-icon>
                <van-circle v-else-if="item.state == 1"
                            :size="((appFontSize+3)*0.01)+'rem'"
                            v-model="item.schedule"
                            :rate="item.schedule"
                            stroke-width="150"></van-circle>
                <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                          v-else-if="item.state == 3"
                          color="#ccc"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          name="warning-o"></van-icon>
              </div>
            </div>
          </div>
        </div> -->
      <!-- </div> -->
    </div>
    <!-- 点击按钮来切换横屏 -->
    <div v-if="TogglehorizontalScreen==='1'"
         class="clickAutomatic">
      <div class="bg_one">
        <div class="top_big">
          <div class="top_name">{{name}}</div>
          <div class="top_time">{{time}}</div>
          <div class="top_address">{{address}}</div>
        </div>
      </div>
      <div class="fiveBtn">
        <div class="fiveBtn_item"
             v-for="item in fiveBtn"
             :key="item.id"
             @click="fiveBtnClick(item)">
          <img :src="item.imgBtn"
               alt=""
               style="width: 60%;">
          <div style="color: #333333;margin-top:8px;font-size: 0.3rem;">{{item.name}}</div>
        </div>
      </div>
      <!-- 会议通知 -->
      <div class="content_title_box">
        <div class="content_border"></div>
        <div style="margin-left: 6px;color:#0D6AD8;font-weight: 800;">会议通知</div>
      </div>
      <div :class="meetContent?'content':'nullContent'"
           v-html="meetContent?meetContent:''"></div>
      <div v-if="attachInfo.data.length != 0"
           style="background:#fff;">
        <div class="general_attach">
          <div v-for="(item,index) in attachInfo.data"
               :key="index"
               class="general_attach_item flex_box flex_align_center click"
               @click="annexClick(item,false)">
            <img class="general_attach_icon"
                 style="width: 0.4rem;height: 0.5rem;"
                 :src="require('../../assets/img/fileicon/icon_pdf.png')" />
            <div class="flex_placeholder flex_box flex_align_center">
              <div class="general_attach_name text_one2"
                   style="font-size: 0.34rem;">{{item.name}}
              </div>
              <div class="general_attach_size"
                   style="font-size: 0.22rem;">{{general.getFileSize(item.size)}}
              </div>
            </div>
            <div v-if="item.state != 2"
                 class="general_attach_state flex_box flex_align_center flex_justify_content"
                 :style="general.loadConfigurationSize([7,7])">
              <van-icon v-if="item.state == 0"
                        class-prefix="iconfont"
                        color="#ccc"
                        :size="((appFontSize+3)*0.01)+'rem'"
                        name="xiazai"></van-icon>
              <van-circle v-else-if="item.state == 1"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          v-model="item.schedule"
                          :rate="item.schedule"
                          stroke-width="150"></van-circle>
              <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                        v-else-if="item.state == 3"
                        color="#ccc"
                        :size="((appFontSize+3)*0.01)+'rem'"
                        name="warning-o"></van-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 手机自动旋转切换横屏 -->
    <div v-if="toggleScreen1==='1'"
         class="AutomaticRotation">
      <div style="height: 2.3rem;display: flex;justify-content: center;">
        <img src="../../assets/img/qingdao/gengBg.png"
             alt=""
             style="width: 100%;height: 100%;">
        <div class="top_bigs">
          <div class="top_name">{{name}}</div>
          <div class="top_time">{{time}}</div>
          <div class="top_address">{{address}}</div>
        </div>
      </div>
      <div class="fiveBtn">
        <div class="fiveBtn_item"
             v-for="item in fiveBtn"
             :key="item.id"
             @click="fiveBtnClick(item)">
          <img :src="item.imgBtn"
               alt=""
               style="width: 60%;">
          <div style="color: #333333;margin-top:8px;font-size: 0.3rem;">{{item.name}}</div>
        </div>
      </div>
      <!-- 会议通知 -->
      <div class="content_title_box">
        <div class="content_border"></div>
        <div style="margin-left: 6px;color:#0D6AD8;font-weight: 800;">会议通知</div>
      </div>
      <div :class="meetContent?'content':'nullContent'"
           v-html="meetContent?meetContent:''"></div>
      <div v-if="attachInfo.data.length != 0"
           style="background:#fff;">
        <div class="general_attach">
          <div v-for="(item,index) in attachInfo.data"
               :key="index"
               class="general_attach_item flex_box flex_align_center click"
               @click="annexClick(item,false)">
            <img class="general_attach_icon"
                 style="width: 0.4rem;height: 0.5rem;"
                 :src="require('../../assets/img/fileicon/icon_pdf.png')" />
            <div class="flex_placeholder flex_box flex_align_center">
              <div class="general_attach_name text_one2"
                   style="font-size: 0.34rem;">{{item.name}}
              </div>
              <div class="general_attach_size"
                   style="font-size: 0.22rem;">{{general.getFileSize(item.size)}}
              </div>
            </div>
            <div v-if="item.state != 2"
                 class="general_attach_state flex_box flex_align_center flex_justify_content"
                 :style="general.loadConfigurationSize([7,7])">
              <van-icon v-if="item.state == 0"
                        class-prefix="iconfont"
                        color="#ccc"
                        :size="((appFontSize+3)*0.01)+'rem'"
                        name="xiazai"></van-icon>
              <van-circle v-else-if="item.state == 1"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          v-model="item.schedule"
                          :rate="item.schedule"
                          stroke-width="150"></van-circle>
              <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                        v-else-if="item.state == 3"
                        color="#ccc"
                        :size="((appFontSize+3)*0.01)+'rem'"
                        name="warning-o"></van-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--加载中提示 首次为骨架屏-->
    <!-- <div v-if="
             showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div> -->
    <!--返回顶部-->
    <ul v-if="footerBtnsShow"
        class="footer_btn_box"
        :style="'bottom:'+(safeAreaBottom+30)+'px;'">
      <transition name="van-slide-right">
        <div v-if="footerMore.show">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((general.appFontSize+25)*0.01)+'rem'"
                        name="upgrade"></van-icon>
            </div>
            <div v-if="item.type == 'btn'"
                 class="van-button-box"
                 style="font-size: 0.3rem;">
              <van-button loading-type="spinner"
                          :loading-size="((general.appFontSize)*0.01)+'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)"
                          :icon="item.icon">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </transition>
      <div v-if="title && footerBtns.length != 0"
           @click="footerMore.show = !footerMore.show;"
           :style="general.loadConfigurationSize(15)+'border-radius:50%;background:'+appTheme"
           class="footer_item flex_box flex_align_center flex_justify_content">
        <van-icon :size="((general.appFontSize+2)*0.01)+'rem'"
                  color="#FFF"
                  :name="footerMore.show?'arrow-down':'ellipsis'"></van-icon>
      </div>
    </ul>
    <!-- 参会完成核对个人信息弹框 -->
    <van-dialog v-model:show="showCheck"
                title="参会成功,请您核对个人信息"
                show-cancel-button
                confirmButtonColor="#3088fe"
                @confirm="Clickdialog">
      <van-field v-model="form.name"
                 label="姓名"
                 disabled
                 placeholder="请输入分类名字" />
      <van-field v-model="form.mobile"
                 label="手机号"
                 disabled
                 placeholder="请输入手机号" />
      <van-field v-model="form.position"
                 label="单位及职务"
                 placeholder="请输入单位及职务" />
    </van-dialog>
    <van-action-sheet v-model:show="showSignIn"
                      :description="description"
                      :actions="actions"
                      @select="onSelect"
                      cancel-text="取消" />
    <van-overlay :show="dialogShow"
                 @click="showSignIn = false">
      <van-dialog v-model:show="dialogShow"
                  :title="dialogTitle"
                  :width="'288px'"
                  :overlay="false"
                  @confirm="confirm"
                  show-cancel-button>
        <div class="inherit"
             style="padding: 20px 0 20px 0;">
          <!-- 密码输入框 -->
          <van-password-input :value="value"
                              :mask="false"
                              :length="signlength"
                              :focused="showKeyboard"
                              @focus="showKeyboard = true" />
        </div>

      </van-dialog>
      <!-- 数字键盘 -->
      <van-number-keyboard v-model="value"
                           :show="showKeyboard"
                           :z-index="'99'"
                           @blur="showKeyboard = false" />
    </van-overlay>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Toast, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'electronicreadingItemDetails2',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      scrollTop: 0,
      safeAreaBottom: 0,
      recordId: route.query.id || '',
      meetingTypeId: route.query.meetingTypeId,
      name: '',
      time: '',
      address: '',
      content: '',
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      module: '1',
      loading: false,
      finished: false,
      refreshing: false,
      fiveBtn: [],
      showCheck: false, // 参会完弹框
      form: { // 参会完弹框表单
        name: JSON.parse(sessionStorage.getItem('user')).userName,
        mobile: JSON.parse(sessionStorage.getItem('user')).mobile,
        position: JSON.parse(sessionStorage.getItem('user')).position
      },
      showSignIn: false, // 签到弹框
      actions: [{ name: '签到口令', color: '#3088fe' }], // 签到选择二维码还是口令
      description: '签到方式',
      dialogShow: false,
      dialogTitle: '',
      showKeyboard: true,
      value: '',
      signlength: 4,
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部  btn为按钮
      footerMore: { show: true }, // 展开附加按钮
      attachInfo: { name: '附件', data: [{ url: 'http://**************:81/lzt/flowAttachment/masterRepository/2022/10/31/530236917493530624.pdf', name: '青岛市违法建设治理条例（草案）.pdf', size: '117144', path: '/var/mobile/Containers/Data/Application/8203B084-B5F1-4301-B74D-3ECDA42FE99C/Documents/uzfs/A6106332267096/fileCache/青岛市违法建设治理条例（草案）.pdf', iconInfo: { name: 'icon_pdf.png', type: 'pdf', convertType: '20' }, schedule: -1, state: 2 }] }, // 附件对象
      meetList: [],
      fileUrl: '',
      fileName: '',
      toggleScreen1: route.query.toggleScreen1, // 手机自动旋转切换横屏
      Verticalscreen: route.query.Verticalscreen, // 竖屏
      TogglehorizontalScreen: route.query.TogglehorizontalScreen, // 横屏
      fiveId: 1,
      meetContent: '',
      meetContents: ''
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      // console.log('route.query.toggleScreen1==>', typeof route.query.toggleScreen1)
      // console.log('route.query.Verticalscreen==>', typeof route.query.Verticalscreen)
      // console.log('route.query.TogglehorizontalScreen==>', typeof route.query.TogglehorizontalScreen)
      if (window.orientation === 90 || window.orientation === -90) {
        data.toggleScreen1 = '1'
        data.Verticalscreen = '0'
        data.TogglehorizontalScreen = '0'
      }
      window.addEventListener('onorientationchange' in window ? 'orientationchange' : 'resize', function () {
        if (window.orientation === 180 || window.orientation === 0) {
          data.toggleScreen1 = '0'
          data.Verticalscreen = '1'
          data.TogglehorizontalScreen = '0'
        }
        if (window.orientation === 90 || window.orientation === -90) {
          data.toggleScreen1 = '1'
          data.Verticalscreen = '0'
          data.TogglehorizontalScreen = '0'
        }
      })

      var fiveBtns = [
        { id: 1, name: '会议通知', imgBtn: require('../../assets/img/qingdao/shouce.png') },
        { id: 2, name: '会议手册', imgBtn: require('../../assets/img/qingdao/richeng.png') },
        // { id: 3, name: '座位表', imgBtn: require('../../assets/img/qingdao/zuoci.png') },
        { id: 4, name: '会议文件', imgBtn: require('../../assets/img/qingdao/wenjian.png') },
        { id: 5, name: '历史会议', imgBtn: require('../../assets/img/qingdao/lishi.png') }
      ]
      if (data.meetingTypeId === '1' || data.meetingTypeId === '2') {
        data.fiveBtn = fiveBtns
      } else {
        data.fiveBtn = fiveBtns.filter(item => item.id !== 3)
      }
      onRefresh()
      window.onscroll = function () {
        var scrollTop = document.documentElement.scrollTop || document.body.scrollTop
        data.scrollTop = scrollTop
      }
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getHistoricalConference()
      getMeetingFilesList()
    }
    const onLoad = () => {
    }
    // 获取会议列表
    const getHistoricalConference = async () => {
      const res = await $api.electronicreading.getHistoricalConference({
        meetingType: data.meetingTypeId
      })
      var { data: list } = res
      data.meetList = data.meetList.concat(list)
      getDetails()
    }
    // 点击会议通知调用接口
    const getMeetingFilesList = async (id) => {
      var postParam = {
        conferenceId: data.recordId,
        pageNo: 1,
        pageSize: 50
      }
      if (id === 2) {
        var res = await $api.electronicreading.meetingScheduleList(postParam)
      } else {
        res = await $api.electronicreading.conferencenoticeList(postParam)
      }
      var { data: list } = res
      data.attachInfo.data = []
      if (list[0]) {
        console.log('list===>>', list[0].content)
        data.meetContent = list[0].content || ''
        var attachmentList = list[0].attachmentList
        if (attachmentList) {
          if (attachmentList.length !== 0) {
            attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
              var item = {}
              item.url = _eItem.filePath || ''
              item.name = _eItem.fileName || ''
              item.size = _eItem.fileSize || ''
              // data.annexCheck(item) // 附件检测 拿到附件缓存 信息
              data.attachInfo.data.push(item)
            })
          }
        }
      }
    }
    // 点击会议议程调用接口
    const getMeetingAgenda = async (id) => {
      if (id === 2) {
        var postParam = {
          conferenceId: data.recordId,
          pageNo: 1,
          pageSize: 50
        }
        const res = await $api.electronicreading.meetingScheduleList(postParam) // 详情
        var { data: list2 } = res
        console.log('🚀🚀🚀🚀🚀🚀会议日程', list2)
        data.loading = false
        data.refreshing = false
        data.finished = true
        if (list2) {
          list2.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
            _eItem.attachmentList.forEach(function (_aItem, _aIndex, _aArr) {
              data.fileUrl = _aItem.filePath || ''
              data.fileName = _aItem.fileName || ''
              var param = {
                url: data.fileUrl,
                name: data.fileName
              }
              if (param.url && param.name) {
                router.push({ name: 'superFile', query: param })
              } else {
                Toast('暂无信息')
              }
            })
          })
        } else {
          Toast('暂无信息')
        }
      }
    }
    // 五个按钮点击事件
    const fiveBtnClick = (_item) => {
      console.log('_item===>', _item)
      switch (_item.id) {
        case 2:
          getMeetingAgenda(_item.id)
          // getMeetingFilesList(_item.id)
          // data.fiveId = _item.id
          break
        case 1:
          getMeetingFilesList(_item.id)
          data.fiveId = _item.id
          // router.push({ path: 'meetingNoticeList', query: { id: _item.id, recordId: data.recordId, meetingTypeId: data.meetingTypeId } })
          break
        case 4:
          router.push({ path: 'fiveFile2', query: { id: _item.id, recordId: data.recordId, meetingTypeId: data.meetingTypeId } })
          break
        case 3:
          // if (param.url && param.name) {
          //   router.push({ name: 'superFile', query: param })
          // } else {
          //   Toast('暂无内容')
          // }
          break
        case 5:
          router.push({ path: 'historicalMeetingView', query: { id: _item.id, meetingTypeId: data.meetingTypeId } })
          break
      }
    }

    // 获取详情数据
    const getDetails = async () => {
      var id = data.meetList[0].id || route.query.id
      const res = await $api.conferenceActivitiesFile.conferencenfo(id) // 详情
      if (res) {
        var { data: list } = res
        data.showSkeleton = false
        data.firstAjax = true
        var info = list || {}
        data.title = info.name || ''// 标题
        data.name = info.name || ''// 标题
        //  + '至' + dayjs(info.endTime).format('YYYY-MM-DD HH:mm')
        data.time = info.startTime ? dayjs(info.startTime).format('YYYY-MM-DD HH:mm') : ''
        data.address = info.place || ''// 地点
        data.content = info.content || ''
        window.time = info.startTime
        data.id = info.id || ''
        var collection = info.collection// 是否补录 补录的数据不需要报名签到和请假
        var signUpItem = { name: '参会', type: 'btn', click: 'signUp', icon: 'edit', color: '', loading: false, disabled: false }
        var leaveItem = { name: '请假', type: 'btn', click: 'leave', icon: 'tosend', color: '', loading: false, disabled: false }
        var leaveApprovalItem = { name: '请假审核', type: 'btn', click: 'meetingleaveApproval', icon: 'newspaper-o', color: '', loading: false, disabled: false }
        var signUpBtn = general.getItemForKey(signUpItem.click, data.footerBtns, 'click')// 报名
        var leaveBtn = general.getItemForKey(leaveItem.click, data.footerBtns, 'click')// 请假
        var leaveApprovalItemBtn = general.getItemForKey(leaveApprovalItem.click, data.footerBtns, 'click')// 请假审核
        general.delItemForKey(signUpBtn, data.footerBtns, 'click')
        general.delItemForKey(leaveBtn, data.footerBtns, 'click')
        general.delItemForKey(leaveApprovalItemBtn, data.footerBtns, 'click')
        // var userId = JSON.parse(sessionStorage.getItem('user')).id
        if (!collection) { // 不是补录 就有操作
          var signUp = info.isSignUp// 是否可以报名
          var signUpList = info.signUpList || []// 所有报名人
          var signUpIn = general.getItemForKey(data.user.id, signUpList, 'userId')// 当前人是否已报名
          console.log('data.meetingTypeId===>', data.meetingTypeId)
          if (data.meetingTypeId === '1') {
            if (signUp === '1' || signUpIn) {
              if (signUpIn) {
                signUpItem.name = '已参会'
                signUpItem.color = '#ccc'
                signUpItem.disabled = true
              }
              data.footerBtns.push(signUpItem)
            }
          }
          var leave = info.isLeave// 是否可以请假
          if (data.meetingTypeId === '1') {
            if (leave === '1') {
              data.footerBtns.push(leaveItem)
              var leaveName = [{ name: '请假审核中' }, { name: '请假已通过' }, { name: '请假不通过' }]
              // status 0审核中 1通过
              var leaveState = info.leaveStat || []
              if (leaveState) {
                leaveState.forEach(item => {
                  leaveItem.name = general.isParameters(item.state) ? leaveName[Number(item.state)].name : '请假'
                  leaveItem.leaveId = item.id || ''
                })
              }
            }
          }
        }
        // 是否显示请假审核按钮
        var isLeaveAdmin = info.isLeaveAdmin
        if (isLeaveAdmin === '1') {
          data.footerBtns.push(leaveApprovalItem)
        }
      } else {
        Toast('网络异常稍后再试')
      }
    }
    // 底部悬浮按钮事件
    const footerBtnClick = (_item) => {
      console.error(JSON.stringify(_item))
      switch (_item.click) {
        case 'signUp':// 报名
          _item.loading = true
          _item.loadingText = '参会'
          $api.conferenceActivitiesFile.conferenceAddMySignUp({
            conferenceIds: data.id
          }).then(res => {
            console.log('res====', res)
            _item.loading = false
            if (!res) {
              Toast('失败,请重试!')
            } else {
              var code = res.errcode || 0
              if (code === 200) {
                Toast('参会成功')
                data.showCheck = true
                getDetails()
              } else {
                Toast('失败,请重试!')
              }
            }
          })
          break
        case 'signIn':// 签到
          data.showSignIn = true
          break
        case 'leave':// 请假
          router.push({ name: 'meetingLeave', query: { title: '请假', id: data.id, paramType: 'addLeave', leaveId: _item.leaveId } })
          break
        case 'matera':// 会议资料
          router.push({
            name: 'fileList', query: { relateType: _item.click, id: data.recordId, sysType: 'meeting', title: _item.name }
          })
          break
        case 'meetingleaveApproval':// 请假审核
          router.push({
            name: 'meetingleaveApproval', query: { relateType: _item.click, id: data.recordId, sysType: 'meeting', title: _item.name }
          })
          break
      }
    }
    // 弹框核对信息
    const Clickdialog = async (_item) => {
      const res = await $api.conferenceActivitiesFile.conferenceUpdateUserInfo({
        conferenceId: data.id,
        userId: data.user.id,
        position: data.form.position
      })
      if (res.errcode === 200) {
        Toast('核对完成')
      }
    }
    const onSelect = async (item) => {
      data.showSignIn = false
      switch (item.name) {
        case '签到口令':
          data.dialogTitle = '会议签到码'
          data.value = ''
          data.dialogShow = true
          data.showKeyboard = true
          break
      }
    }
    const confirm = async () => {
      console.log(data.value)
      if (!data.value || data.value.length !== 4) {
        Toast('签到口令不正确')
        return
      }
      const res = await $api.conferenceActivitiesFile.conferenceAddMySignIn({
        conferenceId: data.id,
        signInType: 'signInCommand',
        command: data.value
      })
      var code = res.errcode || 0
      if (code === 200) {
        Toast('签到成功')
        onRefresh()
      } else {
        Toast(res.errmsg || res.data)
      }
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    return { ...toRefs(data), onRefresh, onLoad, general, fiveBtnClick, footerBtnClick, Clickdialog, onSelect, confirm, annexClick }
  }
}
</script>
<style lang="less" scoped>
.electronicreadingItemDetails {
  min-height: 100vh;
  background: #fff;
  .header {
    position: fixed;
    width: 100%;
    z-index: 1;
  }
  .bg_one {
    background-position: bottom;
    background-size: cover;
    height: 270px;
    // background-color: #3f7ed5;
    background-image: url(../../assets/img/qingdao/meetInfoBg.png);
    display: flex;
    flex-direction: column;
    align-items: center;
    .top_big {
      margin-top: 4rem;
      text-align: center;
      color: #fff;
      .top_name {
        margin: 0 0.3rem;
        font-size: 0.4rem;
      }
      .top_time {
        margin: 0.3rem 0.4rem 0.2rem 0.4rem;
        border-top: 1px solid #ccc;
        padding-top: 0.3rem;
        font-size: 0.35rem;
      }
      .top_address {
        font-size: 0.3rem;
        margin: 0.3rem;
      }
    }
  }
  .fiveBtn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 20px;
    .fiveBtn_item {
      display: flex;
      align-items: center;
      flex-direction: column;
    }
  }
  .content_title_box {
    display: flex;
    margin: 25px 20px 18px 20px;
    align-items: center;
    .content_border {
      width: 5px;
      height: 20px;
      background: #0d6ad8;
    }
  }
  .content {
    margin: 0px 20px 20px 20px;
    font-size: 13px;
    line-height: 1.8;
  }
  .nullContent {
    margin: 0px 20px 20px 20px;
    color: #767373;
    font-size: 15px;
    text-align: center;
  }

  .clickAutomatic {
    width: 100vh;
    height: 100vh;
    transform: rotate(90deg);
    transform-origin: 50vw 50vw;
    .bg_one {
      background-position: bottom;
      background-size: cover;
      height: 150px;
      background-image: url(../../assets/img/qingdao/gengBg.png);
      // display: flex;
      // flex-direction: column;
      // align-items: center;
      .top_big {
        margin-top: 1.8rem;
        text-align: center;
        color: #fff;
        .top_name {
          margin: 0 0.3rem;
          font-size: 0.4rem;
        }
        .top_time {
          margin: 0.25rem 0.4rem 0.15rem 0.4rem;
          border-top: 1px solid #ccc;
          padding-top: 0.1rem;
          font-size: 0.3rem;
        }
        .top_address {
          font-size: 0.3rem;
          margin: 0.2rem;
        }
      }
    }
  }
  .AutomaticRotation {
    // width: 100vh;
    // height: 100vh;
    .bg_ones {
      // background-position: bottom;
      // background-size: cover;
      // height: 140px;
      // background-image: url(../../assets/img/jinqingdaoing/gengBg.png);
      // display: flex;
      // flex-direction: column;
      // align-items: center;
    }
    .top_bigs {
      margin-top: 0.85rem;
      color: #fff;
      position: absolute;
      top: 0;
      text-align: center;
      .top_name {
        margin: 0 0.2rem;
        font-size: 0.2rem;
      }
      .top_time {
        margin: 0.2rem 0.4rem 0.1rem 0.4rem;
        border-top: 1px solid #ccc;
        padding-top: 0.06rem;
        font-size: 0.16rem;
      }
      .top_address {
        font-size: 0.16rem;
        margin: 0.1rem;
      }
    }
  }
}
</style>

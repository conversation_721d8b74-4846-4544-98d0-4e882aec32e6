{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue", "mtime": 1756438284587}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\babel.config.js", "mtime": 1754028950133}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["useRoute", "inject", "reactive", "toRefs", "onMounted", "nextTick", "echarts", "debounce", "name", "components", "_Dialog", "Component", "_Overlay", "_ActionSheet", "_PasswordInput", "_NumberKeyboard", "_Icon", "_Tag", "_Image", "_Grid", "_GridItem", "_NavBar", "_Sticky", "props", "color", "String", "id", "list", "Array", "setup", "route", "ifzx", "appTheme", "general", "isShowHead", "data", "safeAreaTop", "SYS_IF_ZX", "appFontSize", "relateType", "query", "title", "user", "JSON", "parse", "sessionStorage", "getItem", "viewportWidth", "myChart", "chartDom", "document", "getElementById", "window", "innerWidth", "documentElement", "clientWidth", "init", "setOptions", "addEventListener", "resize", "options", "legend", "orient", "bottom", "parseInt", "left", "width", "height", "padding", "textStyle", "fontSize", "formatter", "total", "value", "map", "v", "for<PERSON>ach", "item", "Number", "p", "Math", "round", "itemWidth", "itemHeight", "series", "type", "startAngle", "radius", "avoidLabelOverlap", "itemStyle", "borderRadius", "borderColor", "borderWidth", "label", "<PERSON><PERSON><PERSON><PERSON>", "show", "position", "params", "alignTo", "labelLine", "length", "length2", "emphasis", "options2", "tooltip", "trigger", "options3", "minAngle", "proportion", "rich", "align", "labelLayout", "isLeft", "labelRect", "x", "getWidth", "points", "labelLinePoints", "setOption", "on", "e"], "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\components\\pie.vue"], "sourcesContent": ["<template>\r\n  <div :id=\"id\">\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\nimport { debounce } from '../../../utils/debounce.js'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'\r\nexport default {\r\n  name: 'pie',\r\n  components: {\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: <PERSON><PERSON>\r\n  },\r\n  props: {\r\n    color: String,\r\n    id: String,\r\n    list: Array\r\n  },\r\n  setup (props) {\r\n    const route = useRoute()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    // const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      viewportWidth: ''\r\n    })\r\n    var myChart = null\r\n    onMounted(() => {\r\n      nextTick(() => {\r\n        var chartDom = document.getElementById(props.id)\r\n        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth\r\n        myChart = echarts.init(chartDom)\r\n        setOptions()\r\n      })\r\n      // 监听窗口尺寸变化事件\r\n      window.addEventListener('resize', debounce(() => {\r\n        myChart.resize() // 调整图表大小\r\n        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth\r\n        setOptions()\r\n      }, 500))\r\n    })\r\n    const setOptions = () => {\r\n      // console.log(parseInt(data.viewportWidth * 0.04))\r\n      var options = {\r\n        legend: {\r\n          orient: 'horizontal', // 或 'horizontal'\r\n          bottom: -parseInt(data.viewportWidth * 0.013),\r\n          left: 'center',\r\n          width: parseInt(data.viewportWidth * 0.9),\r\n          height: parseInt(data.viewportWidth * 0.4),\r\n          padding: [parseInt(data.viewportWidth * 0.1), parseInt(data.viewportWidth * 0.01)],\r\n          // 设置图例文字的样式\r\n          textStyle: {\r\n            fontSize: parseInt(data.viewportWidth * 0.03),\r\n            color: '#ADADAD'\r\n          },\r\n          formatter: function (name) { // 该函数用于设置图例显示后的百分比\r\n            var total = 0\r\n            var value\r\n            props.list.map(v => {\r\n              return {\r\n                value: v.value,\r\n                name: v.name\r\n              }\r\n            }).forEach((item) => {\r\n              total += Number(item.value)\r\n              if (item.name === name) {\r\n                value = item.value\r\n              }\r\n            })\r\n            var p = Math.round(((value / total) * 100)) // 求出百分比\r\n            return `${name} | ${p}%` // 返回出图例所显示的内容是名称+百分比\r\n          },\r\n          itemWidth: parseInt(data.viewportWidth * 0.04),\r\n          itemHeight: parseInt(data.viewportWidth * 0.03)\r\n        },\r\n        color: ['#3893ff', '#a938fb', '#ffd434', '#ff7389', '#17c78a', '#ff7a2d'],\r\n        series: [\r\n          {\r\n            name: '代表年龄分析',\r\n            type: 'pie',\r\n            startAngle: 20, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.04) + '%', parseInt(data.viewportWidth * 0.1) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.015),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.009)\r\n            },\r\n            label: {\r\n              bleedMargin: 10,\r\n              show: true,\r\n              position: 'outside',\r\n              // data: ['f'],\r\n              formatter: function (params) {\r\n                return `${params.name}\\n${params.value}人`\r\n              },\r\n              fontSize: parseInt(data.viewportWidth * 0.03),\r\n              alignTo: 'labelLine'\r\n            },\r\n            labelLine: {\r\n              show: true,\r\n              length: parseInt(data.viewportWidth * 0.05),\r\n              length2: parseInt(data.viewportWidth * 0.12)\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine' },\r\n              { value: 735, name: 'Direct' },\r\n              { value: 580, name: 'Email' },\r\n              { value: 484, name: 'Union Ads' },\r\n              { value: 300, name: 'Video Ads' },\r\n              { value: 200, name: 'ghjkgjkAds' }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      var options2 = {\r\n        tooltip: {\r\n          trigger: 'item'\r\n        },\r\n        series: [\r\n          {\r\n            name: '性别',\r\n            type: 'pie',\r\n            startAngle: 60, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.11) + '%', parseInt(data.viewportWidth * 0.18) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.015),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.01)\r\n            },\r\n            label: {\r\n              show: false\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine', itemStyle: { color: '#3da2ff' } },\r\n              { value: 735, name: 'Direct', itemStyle: { color: '#ff738c' } }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      var options3 = {\r\n        color: ['#67DBFF', '#FF61C9', '#64A2FF', '#FF9567', '#BCFF87', '#FF6D6D', '#61E89F', '#BC87FF', '#FFD056'],\r\n        series: [\r\n          {\r\n            name: '类型占比',\r\n            type: 'pie',\r\n            minAngle: 30,\r\n            // startAngle: 90, // 调整起始角度\r\n            radius: [parseInt(data.viewportWidth * 0.06) + '%', parseInt(data.viewportWidth * 0.12) + '%'],\r\n            avoidLabelOverlap: false,\r\n            itemStyle: {\r\n              borderRadius: parseInt(data.viewportWidth * 0.0),\r\n              borderColor: '#fff',\r\n              borderWidth: parseInt(data.viewportWidth * 0.008)\r\n            },\r\n            label: {\r\n              show: true,\r\n              position: 'outside',\r\n              // data: ['f'],\r\n              formatter: function (params) {\r\n                return `${params.data.proportion}%\\n${params.name}`\r\n              },\r\n              fontSize: parseInt(data.viewportWidth * 0.024),\r\n              alignTo: 'labelLine',\r\n              rich: {\r\n                name: {\r\n                  fontSize: 14,\r\n                  color: '#333',\r\n                  align: 'center'\r\n                },\r\n                value: {\r\n                  fontSize: 12,\r\n                  color: '#999'\r\n                }\r\n              }\r\n            },\r\n            labelLayout: function (params) {\r\n              const isLeft = params.labelRect.x < myChart.getWidth() / 2\r\n              const points = params.labelLinePoints\r\n              points[2][0] = isLeft\r\n                ? params.labelRect.x\r\n                : params.labelRect.x + params.labelRect.width\r\n              return {\r\n                labelLinePoints: points\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: true,\r\n              length: parseInt(data.viewportWidth * 0.08),\r\n              length2: parseInt(data.viewportWidth * 0.1)\r\n            },\r\n            emphasis: {\r\n            },\r\n            data: [\r\n              { value: 1048, name: 'Search Engine' },\r\n              { value: 735, name: 'Direct' },\r\n              { value: 580, name: 'Email' },\r\n              { value: 484, name: 'Union Ads' },\r\n              { value: 300, name: 'Video Ads' },\r\n              { value: 200, name: 'ghjkgjkAds' }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n      nextTick(() => {\r\n        if (props.id === 'pie1') {\r\n          options.series[0].data = props.list\r\n          myChart.setOption(options)\r\n        } else if (props.id === 'pie2') {\r\n          options2.series[0].data = props.list\r\n          myChart.setOption(options2)\r\n        } else if (props.id === 'pie3') {\r\n          options3.series[0].data = props.list\r\n          myChart.on('click', (e) => {\r\n            // console.log(e)\r\n            // window.location.href = e.data.url\r\n          })\r\n          myChart.setOption(options3)\r\n        }\r\n      })\r\n    }\r\n    return { ...toRefs(data), general }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n#pie1 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 260px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#pie2 {\r\n  width: 100%;\r\n  height: 100%;\r\n  // margin: 10px 0;\r\n  box-sizing: border-box;\r\n  background: #fff;\r\n}\r\n\r\n#pie3 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 220px;\r\n  margin: 10px 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n#pie4 {\r\n  background: #fff;\r\n  width: 100%;\r\n  height: 100%;\r\n  // margin: 10px 0;\r\n  box-sizing: border-box;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,QAAO,QAAS,YAAW;AACpC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AAClE,OAAO,KAAKC,OAAM,MAAO,SAAQ;AACjC,SAASC,QAAO,QAAS,4BAA2B;AAEpD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACV,CAACC,OAAA,CAAOC,SAAS,CAACH,IAAI,GAAGE,OAAA,CAAOC,SAAS;IACzC,CAACC,QAAA,CAAQJ,IAAI,GAAAI,QAAU;IACvB,CAACC,YAAA,CAAYL,IAAI,GAAAK,YAAc;IAC/B,CAACC,cAAA,CAAcN,IAAI,GAAAM,cAAgB;IACnC,CAACC,eAAA,CAAeP,IAAI,GAAAO,eAAiB;IACrC,CAACC,KAAA,CAAKR,IAAI,GAAAQ,KAAO;IACjB,CAACC,IAAA,CAAIT,IAAI,GAAAS,IAAM;IACf,CAACC,MAAA,CAASV,IAAI,GAAAU,MAAW;IACzB,CAACC,KAAA,CAAKX,IAAI,GAAAW,KAAO;IACjB,CAACC,SAAA,CAASZ,IAAI,GAAAY,SAAW;IACzB,CAACC,OAAA,CAAOb,IAAI,GAAAa,OAAS;IACrB,CAACC,OAAA,CAAOd,IAAI,GAAAc;EACd,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAEC,MAAM;IACbC,EAAE,EAAED,MAAM;IACVE,IAAI,EAAEC;EACR,CAAC;EACDC,KAAIA,CAAGN,KAAK,EAAE;IACZ,MAAMO,KAAI,GAAI9B,QAAQ,CAAC;IACvB,MAAM+B,IAAG,GAAI9B,MAAM,CAAC,OAAO;IAC3B,MAAM+B,QAAO,GAAI/B,MAAM,CAAC,WAAW;IACnC,MAAMgC,OAAM,GAAIhC,MAAM,CAAC,UAAU;IACjC,MAAMiC,UAAS,GAAIjC,MAAM,CAAC,aAAa;IACvC;IACA;IACA,MAAMkC,IAAG,GAAIjC,QAAQ,CAAC;MACpBkC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAEN,IAAI;MACfO,WAAW,EAAEL,OAAO,CAACE,IAAI,CAACG,WAAW;MACrCN,QAAQ,EAAEA,QAAQ;MAClBE,UAAU,EAAEA,UAAU;MACtBK,UAAU,EAAET,KAAK,CAACU,KAAK,CAACD,UAAS,IAAK,EAAE;MACxCE,KAAK,EAAEX,KAAK,CAACU,KAAK,CAACC,KAAI,IAAK,EAAE;MAC9BC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MAChDC,aAAa,EAAE;IACjB,CAAC;IACD,IAAIC,OAAM,GAAI,IAAG;IACjB5C,SAAS,CAAC,MAAM;MACdC,QAAQ,CAAC,MAAM;QACb,IAAI4C,QAAO,GAAIC,QAAQ,CAACC,cAAc,CAAC5B,KAAK,CAACG,EAAE;QAC/CS,IAAI,CAACY,aAAY,GAAIK,MAAM,CAACC,UAAS,IAAKH,QAAQ,CAACI,eAAe,CAACC,WAAU;QAC7EP,OAAM,GAAI1C,OAAO,CAACkD,IAAI,CAACP,QAAQ;QAC/BQ,UAAU,CAAC;MACb,CAAC;MACD;MACAL,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAEnD,QAAQ,CAAC,MAAM;QAC/CyC,OAAO,CAACW,MAAM,CAAC,GAAE;QACjBxB,IAAI,CAACY,aAAY,GAAIK,MAAM,CAACC,UAAS,IAAKH,QAAQ,CAACI,eAAe,CAACC,WAAU;QAC7EE,UAAU,CAAC;MACb,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IACD,MAAMA,UAAS,GAAIA,CAAA,KAAM;MACvB;MACA,IAAIG,OAAM,GAAI;QACZC,MAAM,EAAE;UACNC,MAAM,EAAE,YAAY;UAAE;UACtBC,MAAM,EAAE,CAACC,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,KAAK,CAAC;UAC7CkB,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAEF,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,GAAG,CAAC;UACzCoB,MAAM,EAAEH,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,GAAG,CAAC;UAC1CqB,OAAO,EAAE,CAACJ,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,GAAG,CAAC,EAAEiB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,CAAC,CAAC;UAClF;UACAsB,SAAS,EAAE;YACTC,QAAQ,EAAEN,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,CAAC;YAC7CvB,KAAK,EAAE;UACT,CAAC;UACD+C,SAAS,EAAE,SAAAA,CAAU/D,IAAI,EAAE;YAAE;YAC3B,IAAIgE,KAAI,GAAI;YACZ,IAAIC,KAAI;YACRlD,KAAK,CAACI,IAAI,CAAC+C,GAAG,CAACC,CAAA,IAAK;cAClB,OAAO;gBACLF,KAAK,EAAEE,CAAC,CAACF,KAAK;gBACdjE,IAAI,EAAEmE,CAAC,CAACnE;cACV;YACF,CAAC,CAAC,CAACoE,OAAO,CAAEC,IAAI,IAAK;cACnBL,KAAI,IAAKM,MAAM,CAACD,IAAI,CAACJ,KAAK;cAC1B,IAAII,IAAI,CAACrE,IAAG,KAAMA,IAAI,EAAE;gBACtBiE,KAAI,GAAII,IAAI,CAACJ,KAAI;cACnB;YACF,CAAC;YACD,IAAIM,CAAA,GAAIC,IAAI,CAACC,KAAK,CAAGR,KAAI,GAAID,KAAK,GAAI,GAAI,GAAE;YAC5C,OAAO,GAAGhE,IAAI,MAAMuE,CAAC,GAAE,EAAE;UAC3B,CAAC;UACDG,SAAS,EAAElB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,CAAC;UAC9CoC,UAAU,EAAEnB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI;QAChD,CAAC;QACDvB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QACzE4D,MAAM,EAAE,CACN;UACE5E,IAAI,EAAE,QAAQ;UACd6E,IAAI,EAAE,KAAK;UACXC,UAAU,EAAE,EAAE;UAAE;UAChBC,MAAM,EAAE,CAACvB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,IAAI,GAAG,EAAEiB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,GAAG,IAAI,GAAG,CAAC;UAC7FyC,iBAAiB,EAAE,KAAK;UACxBC,SAAS,EAAE;YACTC,YAAY,EAAE1B,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,KAAK,CAAC;YAClD4C,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE5B,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,KAAK;UAClD,CAAC;UACD8C,KAAK,EAAE;YACLC,WAAW,EAAE,EAAE;YACfC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,SAAS;YACnB;YACAzB,SAAS,EAAE,SAAAA,CAAU0B,MAAM,EAAE;cAC3B,OAAO,GAAGA,MAAM,CAACzF,IAAI,KAAKyF,MAAM,CAACxB,KAAK,GAAE;YAC1C,CAAC;YACDH,QAAQ,EAAEN,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,CAAC;YAC7CmD,OAAO,EAAE;UACX,CAAC;UACDC,SAAS,EAAE;YACTJ,IAAI,EAAE,IAAI;YACVK,MAAM,EAAEpC,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,CAAC;YAC3CsD,OAAO,EAAErC,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI;UAC7C,CAAC;UACDuD,QAAQ,EAAE,CACV,CAAC;UACDnE,IAAI,EAAE,CACJ;YAAEsC,KAAK,EAAE,IAAI;YAAEjE,IAAI,EAAE;UAAgB,CAAC,EACtC;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAS,CAAC,EAC9B;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAQ,CAAC,EAC7B;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAY,CAAC,EACjC;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAY,CAAC,EACjC;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAa;QAErC;MAEJ;MACA,IAAI+F,QAAO,GAAI;QACbC,OAAO,EAAE;UACPC,OAAO,EAAE;QACX,CAAC;QACDrB,MAAM,EAAE,CACN;UACE5E,IAAI,EAAE,IAAI;UACV6E,IAAI,EAAE,KAAK;UACXC,UAAU,EAAE,EAAE;UAAE;UAChBC,MAAM,EAAE,CAACvB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,IAAI,GAAG,EAAEiB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,IAAI,GAAG,CAAC;UAC9FyC,iBAAiB,EAAE,KAAK;UACxBC,SAAS,EAAE;YACTC,YAAY,EAAE1B,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,KAAK,CAAC;YAClD4C,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE5B,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI;UACjD,CAAC;UACD8C,KAAK,EAAE;YACLE,IAAI,EAAE;UACR,CAAC;UACDO,QAAQ,EAAE,CACV,CAAC;UACDnE,IAAI,EAAE,CACJ;YAAEsC,KAAK,EAAE,IAAI;YAAEjE,IAAI,EAAE,eAAe;YAAEiF,SAAS,EAAE;cAAEjE,KAAK,EAAE;YAAU;UAAE,CAAC,EACvE;YAAEiD,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE,QAAQ;YAAEiF,SAAS,EAAE;cAAEjE,KAAK,EAAE;YAAU;UAAE;QAElE;MAEJ;MACA,IAAIkF,QAAO,GAAI;QACblF,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QAC1G4D,MAAM,EAAE,CACN;UACE5E,IAAI,EAAE,MAAM;UACZ6E,IAAI,EAAE,KAAK;UACXsB,QAAQ,EAAE,EAAE;UACZ;UACApB,MAAM,EAAE,CAACvB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,IAAI,GAAG,EAAEiB,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,IAAI,GAAG,CAAC;UAC9FyC,iBAAiB,EAAE,KAAK;UACxBC,SAAS,EAAE;YACTC,YAAY,EAAE1B,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,GAAG,CAAC;YAChD4C,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE5B,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,KAAK;UAClD,CAAC;UACD8C,KAAK,EAAE;YACLE,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,SAAS;YACnB;YACAzB,SAAS,EAAE,SAAAA,CAAU0B,MAAM,EAAE;cAC3B,OAAO,GAAGA,MAAM,CAAC9D,IAAI,CAACyE,UAAU,MAAMX,MAAM,CAACzF,IAAI,EAAC;YACpD,CAAC;YACD8D,QAAQ,EAAEN,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,KAAK,CAAC;YAC9CmD,OAAO,EAAE,WAAW;YACpBW,IAAI,EAAE;cACJrG,IAAI,EAAE;gBACJ8D,QAAQ,EAAE,EAAE;gBACZ9C,KAAK,EAAE,MAAM;gBACbsF,KAAK,EAAE;cACT,CAAC;cACDrC,KAAK,EAAE;gBACLH,QAAQ,EAAE,EAAE;gBACZ9C,KAAK,EAAE;cACT;YACF;UACF,CAAC;UACDuF,WAAW,EAAE,SAAAA,CAAUd,MAAM,EAAE;YAC7B,MAAMe,MAAK,GAAIf,MAAM,CAACgB,SAAS,CAACC,CAAA,GAAIlE,OAAO,CAACmE,QAAQ,CAAC,IAAI;YACzD,MAAMC,MAAK,GAAInB,MAAM,CAACoB,eAAc;YACpCD,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIJ,MAAK,GAChBf,MAAM,CAACgB,SAAS,CAACC,CAAA,GACjBjB,MAAM,CAACgB,SAAS,CAACC,CAAA,GAAIjB,MAAM,CAACgB,SAAS,CAAC/C,KAAI;YAC9C,OAAO;cACLmD,eAAe,EAAED;YACnB;UACF,CAAC;UACDjB,SAAS,EAAE;YACTJ,IAAI,EAAE,IAAI;YACVK,MAAM,EAAEpC,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,IAAI,CAAC;YAC3CsD,OAAO,EAAErC,QAAQ,CAAC7B,IAAI,CAACY,aAAY,GAAI,GAAG;UAC5C,CAAC;UACDuD,QAAQ,EAAE,CACV,CAAC;UACDnE,IAAI,EAAE,CACJ;YAAEsC,KAAK,EAAE,IAAI;YAAEjE,IAAI,EAAE;UAAgB,CAAC,EACtC;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAS,CAAC,EAC9B;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAQ,CAAC,EAC7B;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAY,CAAC,EACjC;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAY,CAAC,EACjC;YAAEiE,KAAK,EAAE,GAAG;YAAEjE,IAAI,EAAE;UAAa;QAErC;MAEJ;MACAH,QAAQ,CAAC,MAAM;QACb,IAAIkB,KAAK,CAACG,EAAC,KAAM,MAAM,EAAE;UACvBkC,OAAO,CAACwB,MAAM,CAAC,CAAC,CAAC,CAACjD,IAAG,GAAIZ,KAAK,CAACI,IAAG;UAClCqB,OAAO,CAACsE,SAAS,CAAC1D,OAAO;QAC3B,OAAO,IAAIrC,KAAK,CAACG,EAAC,KAAM,MAAM,EAAE;UAC9B6E,QAAQ,CAACnB,MAAM,CAAC,CAAC,CAAC,CAACjD,IAAG,GAAIZ,KAAK,CAACI,IAAG;UACnCqB,OAAO,CAACsE,SAAS,CAACf,QAAQ;QAC5B,OAAO,IAAIhF,KAAK,CAACG,EAAC,KAAM,MAAM,EAAE;UAC9BgF,QAAQ,CAACtB,MAAM,CAAC,CAAC,CAAC,CAACjD,IAAG,GAAIZ,KAAK,CAACI,IAAG;UACnCqB,OAAO,CAACuE,EAAE,CAAC,OAAO,EAAGC,CAAC,IAAK;YACzB;YACA;UAAA,CACD;UACDxE,OAAO,CAACsE,SAAS,CAACZ,QAAQ;QAC5B;MACF,CAAC;IACH;IACA,OAAO;MAAE,GAAGvG,MAAM,CAACgC,IAAI,CAAC;MAAEF;IAAQ;EACpC;AACF", "ignoreList": []}]}
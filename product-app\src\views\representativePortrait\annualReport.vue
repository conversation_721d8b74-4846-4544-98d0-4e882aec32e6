<template>
  <div class="annualReport"
       @touchstart="handleTouchStart"
       @touchmove="handleTouchMove"
       @touchend="handleTouchEnd">
    <div style="position: absolute;top: 10px;right: 10px;z-index: 2;">
      <audio ref="audioPlayer"
             id="play1"
             loop>
        <source src="../../assets/img/audio/yekongdejijing.mp3"
                type="audio/mpeg">
      </audio>
      <div class="flex_box flex_align_center">
        <div v-if="!isPlaying"
             style="color:#fff;font-size: 12px;margin-right: 10px;">打开音乐，体验更佳哦！</div>
        <div>
          <img v-if="!isPlaying"
               src="../../assets/img/audio/g_musicOff.png"
               alt=""
               style="width: 42px;height: 40px;"
               @click.stop="playMusic"
               :class="{ rotate: !isPlaying }">
          <img v-else
               src="../../assets/img/audio/g_playMusic.png"
               alt=""
               style="width: 34px;height: 34px;"
               @click.stop="stopMusic"
               :class="{ rotate: isPlaying }">
        </div>
      </div>
    </div>
    <transition name="fade"
                mode="out-in"
                ref="transition">
      <div class="container_swipe">
        <Page1 class="page"
               :showText1="page1ShowText1"
               :showText2="page1ShowText2"
               :showText3="page1ShowText3"
               :class="{ 'active': currentPage == 1 }"></Page1>
        <Page2 class="page"
               :pageData="pageData"
               :showText1="page2ShowText1"
               :showText2="page2ShowText2"
               :showText3="page2ShowText3"
               :class="{ 'active': currentPage == 2 }"></Page2>
        <Page3 class="page"
               :pageData="pageData"
               :showText1="page3ShowText1"
               :showText2="page3ShowText2"
               :showText3="page3ShowText3"
               :class="{ 'active': currentPage == 3 }"></Page3>
        <Page4 class="page"
               :pageData="pageData"
               :showText1="page4ShowText1"
               :showText2="page4ShowText2"
               :showText3="page4ShowText3"
               :class="{ 'active': currentPage == 4 }"></Page4>
        <Page5 class="page"
               :pageData="page5Data"
               :showText1="page5ShowText1"
               :showText2="page5ShowText2"
               :showText3="page5ShowText3"
               :class="{ 'active': currentPage == 5 }"></Page5>
        <Page6 class="page"
               :pageData="pageData"
               :showText1="page6ShowText1"
               :showText2="page6ShowText2"
               :showText3="page6ShowText3"
               :class="{ 'active': currentPage == 6 }"></Page6>
        <Page7 class="page"
               :pageData="dutyListData"
               :showText1="page7ShowText1"
               :showText2="page7ShowText2"
               :showText3="page7ShowText3"
               :class="{ 'active': currentPage == 7 }"></Page7>
        <Page8 class="page"
               :pageData="randerData"
               :showText1="page8ShowText1"
               :showText2="page8ShowText2"
               :showText3="page8ShowText3"
               :class="{ 'active': currentPage == 8 }"></Page8>
        <Page9 class="page"
               :showText1="page9ShowText1"
               :showText2="page9ShowText2"
               :showText3="page9ShowText3"
               :class="{ 'active': currentPage == 9 }"></Page9>
      </div>
    </transition>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, onBeforeUnmount, ref, watch } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
import Page1 from './component/Page1.vue'
import Page2 from './component/Page2.vue'
import Page3 from './component/Page3.vue'
import Page4 from './component/Page4.vue'
import Page5 from './component/Page5.vue'
import Page6 from './component/Page6.vue'
import Page7 from './component/Page7.vue'
import Page8 from './component/Page8.vue'
import Page9 from './component/Page9.vue'
export default {
  name: 'annualReport',
  components: {
    Page1,
    Page2,
    Page3,
    Page4,
    Page5,
    Page6,
    Page7,
    Page8,
    Page9,
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  setup () {
    // const router = useRouter()
    const $api = inject('$api')
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const startX = ref(0)
    const startY = ref(0)
    const startTime = ref(0)
    const isScrolling = ref(false)
    const currentPage = ref(1)
    const isPlaying = ref(false)
    const audioPlayer = ref(null)
    const transition = ref(null)
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      typePlay: route.query.typePlay || false,
      userName: '',
      page1ShowText1: false,
      page1ShowText2: false,
      page1ShowText3: false,
      page2ShowText1: false,
      page2ShowText2: false,
      page2ShowText3: false,
      page3ShowText1: false,
      page3ShowText2: false,
      page3ShowText3: false,
      page4ShowText1: false,
      page4ShowText2: false,
      page4ShowText3: false,
      page5ShowText1: false,
      page5ShowText2: false,
      page5ShowText3: false,
      page6ShowText1: false,
      page6ShowText2: false,
      page6ShowText3: false,
      page7ShowText1: false,
      page7ShowText2: false,
      page7ShowText3: false,
      page8ShowText1: false,
      page8ShowText2: false,
      page8ShowText3: false,
      page9ShowText1: false,
      page9ShowText2: false,
      page9ShowText3: false,
      pageData: {},
      page5Data: {},
      page2Show: 0,
      page3Show: 0,
      page4Show: 0,
      page5Show: 0,
      page6Show: 0,
      page7Show: 0,
      page8Show: 10,
      page9Show: 10,
      dutyListData: [],
      randerData: []
    })
    if (data.title) {
      document.title = data.title
    }
    watch(currentPage, (newPage, oldPage) => {
      switch (newPage) {
        case 0:
          executePage0Code()
          break
        case 1:
          executePage1Code()
          break
        case 2:
          executePage2Code()
          break
        case 3:
          executePage3Code()
          break
        case 4:
          executePage4Code()
          break
        case 5:
          executePage5Code()
          break
        case 6:
          executePage6Code()
          break
        case 7:
          executePage7Code()
          break
        case 8:
          executePage8Code()
          break
        case 9:
          executePage9Code()
          break
        // 其他页面的逻辑...
        default:
          break
      }
    })
    onMounted(() => {
      data.userName = data.user.userName
      memberPortraitDutyTime()
      getPages3Recommendation()
      getAppDutyDetail()
      memberPortraitDutyData()
      preventScroll()
      executePage1Code()
      if (data.typePlay) {
        audioPlayer.value.play()
        isPlaying.value = true
      }
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const handleOpenPage = () => {
      console.log('父组件打印的')
      currentPage.value = 1
      audioPlayer.value.play()
      isPlaying.value = true
    }
    const playMusic = () => {
      audioPlayer.value.play()
      isPlaying.value = true
    }
    const stopMusic = () => {
      audioPlayer.value.pause()
      isPlaying.value = false
    }
    const executePage0Code = () => {
      data.page1ShowText1 = false
      data.page1ShowText2 = false
      data.page1ShowText3 = false
    }
    const executePage1Code = () => {
      empty()
      setTimeout(() => {
        data.page1ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page1ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page1ShowText3 = true
      }, 3000)
    }
    const executePage2Code = () => {
      empty()
      setTimeout(() => {
        data.page2ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page2ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page2ShowText3 = true
      }, 3000)
    }
    const executePage3Code = () => {
      empty()
      setTimeout(() => {
        data.page3ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page3ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page3ShowText3 = true
      }, 3000)
    }
    const executePage4Code = () => {
      empty()
      setTimeout(() => {
        data.page4ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page4ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page4ShowText3 = true
      }, 3000)
    }
    const executePage5Code = () => {
      empty()
      setTimeout(() => {
        data.page5ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page5ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page5ShowText3 = true
      }, 3000)
    }
    const executePage6Code = () => {
      empty()
      setTimeout(() => {
        data.page6ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page6ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page6ShowText3 = true
      }, 3000)
    }
    const executePage7Code = () => {
      empty()
      setTimeout(() => {
        data.page7ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page7ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page7ShowText3 = true
      }, 3000)
    }
    const executePage8Code = () => {
      empty()
      setTimeout(() => {
        data.page8ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page8ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page8ShowText3 = true
      }, 3000)
    }
    const executePage9Code = () => {
      empty()
      setTimeout(() => {
        data.page9ShowText1 = true
      }, 1000)
      setTimeout(() => {
        data.page9ShowText2 = true
      }, 2000)
      setTimeout(() => {
        data.page9ShowText3 = true
      }, 3000)
    }
    const empty = () => {
      data.page2ShowText1 = false
      data.page2ShowText2 = false
      data.page2ShowText3 = false
      data.page3ShowText1 = false
      data.page3ShowText2 = false
      data.page3ShowText3 = false
      data.page4ShowText1 = false
      data.page4ShowText2 = false
      data.page4ShowText3 = false
      data.page5ShowText1 = false
      data.page5ShowText2 = false
      data.page5ShowText3 = false
      data.page6ShowText1 = false
      data.page6ShowText2 = false
      data.page6ShowText3 = false
      data.page7ShowText1 = false
      data.page7ShowText2 = false
      data.page7ShowText3 = false
      data.page8ShowText1 = false
      data.page8ShowText2 = false
      data.page8ShowText3 = false
      data.page9ShowText1 = false
      data.page9ShowText2 = false
      data.page9ShowText3 = false
    }
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
      const touch = event.touches[0]
      startX.value = touch.clientX
      startY.value = touch.clientY
      startTime.value = Date.now()
      isScrolling.value = false
    }
    const handleTouchMove = (event) => {
      const touch = event.touches[0]
      const deltaX = Math.abs(touch.clientX - startX.value)
      const deltaY = Math.abs(touch.clientY - startY.value)
      if (deltaY > deltaX) {
        isScrolling.value = true
      }
    }
    const handleTouchEnd = (event) => {
      const touch = event.changedTouches[0]
      const endX = touch.clientX
      const endY = touch.clientY
      const endTime = Date.now()
      const deltaX = Math.abs(endX - startX.value)
      const deltaY = Math.abs(endY - startY.value)
      const duration = endTime - startTime.value
      touchMoveY.value = event.changedTouches[0].clientY
      if (!isScrolling.value && deltaX < 10 && deltaY < 10 && duration < 300) {
        // 触发点击事件
        console.log('触发点击事件')
      } else if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        let nextPage = currentPage.value + 1
        while (nextPage <= 9) {
          if (data[`page${nextPage}Show`] !== 0) {
            currentPage.value = nextPage
            break
          }
          nextPage++
        }
      } else {
        // 向下滑动
        console.log('向下滑动')
        let prevPage = currentPage.value - 1
        while (prevPage >= 1) {
          if (data[`page${prevPage}Show`] !== 0) {
            currentPage.value = prevPage
            break
          }
          prevPage--
        }
      }
    }
    // 获取数据
    const memberPortraitDutyTime = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyTime()
      data.pageData = res.data
      data.page2Show = res.data.loginTimes || 0
      data.page3Show = res.data.participateServer || 0
      data.page4Show = res.data.publishDuty || 0
      data.page6Show = (res.data.watchVideoTime && res.data.readBookTime) || 0
    }
    // 获取雷达图数据
    const memberPortraitDutyData = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyData()
      data.randerData = res.data
      console.log('data.randerData===>', data.randerData)
    }
    // 获取亚威建议数据
    const getPages3Recommendation = async () => {
      const res = await $api.representativePortrait.getPersonSubmitInfo({ personCode: data.user.id })
      console.log('获取代表建议===>>', res)
      data.page5Data = res.result
      data.page5Show = res.result.submitCount || 0
    }
    // 获取履职足迹
    const getAppDutyDetail = async () => {
      const param = {
        pageNo: 1,
        pageSize: 99,
        year: 2023,
        userId: data.user.id,
        areaId: sessionStorage.getItem('areaId')
      }
      const res = await $api.performanceFiles.getAppDutyDetail(param)
      console.log('获取履职足迹===>', res)
      const list = res.data
      list.forEach(item => {
        var date = new Date(item.date)
        var month = date.getMonth() + 1
        var day = date.getDate()
        var formattedDate = month + '月' + day + '日'
        item.time = formattedDate
        switch (item.type) {
          case 'suggest': // 建议
            item.typeName = '建议'
            break
          case 'proposal': // 提案
            item.typeName = '提案'
            break
          case 'officeOnline': // 委员值班
            item.typeName = '委员值班'
            break
          case 'social': // 社情民意
            item.typeName = '社情民意'
            break
          case 'activity': // 活动
          case '49': // 活动
            item.typeName = '活动'
            break
          case 'survey': // 意见征集
            item.typeName = '意见征集'
            break
          case 'learning': // 考试
            item.typeName = '学习培训'
            break
          case 'meet': // 会议
            item.typeName = '会议'
            break
          case 'bill': // 议案
            item.typeName = '议案'
            break
          case '211': // 履职补录
            item.typeName = '活动'
            break
        }
      })
      data.dutyListData = data.dutyListData.concat(list)
      data.page7Show = data.dutyListData.length || 0
    }
    return { ...toRefs(data), $general, handleTouchStart, currentPage, handleTouchMove, handleTouchEnd, isPlaying, audioPlayer, playMusic, stopMusic, handleOpenPage, transition }
  }
}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiYuan-2";
  src: url("../../assets/img/timeAxis/font/YouSheBiaoTiYuan-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}

.annualReport {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
  }
  .page {
    opacity: 0;
    transition: opacity 3s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    pointer-events: auto;
  }

  .active {
    opacity: 1;
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 3s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }
  .rotate {
    animation: rotate 2s linear infinite;
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>

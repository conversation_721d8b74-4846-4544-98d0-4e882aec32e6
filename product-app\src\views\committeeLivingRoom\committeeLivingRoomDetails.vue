<template>
  <div class="committeeLivingRoom">
    <template v-if="details.firstAjax">
      <div class="onduty_box">
        <div class="onduty_title"
             :style="$general.loadConfiguration(-1)">{{details.title}}</div>
        <div class="onduty_user flex_box flex_align_center">
          <van-image style="width:40px;height:40px;"
                     fit="contain"
                     :src="details.userUrl">
          </van-image>
          <div class="flex_placeholder ">
            <div class="flex_box flex_align_center">
              <div class="onduty_main_s"
                   :style="$general.loadConfiguration(-1)">{{details.userName}}</div>
              <div class="onduty_user_dele"
                   :style="$general.loadConfiguration(-4)">{{details.userDeleName}}</div>
            </div>
            <div v-if="details.userPosition"
                 class="onduty_user_position"
                 :style="$general.loadConfiguration(-4)">{{details.userPosition}}</div>
          </div>
        </div>
        <div :style="$general.loadConfiguration(-3)+'color:#666;line-height:1.46;margin-top:5px;'">{{dayjs(details.time).format('YYYY-MM-DD')}}</div>
        <div :style="$general.loadConfiguration(-1)+'color:#666;line-height:1.46;margin-top:10px;'">{{details.content}}</div>
        <!-- <template v-if="externalLinks">
          <div v-if="externalLinks.indexOf('.mp4')>=0 || externalLinks.indexOf('.MP4')>=0"
               class="videoBox"
               style="margin-bottom: 10px;"
               @click.stop="">
            <video :class="'videoItem video'+ recordId"
                   width="100%"
                   height="auto"
                   controls
                   :poster="poster || '../../../images/btn_camera_open.png'"
                   :src="externalLinks"></video>
          </div>
          <div v-else
               :style="$general.loadConfiguration(-1)+'color:#666;line-height:1.46;margin-top:10px;'">链接：<span @click="$o.openUrl({url:externalLinks})"
                  style="color: blue;"
                  class="inherit"
                  v-html="externalLinks"></span></div>
        </template> -->
      </div>
      <div class="flex_box flex_align_center"
           style="padding: 5px 14px; ">
        <div :style="'width:2px;height: 20px;background:'+appTheme"></div>
        <div :style="$general.loadConfiguration(-1)+'color:#333;line-height:1.46;margin-left:5px;font-weight: 600;'">{{commentInfo.name}}</div>
      </div>
      <ul class="lam_box"
          v-if="listData.length != 0">
        <li class="lam_item click"
            v-for="(item,index) in listData"
            :key="index"
            @click="clickLam(item)">
          <div class="flex_box flex_align_center">
            <van-image width="30px"
                       height="30px"
                       fit="contain"
                       :src="item.url">
            </van-image>
            <!-- <zy-photo :style="$general.loadConfigurationSize(22)+'margin-right:8px;'"
                      :key="item.refresh"
                      :data="item"></zy-photo> -->
            <div class="flex_placeholder lam_user">
              <div class="flex_box flex_align_center">
                <div class="lam_user_name"
                     :style="$general.loadConfiguration(-3)">{{item.name}}</div>
                <div v-if="item.source != 'APP'"
                     :style="$general.loadConfiguration(-6)+'color:#f3989a;'"
                     class="review_status">网民</div>
                <div class="flex_placeholder"></div>
                <template v-if="isStaff">
                  <div v-if="item.isPublic == '待审核'"
                       class="review_btn"
                       :style="$general.loadConfiguration(-6)+'background:'+appTheme">审核</div>
                  <div v-else
                       :style="$general.loadConfiguration(-6)+'color:'+(item.isPublic=='审核通过'?'#3088FE':'#8C96A2')+';background:'+(item.isPublic=='审核通过'?'#DCEBFF':'#EBF0F6')+';'"
                       class="review_status">{{item.isPublic}}</div>
                </template>
                <template v-else-if="ifCommitteeWY">
                  <div v-if="item.replyList.length == 0"
                       class="review_btn"
                       :style="$general.loadConfiguration(-6)+'background:'+appTheme">回复</div>
                </template>
              </div>
              <div class="lam_time"
                   :style="$general.loadConfiguration(-4)">{{dayjs(item.createDate).format('YYYY-MM-DD HH:mm')}}</div>
            </div>
          </div>
          <div class="lam_title text_two"
               :style="$general.loadConfiguration(-3)">{{item.title}}</div>
          <div class="lam_content text_two"
               :style="$general.loadConfiguration(-3)"
               v-html="item.content"></div>
          <!--留言回复-->
          <template v-if="item.replyList.length != 0">
            <div v-for="(nItem,nIndex) in item.replyList"
                 :key="nIndex"
                 class="reply_item">
              <!-- v-if="nItem.state == 1 || ifCommitteeWY || isStaff" -->
              <div class="flex_box flex_align_center ">
                <div class="onduty_main_s flex_placeholder"
                     :style="$general.loadConfiguration(-3)">{{(nItem.isMy?'我的':'委员')+'回复：'}}</div>
                <div v-if="ifCommitteeWY || isStaff"
                     :style="$general.loadConfiguration(-6)+'color:'+(nItem.state==0?'#FE7530':nItem.state==1?'#3088FE':'#8C96A2')">{{nItem.state==0?'待审核':nItem.state==1?'审核通过':'审核不通过'}}</div>
              </div>
              <div class="reply_content text_two"
                   :style="$general.loadConfiguration(-4)"
                   v-html="nItem.content"></div>
            </div>
          </template>
        </li>
      </ul>
      <!--加载中提示 首次为骨架屏-->
      <div v-if="showSkeleton"
           class="notText">
        <van-skeleton v-for="(item,index) in 3"
                      :key="index"
                      title
                      :row="3"></van-skeleton>
      </div>
      <template v-else-if="listData.length == 0">
        <van-empty :style="$general.loadConfiguration(-2)">
          <template #description>
            <div class="van-empty__description_text"
                 :style="$general.loadConfiguration(-1)"
                 v-html="pageNot.text"></div>
            <div class="van-empty__description_summary"
                 :style="$general.loadConfiguration(-3)"
                 v-html="pageNot.summary"></div>
          </template>
        </van-empty>
      </template>
      <div v-else
           class="notText"
           :style="$general.loadConfiguration(-4)"
           v-html="pageNot.text"
           @click="loadMore()"></div>
    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
    <!-- <template v-else-if="!title">
      <van-empty :style="$general.loadConfiguration(-2)"
                 :image="pageNot.url || pageNot.data[pageNot.type].url">
        <template #description>
          <div class="van-empty__description_text"
               :style="$general.loadConfiguration(-1)"
               v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
          <div class="van-empty__description_summary"
               :style="$general.loadConfiguration(-3)"
               v-html="pageNot.summary || pageNot.data[pageNot.type].summary"></div>
        </template>
        <div v-if="pageNot.hasBtn"
             :style="$general.loadConfiguration(-1)">
          <van-button v-if="(pageNot.type==2||pageNot.type==3)&&pageType=='page'"
                      @click.stop="T.closeWin()"
                      round
                      type="info"
                      size="large"
                      :color="appTheme">{{'返回'}}</van-button>
          <van-button v-else-if="pageNot.type==1||pageNot.type==4"
                      @click.stop="T.refreshHeaderLoading();"
                      round
                      type="info"
                      size="large"
                      :color="appTheme">{{'刷新'}}</van-button>
        </div>
      </van-empty>
    </template> -->
    <!--返回顶部 需要加一个空白占位 不然返回顶部 就会错位显示 -->
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box"
          :style="'bottom:'+(safeAreaBottom+58)+'px;'">
        {{'&nbsp;'}}
        <div :style="$general.loadConfiguration()">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="(($general.appFontSize+25)*0.01)+'px'"
                        name="upgrade"></van-icon>
            </div>
            <div v-else-if="item.type == 'btn'"
                 class="van-button-box">
              <van-button loading-type="spinner"
                          :loading-size="(($general.appFontSize)*0.01)+'px'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)">{{item.name}}</van-button>
            </div>
            <div v-else-if="item.type == 'nBtn'"
                 class="footer_nBtn_box flex_box flex_align_center flex_justify_content"
                 :style="loadConfigurationSize(26)+$general.loadConfiguration(-3)+'background:'+(item.color||appTheme)+';color:'+(T.isColorDarkOrLight(item.color||appTheme)=='light'?appTheme:'#fff')"
                 @click="footerBtnClick(item)">{{item.name}}</div>
          </template>
        </div>
      </ul>
    </transition>
    <!--不为一级页面时 适配底部条-->
    <!-- <footer v-if="pageType=='page'"
            :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer> -->
    <div class="addmsg"
         v-if="details.state === '值班中'&&!details.ifCommitteeWY"
         @click="addCommittee"
         :style="'background:'+appTheme">留言</div>
  </div>
</template>
<script>
import { Image, Skeleton } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'committeeLivingRoomDetails',
  components: {
    [Image.name]: Image,
    [Skeleton.name]: Skeleton
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      officeId: route.query.officeId,
      position: route.query.position,
      details: [],
      listData: [],
      showSkeleton: true,
      isStaff: false,
      footerBtns: [], // 底部按钮合集
      refreshing: false,
      commentInfo: { name: '留言' },
      show: false,
      leaveItem: { type: 'nBtn', name: '留言', color: '', click: 'leave' },
      pageNot: { type: '', url: '', text: '', summary: '' }
    })
    onMounted(() => {
      getInfo()
      data.showSkeleton = false
      getListData()
    })
    const onRefresh = () => {
      getInfo()
    }

    // 列表请求
    const getInfo = async () => {
      const res = await $api.committeeLivingRoom.committeeLivingRoomInfo(data.id)
      var { data: details, errcode, errmsg } = res
      const detailsList = details
      details.title = detailsList.title || ''// 标题
      details.firstAjax = detailsList.content
      if (!detailsList.title) {
        details.showSkeleton = false
        // details.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1// 类型
        details.pageNot.text = (errcode !== 200 ? errmsg : '')
      }
      details.externalLinks = detailsList.externalLinks || ''
      // if (details.externalLinks.indexOf('.mp4') >= 0 || details.externalLinks.indexOf('.MP4') >= 0) {
      //   details.getVideoPoster()
      // }
      details.id = detailsList.id || ''
      details.officeId = detailsList.officeId || ''
      details.time = detailsList.attendDate || ''
      details.state = detailsList.state || ''
      details.userUrl = detailsList.userHeadImg || ''
      details.userName = detailsList.userName || ''
      details.userDeleName = detailsList.deleName || ''
      details.userPosition = detailsList.position || ''
      details.userId = detailsList.userId || ''
      details.isCheck = detailsList.isCheck || '0'
      const us = JSON.parse(sessionStorage.getItem('user'))
      details.ifCommitteeWY = (us.id === details.userId)
      details.isStaff = (us.id === details.staffId)
      // $general.delItemForKey(data.leaveItem.click, details.footerBtns, 'click')
      // if (detailsList.state === '值班中' && !detailsList.ifCommitteeWY) { // 是正在值班
      //   details.footerBtns.push(data.leaveItem)
      // }

      data.details = details
      console.log(data.details)
      getListData()
    }

    // 留言列表
    const getListData = async () => {
      const param = {
        pageNo: 1,
        pageSize: 100,
        keyword: '',
        isPublic: data.isStaff ? '' : '1',
        officeId: data.officeId,
        topicId: data.id
      }
      const res = await $api.committeeLivingRoom.committeeLivingRoomLetterList(param)
      var { data: list, errcode, errmsg, total } = res
      data.pageNot.type = total ? (errcode === 200 ? 1 : 0) : 0
      data.pageNot.text = (errcode !== 200 ? errmsg : '')// 只有接口报的异常才改文字
      data.pageNot.summary = ''
      data.pageNot.url = ''
      if (data.pageNot.type === 0) {
        data.pageNot.url = require('../../assets/img/icon_not_comment.png')
        data.pageNot.text = '暂无' + data.commentInfo.name
        data.pageNot.summary = (data.details.state === '值班中' ? ('快来留下您的' + data.commentInfo.name + '吧~') : (data.details.state === '未开始' ? '值班还未开始哦' : '值班已经结束哦'))
      }
      list.forEach(item => {
        const itemData = item
        item.id = itemData.id || ''// id
        item.name = itemData.name || ''//
        item.userId = itemData.userId || ''
        item.url = itemData.userHeadImg || ''
        item.time = itemData.createDate
        item.title = itemData.title
        item.content = itemData.content
        item.isPublic = itemData.isPublic
        item.source = itemData.source
        item.relateType = '50.1'
        item.replyList = itemData.replyList || []
        item.replyList.forEach(function (_rItem, _rIndex, _rArr) {
          item.replyList.push({
            id: _rItem.id,
            isMy: _rItem.createBy === item.userId,
            state: _rItem.isCheck,
            content: _rItem.content
          })
        })
        data.listData.push(item)
      })
      console.log(data.listData)
    }
    const clickLam = (rows) => {
      router.push({
        path: 'lamDetail',
        query: {
          ifCommitteeWY: data.details.ifCommitteeWY,
          isStaff: data.details.isStaff,
          isCheck: data.details.isCheck,
          id: rows.id
        }
      })
    }
    const addCommittee = () => {
      router.push({ name: 'add', query: { paramType: 'wyzb', topicId: data.id, officeId: data.officeId } })
    }
    const test = (val) => { console.log(val) }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, test, onClickLeft, clickLam, $general, getListData, dayjs, addCommittee }
  }
}
</script>
<style lang="less" >
.committeeLivingRoom {
  width: 100%;
  min-height: 100%;
  background: #fff;
  // background: #f8f8f8;

  .onduty_box {
    padding: 14px 15px;

    .onduty_title {
      font-weight: 600;
      line-height: 1.27;
      color: #333333;
    }

    .onduty_user {
      background: #f8f9fa;
      border-radius: 4px;
      padding: 14px;
      margin-top: 10px;

      .onduty_main_s {
        font-weight: 600;
        color: #333333;
        line-height: 1.46;
      }

      .onduty_user_dele {
        color: #999999;
        margin-left: 10px;
        line-height: 1.3;
      }

      .onduty_user_position {
        color: #666666;
        margin-top: 4px;
        line-height: 1.33;
      }
    }
  }

  .lam_item {
    padding: 12px 15px;
    background: #fff;

    .lam_user_name {
      color: #333333;
      font-weight: 600;
      line-height: 1.53;
    }

    .lam_time {
      line-height: 1.3;
      color: #666666;
      margin-top: 5px;
    }

    .lam_title {
      font-weight: 600;
      color: #333333;
      line-height: 1.46;
      margin-top: 5px;
    }

    .lam_content {
      color: #333333;
      line-height: 1.46;
      margin-top: 5px;
    }

    .reply_item {
      padding: 5px 12px;
      background: #f4f7fa;
      border-radius: 2px;
      margin-top: 5px;

      .reply_content {
        color: #666666;
        line-height: 1.33;
        margin: 5px 0;
      }
    }
  }
  .van-empty__description_summary {
    font-weight: 400;
    color: #999999;
    margin-top: 10px;
  }
  .van-empty__description_text {
    font-weight: 400;
    color: #666666;
    text-align: center;
  }
  .addmsg {
    width: 56px;
    height: 56px;
    text-align: center;
    background: #3088fe;
    box-shadow: 0 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    opacity: 1;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    color: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 50px;
    margin: auto;
  }
}
</style>

<template>
  <div class="bookReader">
    <writeRemeber :show="show"
                  ref="writefn" />
    <van-sticky>
      <van-nav-bar :title="txt.name"
                   left-text=""
                   :left-arrow="isShowHead"
                   @click-left="
                   onClickLeft"
                   @click-right="open">
        <template #right>
          <van-icon name="coupon-o"
                    size="18" />&nbsp;笔记
        </template>
      </van-nav-bar>
    </van-sticky>

    <iframe :src="bookUrl + addUrl + signUrl"
            ref="iframehtml"
            frameborder="0"></iframe>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { onMounted, reactive, inject, toRefs, watch, ref } from 'vue'
import { Empty, Overlay, Tag, Icon, Field, Dialog, NavBar, Sticky } from 'vant'
import md5 from 'js-md5'
import html2canvas from 'html2canvas'
import writeRemeber from '../../../components/writeRemeber/writeRemeber'
export default {
  name: 'bookReader',
  components: {
    writeRemeber,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const $general = inject('$general')
    const route = useRoute()
    // const router = useRouter()
    const writefn = ref(null)
    const iframehtml = ref(null)
    const txt = route.query.txt
    const imgs = route.query.imgs
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      txt: JSON.parse(txt),
      userId: JSON.parse(sessionStorage.getItem('user')).id,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showSkeleton: false,
      firstAjax: false,

      title: '标题',

      headerName: '', // 顶部书本名
      electricity: 80, // 现在的电量 百分比
      isPlugged: false, // 是否充电中
      time: '14:12', // 现在的时间

      bookBg: '#FFF', // 背景
      animType: 'curl', // 翻页样式
      progress: 1, // 初始化打开书本进度
      textStyle: {
        size: 16,
        color: ''
      }, // 字体样式
      filePath: 'widget://wgt/A00002/res/kaka.txt', // 源文件
      codingType: 'gbk', // 文本编码

      atNight: { is: false, bg: '#060606', color: '#252526' }, // 是否夜间模式
      daytime: {
        active: 0,
        data: [
          { name: '', bg: '#f5f5f5', color: '#000000' }, // 默认
          { name: '', bg: '#ddd1ae', color: '#3d2b12' },
          { name: '', bg: '#ddc292', color: '#46270c' },
          { name: '', bg: '#b1c9ad', color: '#1a2019' },
          { name: '', bg: '#e5aed4', color: '#2e2220' },
          { name: '', bg: '#636a74', color: '#8b94a2' }
        ]
      },

      readMinutes: 0,
      task: null, // 计时任务
      bookUrl: '',
      addUrl: '',
      signUrl: '',
      show: false,
      imgs: imgs
    })
    watch(() => data.progress, (newName, oldName) => {
      console.log('newName', newName, oldName)
      updateStatus()
    })
    watch(() => data.readMinutes, (newName, oldName) => {
      console.log('newName', newName, oldName)
      updateStatus()
    })
    onMounted(() => {
      bookReaderInit()
      addCounter()
      getImg()
    })

    const getImg = () => {
      html2canvas(iframehtml, { backgroundColor: '#fff' }).then(canvas => {
        const dataURL = canvas.toDataURL('image/png')
        console.log(dataURL)
      })
    }

    const getReadInfo = async () => {
      var { data: info } = await $api.bookAcademy.readbookinfo({
        bookId: data.id
      })
      if (info) {
        data.headerName = info.bookName// 书名
        data.codingType = info.codingType || 'UTF-8'
        data.progress = info.readPercent || 1// 当前 进度
        data.readMinutes = info.readMinutes || 1// 当前分钟数
        if (data.txt.bookType === '3') {
          data.bookUrl = `http://view.xdocin.com/xdoc?_xdoc=${data.txt.url}`
        } else {
          data.bookUrl = 'https://s.zhangyue.com/' + (data.txt.bookType === '1' ? 'read?bid' : 'audio/ting?bookId') + '=' + data.id + '&' + (data.txt.bookType === '1' ? 'cid' : 'chapterId') + '=' + data.progress + '&'
          data.addUrl = 'appId=' + sessionStorage.getItem('AcademyAppId') + '&rentId=' + sessionStorage.getItem('AcademyRentId') + '&timestamp=' + (Date.parse(new Date()).toString().substr(0, 10)) + '&userId=' + data.userId
          data.signUrl = '&sign=' + zyBookSign(data.addUrl)
          updateStatus()
        }
      }
    }

    const bookReaderInit = async () => {
      localStorage.setItem('lastReadBook' + data.userId, data.id)
      var { data: newData } = await $api.bookAcademy.existBook({
        bookId: data.id
      })
      var bookshelf = newData || false
      if (bookshelf) {
        localStorage.setItem('myLastBook' + data.userId, data.id)
      }
      getReadInfo()
      startTask()
      showTime()// 先算一下时间  然后再1秒循环
      setInterval(function () {
        showTime()
      }, 1000)
    }
    // 计算签名
    const zyBookSign = (str) => {
      var encrypt = md5(str).substr(6, 18)
      return md5(encrypt).substr(10, 16)
    }
    // 获取进度
    const getReadPosition = async () => {
      if (data.txt.bookType === '3') { // 自己的书籍
        return { body: { chapterId: 1 } }
      }
      await $api.bookAcademy.getReadPosition({ id: data.id, addUrl: data.addUrl, signUrl: data.signUrl })
    }
    // 保存进度
    const updateStatus = async () => {
      getReadPosition()
      if (data.txt.bookType === '3') {
        data.progress = 1
      }

      await $api.bookAcademy.updateStatus({ bookId: data.id, readMinutes: data.readMinutes, readPercent: data.progress })
    }
    // 开始计时
    const startTask = async () => {
      if (data.task) {
        stopTask()
      }
      data.task = setInterval(function () {
        data.readMinutes++
        updateStatus()
      }, 1000 * 60)
    }
    // 暂停计时
    const stopTask = async () => {
      clearInterval(data.task)
    }
    // 显示 页面时间
    const showTime = async () => {
      var nowTime = new Date()
      var hours = nowTime.getHours()
      var minutes = nowTime.getMinutes()
      hours = (hours < 10) ? '0' + hours : hours
      minutes = (minutes < 10) ? '0' + minutes : minutes
      data.time = hours + ':' + minutes
    }
    // 增加书院统计
    const addCounter = async () => {
      var datas = {
        dataId: data.id
      }
      await $api.bookAcademy.addCounter(datas)
    }
    // 点击笔记
    const open = () => {
      // 获取ifram元素
      // var iframes = iframehtml.value.contentWindow
      // var iframebody = iframes.document.getElementsByTagName('body')[0]
      // console.log(iframebody)
      // html2canvas(iframebody).then(function (canvas) {
      //   // document.getElementById('view').appendChild(canvas)
      //   var imageData = canvas.toDataURL('image/jpeg')
      //   var binary = atob(imageData.split(',')[1])
      //   var array = []
      //   for (var i = 0; i < binary.length; i++) {
      //     array.push(binary.charCodeAt(i))
      //   }
      //   var bodata = new Blob([new Uint8Array(array)], { type: 'image/jpeg' }) // 二进制
      //   console.log(bodata)
      //   writefn.value.takeScreenshot(imageData)
      // })
      data.show = true
      writefn.value.takeScreenshot(data.imgs, data.id)
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), addCounter, open, writefn, iframehtml, onClickLeft }
  }
}
</script>
<style lang="less" scoped>
@import "./bookReader.less";
</style>

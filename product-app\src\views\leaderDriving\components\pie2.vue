<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'pie2',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: <PERSON><PERSON>
  },
  props: {
    color: String,
    id: String,
    datas: Object
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      var chartDom = document.getElementById(props.id)
      myChart = echarts.init(chartDom)
      data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
      setOptions()
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
    })
    const setOptions = () => {
      var options = {
        legend: {
          show: false,
          top: '3%',
          left: 'left'
        },
        color: ['#3691FF', '#CFE3FF'],
        graphic: [
          {
            type: 'text', // 组件类型
            left: 'center', // 定位
            top: '35%',
            style: { // 样式
              text: props.datas.num + '%', // 文字
              fontSize: parseInt(data.viewportWidth * 0.035), // 文字大小
              textAlign: 'center', // 定位
              fill: '#333333' // 字体颜色
            }
          },
          {
            type: 'text',
            left: 'center',
            top: '55%',
            style: {
              text: props.datas.text,
              fontSize: parseInt(data.viewportWidth * 0.03),
              textAlign: 'center',
              fill: '#999999'
            }
          }
        ],
        series: [
          {
            name: '总安装率',
            type: 'pie',
            radius: ['65%', '85%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data: [
              { value: props.datas.num, name: 'Search Engine' },
              { value: 100 - props.datas.num, name: 'Direct' }
            ]
          }
        ]
      }
      nextTick(() => {
        if (props.id === 'pie1') {
          myChart.setOption(options)
        } else if (props.id === 'pie2') {
          myChart.setOption(options)
        }
      })
    }
    return { ...toRefs(data), general }
  }
}
</script>
<style lang="less" scoped>
#pie1 {
  width: 100%;
  height: 100%;
  // margin: 10px 0;
  box-sizing: border-box;
  background: #fff;
}

#pie2 {
  width: 100%;
  height: 100%;
  // margin: 10px 0;
  box-sizing: border-box;
  background: #fff;
}
</style>

@font-face {
  font-family: "iconfont"; /* Project id 3113361 */
  src: url('iconfont.woff2?t=1654073915586') format('woff2'),
       url('iconfont.woff?t=1654073915586') format('woff'),
       url('iconfont.ttf?t=1654073915586') format('truetype');
}

.icon {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-liebiao3:before {
  content: "\e63c";
}

.icon-gongge:before {
  content: "\e7d6";
}

.icon-tongji:before {
  content: "\e66a";
}

.icon-xiazai:before {
  content: "\e86f";
}

.icon-xuanzhong:before {
  content: "\e627";
}

.icon-weixuanzhong:before {
  content: "\e626";
}

.icon-bianjiedit26:before {
  content: "\e655";
}

.icon-dingdanchuli:before {
  content: "\e615";
}

.icon-bianjisekuai:before {
  content: "\ec7c";
}

.icon-yuyin2:before {
  content: "\e667";
}

.icon-yuyin1:before {
  content: "\e805";
}

.icon-sousuo:before {
  content: "\e633";
}

.icon-tongxunlu:before {
  content: "\e610";
}

.icon-zitidaxiao:before {
  content: "\e604";
}

.icon-shengyinbofang-xian:before {
  content: "\e7e5";
}

.icon-dingwei:before {
  content: "\e6ad";
}

.icon-yuyin:before {
  content: "\e8f7";
}


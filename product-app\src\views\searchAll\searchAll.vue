<template>
  <div class="searchAll">
    <van-nav-bar v-if="isShowHead" :title="title" fixed placeholder safe-area-inset-top left-text="" left-arrow
      @click-left="onClickLeft" />
    <div class="searchAll_top">
      <input type="text" v-model="keyword" class="searchAll_top_inp">
      <van-icon name="cross" v-if="keyword" @click="cross" />
    </div>
    <van-tabs v-model:active="active" swipeable @click-tab="tabsClick">
      <van-tab v-for="it, ind in searchTab" :title="it.name" :key="ind">
        <!-- {{ item.name }} -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
            <template v-for="(item, index) in dataList" :key="index">
              <div class="flex_box vue_newslist_item" @click="openDetails(item)">
                <div class="flex_placeholder vue_newslist_warp">
                  <div class="vue_newslist_title text_two" v-if="item.titleHighlight"
                    :style="$general.loadConfiguration()" v-html="item.titleHighlight"></div>
                  <div class="vue_newslist_title text_two titlefont" :style="$general.loadConfiguration()" v-else
                    v-html="item.title"></div>
                  <div class="flex_placeholder flex_box flex_align_center"></div>
                  <div class="flex_box flex_align_center mtop">
                    <div class="vue_newslist_time" :style="$general.loadConfiguration(-6)">
                      {{ item.time.split(' ')[0] }}
                    </div>
                    <div class="vue_newslist_source flex_placeholder text_one2 tag"
                      :style="$general.loadConfiguration(-6)">{{ item.source || item.createBy }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Tab, Tabs } from 'vant'
export default {
  name: 'searchAll',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const $general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: route.query.keyword || '',
      searchTab: [
        { name: '全部', type: '' },
        { name: '综合资讯', type: 'news' },
        { name: '通知公告', type: 'notice' },
        { name: '学习培训', type: 'study' },
        { name: '意见征集', type: 'survey' },
        { name: '知情明政', type: 'wszl' }
      ],
      searchType: route.query.type || '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      active: '',
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] }

    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
    }
    const getList = async () => {
      const postParam = {
        page: data.pageNo,
        pageSize: data.pageSize,
        index: data.searchType,
        title: data.keyword
      }
      var { data: list, total } = await $api.general.searchData(postParam)
      list.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
        var itemData = _eItem
        _eItem.relateType = itemData.type || ''
        _eItem.id = itemData.id || ''
        _eItem._id = itemData._id || ''
        _eItem.title = itemData.title || ''
        _eItem.time = itemData.submitDate || ''
        _eItem.source = itemData.indexTypeName || ''
        _eItem.titleHighlight = itemData.titleHighlight || ''
        _eItem.highlight = itemData.highlight || ''
        _eItem.url = itemData.coverImgUrl || ''
        _eItem.topImg = itemData.topImg || ''
      })
      data.dataList = data.dataList.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const onClickLeft = () => history.back()
    const cross = () => {
      data.keyword = ''
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const tabsClick = (e) => {
      data.searchType = data.searchTab[data.active].type
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const openDetails = (item) => {
      router.push({ path: '/searchDetails', query: { id: item.id } })
    }
    return { ...toRefs(data), $api, openDetails, tabsClick, cross, onClickLeft, onRefresh, onLoad, $general, confirm }
  }
}
</script>
<style lang="less" scoped>
.searchAll {
  width: 100%;
  height: 100vh;
  background: #fff;

  .searchAll_top {
    width: 97%;
    margin: 10px auto;
    height: 40px;
    background: #f4f4f4;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .searchAll_top_inp {
      width: 90%;
      height: 100%;
    }
  }

  ::v-deep .van-list {
    height: 100vh !important;
  }

  ::v-deep .van-tabs__line {
    background: #3b9cfe;
  }

  // ::v-deep .van-tab {
  //   width: 25%;
  //   .van-tab__text {
  //     font-size: 14px !important;
  //   }
  // }
  ::v-deep .van-tab__panel {
    height: 100vh;
  }
}
</style>

<template>
  <div class="bookNotice">
    <!--搜索-->
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">

      </van-nav-bar>
      <div id="search"
           class="search_box">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"><input :style="'font-size:13px;'"
                   :placeholder="'搜索'"
                   maxlength="100"
                   type="search"
                   @keyup.enter="btnSearch"
                   v-model="keyword" /></form>
        </div>
      </div>
    </van-sticky>

    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--历史阅读list-->
        <ul v-if="history.length != 0"
            class="history_box">
          <van-cell clickable
                    @click="openBookDetails(nItem)"
                    v-for="(nItem,nIndex) in history"
                    :key="nIndex"
                    style="padding:0;">
            <div class="history_item flex_box">
              <div :style="'width:93px;height:117px;'+'position: relative;'">
                <div v-if="nItem.txt.state == 1"
                     class="item_download flex_box flex_align_center flex_justify_content"
                     :style="'font-size:14px;'">
                  <van-circle fill="#FFF"
                              :size="56"
                              v-model="nItem.txt.schedule"
                              :rate="nItem.txt.schedule"
                              stroke-width="100"
                              :text="nItem.txt.schedule+'%'"></van-circle>
                </div>
                <img v-if="nItem.txt.bookType == '2'"
                     class="item_Sound"
                     :style="'width:28px;height:28px;'"
                     :src="icon_hasSound" />
                <img v-if="nItem.txt.isAvailable == '0'"
                     class="item_overdue"
                     src="../../../assets/img/overdue.png" />
                <img style="width: 100%;height: 100%;object-fit: cover;border-radius: 2px;"
                     :src="nItem.img.url" />
              </div>
              <div class="flex_placeholder"
                   style="padding-top: 5px;padding-left: 10px;">
                <div v-if="nItem.name"
                     class="history_name text_one2"
                     :style="'font-size:15px;'"
                     v-html="nItem.name"></div>
                <div v-if="nItem.author"
                     class="history_author text_one2"
                     :style="'font-size:12px;'+'color:'+appTheme"
                     v-html="nItem.author"></div>
                <div v-if="nItem.summary"
                     class="history_summary text_two"
                     :style="'font-size:12px;'"
                     v-html="nItem.summary"></div>
                <div v-if="nItem.txt.bookType == '3'"
                     class="history_progress flex_box flex_align_center">
                  <div class="flex_placeholder">
                    <van-progress :percentage="nItem.progress"
                                  :color="appTheme"
                                  track-color="#D8D8D8"
                                  :show-pivot="false"></van-progress>
                  </div>
                  <div class="history_progress_text"
                       :style="'font-size:12px;'">{{nItem.progress.toFixed(1)}}%</div>
                </div>
                <div v-else
                     class="itemSex_progress text_one2"
                     :style="'font-size:12px;'"
                     v-html="('已阅读第'+nItem.progress+'章')"></div>

                <div @click.stop="goBookReader(nItem.txt,nItem.id)"
                     :style="'font-size:13px;'">
                  <van-button round
                              size="small"
                              type="default">继续阅读</van-button>
                </div>
              </div>
            </div>
          </van-cell>
        </ul>
        <!--数据列表-->
        <ul v-if="!switchs.value?!isNull:true">
          <li class="item_body_warp">
            <div class="item_body"
                 style="padding: 15px 0;">
              <div class="flex_box flex_align_center van-hairline--bottom"
                   style="margin-bottom: 10px;padding-right: 15px;">
                <div class="flex_placeholder"
                     :class="switchs.data.length < 4?'flex_box':''"
                     :style="'font-size:15px;'">
                  <van-tabs v-model="switchs.value"
                            @click="tabClick"
                            swipe-threshold="1"
                            :color="appTheme"
                            :ellipsis="false">
                    <van-tab v-for="(item,index) in switchs.data"
                             :title="item.label"
                             :key="index"
                             :name="item.value"></van-tab>
                  </van-tabs>
                </div>
                <div @click="isSetting = !isSetting;listSelect=[];"
                     style="flex-shrink:0;padding: 8px;flex-shrink:0;">
                  <van-icon v-if="!isSetting"
                            :size="21"
                            :color="appTheme"
                            :name="'setting-o'"></van-icon>
                  <div v-else
                       :style="'font-size:15px;'+'padding-right:4px;margin-right:-18px;width:55px;'">
                    <van-tag style="padding: 1px 10px;"
                             :color="appTheme"
                             round
                             type="primary">{{'完成'}}</van-tag>
                  </div>
                </div>
              </div>
              <van-empty v-if="dataList.length == 0"
                         :style="'font-size:14px;'"
                         :image="icon_no_data"
                         :description="'暂无数据'"></van-empty>
              <van-checkbox-group v-else
                                  ref="checkboxGroup"
                                  v-model="listSelect">
                <div class="itemSex_box flex_box T-flex-flow-row-wrap">
                  <div v-for="(nItem,nIndex) in dataList"
                       :key="nIndex"
                       class="itemSex_item"
                       @click="clickBook(nItem,nIndex)">
                    <div :style="'width:93px;height:119px;'+'position: relative;'">
                      <div v-if="nItem.txt.state == 1"
                           class="item_download flex_box flex_align_center flex_justify_content"
                           :style="'font-size:14px;'">
                        <van-circle fill="#FFF"
                                    :size="56"
                                    v-model="nItem.txt.schedule"
                                    :rate="nItem.txt.schedule"
                                    stroke-width="100"
                                    :text="nItem.txt.schedule+'%'"></van-circle>
                      </div>
                      <img v-if="nItem.txt.bookType == '2'"
                           class="item_Sound"
                           :style="'width:28px;height:28px;'"
                           :src="icon_hasSound" />
                      <img v-if="nItem.txt.isAvailable == '0'"
                           class="item_overdue"
                           src="../../../assets/img/overdue.png" />
                      <img style="width: 100%;height: 100%;object-fit: cover;border-radius: 2px;"
                           :src="nItem.img.url" />
                      <van-checkbox v-if="isSetting"
                                    :icon-size="21"
                                    :checked-color="appTheme"
                                    :ret="'checkbox'+nItem.id"
                                    class="checkbox_item"
                                    :name="nItem"></van-checkbox>
                    </div>
                    <div v-if="nItem.name"
                         class="itemSex_name text_one2"
                         :style="'font-size:15px;'"
                         v-html="nItem.name"></div>
                    <div class="itemSex_progress text_one2"
                         :style="'font-size:12px;'"
                         v-html="nItem.txt.bookType == '3'?('已阅读'+(nItem.progress.toFixed(1))+'%'):('第'+nItem.progress+'章')"></div>
                  </div>
                </div>
              </van-checkbox-group>
            </div>
          </li>
        </ul>
      </van-list>
    </van-pull-refresh>
    <!--底下设置的 时候全选界面-->
    <template v-if="isSetting">
      <div style="height: 40px;"></div>
      <div v-if="isSetting"
           class="select_footer">
        <div class="select_footer_box flex_box flex_align_center">
          <van-checkbox v-if="dataList.length != 0"
                        @click="if($refs['checkboxGroup'])$refs['checkboxGroup'].toggleAll(isAllSelect);"
                        :icon-size="21"
                        v-model="isAllSelect"
                        :checked-color="appTheme">全选</van-checkbox>
          <div class="flex_placeholder"></div>
          <div v-if="dataList.length != 0"
               class="flex_box"
               style="margin-left:10px;">
            <div @click.stop="clickAddCategory()"
                 :style="'padding-right:10px;'">
              <van-tag style="padding: 2px 10px;"
                       :color="appTheme"
                       plain
                       round
                       type="primary">{{'加入分类'}}</van-tag>
            </div>
            <div @click.stop="clickDelete()">
              <van-tag style="padding: 2px 10px;"
                       :color="appTheme"
                       plain
                       round
                       type="primary">{{'删除'}}</van-tag>
            </div>
          </div>
          <div v-else
               @click="clickAddCategory(1)"
               :style="'padding-right:10px;'">
            <van-tag style="padding: 2px 10px;"
                     :color="appTheme"
                     plain
                     round
                     type="primary">{{'分类管理'}}</van-tag>
          </div>
        </div>
      </div>
    </template>
    <!--添加分类-->
    <van-overlay :show="addCategory"
                 z-index="100"
                 :lock-scroll="false">
      <div class="T-flexbox-vertical flex_align_center flex_justify_content">
        <div class="category_box T-flexbox-vertical"
             style="max-height: 70%;">
          <div class="flex_placeholder"
               style="height:1px;overflow-y: auto;-webkit-overflow-scrolling: touch;">
            <van-empty v-if="switchs.data.length <= 1"
                       :style="'font-size:14px;'"
                       :image="icon_no_data"
                       :description="'暂无分类'"></van-empty>
            <div v-else
                 v-for="(nItem,nIndex) in switchs.data"
                 :key='nItem.id'>
              <div v-if="nIndex != 0"
                   class="category_item flex_box flex_align_center">
                <van-icon @click="editCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'edit'"></van-icon>
                <div @click="clickCategory(nItem,nIndex)"
                     class="flex_placeholder"
                     :style="'color:'+appTheme+';text-align: center;'">{{nItem.label}}</div>
                <van-icon @click="deleteCategory(nItem,nIndex)"
                          :size="21"
                          :color="'#666'"
                          :name="'close'"></van-icon>
              </div>
            </div>
          </div>
          <div @click.stop="createCategory()"
               class="category_item flex_box flex_align_center flex_justify_content">
            <van-icon :size="21"
                      :color="appTheme"
                      :name="'plus'"></van-icon>
            <div :style="'color:'+appTheme+';text-align: center;margin:0 0.4rem 0 0.1rem;'">新建分类</div>
          </div>
          <div class="flex_box flex_align_center flex_justify_content">
            <van-tag @click.stop="addCategory = false;"
                     style="padding: 0.04rem 0.25rem;margin-top:0.2rem;border-radius: 0.05rem;"
                     :color="appTheme"
                     plain
                     type="primary">{{'关闭'}}</van-tag>
            <!-- <van-tag @click="clickCategory({label:'',value:''})"
                     style="padding: 0.04rem 0.25rem;margin-top:0.2rem;border-radius: 0.05rem;margin-left: 0.1rem;"
                     :color="appTheme"
                     type="primary">{{'确认'}}</van-tag> -->
          </div>
        </div>
      </div>
    </van-overlay>
    <van-dialog v-model:show="addTypeNameShow"
                title="请输入分类名字"
                :confirmButtonColor="appTheme"
                @confirm="confirmAddTypeName"
                show-cancel-button>
      <van-field v-model="addTypeName"
                 label="分类名字"
                 placeholder="请输入分类名字" />
    </van-dialog>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, Sticky, NavBar, Circle, Progress } from 'vant'
// import moment from 'moment'
export default {
  name: 'bookNotice',
  components: {
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Circle.name]: Circle,
    [Progress.name]: Progress,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      title: route.query.title || '个人书库',
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      listData: [
        // { img: { url: 'http://test.dc.cszysoft.com:20129/lzt/flowAttachment/masterRepository/2021/03/19/316016605421109248.jpg' }, txt: { url: 'http://test.dc.cszysoft.com:20129/lzt/flowAttachment/masterRepository/2021/03/25/318261715198279680.txt', state: 2, schedule: 11, name: '斗罗大陆', path: '/var/mobile/Containers/Data/Application/FAC9D912-71A5-4C8D-BB52-0F485AC8F8A7/Documents/uzfs/A6168351706281/fileCache/318261715198279680.txt', format: 'txt' }, itemId: '319625064641200128', id: '316019837518217216', name: '斗罗大陆', author: '唐家三少', summary: '', progress: 0 }, { img: { url: 'http://test.dc.cszysoft.com:20129/lzt/flowAttachment/masterRepository/2021/03/22/317154480896344064.png' }, txt: { url: 'http://test.dc.cszysoft.com:20129/lzt/flowAttachment/masterRepository/2021/03/29/319628690629591040.zip', state: 0, schedule: -1, name: '斗罗大陆III龙王传说', path: '/var/mobile/Containers/Data/Application/FAC9D912-71A5-4C8D-BB52-0F485AC8F8A7/Documents/uzfs/A6168351706281/fileCache/319628690629591040.zip', format: 'zip' }, itemId: '319627768969035776', id: '317154539763400704', name: '斗罗大陆III龙王传说', author: '唐家三少', summary: '', progress: 0 }
      ], // 列表数据

      listSelect: [],
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部   btn为按钮

      history: [
        // { url: '../../../images/img_test10.png', name: '论美国的民主', author: '作者：【法】托克维尔', summary: '西双版纳出版社', progress: 78 }
      ], // 历史阅读
      isAllSelect: false, // 是否全选
      isSetting: false,
      switchs: { value: '', data: [{ label: '所有书籍', value: '' }] },
      addCategory: false, // 添加分类是否
      optionItem: null,
      editIndex: 0,
      isEdit: false,
      addTypeNameShow: false,
      addTypeName: '',
      isNull: false
    })
    watch(() => data.listSelect, (newName, oldName) => {
      console.log(data.listSelect)
    })
    watch(() => data.keyword, (newName, oldName) => {
      onRefresh()
    })
    onMounted(() => {
      onRefresh()
    })
    if (data.title) {
      document.title = data.title
    }
    const goBookReader = (txt, id) => {
      router.push({ name: 'bookReader', query: { id: id, txt: JSON.stringify(txt) } })
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const getReadbookinfo = async () => {
      const lastReadBook = localStorage.getItem('myLastBook' + data.user.id)
      if (lastReadBook) {
        const { data: _eItem } = await $api.bookAcademy.readbookinfo({ bookId: lastReadBook })
        data.history = []
        var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
        item.itemId = _eItem.id || ''// 书桌id
        item.id = _eItem.bookId || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.progress = Number(_eItem.readPercent || '0')// 进度
        data.history = data.history.concat(item)
      }
      getData()
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        typeId: data.switchs.value

      }
      const { data: list, total } = await $api.bookAcademy.getMyBookList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '3' } }
        item.id = _eItem.bookId || ''// 书本id
        item.businessId = _eItem.id || ''// 业务id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.progress = Number(_eItem.readPercent || '0')// 进度
        var sItem = $general.getItemForKey(item.id, data.dataList, 'id')
        if (!sItem) { newData.push(item) }
      })
      data.dataList = data.dataList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      console.log(row)
      router.push({ name: 'bookNoticeDetails', query: { id: row.id } })
    }
    // 点击 书本
    const clickBook = (_item, _index) => {
      if (data.isSetting) {
        return
      }
      openBookDetails(_item)
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getReadbookinfo()
      getTypeList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }
    const clickCategory = async (_item, _index) => {
      if (data.listSelect.length !== 0) { // 是添加分类
        var clickName = ''; var clickId = ''
        data.listSelect.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          clickName += (clickName ? ',' : '') + _eItem.name
          clickId += (clickId ? ',' : '') + _eItem.id
        })
        const ret = await $api.bookAcademy.updateType({ bookIds: clickId, typeId: _item.value })
        data.addCategory = false
        data.isSetting = false
        data.pageNo = 1
        data.listSelect = []
        Toast('书本【' + clickName + '】添加到' + (!_item.label ? '书架' : ('分类【' + _item.label + '】')) + (ret.data ? '成功' : '失败'))
        getData()
      }
    }
    // 点击删除
    const clickDelete = async () => {
      if (data.listSelect.length === 0) {
        return Toast('请选择要删除的书本')
      }
      var clickName = ''; var clickId = ''
      data.listSelect.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        clickName += (clickName ? ',' : '') + _eItem.name
        clickId += (clickId ? ',' : '') + _eItem.businessId

        if (localStorage.getItem('myLastBook' + data.user.id) === _eItem.id) {
          localStorage.removeItem('myLastBook' + data.user.id)
          getReadbookinfo()
        }
      })
      const ret = await $api.bookAcademy.delsTypeMyBook({ ids: clickId })
      if (ret) {
        data.addCategory = false
        Toast('删除' + (ret.data ? '成功' : '失败'))
        if (ret.data) {
          data.listSelect.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
            var sItem = $general.getItemForKey(_eItem.id, data.listData, 'id')
            if (sItem) {
              $general.delItemForKey(_eItem.id, data.listData, 'id')
            }
          })
          data.listSelect = []
          if (data.listSelect.length === 0) {
            data.isSetting = false
            onRefresh()
          }
        }
      }
    }
    const confirmAddTypeName = async () => {
      var typeName = data.addTypeName
      if (!(typeName).replace(/(^\s*)|(\s*$)/g, '')) {
        return Toast('请输入分类名字')
      }
      var datas = {}
      if (data.isEdit) {
        datas = {
          id: typeName, typeName: typeName
        }
        var { data: editData } = await $api.bookAcademy.editType(datas)
        if (editData) {
          Toast('修改分类' + (editData ? '成功' : '失败'))
          data.switchs.data[data.editIndex].label = typeName
          data.addTypeName = ''
        }
      } else {
        datas = {
          typeName: typeName
        }
        var { data: addData } = await $api.bookAcademy.addType(datas)
        if (addData) {
          Toast('新增分类' + (addData ? '成功' : '失败'))
          data.addTypeName = ''
          data.switchs.data.push({ label: typeName, value: addData })
        }
      }
    }
    // 创建新分类
    const createCategory = () => {
      data.addTypeNameShow = true
      data.isEdit = false
    }
    // 修改分类
    const editCategory = (_item, _index) => {
      data.addTypeNameShow = true
      data.addTypeName = _item.label
      data.editIndex = _index
      data.isEdit = true
    }
    // 删除分类
    const deleteCategory = async (_item, _index) => {
      var datas = {
        id: _item.value
      }
      var { data: list } = await $api.bookAcademy.delType(datas)
      if (list === 1) {
        Toast('删除分类' + (list === 1 ? '成功' : '失败'))
        $general.delItemForKey(_index, data.switchs.data)
        if (_item.value === data.switchs.value) { // 当前 正处于分类  回到所有书籍
          data.switchs.value = data.switchs.data[0].value
          onRefresh()
        }
      }
    }

    // 获取所有分类
    const getTypeList = async () => {
      var datas = {
      }
      var { data: list } = await $api.bookAcademy.getALlTypeList(datas)
      data.switchs.data = [{ label: '所有书籍', value: '' }]
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        data.switchs.data.push({ label: _eItem.typeName, value: _eItem.id })
      })
    }
    // tab切换事件
    const tabClick = (_name, _title) => {
      data.addCategory = false
      data.isSetting = false
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      data.switchs.value = _name
      getData()
    }
    // 点击加入分类
    const clickAddCategory = (_type) => {
      if (data.listSelect.length === 0 && !_type) {
        return Toast('请选择书本')
      }
      data.addCategory = true
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, openDetails, btnSearch, goBookReader, openBookDetails, tabClick, clickAddCategory, clickBook, getTypeList, deleteCategory, createCategory, editCategory, confirmAddTypeName, clickCategory, clickDelete }
  }
}
</script>
<style lang="less" scoped>
.bookNotice {
  width: 100%;
  background: #f9f9f9;
  position: relative;
  .item_overdue {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 52px;
  }
  .category_box {
    width: 70%;
  }
  .search_warp .search_input {
    padding-left: 10px;
  }
  .footer_item_hot {
    position: absolute;
    top: 2px;
    right: 0;
    background: #f92323;
    border-radius: 50%;
    z-index: 1;
  }
  .history_box {
    padding: 5px 14px 15px 14px;
  }
  .history_box .van-cell {
    border-radius: 14px;
    box-shadow: 0px 0px 10px -6px rgba(0, 0, 0, 0.4);
  }
  .history_box .history_item {
    padding: 8px 12px;
  }
  .history_name {
    color: #222;
    font-weight: 500;
    margin-top: 0;
    padding-bottom: 2px;
  }
  .history_summary {
    font-weight: 400;
    margin-top: 5px;
    color: #8b8a8a;
  }
  .history_progress {
    margin-top: 3px;
  }
  .history_progress_text {
    margin-left: 8px;
    color: #8b8a8a;
  }
  .history_item {
    .van-button {
      padding: 5px 8px !important;
      border: var(--van-button-border-width) solid
        var(--van-button-default-border-color) !important;
    }
  }

  .item_body .history_item {
    padding: 8px 0;
  }
  .item_title {
    color: #222;
    font-weight: bold;
    padding: 6px 0;
  }
  .item_title_icon {
    padding: 0 7px;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  .itemSex_box {
    margin: 0 0;
    padding: 0 15px;
  }
  .itemSex_item {
    width: 33.33%;
    padding: 0 6px 5px 6px;
  }
  .itemSex_name {
    color: #222;
    font-weight: 500;
    margin-top: 5px;
    padding-left: 5px;
  }
  .itemSex_progress {
    color: #8b8a8a;
    font-weight: 400;
    margin-top: 1px;
    margin-bottom: 5px;
  }
  .checkbox_item {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
  .select_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #ccc;
  }
  .select_footer_box {
    height: 40px;
    padding: 0 10px;
  }
}
</style>

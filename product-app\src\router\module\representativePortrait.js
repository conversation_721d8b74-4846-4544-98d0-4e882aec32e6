const representativePortraitList = () => import('@/views/representativePortrait/representativePortraitList')
const timeAxis = () => import('@/views/representativePortrait/timeAxis')
const annualReport = () => import('@/views/representativePortrait/annualReport')
const twoDataPage = () => import('@/views/representativePortrait/twoDataPage')
const oneDataPage = () => import('@/views/representativePortrait/oneDataPage')
const Page0 = () => import('@/views/representativePortrait/Page0')
// const Page1 = () => import('@/views/representativePortrait/Page1')
// const Page2 = () => import('@/views/representativePortrait/Page2')
// const Page3 = () => import('@/views/representativePortrait/Page3')
// const Page4 = () => import('@/views/representativePortrait/Page4')
// const Page5 = () => import('@/views/representativePortrait/Page5')
// const Page6 = () => import('@/views/representativePortrait/Page6')
// const Page7 = () => import('@/views/representativePortrait/Page7')
// const Page8 = () => import('@/views/representativePortrait/Page8')
// const Page9 = () => import('@/views/representativePortrait/Page9')
const representativePortraitHome = () => import('@/views/representativePortrait/representativePortraitHome')

const representativePortrait = [{
  path: '/representativePortraitList',
  name: 'representativePortraitList',
  component: representativePortraitList,
  meta: {
    title: '代表画像',
    keepAlive: true
  }
}, {
  path: '/timeAxis',
  name: 'timeAxis',
  component: timeAxis,
  meta: {
    title: '时光轴',
    keepAlive: true
  }
}, {
  path: '/annualReport',
  name: 'annualReport',
  component: annualReport,
  meta: {
    title: '年度报告',
    keepAlive: true
  }
}, {
  path: '/twoDataPage',
  name: 'twoDataPage',
  component: twoDataPage,
  meta: {
    title: '2张页面',
    keepAlive: true
  }
}, {
  path: '/oneDataPage',
  name: 'oneDataPage',
  component: oneDataPage,
  meta: {
    title: '1张页面',
    keepAlive: true
  }
}, {
  path: '/Page0',
  name: 'Page0',
  component: Page0,
  meta: {
    title: '年度报告',
    keepAlive: true
  }
}, {
  path: '/representativePortraitHome',
  name: 'representativePortraitHome',
  component: representativePortraitHome,
  meta: {
    title: '入口页面',
    keepAlive: true
  }
}]
export default representativePortrait

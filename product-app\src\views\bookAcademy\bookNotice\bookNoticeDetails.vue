<template>
  <div class="newsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="'font-size:20px;'"
           v-html="title"></div>
      <div class="n_details_more_box flex_box">
        <!--来源-->
        <div class="n_details_name flex_placeholder"
             :style="'font-size:12px;'">{{org?('来源：'+org):''}}</div>
        <!--资讯类型 可点击更多-->
        <div class=" flex_box flex_align_center flex_justify-content_end">
          <div class="n_details_name"
               :style="'font-size:12px;'+'margin-right: 6px;'">{{dataTime.substring(0,dataTime.indexOf(' '))}}</div>
        </div>
      </div>
    </div>
    <div class="n_details_content"
         :style="'font-size:15px;'"
         v-html="content"></div>
    <div v-if="books.length != 0"
         class="flex_box flex_align_center">
      <div :style="'width: 5px;height: 17px;margin-left:10px;background: '+appTheme"></div>
      <div :style="'font-size:14px;'+'padding:0 10px;'">与它相关的书籍</div>
    </div>
    <div v-if="books.length != 0"
         class="itemSex_box">
      <div v-for="(nItem,nIndex) in books"
           :key="nIndex"
           class="itemSex_item"
           @click="openBookDetails(nItem)">
        <img v-if="nItem.url"
             :style="'width:106px;height:156px;object-fit:contain;margin:auto;'"
             :src="nItem.url" />
        <div v-if="nItem.name"
             class="itemSex_name text_one"
             :style="'font-size:12px;'"
             v-html="nItem.name"></div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { NavBar, Sticky } from 'vant'
import moment from 'moment'
export default {
  name: 'noticeDetails',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      id: route.query.id,
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      appReadStatus: [],
      title: '', // 标题
      dataTime: '', // 时间
      org: '', // 发布部门
      type: '', // 类型
      content: '', // 正文内容
      books: []
    })
    onMounted(() => {
      onRefresh()
    })
    const onRefresh = () => {
      getData()
    }

    const getData = async () => {
      var datas = {
        id: data.id

      }
      const { data: info } = await $api.bookAcademy.getNoticeDetails(datas)
      data.title = info.title || ''// 标题
      data.org = info.officeName || ''// 部门
      data.dataTime = (info.publishDate) ? moment(info.publishDate).format('YYYY-MM-DD HH:mm') : '' // 时间
      data.content = info.content || ''// 内容
      data.books = []
      var newData = []
      info.books.forEach((_eItem, _eIndex, _eArr) => {
        var item = {}
        item.id = _eItem.id || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.url = _eItem.coverImgUrl || ''// 书图
        newData.push(item)
      })
      data.books = data.books.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
    }
    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, openBookDetails }
  }
}
</script>
<style lang="less">
.newsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }

  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
  }
  .n_details_name {
    color: #666;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .n_details_nextImg {
  }

  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
    img {
      margin: 15px 0;
      width: 100% !important;
    }
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
  .list_item {
    margin-top: 15px;
  }
  .add_warp {
    padding: 10px 10px;
    background: #fff;
  }

  .itemSex_item {
    width: 120px;
    padding: 10px 0;
  }
  .itemSex_name {
    color: #5e646d;
    text-align: center;
    margin-top: 5px;
  }

  .itemSex_box {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    padding: 0 12px;
  }
  .itemSex_item {
    display: inline-block;
    border-radius: 1px;
    position: relative;
    -webkit-overflow-scrolling: touch;
  }
}
</style>

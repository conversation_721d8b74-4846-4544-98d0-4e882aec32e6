<template>
  <div class="assign">
    <van-swipe-cell>
      <van-radio-group v-model="checked">
        <van-cell :title="item.text"
                  border
                  v-for="item in areaTree"
                  :key="item.id">
          <!-- 使用 right-icon 插槽来自定义右侧图标 -->
          <template #right-icon>
            <van-radio :name="item.id"></van-radio>
          </template>
        </van-cell>
      </van-radio-group>
    </van-swipe-cell>
    <div class="footer">
      <div class="RandomClappingUpload-submit"
           @click="Submit">提交</div>
      <div class="RandomClappingUpload-submits"
           @click="router.back()">取消</div>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ImagePreview, Uploader, Image as VanImage, Toast, TreeSelect, SwipeCell, RadioGroup, Radio, Dialog } from 'vant'
export default ({
  name: 'assign',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    [TreeSelect.name]: TreeSelect,
    [Uploader.name]: Uploader,
    [RadioGroup.name]: RadioGroup,
    [Radio.name]: Radio,
    [SwipeCell.name]: SwipeCell
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      module: route.query.module,
      id: route.query.id,
      activeId: '',
      activeIndex: '0',
      title: '',
      content: '',
      address: '',
      checked: '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      areaIds: '370200',
      username: '',
      mobile: '',
      longitude: '',
      dimensionality: '',
      township: '',
      Uploadmax: 3,
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      mapVisible: false,
      selectTitleList: [],
      selectHome: {},
      messageDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      standbyTwo: '',
      areaTree: [
        // { text: '青岛市', id: '370200', children: [] },
      ],
      areaTrees: [],
      navindex: '0',
      navItem: {},
      cityNames: '',
      districtNames: ''
    })
    onMounted(() => {
      console.log(data.user, 'user')
      data.username = data.user.userName
      data.mobile = data.user.mobile
      getareaTree()
    })
    const getareaTree = async () => {
      const { data: list } = await $api.RandomClapping.areaTree()
      data.areaTrees = list
      const { data: { cityNames, districtNames } } = await $api.general.nologin({ codes: 'cityNames,districtNames' })
      data.cityNames = cityNames
      data.districtNames = districtNames
      data.areaTree = [
        { text: '市南区' + districtNames, id: '370202', children: [] },
        { text: '市北区' + districtNames, id: '370203', children: [] },
        { text: '李沧区' + districtNames, id: '370213', children: [] },
        { text: '崂山区' + districtNames, id: '370212', children: [] },
        { text: '黄岛区' + districtNames, id: '370211', children: [] },
        { text: '城阳区' + districtNames, id: '370214', children: [] },
        { text: '即墨区' + districtNames, id: '370215', children: [] },
        { text: '胶州市' + districtNames, id: '370281', children: [] },
        { text: '平度市' + districtNames, id: '370283', children: [] },
        { text: '莱西市' + districtNames, id: '370285', children: [] }
      ]
      getInfo()
    }
    const getInfo = async () => {
      selectTitle()
      // getareaTree()
      const { data: list } = await $api.RandomClapping.representativemessageInfo(data.id)
      data.selectHome.name = list.standbyTwo
      data.selectHome.id = list.standbyTwo
    }
    const selectTitle = async (e) => {
      const { data: list } = await $api.RandomClapping.representativehomeSelectTitle({ areaId: data.areaTree[data.navindex].id })
      data.selectTitleList = list.map(item => {
        return {
          text: item.title,
          id: item.id,
          longitude: item.longitude,
          latitude: item.dimension,
          standbyFour: item.standbyFour
        }
      })
      data.areaTree[data.navindex].children = data.selectTitleList
    }
    // const getareaTree = async () => {
    //   const { data: list } = await $api.RandomClapping.areaTree()
    //   data.areaTree = list
    // }
    const imgUploader = async (file) => {
      console.log(file, 'file')
      console.log(data.images, 'data.images')
      const item = { url: file.content, uploadUrl: '', name: '', uploadId: '', status: 'uploading', module: 'lzbl' }
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'lzbl')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.status = 'done'
        item.url = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.status = 'failed'
        item.error = ret.errmsg || ''
      }
      data.attachmentIds.push(item)
    }

    // 地图选点
    const mapConfirm = ({ positionObj }) => {
      data.address = positionObj.address
      data.longitude = positionObj.lon
      data.dimensionality = positionObj.lat
      data.township = findIdByName(data.areaTree, positionObj.township)
      data.adcode = positionObj.adcode
      console.log('地图选点', data.township)
      console.log('地图选点', data.adcode)
      data.mapVisible = false
      data.selectHome = data.selectTitleList.find(item => {
        return item.standbyFour == data.township // eslint-disable-line
      })
      console.log(data.selectHome)
      console.log(data.selectTitleList)
    }
    // 递归函数
    function findIdByName (data, name) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].name === name) {
          return data[i].id
        } else if (data[i].children.length > 0) {
          var foundId = findIdByName(data[i].children, name)
          if (foundId !== null) {
            return foundId
          }
        }
      }
      return null // 如果未找到匹配项，则返回 null
    }
    // 提交
    const Submit = async () => {
      console.log(data.checked)
      var name = data.areaTree.find(e => e.id == data.checked).text// eslint-disable-line
      Dialog.confirm({
        message: '是否确认交办至' + name,
        confirmButtonColor: '#389eff'
      })
        .then(async () => {
          const param = {
            id: data.id,
            escalationId: data.checked,// eslint-disable-line
            standbyName: name,
            state: 8,
            escalationType: '10002',// eslint-disable-line
            areaId: data.checked// eslint-disable-line
          }
          const res = await $api.RandomClapping.representativemessageEscalation(param)
          if (res.errcode === 200) {
            Toast('交办成功')
            router.back()
          }
        })
        .catch(() => {
          Toast('取消交办')
        })
    }
    const navClick = (e) => {
      data.navindex = e
      selectTitle()
    }
    const itemClick = (e) => {
      data.navItem = e
    }
    return { ...toRefs(data), dayjs, route, router, $api, navClick, itemClick, imgUploader, ImagePreview, $general, mapConfirm, Submit }
  }
})
</script>
<style lang='less'>
:root {
  --van-sidebar-selected-border-color: #006ef1;
  --van-tree-select-item-active-color: #006ef1;
}

.assign {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;

  .van-tree-select__item {
    line-height: 4.3 !important;
  }

  // padding: 15px;
  .footer {
    width: 90%;
    height: 56px;
    position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .RandomClappingUpload-submit {
    width: 45%;
    height: 40px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }

  .RandomClappingUpload-submits {
    width: 45%;
    height: 40px;
    border: 1px solid #3088fe;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #3088fe;
    border-radius: 5px;
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../../assets/font/PingFang Medium_downcc.otf");
  }

  .RandomClappingUpload-box {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  // .RandomClappingUpload-submit {
  //   position: fixed;
  //   left: 50%;
  //   bottom: 2%;
  //   transform: translateX(-50%);
  //   width: 90%;
  //   height: 56px;
  //   background: #3088fe;
  //   box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
  //   font-size: 16px;
  //   font-family: PingFang SC;
  //   font-weight: 400;
  //   line-height: 56px;
  //   text-align: center;
  //   color: #ffffff;
  //   border-radius: 5px;
  // }
}
</style>

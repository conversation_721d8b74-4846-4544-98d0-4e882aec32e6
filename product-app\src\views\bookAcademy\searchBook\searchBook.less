html,
body {
  background: #FFF;
}

.libraryDetails {
  width: 100%;

  .item_overdue {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 52px;
  }

  .store_hint_img {
    position: relative;
    width: 100%;
    height: 215px;
    object-fit: cover;
  }
  .box_yj {
    background: #FFF;
    border-radius: 10px;
  }

  .store_details_box {
    background: #FFF;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    position: absolute;
    width: 100%;
    min-height: 16px;
    margin-top: -16px;
  }

  .itemSex_box {
    margin: 0 0px;
  }

  .itemSex_item {
    width: 33.333%;
    padding: 10px 10px 15px 10px;
  }

  .itemSex_name {
    width: 100%;
    color: #5E646D;
    font-weight: 500;
    margin-top: 0.0px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .itemSex_author {
    width: 100%;
    color: #A5A5A5;
    font-weight: 400;
    margin-top: 3px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .van-tab {
    max-width: 360px;
  }
}
.searchBook {
  width: 100%;
  background: #FFF;

  .item_overdue {
    position: absolute;
    top: 0;
    right: 0;
    width: 52px;
    height: 52px;
  }

  .history_item {
    min-height: 40px;
  }

  .popular_box {
    margin-top: 30px;
    padding: 0 16px;
  }

  .popular_item {
    margin: 19px 14px 0 0;
    padding: 7px 15px 7px 7px;
    background: #F3f3f3;
    color: #5E646D;
    border-radius: 20px;
  }

  .search_box {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
  }

  .search_list_box {
    padding: 11px 15px;
  }

  .search_item {
    padding: 13px 10px;
    box-shadow: 0px 0px 26px -11px rgba(0, 0, 0, 0.4);
    border-radius: 16px;
  }

  .search_item+.search_item {
    margin-top: 20px;
  }
}
<template>
  <div class="peopleList">
    <van-nav-bar v-if="isShowHead" :title="title" fixed placeholder safe-area-inset-top left-text="" left-arrow
      @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <div v-if="showSearch" id="search" class="search_box" :style="$general.loadConfiguration()">
        <div class="search_warp flex_box">
          <div @click="search();" class="search_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="$general.data.appFontSize + 'px'" :color="'#666'" :name="'search'"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input" action="javascript:return true;">
            <input id="searchInput" class="flex_placeholder" :style="$general.loadConfiguration(-1)"
              :placeholder="seachPlaceholder" maxlength="100" type="text" ref="btnSearch" @keyup.enter="search()"
              v-model="seachText" />
            <div v-if="seachText" @click="seachText = ''; search();"
              class="search_btn flex_box flex_align_center flex_justify_content">
              <van-icon :size="$general.data.appFontSize + 'px'" :color="'#999'" :name="'clear'"></van-icon>
            </div>
          </form>
          <van-dropdown-menu class="search-dropdown-menu flex_box flex_align_center" :active-color="appTheme"
            :style="$general.loadConfiguration(-3)">
            <van-dropdown-item title="筛选" ref="filter">
              <template v-for="(item, index) in filters" :key="index">
                <van-cell v-if="item.show" :title="item.title" :style="$general.loadConfiguration()">
                  <template v-slot:right-icon>
                    <!--选择-->
                    <van-dropdown-menu v-if="item.type == 'select'" :active-color="appTheme">
                      <van-dropdown-item v-model="item.value" get-container="#search"
                        :options="item.data"></van-dropdown-item>
                    </van-dropdown-menu>
                    <!--开关-->
                    <van-switch v-else-if="item.type == 'switch'" :active-color="appTheme" v-model="item.value"
                      :size="$general.data.appFontSize + 8 + 'px'"></van-switch>
                    <!--其它只展示文字-->
                    <div v-else :style="$general.loadConfiguration()">{{ item.value }}</div>
                  </template>
                </van-cell>
              </template>
              <div class="flex_box">
                <van-button block @click="onReset">重置</van-button>
                <van-button block :color="appTheme" @click="onConfirm">确认</van-button>
              </div>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
      </div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
          <!--数据列表-->
          <ul class="vue_newslist_box">
            <van-swipe-cell v-for="(item, index) in dataList" :key="index" class="van-hairline--bottom">
              <div class="top_box">
                <div class="flex_box flex_align_center" :class="item.sex == 1 ? 'man' : 'woman'"
                  :style="$general.loadConfiguration(-3)">
                  <img :style="$general.loadConfigurationSize(-1, 'h')"
                    :src="item.sex == 1 ? require('../../assets/img/man.png') : require('../../assets/img/woman.png')" />
                  <div v-if="item.age" style="min-width: 2.3em;margin-left: 5px;" class="inherit flex_placeholder">
                    {{ item.age + (item.age ? '岁' : '') }}</div>
                </div>
              </div>
              <van-cell clickable class="vue_newslist_item " @click="openDetails(item)">
                <div class="flex_box">
                  <img class="vue_newslist_img" v-if="item.url" :src="item.url" />
                  <div class="flex_placeholder vue_newslist_warp">
                    <div class="vue_newslist_title text_two" :style="$general.loadConfiguration(0)">
                      {{ item.name }}
                    </div>
                    <div class="vue_newslist_title text_two team" :style="$general.loadConfiguration(-2)">
                      {{ item.team }}
                    </div>
                    <div class="vue_newslist_title text_two des" :style="$general.loadConfiguration(-2)">
                      {{ item.des }}
                    </div>
                  </div>
                </div>
              </van-cell>
            </van-swipe-cell>
          </ul>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, SwipeCell } from 'vant'
export default {
  name: 'peopleList',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [SwipeCell.name]: SwipeCell,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filter: null,
      filters: [
        { show: $ifzx, type: 'select', key: 'committee', title: '请选择所属专委会', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        { show: $ifzx, type: 'select', key: 'deleId', title: '请选择界别', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        { show: !$ifzx, type: 'select', key: 'representerElement', title: '请选择所属结构', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        { show: !$ifzx, type: 'select', key: 'representerTeam', title: '请选择代表团', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        { show: true, type: 'select', key: 'party', title: '请选择党派', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        { show: true, type: 'select', key: 'nation', title: '请选择民族', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        {
          show: true,
          type: 'select',
          key: 'birthday',
          title: '请选择年龄',
          value: '',
          defaultValue: '',
          data: [{ text: '所有', value: '' }, { text: '0岁-9岁', value: '0' }, { text: '10岁-19岁', value: '10' }, { text: '20岁-29岁', value: '20' }, { text: '30岁-39岁', value: '30' }, { text: '40岁-49岁', value: '40' }, { text: '50岁-59岁', value: '50' }, { text: '60岁-69岁', value: '60' }, { text: '70岁-79岁', value: '70' }, { text: '80岁以上', value: '80' }, { text: '其他', value: '999' }]
        },
        { show: true, type: 'select', key: 'sex', title: '请选择性别', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },
        { show: false, type: 'select', key: 'hasVacant', title: '请选择是否出缺', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] }
      ], // 筛选集合
      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },
      showSearch: true

    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      var key = route.query.key
      var type = route.query.type
      console.log(key + '===' + type)
      if (key) {
        if (type === 'representerElement') {
          $general.getItemForKey('representerElement', data.filters, 'key').value = key
        }
        if (type === 'representerTeam') {
          $general.getItemForKey('representerTeam', data.filters, 'key').value = key
        }
        if (type === 'deleId') {
          $general.getItemForKey('deleId', data.filters, 'key').value = key
        }
        if (type === 'party') {
          $general.getItemForKey('party', data.filters, 'key').value = key
        }
        if (type === 'nation') {
          $general.getItemForKey('nation', data.filters, 'key').value = key
        }
        if (type === 'sex') {
          $general.getItemForKey('sex', data.filters, 'key').value = key
        }
        if (type === 'birthday') {
          $general.getItemForKey('birthday', data.filters, 'key').value = key
        }
        if (type === 'hasVacant') {
          $general.getItemForKey('hasVacant', data.filters, 'key').value = key
        }
      }
      getFiter()
      setTimeout(() => {
        onRefresh()
      }, 100)
    })
    const getFiter = async () => {
      var param = {
        types: 'representer_element,representer_team,vacant_type,party_type,nation_type,sex,yes_no'
      }
      if (data.SYS_IF_ZX) {
        param.types = 'committee_type,vacant_type,dele_type,party_type,nation_type,sex,yes_no'
      }
      const res = await $api.general.pubkvs(param)
      if (res) {
        var datas = res.data
        var committee = datas.committee_type || []
        for (var i in committee) {
          var item = {}
          item.text = committee[i].value
          item.value = committee[i].id
          $general.getItemForKey('committee', data.filters, 'key').data.push(item)
        }
        var deleId = datas.dele_type || []
        deleId.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('deleId', data.filters, 'key').data.push(item)
        })
        var representerElement = datas.representer_element || []
        representerElement.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('representerElement', data.filters, 'key').data.push(item)
        })
        var representerTeam = datas.representer_team || []
        representerTeam.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('representerTeam', data.filters, 'key').data.push(item)
        })
        var hasVacant = data.vacant_type || []
        hasVacant.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('party', data.filters, 'key').data.push(item)
        })
        var party = datas.party_type || []
        party.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('party', data.filters, 'key').data.push(item)
        })
        var nation = datas.nation_type || []
        nation.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('nation', data.filters, 'key').data.push(item)
        })
        var sex = datas.sex || []
        sex.forEach(element => {
          var item = {}
          item.text = element.value
          item.value = element.id
          $general.getItemForKey('sex', data.filters, 'key').data.push(item)
        })
      }
    }
    const getInfo = async () => {
      const param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText,
        memberType: (data.SYS_IF_ZX ? 1 : 3),
        startBirthday: '',
        endBirthday: '',
        isUsing: '1'
      }
      for (var i = 0; i < data.filters.length; i++) {
        const filtersItem = data.filters[i]
        if (filtersItem.key === 'birthday') {
          if (filtersItem.value) {
            var birthday = $general.getItemForKey('birthday', data.filters, 'key').data
            var nItem = $general.getItemForKey(filtersItem.value, birthday, 'value')
            console.log(nItem.value)
            console.log(nItem.text)
            if (nItem.text === '其他') {
              param.startBirthday = 999
              param.endBirthday = 1000
            } else if (nItem.text.indexOf('-') >= 0) {
              param.startBirthday = nItem.text.split('-')[0].split('岁')[0]
              param.endBirthday = nItem.text.split('-')[1].split('岁')[0]
            } else if (nItem.text.indexOf('以上') >= 0) {
              param.startBirthday = nItem.text.split('岁以上')[0]
              param.endBirthday = 1000
            } else {
              param.startBirthday = nItem.text.split('岁')[0]
              param.endBirthday = 1000
            }
          }
        } else {
          param[filtersItem.key] = filtersItem.value
        }
      }
      const res = await $api.peopleInformation.getMemberList(param)
      var { data: list, total } = res
      const newData = []
      list.forEach(item => {
        const _eItem = item
        item.name = _eItem.userName || ''// 姓名
        item.url = _eItem.headImg || ''// 头像
        item.team = _eItem.representerTeam || _eItem.deleId || ''// 代表团 界别
        item.des = _eItem.position || ''// 职务
        item.sex = _eItem.sex === '女' ? 0 : 1// 性别
        item.age = _eItem.age || ''// 年龄
        item.relateType = 'representer'// 类型
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    watch(() => data.dataList, (newName, oldName) => {

    })

    // 筛选重置事件
    const onReset = () => {
      for (var i = 0; i < data.filters.length; i++) {
        data.filters[i].value = data.filters[i].defaultValue
      }
    }
    // 筛选确定事件
    const onConfirm = () => {
      data.filter.toggle()
      onRefresh()
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getInfo()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getInfo()
    }
    const openDetails = (_item) => {
      router.push({ name: 'personData', query: { id: _item.id } })
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, confirm, openDetails, onReset, onConfirm }
  }
}
</script>
<style lang="less" scoped>
.peopleList {
  background: #f8f8f8;

  .a_box_warp {
    background: #ffffff;
    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
    overflow: hidden;
  }

  .search_box {
    background: #ffffff;
  }

  .a_search_box {
    padding: 14px 15px 0 15px;
  }

  .a_search_select_box {
    padding: 2px 0 2px 10px;
  }

  .a_search_select_text {
    color: #222222;
    font-weight: 500;
    line-height: 1.46;
  }

  .a_search_box form {
    padding: 0 13px;
  }

  .a_search_btn_box {
    padding: 9px 16px;
  }

  .a_search_select_text_icon {
    position: relative;
    margin-left: 6px;
    width: 13px;
  }

  .a_search_select_text_icon::after {
    position: absolute;
    top: 50%;
    margin-top: -5px;
    border: 3px solid;
    border-color: transparent transparent #222 #222;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    opacity: 0.8;
    content: "";
  }

  .a_select_btn_box {
    background: #666666;
    margin-left: 5px;
    font-weight: 500;
    line-height: 1.5;
    color: #ffffff;
    padding: 7px 11px;
    border-radius: 2px;
  }

  #app .vue_newslist_item {
    padding: 15px 15px;
  }

  #app .vue_newslist_box .van-cell {
    background-color: rgba(0, 0, 0, 0) !important;
  }

  .vue_newslist_img {
    width: 55px;
    min-height: 0;
    height: 74px;
    border-radius: 2px;
    margin-right: 10px;
    background-position: center;
  }

  .van-hairline--bottom {
    width: calc(100% - 32px);
    left: 0;
    right: 0;
    margin: auto;
    margin-top: 10px;
    background: #ffffff;
    box-shadow: 0px 3px 20px rgba(34, 85, 172, 0.12);
    opacity: 1;
    border-radius: 4px;
  }

  .vue_newslist_title {
    font-weight: 600;
    color: #222222;
    margin-bottom: 0;
  }

  .vue_newslist_warp {
    padding-bottom: 0;
    position: relative;
  }

  .team {
    font-weight: 500;
    color: #222222;
    margin-top: 8px;
  }

  .des {
    font-weight: 500;
    color: #222222;
    margin-top: 8px;
  }

  .top_box {
    position: absolute;
    right: 15px;
    top: 15px;
    color: #6499f0;
    opacity: 1;
    z-index: 99;
  }

  .woman {
    color: #f06981;
  }

  .van-swipe-cell__wrapper {
    position: relative;
  }
}
</style>

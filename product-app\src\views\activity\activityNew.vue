<template>
  <div class="activityNew">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <div style="width: 100%;display: flex;justify-content: space-between; align-items:center;border-bottom: 1px solid #eee;">
          <van-cell title="活动大类"
                    required
                    class="titleStyle" />
          <van-dropdown-menu active-color="#1989fa"
                            class="marginTop"
                            :rules="[{ required: true, message: '请选择活动大类' }]">
            <van-dropdown-item v-model="form.meetType"
                              @close="close"
                              :options="meetType" />
          </van-dropdown-menu>
        </div>

       <div style="width: 100%;display: flex;justify-content: space-between; align-items:center;border-bottom: 1px solid #eee;">
          <van-cell title="活动小类"
                    class="titleStyle" />
          <van-dropdown-menu active-color="#1989fa" class="marginTop" :rules="[{ required: true, message: '请选择活动小类' }]">
            <van-dropdown-item v-model="form.typeSmall"
                              :options="typeSmall" />
          </van-dropdown-menu>
        </div>
         <van-field v-model="form.meetName"
                   required
                   name="活动主题"
                   maxlength="50"
                   show-word-limit
                   label-width="4.8em"
                   label="活动主题"
                   placeholder="请输入活动主题"
                   :rules="rules.meetName" />
         <van-field class="newContent"
                   v-model="form.content"
                   required
                   name="content"
                   label="内容"
                   rows="6"
                   maxlength="2000"
                   show-word-limit
                   type="textarea"
                   placeholder="请输入内容"
                   :rules="rules.content" />
        <van-field v-model="form.organizer"
                   required
                   name="组织部门"
                   show-word-limit
                   label-width="4.8em"
                   label="组织部门"
                   placeholder="请输入组织部门"
                   :rules="rules.organizer" />
        <van-field v-model="form.address"
                   required
                   name="活动地点"
                   show-word-limit
                   label-width="4.8em"
                   label="活动地点"
                   placeholder="请输入活动地点"
                   :rules="rules.address" />
        <van-field size="large"
          class="time-box"
          input-align="right"
          label="报名开始日期"
          readonly
          v-model="form.signBeginTime"
          placeholder="请选择报名开始日期"
          :rules="rules.signBeginTime"
          required
          @click="signBeginTimeShow = true" right-icon="clock-o" />
        <van-field size="large"
          class="time-box"
          input-align="right"
          label="报名结束日期"
          readonly
          v-model="form.signEndTime"
          placeholder="请选择报名结束日期"
          :rules="rules.signEndTime"
          required
          @click="signEndTimeShow = true" right-icon="clock-o" />
        <van-field size="large"
          class="time-box"
          input-align="right"
          label="签到开始日期"
          readonly
          v-model="form.meetSignBeginTime"
          placeholder="请选择签到开始日期"
          :rules="rules.meetSignBeginTime"
          required
          @click="meetSignBeginTimeShow = true" right-icon="clock-o" />
        <van-field size="large"
          class="time-box"
          input-align="right"
          label="签到结束日期"
          readonly
          v-model="form.meetSignEndTime"
          placeholder="请选择签到结束日期"
          :rules="rules.meetSignEndTime"
          required
          @click="meetSignEndTimeShow = true" right-icon="clock-o" />
        <van-field size="large"
          class="time-box"
          input-align="right"
          label="活动开始日期"
          readonly
          v-model="form.meetStartTime"
          placeholder="请选择活动开始日期"
          :rules="rules.meetStartTime"
          required
          @click="meetStartTimeShow = true" right-icon="clock-o" />
        <van-field size="large"
          class="time-box"
          input-align="right"
          label="活动结束日期"
          readonly
          v-model="form.meetEndTime"
          placeholder="请选择活动结束日期"
          :rules="rules.meetEndTime"
          required
          @click="meetEndTimeShow = true" right-icon="clock-o" />
        <van-cell-group class="marginTop"
                      @click="invitersShow = !invitersShow">
        <van-field
                  :model-value="invitersData.length > 0 ? '邀请参与活动人员 '+  '(' + invitersData.length + ')' : '邀请参与活动人员'"
                   readonly
                   right-icon="add" />
        <van-field is-link
                   v-if="invitersData.length>0"
                   label-width="4.8em"
                   name="user">
          <template #input>
            <div class="userBox">
              <div class="userItem"
                   v-for="item in invitersData"
                   :key="item.userId">
                <div class="userImg"><img :src="item.headImg"
                       alt=""></div>
                <div class="userName">{{item.name}}</div>
                <!-- TODO: name \ userName  -->
              </div>
            </div>
          </template>
        </van-field>
      </van-cell-group>
       <van-cell-group class="marginTop"
                      @click="signUpsShow = !signUpsShow">
        <van-field
                  :model-value="signUpsData.length > 0 ? '已报名人员 '+  '(' + signUpsData.length + ')' : '已报名人员'"
                   readonly
                   right-icon="add" />
        <van-field is-link
                   v-if="signUpsData.length>0"
                   label-width="4.8em"
                   name="user">
          <template #input>
            <div class="userBox">
              <div class="userItem"
                   v-for="item in signUpsData"
                   :key="item.userId">
                <div class="userImg"><img :src="item.headImg"
                       alt=""></div>
                <div class="userName">{{item.name}}</div>
                <!-- TODO: name \ userName  -->
              </div>
            </div>
          </template>
        </van-field>
      </van-cell-group>
       <van-cell-group class="marginTop"
                      @click="signInsShow = !signInsShow">
        <van-field
                  :model-value="signInsData.length > 0 ? '已签到人员 '+  '(' + signInsData.length + ')' : '已签到人员'"
                   readonly
                   right-icon="add" />
        <van-field is-link
                   v-if="signInsData.length>0"
                   label-width="4.8em"
                   name="user">
          <template #input>
            <div class="userBox">
              <div class="userItem"
                   v-for="item in signInsData"
                   :key="item.userId">
                <div class="userImg"><img :src="item.headImg"
                       alt=""></div>
                <div class="userName">{{item.name}}</div>
                <!-- TODO: name \ userName  -->
              </div>
            </div>
          </template>
        </van-field>
      </van-cell-group>
       <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   class="activity-box"
                   label="是否在手机APP上显示该活动">
          <template #input>
            <van-switch v-model="form.isAppShow"
                        size="20" />
          </template>
        </van-field>
      <van-field name="switch"
                   input-align="right"
                   label-width="4.8em"
                   class="activity-box"
                   label="是否让所有人可以看到该活动">
          <template #input>
            <van-switch v-model="form.isPublish"
                        size="20" />
          </template>
        </van-field>
        <van-field name="switch"
                   input-align="right"
                   class="activity-box"
                   label-width="4.8em"
                   label="是否需要用短信通知活动邀请人">
          <template #input>
            <van-switch v-model="form.isNotice"
                        size="20" />
          </template>
        </van-field>
        <van-field name="switch"
                   input-align="right"
                   class="activity-box"
                   label-width="4.8em"
                   label="是否同步生成活动通知">
          <template #input>
            <van-switch v-model="form.isRelease"
                        size="20" />
          </template>
        </van-field>
        <van-field name="checkboxGroup"
                   class="activity-box"
                   label-width="4.8em"
                   label="请您为本次活动选几个标签吧">
          <template #input>
            <van-checkbox-group v-model="form.label" direction="horizontal">
                 <van-checkbox :name="item.id"
                              v-for="item in activity_label"
                              :key="item.id"
                              shape="square">{{item.value}}</van-checkbox>
              </van-checkbox-group>
            </template>
        </van-field>
     </van-cell-group>
      <div class="newButton">
        <!-- <van-button type="primary"
                    @click="oneReset"
                    native-type="submit">重置以上内容</van-button> -->
        <van-button type="primary"
                    native-type="submit">提交</van-button>
      </div>
    </van-form>

    <van-popup v-model:show="signBeginTimeShow" position="bottom">
        <van-datetime-picker v-model="signBeginTime" title="选择时间" :formatter="formatter" type='datetime'  @confirm="signBeginTimeConfirm(signBeginTime)" @cancel="signBeginTimeShow = false">
        </van-datetime-picker>
    </van-popup>

    <van-popup v-model:show="signEndTimeShow" position="bottom">
        <van-datetime-picker v-model="signEndTime" title="选择时间" :formatter="formatter" type='datetime'  @confirm="signEndTimeConfirm(signEndTime)" @cancel="signEndTimeShow = false">
        </van-datetime-picker>
    </van-popup>

    <van-popup v-model:show="meetSignBeginTimeShow" position="bottom">
        <van-datetime-picker v-model="meetSignBeginTime" title="选择时间" :formatter="formatter" type='datetime'  @confirm="meetSignBeginTimeConfirm(meetSignBeginTime)" @cancel="meetSignBeginTimeShow = false">
        </van-datetime-picker>
    </van-popup>

    <van-popup v-model:show="meetSignEndTimeShow" position="bottom">
        <van-datetime-picker v-model="meetSignEndTime" title="选择时间" :formatter="formatter" type='datetime'  @confirm="meetSignEndTimeConfirm(meetSignEndTime)" @cancel="meetSignEndTimeShow = false">
        </van-datetime-picker>
    </van-popup>

    <van-popup v-model:show="meetStartTimeShow" position="bottom">
        <van-datetime-picker v-model="meetStartTime" title="选择时间" :formatter="formatter" type='datetime'  @confirm="meetStartTimeConfirm(meetStartTime)" @cancel="meetStartTimeShow = false">
        </van-datetime-picker>
    </van-popup>

    <van-popup v-model:show="meetEndTimeShow" position="bottom">
        <van-datetime-picker v-model="meetEndTime" title="选择时间" :formatter="formatter" type='datetime'  @confirm="meetEndTimeConfirm(meetEndTime)" @cancel="meetEndTimeShow = false">
        </van-datetime-picker>
    </van-popup>

    <van-popup v-model:show="invitersShow"
               position="bottom"
               :style="{height:'80%'}">
      <selectActivityUser ref="user"
                          pointCode="point_15"
                          :data="invitersUser"></selectActivityUser>
    </van-popup>
    <van-popup v-model:show="signUpsShow"
               position="bottom"
               :style="{height:'80%'}">
      <selectActivityUser ref="user"
                          pointCode="point_15"
                          :data="signUpsUser"></selectActivityUser>
    </van-popup>
     <van-popup v-model:show="signInsShow"
               position="bottom"
               :style="{height:'80%'}">
      <selectActivityUser ref="user"
                          pointCode="point_15"
                          :data="signInsUser"></selectActivityUser>
    </van-popup>
  </div>
</template>
<script>
import { onMounted, reactive, ref, toRefs, watch, inject } from 'vue'
import { DatetimePicker, DropdownMenu, DropdownItem, Toast } from 'vant'
import { useRouter } from 'vue-router'
import selectActivityUser from './components/selectActivityUser'
export default {
  name: 'activityNew',
  components: {
    selectActivityUser,
    [DatetimePicker.name]: DatetimePicker,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem
  },
  setup () {
    const dayjs = require('dayjs')
    const router = useRouter()
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      form: {
        meetType: '',
        typeSmall: '',
        meetName: '',
        content: '',
        organizer: '',
        address: '',
        signBeginTime: '',
        signEndTime: '',
        meetSignBeginTime: '',
        meetSignEndTime: '',
        meetStartTime: '',
        meetEndTime: '',
        isAppShow: true,
        isPublish: true,
        isNotice: false,
        isRelease: false,
        label: []
      },
      rules: {
        meetName: [{ required: true, message: '请输入活动主题' }],
        content: [{ required: true, message: '请输入内容' }],
        organizer: [{ required: true, message: '请输入组织部门' }],
        address: [{ required: true, message: '请输入活动地点' }],
        signBeginTime: [{ required: true, message: '请选择报名开始日期' }],
        signEndTime: [{ required: true, message: '请选择报名结束日期' }],
        meetSignBeginTime: [{ required: true, message: '请选择签到开始日期' }],
        meetSignEndTime: [{ required: true, message: '请选择签到结束日期' }],
        meetStartTime: [{ required: true, message: '请选择报名开始时间' }],
        meetEndTime: [{ required: true, message: '请选择报名结束时间' }]
      },
      meetType: [],
      typeSmall: [],
      signBeginTimeShow: false,
      signEndTimeShow: false,
      meetSignBeginTimeShow: false,
      meetSignEndTimeShow: false,
      meetStartTimeShow: false,
      meetEndTimeShow: false,
      signBeginTime: '',
      signEndTime: '',
      meetSignBeginTime: '',
      meetSignEndTime: '',
      meetStartTime: '',
      meetEndTime: '',
      invitersShow: false,
      inviters: '',
      invitersData: [],
      invitersUser: [],
      signUpsShow: false,
      signUps: '',
      signUpsData: [],
      signUpsUser: [],
      signInsShow: false,
      signIns: '',
      signInsData: [],
      signInsUser: [],
      activity_label: []
    })
    const $api = inject('$api')
    const user = ref(null)
    watch(() => data.invitersShow, () => {
      if (!data.invitersShow) {
        data.invitersData = user.value.selectedUser
      }
    })

    watch(() => data.signUpsShow, () => {
      if (!data.signUpsShow) {
        data.signUpsData = user.value.selectedUser
      }
    })

    watch(() => data.signInsShow, () => {
      if (!data.signInsShow) {
        data.signInsData = user.value.selectedUser
      }
    })

    onMounted(() => {
      dictionaryPubkvs()
      getTextTree()
    })
    const onSubmit = async (values) => {
      if (data.form.meetType === '') {
        Toast({
          message: '请选择活动大类',
          position: 'bottom'
        })
      } else {
        var datas = data.form
        var labelData = ''
        if (datas.label) {
          labelData = datas.label.join(',') // 标签
        }
        var invitersData = []
        data.invitersData.forEach(item => {
          invitersData.push(item.userId)
        })
        var signUpsData = []
        data.signUpsData.forEach(item => {
          signUpsData.push(item.userId)
        })
        var signInsData = []
        data.signInsData.forEach(item => {
          signInsData.push(item.userId)
        })
        const res = await $api.activity.activitySaveActivity({
          empty: '1',
          meetType: datas.meetType,
          typeSmall: datas.typeSmall,
          meetName: datas.meetName,
          content: datas.content,
          organizer: datas.organizer,
          address: datas.address,
          signBeginTime: datas.signBeginTime,
          signEndTime: datas.signEndTime,
          meetSignBeginTime: datas.meetSignBeginTime,
          meetSignEndTime: datas.meetSignEndTime,
          meetStartTime: datas.meetStartTime,
          meetEndTime: datas.meetEndTime,
          signUpTime: [datas.signBeginTime, datas.signEndTime],
          signTime: [datas.meetSignBeginTime, datas.meetSignEndTime],
          activityTime: [datas.meetStartTime, datas.meetEndTime],
          label: labelData,
          inviters: invitersData.join(',').toString(),
          signUps: signUpsData.join(',').toString(),
          signIns: signInsData.join(',').toString(),
          isAppShow: datas.isAppShow ? '1' : '0',
          isPublish: datas.isPublish ? '1' : '0',
          isNotice: datas.isNotice ? '1' : '0',
          isRelease: datas.isRelease ? '1' : '0'
        })
        var { errcode, errmsg } = res
        if (errcode === 200) {
          Toast.success(errmsg)
          router.go(-1)
        }
      }
    }
    const oneReset = () => {
      data.form = ''
    }
    const signBeginTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.signBeginTime = new Date(_item.value.replace(/-/g, '/'))
      data.form.signBeginTime = _item.value
      data.signBeginTimeShow = false
    }
    const signEndTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.form.signEndTime = _item.value
      data.signEndTimeShow = false
    }
    const meetSignBeginTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.form.meetSignBeginTime = _item.value
      data.meetSignBeginTimeShow = false
    }
    const meetSignEndTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.form.meetSignEndTime = _item.value
      data.meetSignEndTimeShow = false
    }
    const meetStartTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.form.meetStartTime = _item.value
      data.meetStartTimeShow = false
    }
    const meetEndTimeConfirm = (_item) => {
      _item.value = dayjs(_item).format('YYYY-MM-DD HH:mm')
      data.form.meetEndTime = _item.value
      data.meetEndTimeShow = false
    }
    // 格式化时间
    const formatter = (type, value) => {
      if (type === 'year') {
        return value + '年'
      } else if (type === 'month') {
        return value + '月'
      } else if (type === 'day') {
        return value + '日'
      } else if (type === 'hour') {
        return value + '时'
      } else if (type === 'minute') {
        return value + '分'
      }
      return value
    }
    const dictionaryPubkvs = async () => {
      const res = await $api.activity.dictionaryPubkvs({
        types: 'activity_label'
      })
      var { data: list } = res
      data.activity_label = list.activity_label
    }
    const getTextTree = async () => {
      const res = await $api.activity.treelist({
        treeType: 3
      })
      var arr = []
      res.data.forEach(item => {
        arr.push({ text: item.name, value: item.id, children: item.children })
      })
      data.meetType = arr
    }
    // 关闭下拉菜单栏时触发(选择菜单栏里内容后触发)
    const close = () => {
      var arrSmall = []
      data.meetType.forEach(itemBig => {
        if (data.form.meetType === itemBig.value) {
          itemBig.children.forEach(itemSmall => {
            arrSmall.push({ text: itemSmall.name, value: itemSmall.id })
          })
        }
      })
      data.typeSmall = arrSmall
    }
    return { ...toRefs(data), user, onSubmit, oneReset, dictionaryPubkvs, getTextTree, close, signBeginTimeConfirm, signEndTimeConfirm, meetSignBeginTimeConfirm, meetSignEndTimeConfirm, meetStartTimeConfirm, meetEndTimeConfirm, formatter }
  }
}
</script>
<style lang="less" >
.activityNew {
  background: #fff;
    .van-form {
      width: 100%;
      .van-cell-group--inset{
        margin: 0;
      }
      .newContent {
        flex-wrap: wrap;
        .van-cell__title {
          width: 100%;
          margin-bottom: 6px;
        }
        .van-field__body {
          background-color: #e8e8e8;
          padding: 6px 12px;
        }
      }
     .van-field__control {
        font-size: 14px;
      }
      .time-box {
        .van-cell__title {
          width: 120px;
        }
      }
      .activity-box {
        .van-cell__title{
          width: 220px!important;
        }
      }
    }
    .titleStyle {
      background-color: #fff;
      height: 48px;
      // width: 100%;
      width: 50%;
    }
    .van-dropdown-menu__title {
      margin-right: 15px;
    }
    --van-dropdown-menu-box-shadow: 0;
    .marginTop {
      margin-top: 20px;
    }
    .newButton {
      display: flex;
      justify-content: space-around;
      padding: 36px 18px;
      padding-bottom: 88px;
      .van-button {
        width: 128px;
        height: 36px;
      }
    }
    .userBox {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      max-height: 110px;
      overflow-y: scroll;
      .userItem {
        display: flex;
        align-items: center;
        padding: 2px 0;
        padding-right: 6px;
        margin-right: 12px;
        margin-bottom: 6px;
        .userImg {
          width: 22px;
          height: 22px;
          border-radius: 50%;
          overflow: hidden;
          img {
            height: 100%;
            margin: auto;
            display: block;
          }
        }
        .userName {
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 17px;
          color: #666666;
          padding-left: 6px;
        }
      }
    }
}
</style>>

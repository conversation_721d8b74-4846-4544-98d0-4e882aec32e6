<template>
  <div class="fiveFile2">
    <!--展示小号附件-->
    <div v-if="listData.length != 0"
         class="general_attach_big">
      <div v-for="(item,index) in listData"
           :key="index"
           class="flex_box flex_align_center">
        <div class="general_attach_big_item van-hairline--bottom flex_box flex_align_center click"
             @click="openDetails(item)">
          <img class="general_attach_big_icon"
               :style="general.loadConfigurationSize([26,32])"
               :src="require('../../assets/img/fileicon/'+item.iconInfo.name)" />

          <div class="flex_placeholder">
            <div class="general_attach_big_name text_one2"
                 :style="general.loadConfiguration(-1)">{{item.name}}
            </div>
            <div v-if="pageParam.isDetails"
                 class="flex_box flex_align_center"
                 style="margin-top:0.06rem;">
              <div v-if="item.size"
                   class="general_attach_big_size flex_placeholder"
                   :style="general.loadConfiguration(-4)">{{getFileSize(item.size)}}</div>
              <div v-if="item.state != 2"
                   class="general_attach_state flex_box flex_align_center flex_justify_content"
                   :style="general.loadConfigurationSize([7,7])">
                <van-icon v-if="item.state == 0"
                          class-prefix="iconfont"
                          color="#ccc"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          name="xiazai"></van-icon>
                <van-circle v-else-if="item.state == 1"
                            :size="((appFontSize+3)*0.01)+'rem'"
                            v-model="item.schedule"
                            :rate="item.schedule"
                            stroke-width="150"></van-circle>
                <van-icon @click.stop="T.toast('缓存异常，请点击标题重试');"
                          v-else-if="item.state == 3"
                          color="#ccc"
                          :size="((appFontSize+3)*0.01)+'rem'"
                          name="warning-o"></van-icon>
              </div>
            </div>
          </div>
          <van-switch v-if="management && !pageParam.isDetails"
                      :active-color="appTheme"
                      @click.stop=""
                      @change="editIsAppShow(item)"
                      v-model="item.isShow"
                      :size="((appFontSize+8)*0.01)+'rem'">
          </van-switch>
        </div>
      </div>
    </div>
    <div v-else
         style="font-size: 0.4rem;color: #747474;text-align: center;padding: 30px;">暂无数据</div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>

    <!--返回顶部 需要加一个空白占位 不然返回顶部 就会错位显示 -->
    <transition name="van-fade">
      <ul v-if="footerBtnsShow"
          class="footer_btn_box">
        {{'&nbsp;'}}
        <div :style="general.loadConfiguration()">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((appFontSize+25)*0.01)+'rem'"
                        name="upgrade"></van-icon>
            </div>
            <div v-if="item.type == 'btn'"
                 class="van-button-box">
              <van-button loading-type="spinner"
                          :loading-size="((appFontSize)*0.01)+'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </ul>
    </transition>
    <!--不为一级页面时 适配底部条-->
    <footer :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer>
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Toast, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'fiveFile2',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const data = reactive({
      id: route.query.id || '',
      files: route.query.files || '',
      isShow: route.query.isShow || '',
      pageParam: { isDetails: false },
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      scrollTop: 0, // 页面划动距离
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      listData: [], // 列表数据
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部  btn为按钮
      management: false, // 是否资料管理
      recordId: route.query.recordId,
      meetingTypeId: route.query.meetingTypeId
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      getMeetingFilesList()
    })
    // 会议通知、会议文件列表
    const getMeetingFilesList = async () => {
      var postParam = {
        conferenceId: data.recordId,
        pageNo: 1,
        pageSize: 50
      }
      console.log('data.id==>', data.id)
      if (data.id === '1') {
        getDetails()
      }
      if (data.id === '4') {
        const res = await $api.electronicreading.conferencefileList(postParam) // 详情
        var { data: list3 } = res
        console.log('🚀🚀🚀🚀🚀🚀list3', list3)
        data.loading = false
        data.refreshing = false
        data.finished = true
        if (list3.attachmentList) {
          list3.attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
            var item = {}
            item.id = _eItem.id
            item.url = _eItem.filePath || ''
            item.name = _eItem.fileName || ''
            item.files = _eItem.files || ''
            item.isShow = _eItem.isRelease === 1// 是否显示
            item.iconInfo = general.getFileTypeAttr(_eItem.fileType)
            data.listData.push(item)
          })
        } else {
          Toast('暂无信息')
        }
      }
    }

    // 获取会议通知详情
    const getDetails = async () => {
      const res = await $api.electronicreading.conferenceNoticeInfo(data.recordId) // 详情
      var { data: list } = res
      console.log('list', list)
      if (list.attachmentList) {
        list.attachmentList.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          var item = {}
          item.id = _eItem.id
          item.url = _eItem.filePath || ''
          item.name = _eItem.fileName || ''
          item.files = _eItem.files || ''
          item.isShow = _eItem.isRelease === 1// 是否显示
          item.iconInfo = general.getFileTypeAttr(_eItem.fileType)
          data.listData.push(item)
        })
      } else {
        Toast('暂无信息')
      }
    }

    const openDetails = (item) => {
      var param = {
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    const onRefresh = () => {
    }
    const onLoad = () => {

    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, general, confirm, openDetails }
  }
}
</script>
<style lang="less" scoped>
.fiveFile2 {
  background: #f8f8f8;
}
</style>

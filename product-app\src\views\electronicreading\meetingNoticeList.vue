<template>
  <div class="meetingNoticeList">
    <div v-if="meetingNoticeListdata.length != 0">
      <div v-for="(item,index) in meetingNoticeListdata"
           :key="index"
           class="vue_newslist3_warp">
        <van-cell clickable
                  class="vue_newslist3_item"
                  @click="openDetails(item)">
          <div class="historName"
               v-html="item.title"></div>
          <div class="historTime">{{dayjs(item.createDate).format('YYYY-MM-DD HH:mm')}}</div>
        </van-cell>
      </div>
    </div>
    <div v-else
         style="font-size: 0.4rem;color: #747474;text-align: center;padding: 30px;">暂无数据</div>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="
             showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage } from 'vant'
export default {
  name: 'meetingNoticeList',
  components: {
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      meetingNoticeListdata: [],
      recordId: route.query.recordId,
      id: route.query.id,
      meetingTypeId: JSON.parse(sessionStorage.getItem('meetingTypeId'))
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getMeetingFilesList()
    })
    // 列表
    const getMeetingFilesList = async () => {
      var postParam = {
        conferenceId: data.recordId,
        pageNo: 1,
        pageSize: 50
      }
      const res = await $api.electronicreading.conferencenoticeList(postParam)
      var { data: list } = res
      data.meetingNoticeListdata = data.meetingNoticeListdata.concat(list)
    }
    // 详情
    const openDetails = (row) => {
      router.push({ path: 'fiveFile2', query: { id: data.id, recordId: row.id, meetingTypeId: data.meetingTypeId } })
    }
    const onRefresh = () => {
    }
    const onLoad = () => {
    }
    return { ...toRefs(data), onRefresh, onLoad, $general, dayjs, openDetails }
  }
}
</script>
<style lang="less" scoped>
.meetingNoticeList {
  width: 100%;
  .historName {
    font-size: 0.42rem;
  }
  .historTime {
    font-size: 0.35rem;
    color: #8d8c8c;
    margin-top: 0.5rem;
  }
}
</style>

{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\api\\http.js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\api\\http.js", "mtime": 1755510138693}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\babel.config.js", "mtime": 1754028950133}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["axios", "Qs", "router", "CryptoJs", "loginUc", "baseURL", "console", "log", "process", "env", "STAGE", "window", "location", "protocol", "hostname", "port", "timeout", "defaults", "headers", "post", "interceptors", "request", "use", "config", "url", "indexOf", "sessionStorage", "getItem", "token", "Authorization", "certId", "header", "areaId", "JSON", "parse", "method", "Object", "prototype", "toString", "call", "data", "stringify", "appSecret", "clientId", "clientTypeId", "timestamp", "Date", "signature", "getSignature", "includes", "error", "Promise", "reject", "HTTPMethod", "toUpperCase", "URI", "param", "split", "e", "message", "Message", "getPara<PERSON>", "hash", "HmacSHA256", "_httpMethod", "_param", "j<PERSON><PERSON><PERSON>", "signStr", "i", "s", "key", "value", "undefined", "paramIntValue", "k", "length", "charCodeAt", "response", "<PERSON><PERSON><PERSON>", "resolve", "_Toast", "fail", "errmsg", "clear", "push", "name", "HTTP", "_request", "filter", "then", "res", "catch", "err", "file", "type", "_file", "params", "responseType"], "sources": ["D:/zy/xm/h5/qdrd_h5/product-app/src/api/http.js"], "sourcesContent": ["import axios from 'axios'\r\nimport Qs from 'qs'\r\nimport router from '../router'\r\nimport CryptoJs from 'crypto-js/crypto-js'\r\nimport {\r\n  Toast\r\n} from 'vant'\r\n\r\nvar loginUc = 'http://**************:22324/server'\r\nvar baseURL = 'http://**************:22325/lzt' // 河南政协平台版\r\n// loginUc = 'https://lzpt.hnzx.gov.cn/platform-server'\r\n// baseURL = 'https://lzpt.hnzx.gov.cn/platform-lzt'// 河南政协正数-测试环境域名\r\nconsole.log(process.env.STAGE)\r\nif (process.env.STAGE == 'zyrd_qingdao_test') { // eslint-disable-line\r\n  loginUc = 'http://**************:22507/server'\r\n  baseURL = 'http://**************:22508/lzt' // 正宇政协产品\r\n  // loginUc = 'http://**************/server'\r\n  // baseURL = 'http://**************/lzt'\r\n  // loginUc = 'http://localhost:8080/server'\r\n  // baseURL = 'http://localhost:8080/lzt'\r\n} else if (process.env.STAGE == 'zyrd_qingdao_sdt') { // eslint-disable-line\r\n  loginUc = 'http://************:22507/server'\r\n  baseURL = 'http://************:22508/lzt' // 青岛人大山东通正式请求\r\n} else if (process.env.STAGE == 'zyrd_platform_prod_81') { // eslint-disable-line\r\n  loginUc = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/server`\r\n  baseURL = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/lzt`\r\n}\r\n\r\nvar timeout = 16000\r\naxios.defaults.baseURL = baseURL\r\n\r\n// 请求超时时间\r\naxios.defaults.timeout = timeout\r\n// 设置post请求头\r\naxios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'\r\n// 请求拦截器\r\naxios.interceptors.request.use(\r\n  config => {\r\n    if (config.url.indexOf('push/rongCloud') >= 0) {\r\n      config.baseURL = sessionStorage.getItem('tomcatAddress')\r\n      // } else if (config.url.indexOf('www.yozodcs.com') >= 0) {\r\n    } else if (config.url.indexOf('rest/oagxh/personal_getdetail_v7') >= 0) {\r\n      const token = sessionStorage.getItem('otherToken') || ''\r\n      config.headers.Authorization = token\r\n      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'\r\n    } else {\r\n      config.headers.certId = sessionStorage.getItem('useridN')\r\n      if (config.header.token) {\r\n        config.headers.token = config.header.token\r\n      } else {\r\n        // 自定义请求头参数\r\n        const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token') || ''\r\n        const areaId = JSON.parse(sessionStorage.getItem('areaId')) || ''\r\n        config.headers.Authorization = token ? JSON.parse(token) : 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n        config.headers['u-login-areaId'] = areaId || ''\r\n      }\r\n      if (config.method === 'post') {\r\n        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n          if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n            config.data = Qs.stringify(config.data)\r\n          }\r\n        }\r\n      }\r\n      var appSecret = '3d5ef53424f24852a8fc1d15a1072b42'\r\n      var clientId = '816cc044c46247d987bbf611ced1cf5e'\r\n      var clientTypeId = sessionStorage.getItem('BigDataUser') || 'qdrdplatform'\r\n      var timestamp = Date.parse(new Date()) / 1000 // 时间戳，精确到秒\r\n      config.headers.timestamp = timestamp\r\n      config.headers.clientId = clientId\r\n      config.headers.clientTypeId = clientTypeId\r\n      config.headers.signature = getSignature(config.url, config.method, timestamp, clientId, appSecret)\r\n      config.headers['u-app-user'] = true\r\n      if (config.url.includes('datacenter/common') && process.env.STAGE == 'zyrd_platform_prod_81') {// eslint-disable-line\r\n        config.baseURL = 'http://**************'\r\n      } else if (config.url.includes('datacenter/common')) {\r\n        config.baseURL = 'http://**************'\r\n      }\r\n    }\r\n    return config\r\n  }, error => {\r\n    // 对请求错误做些什么\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nfunction getSignature(url, method, timestamp, clientId, appSecret) {\r\n  var HTTPMethod = method ? method.toUpperCase() : 'GET'\r\n  var URI = ''\r\n  var param = ''\r\n  url = baseURL + url\r\n  try {\r\n    if (url.indexOf('?') >= 0) {\r\n      URI = url.split('?')[0].split(url.split('/')[3])[1]\r\n      param = HTTPMethod === 'POST' ? '' : (url.split('?')[1])\r\n    } else {\r\n      URI = url.split(url.split('/')[3])[1]\r\n    }\r\n  } catch (e) {\r\n    console.error('语句异常：' + e.message)\r\n  }\r\n  var Message = timestamp + '-' + HTTPMethod + '-' + URI + '-' + getParam(HTTPMethod, param)\r\n  var hash = '' + CryptoJs.HmacSHA256(Message, appSecret)\r\n  return hash\r\n}\r\n/**\r\n * _httpMethod:请求方法（必须大写）\r\n * _param参数字符串\r\n * 将所有GET参数值的 char 值加和生成一个整数 POST方法返回0\r\n */\r\nfunction getParam(_httpMethod, _param) {\r\n  var jsonarr = _param.split('&')\r\n  var signStr = ''\r\n  for (var i in jsonarr) {\r\n    var s = '' + jsonarr[i]\r\n    var key = s.split('=')[0]\r\n    var value = s.split('=')[1]\r\n    // eslint-disable-next-line eqeqeq\r\n    if (key == 'timestamp' || key == 'clientId' || key == 'signatrue') {\r\n      continue\r\n    }\r\n    // eslint-disable-next-line eqeqeq\r\n    if (!value || value == '' || value == undefined || value == null) {\r\n      continue\r\n    }\r\n    signStr += key\r\n  }\r\n  // eslint-disable-next-line eqeqeq\r\n  if (signStr == '') {\r\n    return 0\r\n  }\r\n  var paramIntValue = 0\r\n  for (var k = 0; k < signStr.length; k++) {\r\n    paramIntValue += signStr.charCodeAt(k)\r\n  }\r\n  // eslint-disable-next-line eqeqeq\r\n  if (_httpMethod == 'POST') {\r\n    paramIntValue = 0\r\n  }\r\n  return paramIntValue\r\n}\r\n\r\n// 响应拦截器\r\naxios.interceptors.response.use(\r\n  response => {\r\n    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据\r\n    if (response.data.errcode === 200) {\r\n      return Promise.resolve(response)\r\n    } else if (response.data.errcode === 302) {\r\n      Toast.fail(response.data.errmsg || response.data.message)\r\n      sessionStorage.clear()\r\n      router.push({\r\n        name: 'login'\r\n      })\r\n      return Promise.reject(response)\r\n    } else if (response.data.errcode === undefined) { // undefind 为文件下载接口\r\n      return Promise.resolve(response)\r\n    } else {\r\n      if (response.data.errmsg) {\r\n        Toast.fail(response.data.errmsg || response.data.message)\r\n      }\r\n      return Promise.reject(response)\r\n    }\r\n  }, error => {\r\n    if (error.message.includes('timeout')) {\r\n      Toast.fail('请求超时，请稍后重试！')\r\n      return Promise.reject(error)\r\n    } else if (error.message.includes('404')) {\r\n      Toast.fail('404了哟~')\r\n    } else if (error && (error.response.data.errmsg || error.response.data.message)) {\r\n      Toast.fail(error.response.data.errmsg || error.response.data.message)\r\n      return Promise.reject(error)\r\n    }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\nclass HTTP {\r\n  request({\r\n    url,\r\n    data = {},\r\n    method = 'post',\r\n    header = {}\r\n  }) {\r\n    return new Promise((resolve, reject) => {\r\n      this._request(url, this.filter(data), method, header, resolve, reject)\r\n    })\r\n  }\r\n\r\n  _request(url, data = {}, method, header, resolve, reject) {\r\n    axios({\r\n      url: url,\r\n      data: data,\r\n      method: method,\r\n      header: header\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  }\r\n\r\n  filter(param) { // 参数过滤\r\n    var data = param\r\n    for (var key in data) {\r\n      if (data[key] === null) {\r\n        delete data[key]\r\n      }\r\n    }\r\n    return data\r\n  }\r\n\r\n  file({\r\n    url,\r\n    data = {},\r\n    type = 'blob',\r\n    method = 'post',\r\n    header = {}\r\n  }) {\r\n    return new Promise((resolve, reject) => {\r\n      this._file(url, this.filter(data), method, header, type, resolve, reject)\r\n    })\r\n  }\r\n\r\n  _file(url, params, method, header, type, resolve, reject) {\r\n    axios({\r\n      url: url,\r\n      data: params,\r\n      method: method,\r\n      header: header,\r\n      responseType: type\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  }\r\n}\r\n\r\nexport {\r\n  HTTP,\r\n  loginUc\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,EAAE,MAAM,IAAI;AACnB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,QAAQ,MAAM,qBAAqB;AAK1C,IAAIC,OAAO,GAAG,oCAAoC;AAClD,IAAIC,OAAO,GAAG,iCAAiC,EAAC;AAChD;AACA;AACAC,OAAO,CAACC,GAAG,CAACC,OAAO,CAACC,GAAG,CAACC,KAAK,CAAC;AAC9B,IAAIF,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,mBAAmB,EAAE;EAAE;EAC9CN,OAAO,GAAG,oCAAoC;EAC9CC,OAAO,GAAG,iCAAiC,EAAC;EAC5C;EACA;EACA;EACA;AACF,CAAC,MAAM,IAAIG,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,kBAAkB,EAAE;EAAE;EACpDN,OAAO,GAAG,kCAAkC;EAC5CC,OAAO,GAAG,+BAA+B,EAAC;AAC5C,CAAC,MAAM,IAAIG,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,uBAAuB,EAAE;EAAE;EACzDN,OAAO,GAAG,GAAGO,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKF,MAAM,CAACC,QAAQ,CAACE,QAAQ,IAAIH,MAAM,CAACC,QAAQ,CAACG,IAAI,SAAS;EACnGV,OAAO,GAAG,GAAGM,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKF,MAAM,CAACC,QAAQ,CAACE,QAAQ,IAAIH,MAAM,CAACC,QAAQ,CAACG,IAAI,MAAM;AAClG;AAEA,IAAIC,OAAO,GAAG,KAAK;AACnBhB,KAAK,CAACiB,QAAQ,CAACZ,OAAO,GAAGA,OAAO;;AAEhC;AACAL,KAAK,CAACiB,QAAQ,CAACD,OAAO,GAAGA,OAAO;AAChC;AACAhB,KAAK,CAACiB,QAAQ,CAACC,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC,GAAG,iDAAiD;AAC/F;AACAnB,KAAK,CAACoB,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAI;EACR,IAAIA,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;IAC7CF,MAAM,CAAClB,OAAO,GAAGqB,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD;EACF,CAAC,MAAM,IAAIJ,MAAM,CAACC,GAAG,CAACC,OAAO,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;IACtE,MAAMG,KAAK,GAAGF,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;IACxDJ,MAAM,CAACL,OAAO,CAACW,aAAa,GAAGD,KAAK;IACpCL,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC,GAAG,mCAAmC;EACtE,CAAC,MAAM;IACLK,MAAM,CAACL,OAAO,CAACY,MAAM,GAAGJ,cAAc,CAACC,OAAO,CAAC,SAAS,CAAC;IACzD,IAAIJ,MAAM,CAACQ,MAAM,CAACH,KAAK,EAAE;MACvBL,MAAM,CAACL,OAAO,CAACU,KAAK,GAAGL,MAAM,CAACQ,MAAM,CAACH,KAAK;IAC5C,CAAC,MAAM;MACL;MACA,MAAMA,KAAK,GAAGF,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC,IAAID,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;MAC1F,MAAMK,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACR,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE;MACjEJ,MAAM,CAACL,OAAO,CAACW,aAAa,GAAGD,KAAK,GAAGK,IAAI,CAACC,KAAK,CAACN,KAAK,CAAC,GAAG,gCAAgC;MAC3FL,MAAM,CAACL,OAAO,CAAC,gBAAgB,CAAC,GAAGc,MAAM,IAAI,EAAE;IACjD;IACA,IAAIT,MAAM,CAACY,MAAM,KAAK,MAAM,EAAE;MAC5B,IAAIC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAChB,MAAM,CAACiB,IAAI,CAAC,IAAI,mBAAmB,EAAE;QAAE;QACxE,IAAIjB,MAAM,CAACL,OAAO,CAAC,cAAc,CAAC,KAAK,gCAAgC,EAAE;UACvEK,MAAM,CAACiB,IAAI,GAAGvC,EAAE,CAACwC,SAAS,CAAClB,MAAM,CAACiB,IAAI,CAAC;QACzC;MACF;IACF;IACA,IAAIE,SAAS,GAAG,kCAAkC;IAClD,IAAIC,QAAQ,GAAG,kCAAkC;IACjD,IAAIC,YAAY,GAAGlB,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,IAAI,cAAc;IAC1E,IAAIkB,SAAS,GAAGC,IAAI,CAACZ,KAAK,CAAC,IAAIY,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,EAAC;IAC9CvB,MAAM,CAACL,OAAO,CAAC2B,SAAS,GAAGA,SAAS;IACpCtB,MAAM,CAACL,OAAO,CAACyB,QAAQ,GAAGA,QAAQ;IAClCpB,MAAM,CAACL,OAAO,CAAC0B,YAAY,GAAGA,YAAY;IAC1CrB,MAAM,CAACL,OAAO,CAAC6B,SAAS,GAAGC,YAAY,CAACzB,MAAM,CAACC,GAAG,EAAED,MAAM,CAACY,MAAM,EAAEU,SAAS,EAAEF,QAAQ,EAAED,SAAS,CAAC;IAClGnB,MAAM,CAACL,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;IACnC,IAAIK,MAAM,CAACC,GAAG,CAACyB,QAAQ,CAAC,mBAAmB,CAAC,IAAIzC,OAAO,CAACC,GAAG,CAACC,KAAK,IAAI,uBAAuB,EAAE;MAAC;MAC7Fa,MAAM,CAAClB,OAAO,GAAG,uBAAuB;IAC1C,CAAC,MAAM,IAAIkB,MAAM,CAACC,GAAG,CAACyB,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MACnD1B,MAAM,CAAClB,OAAO,GAAG,uBAAuB;IAC1C;EACF;EACA,OAAOkB,MAAM;AACf,CAAC,EAAE2B,KAAK,IAAI;EACV;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,SAASF,YAAYA,CAACxB,GAAG,EAAEW,MAAM,EAAEU,SAAS,EAAEF,QAAQ,EAAED,SAAS,EAAE;EACjE,IAAIW,UAAU,GAAGlB,MAAM,GAAGA,MAAM,CAACmB,WAAW,CAAC,CAAC,GAAG,KAAK;EACtD,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,KAAK,GAAG,EAAE;EACdhC,GAAG,GAAGnB,OAAO,GAAGmB,GAAG;EACnB,IAAI;IACF,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACzB8B,GAAG,GAAG/B,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAACjC,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnDD,KAAK,GAAGH,UAAU,KAAK,MAAM,GAAG,EAAE,GAAI7B,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;IAC1D,CAAC,MAAM;MACLF,GAAG,GAAG/B,GAAG,CAACiC,KAAK,CAACjC,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVpD,OAAO,CAAC4C,KAAK,CAAC,OAAO,GAAGQ,CAAC,CAACC,OAAO,CAAC;EACpC;EACA,IAAIC,OAAO,GAAGf,SAAS,GAAG,GAAG,GAAGQ,UAAU,GAAG,GAAG,GAAGE,GAAG,GAAG,GAAG,GAAGM,QAAQ,CAACR,UAAU,EAAEG,KAAK,CAAC;EAC1F,IAAIM,IAAI,GAAG,EAAE,GAAG3D,QAAQ,CAAC4D,UAAU,CAACH,OAAO,EAAElB,SAAS,CAAC;EACvD,OAAOoB,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,QAAQA,CAACG,WAAW,EAAEC,MAAM,EAAE;EACrC,IAAIC,OAAO,GAAGD,MAAM,CAACR,KAAK,CAAC,GAAG,CAAC;EAC/B,IAAIU,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,IAAIF,OAAO,EAAE;IACrB,IAAIG,CAAC,GAAG,EAAE,GAAGH,OAAO,CAACE,CAAC,CAAC;IACvB,IAAIE,GAAG,GAAGD,CAAC,CAACZ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzB,IAAIc,KAAK,GAAGF,CAAC,CAACZ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA,IAAIa,GAAG,IAAI,WAAW,IAAIA,GAAG,IAAI,UAAU,IAAIA,GAAG,IAAI,WAAW,EAAE;MACjE;IACF;IACA;IACA,IAAI,CAACC,KAAK,IAAIA,KAAK,IAAI,EAAE,IAAIA,KAAK,IAAIC,SAAS,IAAID,KAAK,IAAI,IAAI,EAAE;MAChE;IACF;IACAJ,OAAO,IAAIG,GAAG;EAChB;EACA;EACA,IAAIH,OAAO,IAAI,EAAE,EAAE;IACjB,OAAO,CAAC;EACV;EACA,IAAIM,aAAa,GAAG,CAAC;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,OAAO,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCD,aAAa,IAAIN,OAAO,CAACS,UAAU,CAACF,CAAC,CAAC;EACxC;EACA;EACA,IAAIV,WAAW,IAAI,MAAM,EAAE;IACzBS,aAAa,GAAG,CAAC;EACnB;EACA,OAAOA,aAAa;AACtB;;AAEA;AACAzE,KAAK,CAACoB,YAAY,CAACyD,QAAQ,CAACvD,GAAG,CAC7BuD,QAAQ,IAAI;EACV;EACA,IAAIA,QAAQ,CAACrC,IAAI,CAACsC,OAAO,KAAK,GAAG,EAAE;IACjC,OAAO3B,OAAO,CAAC4B,OAAO,CAACF,QAAQ,CAAC;EAClC,CAAC,MAAM,IAAIA,QAAQ,CAACrC,IAAI,CAACsC,OAAO,KAAK,GAAG,EAAE;IACxCE,MAAA,CAAMC,IAAI,CAACJ,QAAQ,CAACrC,IAAI,CAAC0C,MAAM,IAAIL,QAAQ,CAACrC,IAAI,CAACmB,OAAO,CAAC;IACzDjC,cAAc,CAACyD,KAAK,CAAC,CAAC;IACtBjF,MAAM,CAACkF,IAAI,CAAC;MACVC,IAAI,EAAE;IACR,CAAC,CAAC;IACF,OAAOlC,OAAO,CAACC,MAAM,CAACyB,QAAQ,CAAC;EACjC,CAAC,MAAM,IAAIA,QAAQ,CAACrC,IAAI,CAACsC,OAAO,KAAKN,SAAS,EAAE;IAAE;IAChD,OAAOrB,OAAO,CAAC4B,OAAO,CAACF,QAAQ,CAAC;EAClC,CAAC,MAAM;IACL,IAAIA,QAAQ,CAACrC,IAAI,CAAC0C,MAAM,EAAE;MACxBF,MAAA,CAAMC,IAAI,CAACJ,QAAQ,CAACrC,IAAI,CAAC0C,MAAM,IAAIL,QAAQ,CAACrC,IAAI,CAACmB,OAAO,CAAC;IAC3D;IACA,OAAOR,OAAO,CAACC,MAAM,CAACyB,QAAQ,CAAC;EACjC;AACF,CAAC,EAAE3B,KAAK,IAAI;EACV,IAAIA,KAAK,CAACS,OAAO,CAACV,QAAQ,CAAC,SAAS,CAAC,EAAE;IACrC+B,MAAA,CAAMC,IAAI,CAAC,aAAa,CAAC;IACzB,OAAO9B,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B,CAAC,MAAM,IAAIA,KAAK,CAACS,OAAO,CAACV,QAAQ,CAAC,KAAK,CAAC,EAAE;IACxC+B,MAAA,CAAMC,IAAI,CAAC,QAAQ,CAAC;EACtB,CAAC,MAAM,IAAI/B,KAAK,KAAKA,KAAK,CAAC2B,QAAQ,CAACrC,IAAI,CAAC0C,MAAM,IAAIhC,KAAK,CAAC2B,QAAQ,CAACrC,IAAI,CAACmB,OAAO,CAAC,EAAE;IAC/EqB,MAAA,CAAMC,IAAI,CAAC/B,KAAK,CAAC2B,QAAQ,CAACrC,IAAI,CAAC0C,MAAM,IAAIhC,KAAK,CAAC2B,QAAQ,CAACrC,IAAI,CAACmB,OAAO,CAAC;IACrE,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;EAC9B;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AACD,MAAMoC,IAAI,CAAC;EACTjE,OAAOA,CAAC;IACNG,GAAG;IACHgB,IAAI,GAAG,CAAC,CAAC;IACTL,MAAM,GAAG,MAAM;IACfJ,MAAM,GAAG,CAAC;EACZ,CAAC,EAAE;IACD,OAAO,IAAIoB,OAAO,CAAC,CAAC4B,OAAO,EAAE3B,MAAM,KAAK;MACtC,IAAI,CAACmC,QAAQ,CAAC/D,GAAG,EAAE,IAAI,CAACgE,MAAM,CAAChD,IAAI,CAAC,EAAEL,MAAM,EAAEJ,MAAM,EAAEgD,OAAO,EAAE3B,MAAM,CAAC;IACxE,CAAC,CAAC;EACJ;EAEAmC,QAAQA,CAAC/D,GAAG,EAAEgB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,EAAEJ,MAAM,EAAEgD,OAAO,EAAE3B,MAAM,EAAE;IACxDpD,KAAK,CAAC;MACJwB,GAAG,EAAEA,GAAG;MACRgB,IAAI,EAAEA,IAAI;MACVL,MAAM,EAAEA,MAAM;MACdJ,MAAM,EAAEA;IACV,CAAC,CAAC,CAAC0D,IAAI,CAACC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAG,CAAClD,IAAI,CAAC;IACnB,CAAC,CAAC,CAACmD,KAAK,CAACC,GAAG,IAAI;MACdxC,MAAM,CAACwC,GAAG,CAAC;IACb,CAAC,CAAC;EACJ;EAEAJ,MAAMA,CAAChC,KAAK,EAAE;IAAE;IACd,IAAIhB,IAAI,GAAGgB,KAAK;IAChB,KAAK,IAAIc,GAAG,IAAI9B,IAAI,EAAE;MACpB,IAAIA,IAAI,CAAC8B,GAAG,CAAC,KAAK,IAAI,EAAE;QACtB,OAAO9B,IAAI,CAAC8B,GAAG,CAAC;MAClB;IACF;IACA,OAAO9B,IAAI;EACb;EAEAqD,IAAIA,CAAC;IACHrE,GAAG;IACHgB,IAAI,GAAG,CAAC,CAAC;IACTsD,IAAI,GAAG,MAAM;IACb3D,MAAM,GAAG,MAAM;IACfJ,MAAM,GAAG,CAAC;EACZ,CAAC,EAAE;IACD,OAAO,IAAIoB,OAAO,CAAC,CAAC4B,OAAO,EAAE3B,MAAM,KAAK;MACtC,IAAI,CAAC2C,KAAK,CAACvE,GAAG,EAAE,IAAI,CAACgE,MAAM,CAAChD,IAAI,CAAC,EAAEL,MAAM,EAAEJ,MAAM,EAAE+D,IAAI,EAAEf,OAAO,EAAE3B,MAAM,CAAC;IAC3E,CAAC,CAAC;EACJ;EAEA2C,KAAKA,CAACvE,GAAG,EAAEwE,MAAM,EAAE7D,MAAM,EAAEJ,MAAM,EAAE+D,IAAI,EAAEf,OAAO,EAAE3B,MAAM,EAAE;IACxDpD,KAAK,CAAC;MACJwB,GAAG,EAAEA,GAAG;MACRgB,IAAI,EAAEwD,MAAM;MACZ7D,MAAM,EAAEA,MAAM;MACdJ,MAAM,EAAEA,MAAM;MACdkE,YAAY,EAAEH;IAChB,CAAC,CAAC,CAACL,IAAI,CAACC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAG,CAAClD,IAAI,CAAC;IACnB,CAAC,CAAC,CAACmD,KAAK,CAACC,GAAG,IAAI;MACdxC,MAAM,CAACwC,GAAG,CAAC;IACb,CAAC,CAAC;EACJ;AACF;AAEA,SACEN,IAAI,EACJlF,OAAO", "ignoreList": []}]}
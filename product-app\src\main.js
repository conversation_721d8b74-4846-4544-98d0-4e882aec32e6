import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import vant from './assets/vant'
import 'vant/lib/index.css'
import 'amfe-flexible'
import './assets/css/index.less'
import './assets/icon/iconfont.css'
import Hammer from 'hammerjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 调试器
// import Vconsole from 'vconsole'
// new Vconsole() // eslint-disable-line
// if (process.env.NODE_ENV === 'production') {
//   if (window) {
//     window.console.log = function () { }
//   }
// }
const app = createApp(App)
app.directive('tap', {
  beforeMount (el, binding) {
    const hammerTest = new Hammer(el)
    hammerTest.on('tap', binding.value)
  }
})
app.directive('doubletap', {
  beforeMount (el, binding) {
    const hammerTest = new Hammer(el)
    hammerTest.on('doubletap', binding.value)
  }
})
app.directive('press', {
  beforeMount (el, binding) {
    const hammerTest = new Hammer(el)
    hammerTest.on('press', binding.value)
  }
})
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(store).use(router).use(vant).mount('#app')

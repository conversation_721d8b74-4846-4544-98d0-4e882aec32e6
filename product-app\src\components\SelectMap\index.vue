<template>
  <!-- 地图选择位置，包括地图点击选点，搜索 -->
  <div class="StationMap">
    <van-nav-bar title="选择具体问题位置"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text="返回"
                 right-text="确定"
                 left-arrow
                 @click-left="close"
                 @click-right="confirm" />
    <!-- 高德关键字搜索，个人每天限制100次调用，企业限值1000次 -->
    <div class="searchArea">
      <van-search v-model="searchVal"
                  show-action
                  label="地址"
                  placeholder="请输入具体位置"
                  @search="onSearch">
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
      <van-dropdown-menu v-if="showsearchResult">
        <van-dropdown-item v-model="checkedVal"
                           :options="positionArr"
                           @change="searchPosition" />
      </van-dropdown-menu>
    </div>

    <!-- 地图容器 -->
    <div id="containerMap"></div>
    <div class="wrap">
      已选择：{{ positionObj.address }}<br>
      <!-- 附近代表之家：{{ selectHome.name }} -->
    </div>
  </div>
</template>
<script>
/* eslint-disable */
import { reactive, toRefs, onMounted } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { Toast, NavBar } from 'vant'
import { watchEffect } from 'vue'

// 添加高德安全密钥（需要去高德开发平台注册申请）
window._AMapSecurityConfig = {
  securityJsCode: '8cc5c72079ec5d3c9d2d53653124745e'
}
const defaultAddress = {
  lon: 36.066938, // 纬度
  lat: 120.382665, // 经度
  address: '青岛'
}
export default {
  props: {
    position: {
      type: Object,
      default: () => defaultAddress
    },
    isShow: {
      type: Boolean,
      default: false
    },
    dataList: {
      type: Array,
      default: []
    }
  },
  components: {
    [NavBar.name]: NavBar
  },
  setup (props, context) {
    const allData = reactive({
      map: null,
      markers: null,
      marker: [],
      positionObj: props.position,
      township: '',
      centerPosition: [], // 默认显示当前定位
      searchVal: '',
      checkedVal: '',
      positionArr: [],
      placeSearchComponent: null,
      showsearchResult: false,
      ifSearchLimit: true, // 超过100次调用后，搜索按钮隐藏
      selectAddress: '',
      mapData: props.dataList,
      selectHome: {},
      AMaps: null,
      adcode: '',
      innerHeight: window.innerHeight,
      innerWidth: window.innerWidth
    })
    watchEffect(() => {
      if (!props.position.lon && !props.position.lat) {
        console.log('--123-->', 123);
      }
    })
    onMounted(() => {
      // 获取用户当前位置
      if (/(Android)/i.test(navigator.userAgent) && window.android) {
        const str = JSON.parse(window.android.requestLocation()).data
        const lon = str.lng.toString().length < 9 ? str.lng : str.lng.toString().substring(0, 9)
        const lat = str.lat.toString().length < 9 ? str.lat : str.lat.toString().substring(0, 9)
        allData.centerPosition = [lon, lat]
      } else {
        allData.centerPosition = [allData.positionObj.lat, allData.positionObj.lon]
      }
      initMap() // DOM初始化完成进行地图初始化
      // getsate()
    })
    // 高德地图
    function initMap () {
      AMapLoader.load({
        key: '78605b002d85e90667dbbaca99bffba0', // 申请好的开发者Key
        version: '2.0',
        plugins: ['AMap.Geocoder', 'AMap.Scale', 'AMap.PlaceSearch', 'AMap.Geolocation']
      })
        .then((AMap) => {
          allData.AMaps = AMap
          allData.map = new AMap.Map('containerMap', {
            resizeEnable: true,
            viewMode: '2D', //  是否为3D地图模式
            zoom: 15, // 初始化地图级别
            // center: allData.centerPosition, // 中心点坐标
            jogEnable: false // 是否使用缓动效果，关闭平移惯性感觉舒服一些
          })
          getsate()
          if (allData.mapData.length) {
            markPoints()
          }
          console.log('-->>', allData.centerPosition)
          onSearch() // 默认搜索
          setMarker(allData.centerPosition) // 设置当前点位显示
          getAdress(allData.centerPosition) // 经纬度转位置
          allData.map.setFitView()
          // 地图添加点击事件
          allData.map.on('click', onMapClick)
          console.log(allData.mapData)

        })
        .catch((err) => {
          console.log(err)
        })
    }
    const getsate = () => {
      allData.map.plugin('AMap.Geolocation', function () {
        var geolocation = new AMap.Geolocation({
          enableHighAccuracy: true, // 是否使用高精度定位，默认：true
          timeout: 10000, // 设置定位超时时间，默认：无穷大
          // offset: [10, 20],  // 定位按钮的停靠位置的偏移量
          zoomToAccuracy: true  //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
          // position: 'RB' //  定位按钮的排放位置,  RB表示右下
        })
        geolocation.getCurrentPosition(function (status, result) {
          if (status == 'complete') {
            onComplete(result)
          } else {
            onError(result)
          }
        })
        function onComplete (data) {
          // data是具体的定位信息
          console.log('data是具体的定位信息', data)
          setMarker([data.position.lng, data.position.lat]) // 设置当前点位显示
          getAdress([data.position.lng, data.position.lat]) // 经纬度转位置
          allData.map.setCenter([data.position.lng, data.position.lat])
        }
        function onError (data) {
          setMarker(allData.centerPosition) // 设置当前点位显示
          getAdress(allData.centerPosition) // 经纬度转位置
          // 定位出错
          console.log('定位出错', data)
        }
      })
    }
    const markPoints = () => {
      allData.mapData.forEach(item => {
        if (item.longitude && item.latitude) {
          // 创建一个 Marker 实例：
          const marker = new AMap.Marker({
            icon: new AMap.Icon({
              image: require('../../assets/img/home.png'), // 自定义图标URL
              size: new AMap.Size(15, 15), // 图标尺寸
              imageSize: new AMap.Size(15, 15) // 图标显示尺寸
            }),
            // size: new AMap.Size(30, 30),
            // anchor: new AMap.Pixel(20, 30),
            position: new AMap.LngLat(item.longitude, item.latitude)   // 经纬度对象，也可以是经纬度构成的一维数组[lng, lat]
          })
          // 将创建的点标记添加到已有的地图实例：
          allData.map.add(marker)
          allData.marker.push(marker)
          //给标记点添加事件
          marker.on('click', (e) => {
            setInfoWindows(e, item)
          })
        }
      })
    }
    const calculateNearestMarker = (map, lnglat) => {
      // console.log('近========>', map)
      var nearestDistance = Infinity
      var nearestMarker = null
      allData.marker.forEach((marker) => {
        const position = marker.getPosition()
        const distance = AMap.GeometryUtil.distance(lnglat, position)
        if (distance < nearestDistance) {
          nearestDistance = distance
          nearestMarker = marker
          console.log('近========>', nearestMarker)
        }
      })

      if (nearestMarker) {
        // nearestMarker.vI.click[0].fn()
        // 在这里可以对最近的标点进行处理，比如改变样式或者进行其他操作
      }
    }
    const setInfoWindows = (e, item) => {
      console.log('allData.mapallData.mapallData.mapallData.mapallData.mapallData.mapallData.mapallData.map', item)
      // 信息窗体的内容
      var content = [
        // e.pos[0],e.pos[1],
        `<div style='\'padding:0px' 0px = '' 4px; \'=''><b>${item.name}</b></div>`,
      ]
      // 创建 infoWindow 实例	
      var infoWindow = new AMap.InfoWindow({
        content: content.join("<br>")  //传入 dom 对象，或者 html 字符串
      })
      // 打开信息窗体
      var dd = allData.map.getCenter()
      // dd.pos = [e.pos[0], e.pos[1]]
      dd.lat = item.latitude
      dd.lng = item.longitude
      allData.selectHome = item
      console.log(dd)
      infoWindow.open(allData.map, dd)
    }
    // 地图点击定位
    function onMapClick (e) {
      if (props.isShow) return
      console.log('地图点击定位--', e.lnglat.lng, e.lnglat.lat)
      allData.positionObj.lon = e.lnglat.lng
      allData.positionObj.lat = e.lnglat.lat
      setMarker([e.lnglat.lng, e.lnglat.lat]) // 设置当前点位显示
      getAdress([e.lnglat.lng, e.lnglat.lat]) // 经纬度转位置
      calculateNearestMarker(allData.map, e.lnglat)
    }
    // 根据获取到的经纬度进行逆地理编码
    function getAdress (lonLat) {
      const Geocoder = new AMap.Geocoder()
      Geocoder.getAddress(lonLat, (status, result) => {
        console.log('getAdress--', status, result)
        if (status === 'complete' && result.info === 'OK') {
          // address即经纬度转换后的地点名称
          allData.positionObj.address = result.regeocode.formattedAddress
          // allData.township = result.regeocode.addressComponent.towncode
          allData.township = result.regeocode.addressComponent.township
          allData.adcode = result.regeocode.addressComponent.adcode
          console.log('--allData.positionObj.address>>', allData.positionObj.address)
        } else {
          console.log('经纬度转位置失败')
        }
      })
    }
    // 设置标注点位
    function setMarker (lonLat) {
      if (allData.markers) allData.map.remove(allData.markers)
      allData.markers = new AMap.Marker({
        position: lonLat,
        map: allData.map
      })
    }
    // 搜索位置
    function onSearch () {
      // 超过100次调用后,直接在地图上选点
      if (!allData.searchVal.trim()) return
      if (!allData.ifSearchLimit) {
        console.log(allData.searchVal)
        Toast('搜索超出限值,可直接在地图上点击选择')
        return false
      }
      allData.positionArr = []
      allData.placeSearchComponent = new AMap.PlaceSearch()
      allData.placeSearchComponent.search(allData.searchVal, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          allData.showsearchResult = true
          result.poiList.pois.map((item) => {
            allData.positionArr.push({ text: item.address, value: item.id, location: item.location })
          })
          allData.checkedVal = allData.positionArr[0].value
          allData.ifSearchLimit = true
          searchPosition(allData.positionArr[0].value)
        } else {
          allData.showsearchResult = false
          allData.ifSearchLimit = false
          allData.positionArr = []
          Toast('搜索超出限值,可直接在地图上点击选择')
        }
      })
    }
    // 搜索选择一个位置后
    function searchPosition (val) {
      const arr = allData.positionArr.filter((item) => item.value === val)
      allData.positionObj.address = arr[0].text
      allData.positionObj.lon = arr[0].location.lng
      allData.positionObj.lat = arr[0].location.lat
      // allData.township = arr[0]
      calculateNearestMarker(allData.map, [arr[0].location.lng, arr[0].location.lat])
      allData.map.setCenter([arr[0].location.lng, arr[0].location.lat])
      setMarker([arr[0].location.lng, arr[0].location.lat]) // 设置当前点位显示
      getAdress([arr[0].location.lng, arr[0].location.lat])
      console.log('选择位置后---', arr)
    }
    // 关闭返回事件
    function close () {
      context.emit('close', allData.positionObj)
    }

    const confirm = () => {
      context.emit('confirm', { positionObj: { ...allData.positionObj, township: allData.township, adcode: allData.adcode }, selectHome: allData.selectHome })
    }

    return {
      ...toRefs(allData),
      close,
      onSearch,
      searchPosition,
      confirm
    }
    /* eslint-enable */
  }
}
</script>
<style lang="less">
.StationMap {
  #containerMap {
    width: 100%;
    height: calc(100vh - 46px);
  }

  .searchArea {
    position: fixed;
    z-index: 999;
    left: 0px;
    top: 45px;
    width: 100%;
  }

  .amap-logo,
  .amap-copyright {
    display: none !important;
  }

  .van-dropdown-menu {
    background: #fff;
    padding-bottom: 10px;
  }
}

.wrap {
  width: 100%;
  height: fit-content;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding-bottom: 15px;
}
</style>

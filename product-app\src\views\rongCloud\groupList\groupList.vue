<template>
  <div class="groupList">
    <van-dialog v-model:show="addGroupInfo.show"
                :title="addGroupInfo.title"
                :before-close="mBeforeClose"
                show-cancel-button
                :confirm-button-color="appTheme"
                @confirm="confirm"
                confirm-button-text="下一步">
      <van-field required
                 clearable
                 class="van-hairline--surround"
                 v-model="addGroupInfo.value"
                 :placeholder="addGroupInfo.placeholder"></van-field>
    </van-dialog>
    <div v-if="selectUser.length != 0"
         class="sel_warp">
      <div v-for="(uItem,uIndex) in selectUser"
           :key="uIndex"
           class="sel_item">
        <van-image v-if="!uItem.notDel"
                   class="sel_del"
                   fit="cover"
                   round
                   :src="icon_upload_delete"
                   :style="'width:20px;height:20px;'"
                   @click.stop="$general.delItemForKey(uIndex,selectUser)"></van-image>
        <van-image :style="'width:45px;height:45px;'"
                   fit="contain"
                   round
                   :src="uItem.url"></van-image>
        <div class="sel_name text_one2"
             :style="'font-size:12px;'">{{uItem.name}}</div>
      </div>
    </div>
    <!--搜索-->
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div id="search"
           class="search_box">
        <div class="search_warp flex_box">
          <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
            <van-icon :size="16"
                      :color="'#757575'"
                      name="search"></van-icon>
          </div>
          <form class="flex_placeholder flex_box flex_align_center search_input"
                action="javascript:return true;"><input :style="'font-size:13px;'+'padding-left: 0;'"
                   :placeholder="'请输入搜索内容'"
                   maxlength="100"
                   type="search"
                   ref="btnSearch"
                   @keyup.enter="btnSearch()"
                   v-model="keyword" /></form>
        </div>
      </div>
    </van-sticky>
    <!--数据列表-->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="content">
          <van-checkbox-group v-model="checked">
            <div v-for="(item,index) in dataList"
                 :key="index"
                 class="user_item flex_box flex_align_center van-hairline--bottom"
                 @click="openDetails(item)">
              <img :style="'width:35px;height:35px;'"
                   class="user_img"
                   round
                   fit="contain"
                   :src="item.url" />
              <div class="user_name flex_placeholder">{{item.name}}</div>
              <van-checkbox v-if="ifReturn"
                            :name="item.id"
                            :icon-size="20"
                            :checked-color="appTheme"
                            shape="square"></van-checkbox>

            </div>
          </van-checkbox-group>
        </div>
      </van-list>
    </van-pull-refresh>
    <ul v-if="footerBtns.length != 0"
        class="footer_btn_box">
      <div :style="$general.loadConfiguration()">
        <template v-for="(item,index) in footerBtns"
                  :key="index">
          <div class="van-button-box"
               v-if="item.show">
            <van-button :color="appTheme"
                        @click="footerBtnClick(item)">{{item.name}}</van-button>
          </div>
        </template>
      </div>
    </ul>
  </div>
  <van-popup v-model:show="show"
             round
             position="bottom"
             :style="{ height: '90%' }">
    <addressBook ref="userComponts"
                 :data="selectUser"
                 :isSelectUser="true" />
    <footer class="footerBtn">
      <div class="footerBtnBox flex_box">
        <van-button @click="addGroup"
                    type="primary"
                    block>确定</van-button>
      </div>
    </footer>
  </van-popup>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Toast, Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, List, Image as VanImage, Sticky, NavBar } from 'vant'
import addressBook from '../addressBook/addressBook'
// import moment from 'moment'
export default {
  name: 'groupList',
  components: {
    addressBook,
    [Button.name]: Button,
    [Sticky.name]: Sticky,
    [NavBar.name]: NavBar,
    [VanImage.name]: VanImage,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [List.name]: List,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay
  },
  props: {
    isSelectUser: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      icon_upload_delete: require('../../../assets/img/icon_upload_delete.png'),
      icon_default_group: require('../../../assets/img/icon_default_group.png'),
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      addGroupInfo: { show: false, title: '新建群组', placeholder: '请输入群组名称', value: '' }, // 是否新建群组
      show: false,
      // 选择的用户集合 id name url notDel[是否可以删除选择]
      userComponts: null,
      selectUser: [
        // { id: 1, name: '超级管理员', url: '', notDel: true }
      ],
      ifReturn: false, // 是否返回人员
      ifReturnNull: false, // 是否能选择返回空 默认不能
      groupType: 2, // 读书群组先固定传2
      title: route.query.title,
      footerBtns: [{ name: '新增群组', type: 'btn', click: 'add', show: false }], // 底部按钮集合  btn为按钮
      userList: [
      ],
      checked: []
    })
    watch(() => data.keyword, (newName, oldName) => {
      onRefresh()
    })
    onMounted(() => {
      const menus = sessionStorage.getItem('menus')
      if (menus.indexOf('auth:talkroup:add') >= 0) {
        data.footerBtns[0].show = true
      }
      onRefresh()
      obtain()
    })
    const obtain = () => {
      data.ifReturn = props.isSelectUser
      data.selectUser = data.selectUser.concat(props.data)
      data.selectUser.forEach(element => {
        data.checked.push(element.id)
      })
    }
    if (data.title) {
      document.title = data.title
    }
    const getData = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        groupType: data.groupType
      }

      const { data: list, total } = await $api.rongCloud.getGroupList(datas)
      const newData = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = {}; var itemData = _eItem
        item.id = itemData.id || ''// id
        item.name = itemData.groupName || ''// 名字
        item.url = itemData.groupImgUrl || data.icon_default_group // 图片
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (_item, _details) => {
      if (data.ifReturn && !_details) {
        // eslint-disable-next-line eqeqeq
        if (data.ifYourself && data.userId == _item.id) {
          Toast('不能选择自己！')
          return
        }
        var nItem = $general.getItemForKey(_item.id, data.selectUser, 'id')// 找出这个对象看在不在
        // 在就删除这个
        if (nItem) {
          if (!nItem.notDel) { // 为非不可删除时才能删除 否则不能动
            $general.delItemForKey(nItem, data.selectUser, 'id')
            var flag = false
            data.userList.forEach(item => {
              const checkitem = $general.getItemForKey(item.id, data.selectUser, 'id')// 找出这个对象看在不在
              // 在就删除这个
              if (checkitem) {
                if (!checkitem.notDel) {
                  data.isHaveCheckUser = true
                  flag = true
                }
              } else {
                if (!flag) { data.isHaveCheckUser = false }
              }
            })
          }
        } else {
          // eslint-disable-next-line eqeqeq
          if (data.selectMax != 0 && data.selectMax == data.selectUser.length) {
            Toast('最多只能选择' + data.selectMax + '人')
            return
          }
          data.isHaveCheckUser = true
          data.selectUser.push({ id: _item.id, name: _item.name, url: _item.url })
        }
        data.checked = []
        data.selectUser.forEach(element => {
          data.checked.push(element.id)
        })
      } else {
        router.push({
          name: 'chatRoom',
          query: { id: (sessionStorage.getItem('rongCloudIdPrefix') + _item.id), conversationType: 3, name: _item.name }
        })
      }
    }
    const btnSearch = () => {
      onRefresh()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getData()
    }
    // 底部按钮事件
    const footerBtnClick = (_item) => {
      switch (_item.click) {
        case 'add':// 新增群组
          data.addGroupInfo.show = true
          break
      }
    }
    const confirm = () => {
      console.log(data.addGroupInfo.value)
      data.selectUser = [{ id: data.user.id, name: data.user.userName, url: data.user.headImg, notDel: true }]
      data.show = true
    }
    const addGroup = async () => {
      console.log(data.userComponts.selectUser || [])
      const userIds = []
      data.userComponts.selectUser.forEach(element => {
        userIds.push(element.id)
      })
      if (!data.addGroupInfo.value) {
        Toast('请输入群组名')
        return
      }
      const datas = {
        groupName: data.addGroupInfo.value,
        groupType: data.groupType,
        sort: 1
      }
      const { data: addInfo } = await $api.rongCloud.addGroup(datas)
      if (addInfo) {
        const res = await $api.rongCloud.addGroupUser({
          groupId: addInfo,
          groupName: data.addGroupInfo.value,
          userIds: userIds.join(',')
        })
        if (res.errcode === 200) {
          Toast('建群成功')
          onRefresh()
          data.show = false
        }
      }
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onLoad, openDetails, btnSearch, onClickLeft, $general, footerBtnClick, confirm, addGroup }
  }
}
</script>
<style lang="less" scoped>
.groupList {
  width: 100%;
  background: #f5f6f9;
  .user_item {
    background: #fff;
    width: 100%;
    padding: 12px;
    position: relative;
    box-sizing: border-box;
  }
  .user_item {
    padding: 11px 14px;
  }
  .user_img {
    margin-right: 15px;
  }
  .van-dialog__content {
    margin: 8px;
  }
  .van-dialog__content .van-field {
    border: 1px solid #eee;
  }
  .sel_warp {
    padding: 5px;
    background: #fff;
    box-sizing: border-box;
    width: 100%;
    white-space: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .sel_warp .sel_item {
    width: 70px;
    text-align: center;
    display: inline-block;
    position: relative;
    margin: 10px 5px;
    box-sizing: border-box;
  }
  .sel_warp .sel_item .sel_name {
    color: #555;
    margin-top: 5px;
  }
  .sel_warp .sel_del {
    position: absolute;
    top: -5px;
    right: 2px;
    z-index: 99;
  }
}
.footerBtn {
  background: #fff;
  padding: 5px 0;
  position: fixed;
  bottom: 0;
  width: 100%;
  .footerBtnBox {
    width: calc(100% - 20px);
    margin-left: 10px;
    .van-button + .van-button {
      width: calc(100% - 20px);
      margin-left: 10px;
    }
  }
}
</style>

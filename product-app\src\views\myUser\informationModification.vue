<template>
  <div class="informationModification">
    <div class="name_box">
      <div class="red_text">姓名（不可变更）</div>
      <van-field v-model="userName"
                 type="text"
                 clearable
                 disabled
                 placeholder="请输入姓名"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">手机号码（不可变更）</div>
      <van-field v-model="mobile"
                 type="text"
                 clearable
                 disabled
                 placeholder="请输入手机号码"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">单位及职务</div>
      <van-field v-model="position"
                 type="text"
                 clearable
                 placeholder="请输入单位及职务"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">办公电话</div>
      <van-field v-model="officePhone"
                 type="text"
                 clearable
                 placeholder="请输入办公电话"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">通讯地址</div>
      <van-field v-model="callAddress"
                 type="text"
                 clearable
                 placeholder="请输入通讯地址"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">邮政编码</div>
      <van-field v-model="postCode"
                 type="text"
                 clearable
                 placeholder="请输入邮政编码"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">电子邮箱</div>
      <van-field v-model="email"
                 type="text"
                 clearable
                 placeholder="请输入电子邮箱"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">社会荣誉</div>
      <van-field v-model="honorInfo"
                 type="textarea"
                 clearable
                 :row="5"
                 placeholder="请输入社会荣誉"></van-field>
    </div>
    <div class="other_box">
      <div class="red_text">社会贡献</div>
      <van-field v-model="contribute"
                 type="textarea"
                 :row="5"
                 clearable
                 placeholder="请输入社会贡献"></van-field>
    </div>
    <footer v-if="hasFooter">
      <van-button loading-type="spinner"
                  :loading-size="((appFontSize-4)*0.01)+'rem'"
                  size="large"
                  :disabled="ifDisabled"
                  :color="appTheme"
                  @click="submit(false)"
                  :loading="ifLoading"
                  loading-text="提交中..."> {{submitText}}</van-button>
    </footer>
  </div>
</template>
<script>

import { useRoute, useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
import { Dialog, Grid, GridItem, Image as VanImage, ActionSheet, Toast } from 'vant'
export default {
  name: 'informationModification',
  components: {
    [ActionSheet.name]: ActionSheet,
    [VanImage.name]: VanImage,
    [Dialog.Component.name]: Dialog.Component,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const data = reactive({
      appTheme: $appTheme,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id || JSON.parse(sessionStorage.getItem('user').id) || '',
      userName: '',
      mobile: '',
      position: '',
      officePhone: '',
      callAddress: '',
      postCode: '',
      email: '',
      honorInfo: '',
      contribute: '',
      hasFooter: true, // 是否有按钮
      ifDisabled: false, // 是否禁用提交
      ifLoading: false, // 是否正在提交
      submitText: '提交'
    })
    onMounted(() => {
      getUserInfo()
    })
    const getUserInfo = async () => {
      var res = await $api.general.existsinfo()
      var { data: info } = res
      var existsAudit = info.existsAudit
      if (existsAudit) {
        data.ifDisabled = true
        data.submitText = '你的信息正在审核中'
      }
      data.userName = info.audit.userName
      data.mobile = info.audit.mobile
      data.position = info.audit.position
      data.officePhone = info.audit.officePhone
      data.callAddress = info.audit.callAddress
      data.postCode = info.audit.postcode
      data.email = info.audit.email
      data.honorInfo = info.audit.honorInfo
      data.contribute = info.audit.contribute
    }
    const submit = async () => {
      var res = await $api.general.userauditAdd({
        userId: data.user.id,
        mobile: data.mobile,
        position: data.position,
        officePhone: data.officePhone,
        callAddress: data.callAddress,
        postCode: data.postCode,
        email: data.email,
        honorInfo: data.honorInfo,
        contribute: data.contribute
      })
      console.log('res===>>', res)
      if (res.errcode === 200) {
        Toast(res.errmsg)
        data.ifDisabled = true
        data.submitText = '你的信息正在审核中'
        setTimeout(() => {
          router.back()
        }, 2000)
      }
    }
    return { ...toRefs(data), $general, submit }
  }
}
</script>
<style lang="less">
.informationModification {
  width: 100%;
  padding-bottom: 50px;
  background: #f6f6f6;
  height: 100vh;
  .name_box {
    background-color: #fff;
  }
  .other_box {
    background-color: #fff;
    margin-top: 10px;
  }
  .red_text {
    margin-left: 10px;
    color: #ee0a24;
  }
}
</style>

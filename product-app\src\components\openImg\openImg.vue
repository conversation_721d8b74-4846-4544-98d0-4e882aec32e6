<template>
  <div class="openImg">
    <div class="close">
      <van-icon name="close"
                @click="router.go(-1)"
                :size="24" />
    </div>
    <van-swipe class="my-swipe"
               indicator-color="white">
      <van-swipe-item v-for="item in imgList"
                      :key="item"><img :src="item"
             alt=""></van-swipe-item>
    </van-swipe>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ImagePreview, Uploader, Image as VanImage, TreeSelect, Swipe, SwipeItem } from 'vant'
export default ({
  name: 'openImg',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    [TreeSelect.name]: TreeSelect,
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    [Uploader.name]: Uploader
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      module: route.query.module,
      id: route.query.id,
      status: route.query.status,
      imgList: route.query.url.split(','),
      activeId: '',
      activeIndex: '0',
      title: '',
      content: '',
      address: '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      areaIds: '370200',
      username: '',
      mobile: '',
      longitude: '',
      dimensionality: '',
      township: '',
      Uploadmax: 3,
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      mapVisible: false,
      selectTitleList: [],
      selectHome: {},
      messageDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      standbyTwo: '',
      areaTree: [
        // { text: '青岛市', id: '370200', children: [] },
      ],
      areaTrees: [],
      navindex: '0',
      navItem: {}
    })
    onMounted(() => {
      getInfo()
    })
    const getInfo = async () => {
      // ImagePreview({
      //   images: [route.query.url],
      //   closeable: true,
      //   onClose (res) {
      //     console.log(res)
      //   }
      // })
    }
    return { ...toRefs(data), dayjs, route, router, $api, ImagePreview, $general }
  }
})
</script>
<style lang='less'>
:root {
  --van-sidebar-selected-border-color: #006ef1;
  --van-tree-select-item-active-color: #006ef1;
}

.openImg {
  height: 100vh;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;

  // padding: 40px;
  .my-swipe {
    width: 320px;
    height: 500px;
  }

  img {
    max-width: 320px;
    max-height: 500px;
    // margin: 0 10px;
  }

  .close {
    position: fixed;
    top: 10px;
    right: 20px;
  }
}
</style>

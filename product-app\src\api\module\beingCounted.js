import { HTTP } from '../http.js'
class beingCounted extends HTTP {
  // 关注或取消
  attention ({ params, type }) {
    return this.request({
      url: `follow/${type}`,
      data: params
    })
  }

  // 查看全部点赞列表
  getLikeUserInfo (params) {
    return this.request({
      url: 'commentCircle/getLikeUserInfo',
      data: params
    })
  }

  // 删除委员说
  committeesayDels (params) {
    return this.request({
      url: 'committeesay/dels',
      data: params
    })
  }

  // 添加委员说
  addCommittee (params) {
    return this.request({
      url: 'committeesay/add',
      data: params
    })
  }

  // 添加委员说评论
  commentInfoAdd (params) {
    return this.request({
      url: 'commentInfo/add',
      data: params
    })
  }

  // 获取委员说列表
  committeesayAppList (params) {
    return this.request({
      url: 'committeesay/app/list',
      data: params
    })
  }

  // 获取代表履职圈详情
  getRepresentativeDetails (params) {
    return this.request({ url: 'committeesay/info/' + params.id })
  }
}
export {
  beingCounted
}

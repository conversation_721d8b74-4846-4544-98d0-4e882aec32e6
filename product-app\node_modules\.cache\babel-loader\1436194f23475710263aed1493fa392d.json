{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue?vue&type=template&id=53054bfa&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue", "mtime": 1756438117302}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\babel.config.js", "mtime": 1754028950133}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "_ctx", "tabList", "item", "onClick", "$event", "$setup", "tabClick", "_normalizeClass", "leaderDriving_tab_item", "leaderDriving_tab_item_active", "active", "value", "key", "name", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_leader_driving_box", "title", "content", "_withCtx", "_hoisted_5", "generalize", "index", "_normalizeStyle", "color", "num", "_hoisted_6", "_toDisplayString", "<PERSON><PERSON><PERSON>", "length", "_createBlock", "_component_Bar", "id", "list", "officeVos", "_hoisted_7", "representative", "representative<PERSON><PERSON>", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_el_icon", "_component_Bottom", "_hoisted_12", "_component_Top", "representerTeam", "memberEducationData", "_component_Radar", "birthday", "_component_Pie", "_hoisted_13", "_hoisted_14", "sex", "_hoisted_15", "_hoisted_16", "src", "require", "alt", "_hoisted_18", "_hoisted_19", "parseInt", "_hoisted_20", "_hoisted_22", "_hoisted_23", "representerElement", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "AdviceByToday", "_hoisted_28", "AdviceByDomain", "suggest_meet", "suggest_flat", "suggestGoLink", "suggestionFlag", "adviceCount", "transacting", "transactAccomplish", "currentCategoryData", "keywords", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "BySatisfaction", "_component_memory_bar", "_hoisted_36", "SatisfactionByData", "_createCommentVNode", "_hoisted_37", "satisfactionStatus", "_cache", "satisfactionAll", "_component_van_icon", "_component_ranking_list", "urlType", "dataList", "ByRepresentative", "click", "ByDelegation", "_hoisted_38", "_hoisted_39", "findWygzsTitleData", "_hoisted_41", "MessagePage", "_hoisted_43", "areaId", "args", "massMessagesClick", "_hoisted_44", "_hoisted_45", "_hoisted_46", "findStudioCountByCityData", "studioCount", "_hoisted_47", "findWygzsTitlesCountShow", "_component_Pie2", "datas", "percentage", "findWygzsTitlesCountData", "responseRate", "text", "_hoisted_48", "repliedCount", "noRreplyCount", "mapListShow", "_component_Map", "mapList", "findHotspotKeywordsData", "_hoisted_49", "findWygzsTitlesRankingData", "findWygzsStudioTitlesCountData", "_hoisted_50", "_hoisted_51", "representative_tab_item", "representative_tab_active", "cityYear", "years", "representative<PERSON><PERSON>", "Date", "getFullYear", "type", "dutynumList", "router", "push", "_hoisted_52", "size", "show", "_hoisted_53", "dumplingYear", "dumplingTab", "delegationScore", "_hoisted_54", "_component_van_popover", "showPopover", "placemen", "actions", "areas", "onSelect", "reference", "actionsText", "dutynumCityList", "innerHTML", "pageNot", "loadMore", "_hoisted_56", "_hoisted_57", "_hoisted_58", "appToday", "_component_pie_2", "_hoisted_59", "_hoisted_60", "rorfNum", "todayLoginNum", "riseOrFallNum", "_hoisted_61", "rorfTime", "todayLoginTimes", "riseOrFallTimes", "tab", "_hoisted_62", "dynamicTab", "dynamic_tab_item", "dynamic_tab_item_active", "dynamicId", "dynamic", "_hoisted_63", "appLoginActivation", "_component_Line", "status", "_hoisted_64", "install", "_hoisted_65", "isExpanded2", "memberCMemTeamInstallount", "slice", "showCount", "representativeAll2", "_hoisted_66", "subactive", "_hoisted_67", "appLoginActivationByNumTim", "_component_line_2", "appLoginActivationCity", "appLoginActivationByMemOff", "_hoisted_68", "groupActivity", "_hoisted_69", "isExpanded1", "appLoginActivationByTeam", "representativeAll1", "isExpanded", "areaInstall", "<PERSON><PERSON><PERSON>", "_hoisted_70", "istrictEntry", "_hoisted_71", "appLoginByArea", "_hoisted_72", "districtActivity", "appLoginActivationByArea", "_hoisted_74", "frameborder", "_component_demo", "_component_van_popup", "round", "closeable", "height", "width"], "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue"], "sourcesContent": ["<template>\r\n  <div class=\"leaderDriving\">\r\n    <div class=\"leaderDriving_top\">\r\n    </div>\r\n    <div class=\"leaderDriving_tab\">\r\n      <div @click=\"tabClick(item)\"\r\n        :class=\"{ leaderDriving_tab_item: true, leaderDriving_tab_item_active: active == item.value }\"\r\n        v-for=\"item in tabList\" :key=\"item.value\">{{ item.name }}</div>\r\n    </div>\r\n    <div v-if=\"active == 1\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市概括\r\n      </div>\r\n      <leader-driving-box title=\"组织概括\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in generalize\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总用户量\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar1\" v-if=\"representativeVos.length\" :list=\"representativeVos\"></Bar>\r\n          <Bar :color=\"'rgba(255, 123, 49)'\" id=\"bar2\" v-if=\"officeVos.length\" :list=\"officeVos\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"市代表变动情况\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in representative\" :key=\"index\"\r\n              @click=\"representativeClick(item)\">\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                <span class=\"leaderDriving_generalize_item_title_span\">{{ item.title }}</span>\r\n                <span v-if=\"index == 0\">\r\n                  <el-icon style=\"color: #41ce81;\">\r\n                    <Bottom style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n                <span v-else>\r\n                  <el-icon style=\"color: #ff6da2;\">\r\n                    <Top style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_num\" :style=\"{ color: index == 0 ? '#41ce81' : '#ff6da2' }\">\r\n                {{ index == 0 ? '-' : '+' }}{{ item.num }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各代表团人数\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(255, 110, 110)'\" id=\"bar3\" v-if=\"representerTeam.length\" :list=\"representerTeam\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表学历分析\">\r\n        <template v-slot:content>\r\n          <Radar id=\"radar1\" v-if=\"memberEducationData.length\" :list=\"memberEducationData\"></Radar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表年龄分析\">\r\n        <template v-slot:content>\r\n          <Pie :id=\"'pie1'\" :list=\"birthday\" v-if=\"birthday.length\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表性别分析\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie\">\r\n            <div class=\"box_left\">\r\n              <Pie :id=\"'pie2'\" v-if=\"sex.length\" :list=\"sex\"></Pie>\r\n            </div>\r\n            <div class=\"box_right\" v-if=\"sex.length\">\r\n              <div class=\"top\">\r\n                <div><img :src=\"require('../../assets/img/man.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[0].name }}性{{ sex[0].value }}名</span>\r\n                <span style=\"color: #3894ff;\">{{ parseInt(sex[0].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <div><img :src=\"require('../../assets/img/woman.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[1].name }}性{{ sex[1].value }}名</span>\r\n                <span style=\"color: #ff8197;\">{{ parseInt(sex[1].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表结构分析\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar4\" v-if=\"representerElement.length\" :list=\"representerElement\">\r\n          </Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 2\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              今日提交总额\r\n            </div>\r\n            <div class=\"suggest_num\">\r\n              <span style=\"font-weight: 700;\">{{ AdviceByToday }}</span>\r\n              件\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议总数\r\n          </div>\r\n          <div class=\"suggest_box\">\r\n            <div :class=\"{ suggest_meet: index == 0, suggest_flat: index == 1 }\" v-for=\"item, index in AdviceByDomain\"\r\n              :key=\"index\">\r\n              <div class=\"meet_num\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1')\">\r\n                <span>{{ item.adviceCount }}</span>\r\n                件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1020')\">\r\n                正在办理<span>{{ item.transacting }}</span>件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1100')\">\r\n                已办结<span>{{ item.transactAccomplish }}</span>件\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"suggest_title\">\r\n            类别占比\r\n          </div>\r\n          <Pie :id=\"'pie3'\" v-if=\"currentCategoryData.length\" :list=\"currentCategoryData\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in keywords\" :key=\"index\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              满意度\r\n            </div>\r\n            <div class=\"suggest_satisfaction\">\r\n              <div class=\"satisfaction_item\" v-for=\"item, index in ['满意', '基本满意', '不满意']\" :key=\"index\">\r\n                <span :style=\"{ 'background': index == 0 ? '#40cd80' : index == 1 ? '#ffd055' : '#ff6d6d' }\"></span>\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_title\">\r\n            <p>建议满意度</p>\r\n            <template v-if=\"BySatisfaction.length\">\r\n              <memory-bar v-for=\"item, index in BySatisfaction\" :key=\"index\" :item=\"item\"></memory-bar>\r\n            </template>\r\n          </div>\r\n          <div class=\"satisfaction_title\" style=\"border: 0;\">\r\n            <p>类别满意度</p>\r\n            <div class=\"satisfaction_item\" v-for=\"item, index in SatisfactionByData\" :key=\"index\">\r\n              <!-- <span>{{ item.name }}</span> -->\r\n              <memory-bar :item=\"item\"></memory-bar>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_all\">\r\n            <p v-if=\"satisfactionStatus\" @click=\"satisfactionAll(false)\"><van-icon name=\"arrow-up\" />\r\n              收起</p>\r\n            <p v-if=\"!satisfactionStatus\" @click=\"satisfactionAll(true)\"><van-icon name=\"arrow-down\" />\r\n              点击展开查看更多</p>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByRepresentative\" :click=\"true\"\r\n            :title=\"['排行', '姓名', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByDelegation\" :click=\"true\"\r\n            :title=\"['排行', '代表团', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 3\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"message_box\">\r\n            <img :src=\"require('../../assets/img/ldjsc_message.png')\" alt=\"\">\r\n            <template v-if=\"findWygzsTitleData && findWygzsTitleData.length != 0\">\r\n              <div class=\"message\">\r\n                <div v-for=\"(item, index) in findWygzsTitleData\" :key=\"index\" @click=\"MessagePage(item)\">\r\n                  <div v-if=\"index < 2\" class=\"news_text_box_item\">{{ item.title }}</div>\r\n                </div>\r\n              </div>\r\n              <div style=\"color: #7e7d7d;padding: 0.1rem;position: absolute;right: 10px;top: 0;\"\r\n                v-if=\"findWygzsTitleData.length >= 2 && (areaId == '370215' || areaId == '370200')\"\r\n                @click=\"massMessagesClick\"> >\r\n              </div>\r\n            </template>\r\n            <!-- <div class=\"message\"\r\n                 v-if=\"findWygzsTitleData.length\">\r\n              <p v-for=\"item,index in findWygzsTitleData\"\r\n                 :key=\"index\"\r\n                 v-show=\"index < 2\"><span>{{ item.title }}</span><span v-if=\"index == 0\">></span></p>\r\n            </div> -->\r\n            <template v-else>\r\n              <div class=\"messageNull\">\r\n                暂无数据\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            数据概括\r\n          </div>\r\n          <div class=\"interface_location_box\">\r\n            <div class=\"interface_location_left\">\r\n              <div class=\"interface_location_left_title\">\r\n                联络站总数量\r\n              </div>\r\n              <div class=\"interface_location_left_bot\">\r\n                <span>{{ findStudioCountByCityData.studioCount }}</span>个\r\n              </div>\r\n            </div>\r\n            <div class=\"interface_location_right\">\r\n              <Pie2 v-if=\"findWygzsTitlesCountShow\"\r\n                :datas=\"{ percentage: findWygzsTitlesCountData.responseRate, num: findWygzsTitlesCountData.num, text: '回复率', }\"\r\n                id=\"pie2\"></Pie2>\r\n            </div>\r\n          </div>\r\n          <div class=\"interface_location_box_bot\">\r\n            <div>\r\n              <p>总留言数</p>\r\n              <p>{{ (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) ?\r\n                (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>已回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.repliedCount ? findWygzsTitlesCountData.repliedCount : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>未回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.noRreplyCount ? findWygzsTitlesCountData.noRreplyCount : '暂无数据' }}</p>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            联络站分布\r\n          </div>\r\n          <Map v-if=\"mapListShow\" :list=\"mapList\" id=\"maplist\"></Map>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in findHotspotKeywordsData\" :key=\"index\" v-show=\"index < 5\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            区市联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsTitlesRankingData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsStudioTitlesCountData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 4\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            市代表标兵\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear == years }\"\r\n              @click=\"representativeTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear != years }\"\r\n              @click=\"representativeTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"medal\" type=\"resumption\" :dataList=\"dutynumList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"router.push('/performanceFilesList')\">\r\n            查看更多\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团排行\r\n            <van-icon name=\"question-o\" color=\"#d5d5d5\" size=\"24\" @click=\"show = true\" />\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear == years }\"\r\n              @click=\"dumplingTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear != years }\"\r\n              @click=\"dumplingTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"trophy\" :dataList=\"delegationScore\" :title=\"['排行', '代表团', '得分']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区市代表标兵\r\n            <div>\r\n              <van-popover v-model:show=\"showPopover\" placemen=\"bottom-end\" :actions=\"areas\" @select=\"onSelect\">\r\n                <template #reference>\r\n                  <p>{{ actionsText }} <van-icon name=\"play\" style=\"transform: rotate(90deg);\" /></p>\r\n                </template>\r\n              </van-popover>\r\n            </div>\r\n          </div>\r\n          <ranking-list type=\"resumption\" urlType=\"medal\" :dataList=\"dutynumCityList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"notText\" style=\"font-size:14px;color: #ccc;\" v-html=\"pageNot.text\" @click=\"loadMore()\"></div>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 5\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市\r\n      </div>\r\n      <leader-driving-box title=\"总安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie1\">\r\n            <div class=\"box_left\">\r\n              <pie-2 v-if=\"appToday.num\" :datas=\"{ num: appToday.num, text: '总安装率' }\" id=\"pie1\"></pie-2>\r\n            </div>\r\n            <div class=\"box_right\">\r\n              <div class=\"top\">\r\n                <p>今日登录人数\r\n                  <span :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\">{{ appToday.todayLoginNum\r\n                  }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\"> <van-icon name=\"down\"\r\n                    style=\"transform: rotate(-90deg)\" /> 较昨日{{ appToday.rorfNum === 1 ? '增加' : '下降' }}{{\r\n                      appToday.riseOrFallNum }}\r\n                </p>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <p>今日登录人次\r\n                  <span :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\">{{\r\n                    appToday.todayLoginTimes }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\"><van-icon name=\"down\" />\r\n                  较昨日{{ appToday.rorfTime === 1 ? '增加' : '下降' }}{{ appToday.riseOrFallTimes }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: dynamicId == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '1')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line1\" :list=\"appLoginActivation\" v-if=\"appLoginActivation.length\" :status=\"dynamicId\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in install\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团安装率排行\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded2 ? memberCMemTeamInstallount : memberCMemTeamInstallount.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll2\">\r\n            {{ isExpanded2 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"青岛市本级活跃度分析\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: subactive == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '2')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            登录人数/人次\r\n          </div>\r\n          <line-2 id=\"lines1\" v-if=\"appLoginActivationByNumTim.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByNumTim\"></line-2>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            活跃度\r\n          </div>\r\n          <Line id=\"line2\" v-if=\"appLoginActivationCity.length\" :status=\"subactive\" :list=\"appLoginActivationCity\">\r\n          </Line>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            机关、代表活跃度\r\n          </div>\r\n          <Line id=\"line3\" v-if=\"appLoginActivationByMemOff.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByMemOff\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团活跃度排行\" color=\"#e2eeff\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: groupActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '3')\"> 本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\"\r\n            :dataList=\"isExpanded1 ? appLoginActivationByTeam : appLoginActivationByTeam.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '活跃度']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll1\">\r\n            {{ isExpanded1 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市各区\r\n      </div>\r\n      <leader-driving-box title=\"总安装率排名\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded ? areaInstall : areaInstall.slice(0, showCount)\"\r\n            :title=\"['排行', '区市', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll\">\r\n            {{ isExpanded ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市登录情况\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: istrictEntry == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '4')\">本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <line-2 id=\"lines2\" v-if=\"appLoginByArea.length\" :status=\"istrictEntry\" :list=\"appLoginByArea\"></line-2>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: districtActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '5')\">\r\n              <div>本{{ item.name }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line4\" v-if=\"appLoginActivationByArea.length\" :status=\"districtActivity\"\r\n            :list=\"appLoginActivationByArea\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 6\">\r\n      <iframe src=\"http://120.221.72.187:9003/cockpit/#/\" frameborder=\"0\"\r\n        style=\"width: 100%;height: 680px;z-index: 99999;-webkit-overflow-scrolling: touch; overflow: scroll;\"></iframe>\r\n    </div>\r\n    <demo></demo>\r\n    <van-popup close-icon=\"close\" round v-model:show=\"show\" closeable :style=\"{ height: '13%', width: '90%' }\">\r\n      <div class=\"popup_con\">\r\n        <div class=\"popup_con_title\">\r\n          提示\r\n        </div>\r\n        <div class=\"info\">代表团积分=代表团中代表之和/总人数</div>\r\n      </div>\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'\r\nimport LeaderDrivingBox from './components/leaderDrivingBox.vue'\r\nimport Bar from './components/bar.vue'\r\nimport Pie from './components/pie.vue'\r\nimport RankingList from './components/rankingList.vue'\r\nimport Map from './components/map.vue'\r\nimport Line from './components/line.vue'\r\nimport Pie2 from './components/pie2.vue'\r\nimport Line2 from './components/line2.vue'\r\nimport Radar from './components/radar.vue'\r\nimport MemoryBar from './components/memoryBar.vue'\r\nimport Demo from './components/demo.vue'\r\nexport default {\r\n  name: 'leaderDriving',\r\n  components: {\r\n    LeaderDrivingBox,\r\n    Bar,\r\n    Pie,\r\n    RankingList,\r\n    Map,\r\n    Line,\r\n    Pie2,\r\n    Line2,\r\n    Radar,\r\n    MemoryBar,\r\n    Demo,\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Circle.name]: Circle\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      pageNot: { text: '' },\r\n      pageNo: 1,\r\n      pageSize: 5,\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId')),\r\n      areas: [],\r\n      areaIdStatus: '',\r\n      years: new Date().getFullYear(),\r\n      showPopover: false,\r\n      actionsText: '',\r\n      active: sessionStorage.getItem('leaderActive') || '1',\r\n      tabList: [{\r\n        name: '组织情况',\r\n        value: '1'\r\n      }, {\r\n        name: '建议情况',\r\n        value: '2'\r\n      }, {\r\n        name: '联络站',\r\n        value: '3'\r\n      }, {\r\n        name: '履职报表',\r\n        value: '4'\r\n      }, {\r\n        name: '运行情况',\r\n        value: '5'\r\n      }, {\r\n        name: '信访情况',\r\n        value: '6'\r\n      }],\r\n      generalize: [\r\n        {\r\n          num: '',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      install: [\r\n        {\r\n          num: '0',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      representative: [\r\n        {\r\n          num: 1,\r\n          title: '出缺代表',\r\n          key: '1',\r\n          type: 'hasVacant'\r\n        },\r\n        {\r\n          num: 1,\r\n          title: '新增代表',\r\n          key: '2',\r\n          type: ''\r\n        }\r\n      ],\r\n      keywordsList: ['教育', '产业链'],\r\n      keywords: ['教育', '产业链', '农业'],\r\n      mapList: [],\r\n      mapListShow: false,\r\n      rate: 50,\r\n      cityYear: new Date().getFullYear(),\r\n      dumplingYear: 2025,\r\n      show: false,\r\n      dynamicTab: [{\r\n        name: '日',\r\n        id: '1'\r\n      }, {\r\n        name: '月',\r\n        id: '2'\r\n      }],\r\n      dynamicId: '1',\r\n      subactive: '1',\r\n      groupActivity: '1',\r\n      istrictEntry: '1',\r\n      districtActivity: '1',\r\n      representativeText: '点击查看更多',\r\n      satisfactionStatus: false,\r\n      sex: [],\r\n      birthday: [],\r\n      party: [],\r\n      representerElement: [],\r\n      representerTeam: [],\r\n      memberEducationData: [],\r\n      officeVos: [],\r\n      representativeVos: [],\r\n      AdviceByToday: '0',\r\n      AdviceByDomain: [],\r\n      currentCategoryData: [],\r\n      BySatisfaction: [],\r\n      SatisfactionBy: [],\r\n      SatisfactionByData: [],\r\n      ByRepresentative: [],\r\n      ByDelegation: [],\r\n      findWygzsTitleData: [],\r\n      findStudioCountByCityData: {},\r\n      findWygzsTitlesCountData: {},\r\n      findWygzsTitlesCountShow: false,\r\n      findHotspotKeywordsData: {},\r\n      findWygzsTitlesRankingData: [],\r\n      findWygzsStudioTitlesCountData: [],\r\n      dutynumList: [],\r\n      delegationScore: [],\r\n      dutynumCityList: [],\r\n      appToday: {\r\n        todayLoginNum: '', // 今日登录人数\r\n        rorfNum: '', // 较昨日上升或下降\r\n        riseOrFallNum: '', // 上升或下降数量\r\n        todayLoginTimes: '', // 今日登陆人次\r\n        rorfTime: '', // 较昨日上升或下降\r\n        riseOrFallTimes: '', // 上升或下降数量\r\n        num: 0\r\n      },\r\n      appLoginActivation: [],\r\n      appInstall: [],\r\n      areaInstall: [],\r\n      showCount: 5, // 控制展示的数据数量，默认为5\r\n      isExpanded: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded1: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded2: false, // 控制是否展开全部数据，默认为false\r\n      memberCMemTeamInstallount: [],\r\n      appLoginActivationByNumTim: [],\r\n      appLoginActivationCity: [],\r\n      appLoginActivationByMemOff: [],\r\n      appLoginActivationByTeam: [],\r\n      appLoginByArea: [],\r\n      appLoginActivationByArea: []\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      const areaList = JSON.parse(sessionStorage.getItem('areas'))\r\n      data.areas = areaList.map(item => {\r\n        return {\r\n          text: item.name,\r\n          id: item.id,\r\n          name: item.name\r\n        }\r\n      })\r\n      // data.areas.splice(0, 1)\r\n      data.actionsText = data.areas[0].name\r\n      data.areaIdStatus = data.areas[0].id\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    })\r\n    // 组织情况\r\n    const organization = () => {\r\n      getMemberCount()\r\n      memberEducation()\r\n      getOrganization()\r\n      memberChange()\r\n    }\r\n    // 建议情况\r\n    const recommendation = () => {\r\n      getAdviceByToday()\r\n      getAdviceByDomain()\r\n      currentCategory()\r\n      keywords()\r\n      getAdviceBySatisfaction()\r\n      getNumberByRepresentative()\r\n      getNumberByDelegation()\r\n    }\r\n    // 联络站\r\n    const interfaceLocation = () => {\r\n      getMapList()\r\n      findWygzsTitleList()\r\n      findStudioCountByCity()\r\n      findWygzsTitlesCount()\r\n      findHotspotKeywords()\r\n      findWygzsTitlesRanking()\r\n      findWygzsStudioTitlesCount()\r\n    }\r\n    // 履职报表\r\n    const PerformanceReport = () => {\r\n      dutynumList(2025)\r\n      delegationScore()\r\n      dutynumCityList()\r\n    }\r\n    // 运行情况\r\n    const runningCondition = () => {\r\n      appTodayLogin()\r\n      appAllInstall()\r\n      appLoginActivation()\r\n      appInstall()\r\n      memberCMemTeamInstallount()\r\n      appLoginActivationByNumTim()\r\n      appLoginActivationCity()\r\n      appLoginActivationByMemOff()\r\n      appLoginActivationByTeam()\r\n      areaInstall()\r\n      appLoginByArea()\r\n      appLoginActivationByArea()\r\n    }\r\n    const getMapList = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByDistrict({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.mapList = res.data\r\n      data.mapListShow = true\r\n    }\r\n    const representativeTab = (y) => {\r\n      data.cityYear = y\r\n      dutynumList(y)\r\n    }\r\n    const dumplingTab = (y) => {\r\n      data.dumplingYear = y\r\n      delegationScore(y)\r\n    }\r\n    const tabClick = (item) => {\r\n      sessionStorage.setItem('leaderActive', item.value)\r\n      data.active = sessionStorage.getItem('leaderActive')\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    }\r\n    const onSelect = (item) => {\r\n      data.actionsText = item.text\r\n      data.areaIdStatus = item.id\r\n      dutynumCityList()\r\n    }\r\n    const dynamic = (id, type) => {\r\n      switch (type) {\r\n        case '1':\r\n          data.dynamicId = id\r\n          appLoginActivation(id)\r\n          break\r\n        case '2':\r\n          data.subactive = id\r\n          appLoginActivationByNumTim(id)\r\n          appLoginActivationCity(id)\r\n          appLoginActivationByMemOff(id)\r\n          break\r\n        case '3':\r\n          data.groupActivity = id\r\n          appLoginActivationByTeam(id)\r\n          break\r\n        case '4':\r\n          data.istrictEntry = id\r\n          appLoginByArea(id)\r\n          break\r\n        case '5':\r\n          data.districtActivity = id\r\n          appLoginActivationByArea(id)\r\n          break\r\n      }\r\n    }\r\n    const satisfactionAll = (type) => {\r\n      data.satisfactionStatus = type\r\n      if (data.satisfactionStatus) {\r\n        data.SatisfactionByData = data.SatisfactionBy\r\n      } else {\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getMemberCount = async () => {\r\n      var res = await $api.leaderDriving.memberCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      if (res.data) {\r\n        data.sex = res.data.sex.map((item, index) => {\r\n          return {\r\n            name: item.name,\r\n            value: item.amount,\r\n            key: item.key,\r\n            itemStyle: { color: index === 0 ? '#3da2ff' : '#ff738c' }\r\n          }\r\n        })\r\n        data.birthday = res.data.birthday.map(item => {\r\n          return {\r\n            key: item.key,\r\n            name: item.name,\r\n            value: item.amount\r\n          }\r\n        })\r\n        data.party = res.data.party.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        })\r\n        data.representerElement = res.data.representerElement.map(item => {\r\n          return {\r\n            value: item.amount,\r\n            key: item.key,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n        data.representerTeam = res.data.representerTeam.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        }).reverse()\r\n      }\r\n      // console.log(data.birthday)\r\n      // console.log('getMemberCount', res.data)\r\n    }\r\n    const memberEducation = async () => {\r\n      var res = await $api.leaderDriving.memberEducation({})\r\n      if (res.data) {\r\n        data.memberEducationData = res.data.map(item => {\r\n          return {\r\n            value: item.value,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const getOrganization = async () => {\r\n      var res = await $api.leaderDriving.getOrganization({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.officeVos = res.data.officeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.representativeVos = res.data.representativeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.generalize[0].num = res.data.totalNumber\r\n      data.generalize[1].num = res.data.representativeNumber\r\n      data.generalize[2].num = res.data.officeNumber\r\n    }\r\n    const memberChange = async () => {\r\n      var res = await $api.leaderDriving.memberChange({})\r\n      data.representative[0].num = res.data.vacantNum\r\n      data.representative[1].num = res.data.repairNum\r\n    }\r\n    const getAdviceByToday = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByToday({ personCode: '' })\r\n      if (res.result) {\r\n        data.AdviceByToday = res.result\r\n      }\r\n    }\r\n    const getAdviceByDomain = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByDomain({})\r\n      if (res.result) {\r\n        data.AdviceByDomain = res.result.reverse()\r\n      }\r\n    }\r\n    const suggestGoLink = (type, mType) => {\r\n      // if (mType) {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&manageType=${mType}&token={{token}}`\r\n      // } else {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&token={{token}}`\r\n      // }\r\n    }\r\n    const currentCategory = async () => {\r\n      var res = await $api.leaderDriving.currentCategory({ type: '' })\r\n      if (res.result) {\r\n        data.currentCategoryData = res.result.map(item => {\r\n          return {\r\n            name: item.name,\r\n            proportion: item.proportion,\r\n            value: item.value,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_cate?type=${item.code}&token={{token}}`\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const keywords = async () => {\r\n      var res = await $api.leaderDriving.keywords({})\r\n      if (res) {\r\n        data.keywordsList = res.data.filter(item => item !== '青岛市')\r\n      }\r\n    }\r\n    const getAdviceBySatisfaction = async () => {\r\n      var res = await $api.leaderDriving.getAdviceBySatisfaction({ type: '' })\r\n      var ress = await $api.leaderDriving.getSatisfactionByCategory({ type: '' })\r\n      if (res) {\r\n        data.BySatisfaction = [res.result].map(item => {\r\n          return {\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionBy = ress.result.map(item => {\r\n          return {\r\n            name: item.suggestName,\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getNumberByRepresentative = async () => {\r\n      var res = await $api.leaderDriving.getNumberByRepresentative({ type: '' })\r\n      if (res) {\r\n        data.ByRepresentative = res.result.map(item => {\r\n          return {\r\n            num: item.issueCount,\r\n            name: item.name,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_mylist?personCode=${item.userCode}&token={{token}}`\r\n          }\r\n        }).slice(0, 5)\r\n      }\r\n    }\r\n    const getNumberByDelegation = async () => {\r\n      var res = await $api.leaderDriving.getNumberByDelegation({ type: '' })\r\n      if (res) {\r\n        data.ByDelegation = res.result.map(item => {\r\n          if (item.delegationName !== '解放军代表团') {\r\n            return {\r\n              num: item.adviceTotal,\r\n              name: item.delegationName,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_group?groupId=${item.delegationCode}&token={{token}}`\r\n            }\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const findWygzsTitleList = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })\r\n      data.findWygzsTitleData = res.data\r\n    }\r\n    const findStudioCountByCity = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByCity({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findStudioCountByCityData = res.data[0]\r\n    }\r\n    const findWygzsTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesCountData = res.data\r\n      data.findWygzsTitlesCountData.num = parseFloat(data.findWygzsTitlesCountData.responseRate.replace('%', ''))\r\n      data.findWygzsTitlesCountShow = true\r\n    }\r\n    const findHotspotKeywords = async () => {\r\n      var res = await $api.leaderDriving.findHotspotKeywords({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findHotspotKeywordsData = res.data.filter(item => item !== '测试')\r\n      // console.log('findHotspotKeywords', res.data)\r\n    }\r\n    const findWygzsTitlesRanking = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesRanking({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesRankingData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      })\r\n    }\r\n    const findWygzsStudioTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsStudioTitlesCount({})\r\n      data.findWygzsStudioTitlesCountData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      }).splice(0, 10)\r\n    }\r\n    const dutynumList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: '1',\r\n        pageSize: '5',\r\n        year: y,\r\n        areaId: data.areaId\r\n      })\r\n      data.dutynumList = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username,\r\n          year: y,\r\n          userid: item.userid\r\n        }\r\n      }).splice(0, 5)\r\n    }\r\n    const delegationScore = async (y) => {\r\n      var res = await $api.leaderDriving.delegationScore({\r\n        pageNo: '1',\r\n        pageSize: '10',\r\n        year: data.years\r\n      })\r\n      data.delegationScore = res.data.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.delegationview\r\n        }\r\n      })\r\n    }\r\n    const dutynumCityList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        year: new Date().getFullYear(),\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.pageNot.text = res && res.errcode !== 200 ? res.errmsg || res.data : ''\r\n      var a = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username\r\n        }\r\n      })\r\n      data.dutynumCityList = data.dutynumCityList.concat(a)\r\n      var LOAD_MORE = '点击加载更多'\r\n      var LOAD_ALL = '已加载完'\r\n      data.pageNot.text = data.dutynumCityList.length === 0 ? '' : res.data.dutyNumListVos.length >= data.pageSize ? LOAD_MORE : LOAD_ALL\r\n    }\r\n    const appTodayLogin = async (y) => {\r\n      var res = await $api.leaderDriving.appTodayLogin({\r\n        areaId: data.areaId\r\n      })\r\n      data.appToday.todayLoginNum = Number(res.data.todayLoginNum) // 今日登录人数\r\n      data.appToday.rorfNum = Number(res.data.rorfNum) // 较昨日上升或下降\r\n      data.appToday.riseOrFallNum = Number(res.data.riseOrFallNum) // 上升或下降数量\r\n      data.appToday.todayLoginTimes = Number(res.data.todayLoginTimes) // 今日登陆人次\r\n      data.appToday.rorfTime = Number(res.data.rorfTime) // 较昨日上升或下降\r\n      data.appToday.riseOrFallTimes = Number(res.data.riseOrFallTimes) // 上升或下降数量\r\n    }\r\n    const appAllInstall = async (y) => {\r\n      var res = await $api.leaderDriving.appAllInstall({\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.appToday.num = Number(res.data.rate.replace('%', ''))\r\n    }\r\n    const appLoginActivation = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId == '370215' ? data.areaId : '' }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivation = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appInstall = async () => {\r\n      var res = await $api.leaderDriving.appInstall({ areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.install[0].num = res.data.totalInstall\r\n        data.install[1].num = res.data.memberInstall\r\n        data.install[2].num = res.data.officeInstall\r\n      }\r\n    }\r\n    const areaInstall = async () => {\r\n      var res = await $api.leaderDriving.areaInstall({ areaId: data.areaId })\r\n      if (res) {\r\n        data.areaInstall = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const memberCMemTeamInstallount = async () => {\r\n      var res = await $api.leaderDriving.memberCMemTeamInstallount({ areaId: data.areaId })\r\n      if (res) {\r\n        data.memberCMemTeamInstallount = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByNumTim = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByNumTim({ type: t, areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivationByNumTim = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            nums: item.times\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationCity = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationCity = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByMemOff = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByMemOff({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationByMemOff = res.data.map(item => {\r\n          return {\r\n            numMem: item.numMem,\r\n            numOff: item.numOff,\r\n            name: item.time,\r\n            activationOff: item.activationOff,\r\n            activationMem: item.activationMem\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByTeam = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByTeam({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByTeam = res.data.map(item => {\r\n          return {\r\n            num: item.activation,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            times: item.times\r\n          }\r\n        })\r\n        console.log('data.appLoginByArea===>', data.appLoginByArea)\r\n      }\r\n    }\r\n    const appLoginActivationByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const MessagePage = async (_item) => {\r\n      window.location.href = `http://120.221.72.187:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`\r\n    }\r\n    const massMessagesClick = async () => {\r\n      router.push({ name: 'messageMorePage' })\r\n    }\r\n    const loadMore = async () => {\r\n      var LOAD_MORE = '点击加载更多'\r\n      var NET_ERR = '网络不小心断开了'\r\n      var LOAD_ING = '加载中，请稍候...'\r\n      if ((data.pageNot.text === LOAD_MORE || data.pageNot.text === NET_ERR) && data.pageNo !== 1) {\r\n        data.pageNot.text = LOAD_ING\r\n        data.pageNo++\r\n        dutynumCityList()\r\n      } else {\r\n        data.pageNo = data.pageNo + 1\r\n        dutynumCityList()\r\n      }\r\n    }\r\n    const representativeAll = () => {\r\n      data.isExpanded = !data.isExpanded\r\n    }\r\n    const representativeAll1 = () => {\r\n      data.isExpanded1 = !data.isExpanded1\r\n    }\r\n    const representativeAll2 = () => {\r\n      data.isExpanded2 = !data.isExpanded2\r\n    }\r\n    const representativeClick = (_item) => {\r\n      router.push({ name: 'peopleList', query: { key: _item.key, type: _item.type } })\r\n    }\r\n    return { ...toRefs(data), loadMore, MessagePage, massMessagesClick, representativeAll, representativeAll1, representativeAll2, suggestGoLink, general, confirm, tabClick, representativeTab, dumplingTab, onSelect, dynamic, router, satisfactionAll, representativeClick }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.leaderDriving {\r\n  background: #f8f8f8;\r\n  box-sizing: border-box;\r\n  padding: 15px 10px 10px 10px;\r\n  height: 100%;\r\n\r\n  .satisfaction_title {\r\n    width: 95%;\r\n    margin: 10px 10px 0 10px;\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid #d8d8d8;\r\n  }\r\n\r\n  .satisfaction_item {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    >span {\r\n      font-size: 14px;\r\n      display: inline-block;\r\n      width: 25%;\r\n    }\r\n  }\r\n\r\n  .satisfaction_all {\r\n    text-align: center;\r\n    color: #3894ff;\r\n    margin: 15px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .dynamic_tab {\r\n    width: 100%;\r\n    height: 100%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n\r\n    .dynamic_tab_item {\r\n      width: 50%;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .dynamic_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .sex_pie1 {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 50%;\r\n      height: 120px;\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // justify-content: space-around;\r\n      font-size: 16px;\r\n\r\n      .top {\r\n        display: flex;\r\n        // align-items: center;\r\n        flex-direction: column;\r\n        margin-bottom: 10px;\r\n        font-size: 16px;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        // align-items: center;\r\n        font-size: 16px;\r\n        flex-direction: column;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .popup_con {\r\n    margin: 0px 10px;\r\n\r\n    .popup_con_title {\r\n      text-align: center;\r\n      font-size: 20px;\r\n      margin: 10px 0;\r\n      font-weight: 700;\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .representative_all {\r\n    width: 100%;\r\n    text-align: center;\r\n    color: #a2a2a2;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .representative_tab {\r\n    width: 94%;\r\n    height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n    margin: 0 10px;\r\n\r\n    .representative_tab_item {\r\n      flex: 1;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      color: #3894ff;\r\n      text-align: center;\r\n    }\r\n\r\n    .representative_tab_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .interface_location_box_bot {\r\n    width: 100%;\r\n    height: 80px;\r\n    background: #f8fbfe;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    text-align: center;\r\n\r\n    >div {\r\n      flex: 1;\r\n\r\n      >p {\r\n        margin: 10px 0;\r\n      }\r\n\r\n      p:nth-child(1) {\r\n        color: #8c9fb7;\r\n      }\r\n\r\n      p:nth-child(2) {\r\n        font-weight: 700;\r\n      }\r\n    }\r\n  }\r\n\r\n  .interface_location_box {\r\n    width: 100%;\r\n    height: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 10px;\r\n\r\n    .interface_location_left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      height: 100%;\r\n\r\n      .interface_location_left_title {\r\n        color: #747474;\r\n      }\r\n\r\n      .interface_location_left_bot {\r\n        >span {\r\n          font-weight: 700;\r\n          color: #3894ff;\r\n          font-size: 45px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .interface_location_right {\r\n      width: 37%;\r\n      height: 90px;\r\n      position: relative;\r\n      margin-right: 30px;\r\n\r\n      .text {\r\n        position: absolute;\r\n        top: 26px;\r\n        left: 22px;\r\n        text-align: center;\r\n\r\n        >p:nth-child(1) {\r\n          font-weight: 700;\r\n          font-size: 20px;\r\n        }\r\n\r\n        >p:nth-child(2) {\r\n          font-size: 12px;\r\n          color: #a2a2a2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_satisfaction {\r\n    width: 65%;\r\n    margin: 0 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .satisfaction_item {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 14px;\r\n\r\n      >span {\r\n        width: 14px;\r\n        height: 14px;\r\n        display: inline-block;\r\n        margin: 0 5px;\r\n        border-radius: 7px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message_box {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    >img {\r\n      height: 50px;\r\n      width: 50px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .message {\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    margin: 0 10px;\r\n\r\n    .news_text_box_item {\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 1;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 2px 0;\r\n      font-size: 15px;\r\n    }\r\n\r\n    p:nth-child(1) {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n\r\n  .messageNull {\r\n    text-align: center;\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #aeaeae;\r\n  }\r\n\r\n  .content_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .hotWord {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    height: 35px;\r\n    padding: 5px 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    .hotWord_item {\r\n      display: flex;\r\n      width: 70%;\r\n      align-items: center;\r\n\r\n      .index {\r\n        margin: 0 10px 0 0;\r\n      }\r\n    }\r\n\r\n    .hotWord_right {\r\n      color: #fff;\r\n      // padding: 3px;\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 3px;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .suggest_box {\r\n    display: flex;\r\n    color: #fff;\r\n\r\n    .suggest_transaction {\r\n      margin-left: 10px;\r\n      margin-bottom: 5px;\r\n\r\n      >span {\r\n        font-size: 22px;\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .suggest_meet {\r\n      flex: 1;\r\n      margin: 0 5px;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg1.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .suggest_flat {\r\n      margin: 0 5px;\r\n      flex: 1;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg2.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_title {\r\n    height: 24px;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    margin: 5px 10px 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    >div {\r\n      width: 29%;\r\n      color: #3894ff;\r\n    }\r\n  }\r\n\r\n  .suggest_num {\r\n    color: #3894ff;\r\n    margin-right: 10px;\r\n\r\n    >span {\r\n      font-size: 28px;\r\n    }\r\n  }\r\n\r\n  .sex_pie {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 60%;\r\n      height: 120px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      font-size: 18px;\r\n\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .leaderDriving_top {\r\n    width: 100%;\r\n    height: 100px;\r\n    // margin: 15px 10px 0;\r\n    background: url(\"../../assets/img/ldjsc_head_bg.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  .leaderDriving_tab {\r\n    margin-top: 10px;\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_tab_item {\r\n      width: 60px;\r\n      height: 60px;\r\n      background: #fff;\r\n      border-radius: 30px;\r\n      text-align: center;\r\n      box-sizing: border-box;\r\n      padding: 10px;\r\n      box-shadow: 0px 5px 15px -3px rgba(138, 138, 138, 0.1);\r\n    }\r\n\r\n    .leaderDriving_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .leaderDriving_title {\r\n    width: 100%;\r\n    height: 30px;\r\n    margin: 10px 0;\r\n    color: #3894ff;\r\n    padding-left: 10px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    position: relative;\r\n  }\r\n\r\n  .leaderDriving_title::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    height: 18px;\r\n    width: 4px;\r\n    top: 4px;\r\n    left: 0px;\r\n    background: #3894ff;\r\n    border-radius: 1px;\r\n  }\r\n\r\n  .leaderDriving_generalize {\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_generalize_item {\r\n      // width: 32%;\r\n      flex: 1;\r\n      height: 100%;\r\n      // padding-left: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .leaderDriving_generalize_item_num {\r\n        font-size: 28px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title {\r\n        color: #8196af;\r\n        // display: flex;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title_span {}\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EAGnBA,KAAK,EAAC;AAAmB;;;;;;EAWnBA,KAAK,EAAC;AAA0B;;EAM5BA,KAAK,EAAC;AAAqC;;EAkB/CA,KAAK,EAAC;AAA0B;;;EAG5BA,KAAK,EAAC;AAAqC;;EACxCA,KAAK,EAAC;AAA0C;;;;;;;;EAoCvDA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAU;;;EAGhBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAK;;;EAERC,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;EACvBA,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;EAE1BD,KAAK,EAAC;AAAK;;;EAERC,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;EACvBA,KAAuB,EAAvB;IAAA;EAAA;AAAuB;;;;;EAgB9BD,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAa;;EAChBC,KAAyB,EAAzB;IAAA;EAAA;AAAyB;;EAW9BD,KAAK,EAAC;AAAa;;;;;EA2BjBA,KAAK,EAAC;AAAc;;EAgBtBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAsB;;EAO9BA,KAAK,EAAC;AAAoB;;EAM1BA,KAAK,EAAC,oBAAoB;EAACC,KAAkB,EAAlB;IAAA;EAAA;;;EAO3BD,KAAK,EAAC;AAAkB;;;;;EA8BxBA,KAAK,EAAC;AAAa;;;EAGfA,KAAK,EAAC;AAAS;;;;EAEMA,KAAK,EAAC;;;EA2B/BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAyB;;EAI7BA,KAAK,EAAC;AAA6B;;EAIrCA,KAAK,EAAC;AAA0B;;EAMlCA,KAAK,EAAC;AAA4B;;EA+BhCA,KAAK,EAAC;AAAc;;;;;EAuCtBA,KAAK,EAAC;AAAoB;;EAe1BA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAoB;;EAW1BA,KAAK,EAAC;AAAe;;;;;;EAsBrBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EAGhBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EAUXA,KAAK,EAAC;AAAK;;EAcfA,KAAK,EAAC;AAAa;;;EAcnBA,KAAK,EAAC;AAA0B;;EAM5BA,KAAK,EAAC;AAAqC;;EAmB/CA,KAAK,EAAC;AAAa;;;EAyBnBA,KAAK,EAAC;AAAa;;;EA6BnBA,KAAK,EAAC;AAAa;;;EAWnBA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;;;;;;;;;;uBA1gBhCE,mBAAA,CAoiBM,OApiBNC,UAoiBM,G,4BAniBJC,mBAAA,CACM;IADDJ,KAAK,EAAC;EAAmB,4BAE9BI,mBAAA,CAIM,OAJNC,UAIM,I,kBAHJH,mBAAA,CAEiEI,SAAA,QAAAC,WAAA,CAAhDC,IAAA,CAAAC,OAAO,EAAfC,IAAI;yBAFbR,mBAAA,CAEiE;MAF3DS,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAC,QAAQ,CAACJ,IAAI;MACvBV,KAAK,EAAAe,eAAA;QAAAC,sBAAA;QAAAC,6BAAA,EAAiET,IAAA,CAAAU,MAAM,IAAIR,IAAI,CAACS;MAAK;MAClEC,GAAG,EAAEV,IAAI,CAACS;wBAAUT,IAAI,CAACW,IAAI,gCAAAC,UAAA;oCAE/Cd,IAAA,CAAAU,MAAM,S,cAAjBhB,mBAAA,CA+FM,OAAAqB,UAAA,G,4BA9FJnB,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAqB,GAAC,QAEjC,qBACAwB,YAAA,CAcqBC,6BAAA;IAdDC,KAAK,EAAC;EAAM;IACbC,OAAO,EAAAC,QAAA,CACtB,MAUM,CAVNxB,mBAAA,CAUM,OAVNyB,UAUM,I,kBATJ3B,mBAAA,CAQMI,SAAA,QAAAC,WAAA,CAR2DC,IAAA,CAAAsB,UAAU,GAAzBpB,IAAI,EAAEqB,KAAK;2BAA7D7B,mBAAA,CAQM;QARDF,KAAK,EAAC,+BAA+B;QAAoCoB,GAAG,EAAEW;UACjF3B,mBAAA,CAGM;QAHDJ,KAAK,EAAC,mCAAmC;QAC3CC,KAAK,EAAA+B,eAAA;UAAAC,KAAA,EAAWF,KAAK,oBAAoBA,KAAK;QAAA;0BAC5CrB,IAAI,CAACwB,GAAG,yBAEb9B,mBAAA,CAEM,OAFN+B,UAEM,EAAAC,gBAAA,CADD1B,IAAI,CAACgB,KAAK,iB;;;MAMvBF,YAAA,CAKqBC,6BAAA;IALDC,KAAK,EAAC;EAAM;IACbC,OAAO,EAAAC,QAAA,CACtB,MAA6G,CAA1DpB,IAAA,CAAA6B,iBAAiB,CAACC,MAAM,I,cAA3EC,YAAA,CAA6GC,cAAA;;MAAvGP,KAAK,EAAE,oBAAoB;MAAEQ,EAAE,EAAC,MAAM;MAAkCC,IAAI,EAAElC,IAAA,CAAA6B;4EACjC7B,IAAA,CAAAmC,SAAS,CAACL,MAAM,I,cAAnEC,YAAA,CAA6FC,cAAA;;MAAvFP,KAAK,EAAE,oBAAoB;MAAEQ,EAAE,EAAC,MAAM;MAA0BC,IAAI,EAAElC,IAAA,CAAAmC;;;kCAGhFvC,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAqB,GAAC,SAEjC,qBACAwB,YAAA,CAwBqBC,6BAAA;IAxBDC,KAAK,EAAC;EAAS;IAChBC,OAAO,EAAAC,QAAA,CACtB,MAoBM,CApBNxB,mBAAA,CAoBM,OApBNwC,UAoBM,I,kBAnBJ1C,mBAAA,CAkBMI,SAAA,QAAAC,WAAA,CAlB2DC,IAAA,CAAAqC,cAAc,GAA7BnC,IAAI,EAAEqB,KAAK;2BAA7D7B,mBAAA,CAkBM;QAlBDF,KAAK,EAAC,+BAA+B;QAAwCoB,GAAG,EAAEW,KAAK;QACzFpB,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAiC,mBAAmB,CAACpC,IAAI;UAChCN,mBAAA,CAYM,OAZN2C,UAYM,GAXJ3C,mBAAA,CAA8E,QAA9E4C,WAA8E,EAAAZ,gBAAA,CAApB1B,IAAI,CAACgB,KAAK,kBACxDK,KAAK,S,cAAjB7B,mBAAA,CAIO,QAAA+C,WAAA,GAHLzB,YAAA,CAEU0B,kBAAA;QAFDjD,KAAuB,EAAvB;UAAA;QAAA;MAAuB;0BAC9B,MAAyE,CAAzEuB,YAAA,CAAyE2B,iBAAA;UAAjElD,KAA8D,EAA9D;YAAA;YAAA;YAAA;UAAA;QAA8D,G;;6BAG1EC,mBAAA,CAIO,QAAAkD,WAAA,GAHL5B,YAAA,CAEU0B,kBAAA;QAFDjD,KAAuB,EAAvB;UAAA;QAAA;MAAuB;0BAC9B,MAAsE,CAAtEuB,YAAA,CAAsE6B,cAAA;UAAjEpD,KAA8D,EAA9D;YAAA;YAAA;YAAA;UAAA;QAA8D,G;;eAIzEG,mBAAA,CAEM;QAFDJ,KAAK,EAAC,mCAAmC;QAAEC,KAAK,EAAA+B,eAAA;UAAAC,KAAA,EAAWF,KAAK;QAAA;0BAChEA,KAAK,qBAAAK,gBAAA,CAAuB1B,IAAI,CAACwB,GAAG,wB;;;MAMjDV,YAAA,CAIqBC,6BAAA;IAJDC,KAAK,EAAC;EAAQ;IACfC,OAAO,EAAAC,QAAA,CACtB,MAA0G,CAAtDpB,IAAA,CAAA8C,eAAe,CAAChB,MAAM,I,cAA1EC,YAAA,CAA0GC,cAAA;;MAApGP,KAAK,EAAE,qBAAqB;MAAEQ,EAAE,EAAC,MAAM;MAAgCC,IAAI,EAAElC,IAAA,CAAA8C;;;MAGvF9B,YAAA,CAIqBC,6BAAA;IAJDC,KAAK,EAAC;EAAQ;IACfC,OAAO,EAAAC,QAAA,CACtB,MAAyF,CAAhEpB,IAAA,CAAA+C,mBAAmB,CAACjB,MAAM,I,cAAnDC,YAAA,CAAyFiB,gBAAA;;MAAlFf,EAAE,EAAC,QAAQ;MAAoCC,IAAI,EAAElC,IAAA,CAAA+C;;;MAGhE/B,YAAA,CAIqBC,6BAAA;IAJDC,KAAK,EAAC;EAAQ;IACfC,OAAO,EAAAC,QAAA,CACtB,MAAgE,CAAvBpB,IAAA,CAAAiD,QAAQ,CAACnB,MAAM,I,cAAxDC,YAAA,CAAgEmB,cAAA;;MAA1DjB,EAAE,EAAE,MAAM;MAAGC,IAAI,EAAElC,IAAA,CAAAiD;;;MAG7BjC,YAAA,CAoBqBC,6BAAA;IApBDC,KAAK,EAAC;EAAQ;IACfC,OAAO,EAAAC,QAAA,CACtB,MAgBM,CAhBNxB,mBAAA,CAgBM,OAhBNuD,WAgBM,GAfJvD,mBAAA,CAEM,OAFNwD,WAEM,GADoBpD,IAAA,CAAAqD,GAAG,CAACvB,MAAM,I,cAAlCC,YAAA,CAAsDmB,cAAA;;MAAhDjB,EAAE,EAAE,MAAM;MAAqBC,IAAI,EAAElC,IAAA,CAAAqD;8EAEhBrD,IAAA,CAAAqD,GAAG,CAACvB,MAAM,I,cAAvCpC,mBAAA,CAWM,OAXN4D,WAWM,GAVJ1D,mBAAA,CAIM,OAJN2D,WAIM,GAHJ3D,mBAAA,CAAkE,cAA7DA,mBAAA,CAAuD;MAAjD4D,GAAG,EAAEC,OAAO;MAA8BC,GAAG,EAAC;4CACzD9D,mBAAA,CAA0E,QAA1E+D,WAA0E,EAAA/B,gBAAA,CAAzC5B,IAAA,CAAAqD,GAAG,IAAIxC,IAAI,IAAG,GAAC,GAAAe,gBAAA,CAAG5B,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,IAAG,GAAC,iBACnEf,mBAAA,CAAwG,QAAxGgE,WAAwG,EAAAhC,gBAAA,CAAvEiC,QAAQ,CAAC7D,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,IAAIX,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,GAAGX,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,YAAW,GAAC,gB,GAEnGf,mBAAA,CAIM,OAJNkE,WAIM,GAHJlE,mBAAA,CAAoE,cAA/DA,mBAAA,CAAyD;MAAnD4D,GAAG,EAAEC,OAAO;MAAgCC,GAAG,EAAC;4CAC3D9D,mBAAA,CAA0E,QAA1EmE,WAA0E,EAAAnC,gBAAA,CAAzC5B,IAAA,CAAAqD,GAAG,IAAIxC,IAAI,IAAG,GAAC,GAAAe,gBAAA,CAAG5B,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,IAAG,GAAC,iBACnEf,mBAAA,CAAwG,QAAxGoE,WAAwG,EAAApC,gBAAA,CAAvEiC,QAAQ,CAAC7D,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,IAAIX,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,GAAGX,IAAA,CAAAqD,GAAG,IAAI1C,KAAK,YAAW,GAAC,gB;;MAM3GK,YAAA,CAKqBC,6BAAA;IALDC,KAAK,EAAC;EAAQ;IACfC,OAAO,EAAAC,QAAA,CACtB,MACM,CAD6CpB,IAAA,CAAAiE,kBAAkB,CAACnC,MAAM,I,cAA5EC,YAAA,CACMC,cAAA;;MADAP,KAAK,EAAE,oBAAoB;MAAEQ,EAAE,EAAC,MAAM;MAAmCC,IAAI,EAAElC,IAAA,CAAAiE;;;6CAKhFjE,IAAA,CAAAU,MAAM,S,cAAjBhB,mBAAA,CAgHM,OAAAwE,WAAA,GA/GJlD,YAAA,CAYqBC,6BAAA;IAXFE,OAAO,EAAAC,QAAA,CACtB,MAQM,CARNxB,mBAAA,CAQM,OARNuE,WAQM,G,4BAPJvE,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,UAE3B,qBACAI,mBAAA,CAGM,OAHNwE,WAGM,GAFJxE,mBAAA,CAA0D,QAA1DyE,WAA0D,EAAAzC,gBAAA,CAAvB5B,IAAA,CAAAsE,aAAa,kB,6CAAU,KAE5D,oB;;MAINtD,YAAA,CAyBqBC,6BAAA;IAxBFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,QAE3B,qBACAI,mBAAA,CAcM,OAdN2E,WAcM,I,kBAbJ7E,mBAAA,CAYMI,SAAA,QAAAC,WAAA,CAZqFC,IAAA,CAAAwE,cAAc,GAA7BtE,IAAI,EAAEqB,KAAK;2BAAvF7B,mBAAA,CAYM;QAZAF,KAAK,EAAAe,eAAA;UAAAkE,YAAA,EAAkBlD,KAAK;UAAAmD,YAAA,EAAqBnD,KAAK;QAAA;QACzDX,GAAG,EAAEW;UACN3B,mBAAA,CAGM;QAHDJ,KAAK,EAAC,UAAU;QAAEW,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsE,aAAa,CAACzE,IAAI,CAAC0E,cAAc;UAC7DhF,mBAAA,CAAmC,cAAAgC,gBAAA,CAA1B1B,IAAI,CAAC2E,WAAW,kB,6CAAU,KAErC,oB,+BACAjF,mBAAA,CAEM;QAFDJ,KAAK,EAAC,qBAAqB;QAAEW,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsE,aAAa,CAACzE,IAAI,CAAC0E,cAAc;uDAA8B,OAClG,qBAAAhF,mBAAA,CAAmC,cAAAgC,gBAAA,CAA1B1B,IAAI,CAAC4E,WAAW,kB,6CAAU,IACzC,oB,+BACAlF,mBAAA,CAEM;QAFDJ,KAAK,EAAC,qBAAqB;QAAEW,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsE,aAAa,CAACzE,IAAI,CAAC0E,cAAc;uDAA8B,MACnG,qBAAAhF,mBAAA,CAA0C,cAAAgC,gBAAA,CAAjC1B,IAAI,CAAC6E,kBAAkB,kB,6CAAU,IAC/C,oB;kEAGJnF,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,QAE3B,qBACwBQ,IAAA,CAAAgF,mBAAmB,CAAClD,MAAM,I,cAAlDC,YAAA,CAAsFmB,cAAA;;MAAhFjB,EAAE,EAAE,MAAM;MAAqCC,IAAI,EAAElC,IAAA,CAAAgF;;;MAG/DhE,YAAA,CAmBqBC,6BAAA;IAlBFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,QAE3B,sB,kBACAE,mBAAA,CAYMI,SAAA,QAAAC,WAAA,CAZqCC,IAAA,CAAAiF,QAAQ,GAAvB/E,IAAI,EAAEqB,KAAK;2BAAvC7B,mBAAA,CAYM;QAZDF,KAAK,EAAC,SAAS;QAAkCoB,GAAG,EAAEW;UACzD3B,mBAAA,CAKM,OALNsF,WAKM,GAJJtF,mBAAA,CAEuB;QAFlBJ,KAAK,EAAC,OAAO;QACfC,KAAK,EAAA+B,eAAA;UAAA,SAAaD,KAAK,oBAAoBA,KAAK,oBAAoBA,KAAK;QAAA;0BACvEA,KAAK,6B,iBAAa,GACvB,GAAAK,gBAAA,CAAG1B,IAAI,iB,GAIDqB,KAAK,Y,cAFb7B,mBAAA,CAIM;;QAJDF,KAAK,EAAC,eAAe;QACvBC,KAAK,EAAA+B,eAAA;UAAA,cAAkBD,KAAK,oBAAoBA,KAAK;QAAA;SACjC,KAEvB,oB;;;MAINP,YAAA,CAiCqBC,6BAAA;IAhCFE,OAAO,EAAAC,QAAA,CACtB,MAUM,CAVNxB,mBAAA,CAUM,OAVNuF,WAUM,G,4BATJvF,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,OAE3B,qBACAI,mBAAA,CAKM,OALNwF,WAKM,I,cAJJ1F,mBAAA,CAGMI,SAAA,QAAAC,WAAA,CAH+C,qBAAqB,GAApCG,IAAI,EAAEqB,KAAK;aAAjD3B,mBAAA,CAGM;QAHDJ,KAAK,EAAC,mBAAmB;QAA+CoB,GAAG,EAAEW;UAChF3B,mBAAA,CAAoG;QAA7FH,KAAK,EAAA+B,eAAA;UAAA,cAAkBD,KAAK,oBAAoBA,KAAK;QAAA;gDAAwC,GACpG,GAAAK,gBAAA,CAAG1B,IAAI,iB;wCAIbN,mBAAA,CAKM,OALNyF,WAKM,G,4BAJJzF,mBAAA,CAAY,WAAT,OAAK,qBACQI,IAAA,CAAAsF,cAAc,CAACxD,MAAM,I,kBACnCpC,mBAAA,CAAyFI,SAAA;MAAAc,GAAA;IAAA,GAAAb,WAAA,CAAvDC,IAAA,CAAAsF,cAAc,GAA7BpF,IAAI,EAAEqB,KAAK;2BAA9BQ,YAAA,CAAyFwD,qBAAA;QAAtC3E,GAAG,EAAEW,KAAK;QAAGrB,IAAI,EAAEA;;0EAG1EN,mBAAA,CAMM,OANN4F,WAMM,G,4BALJ5F,mBAAA,CAAY,WAAT,OAAK,sB,kBACRF,mBAAA,CAGMI,SAAA,QAAAC,WAAA,CAH+CC,IAAA,CAAAyF,kBAAkB,GAAjCvF,IAAI,EAAEqB,KAAK;2BAAjD7B,mBAAA,CAGM;QAHDF,KAAK,EAAC,mBAAmB;QAA4CoB,GAAG,EAAEW;UAC7EmE,mBAAA,kCAAqC,EACrC1E,YAAA,CAAsCuE,qBAAA;QAAzBrF,IAAI,EAAEA;MAAI,kC;sCAG3BN,mBAAA,CAKM,OALN+F,WAKM,GAJK3F,IAAA,CAAA4F,kBAAkB,I,cAA3BlG,mBAAA,CACQ;;MADsBS,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAAyF,eAAe;QAAS9E,YAAA,CAA4B+E,mBAAA;MAAlBlF,IAAI,EAAC;IAAU,I,6CAAG,KACrF,oB,yCACMb,IAAA,CAAA4F,kBAAkB,I,cAA5BlG,mBAAA,CACc;;MADiBS,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAAyF,eAAe;QAAQ9E,YAAA,CAA8B+E,mBAAA;MAApBlF,IAAI,EAAC;IAAY,I,6CAAG,WACjF,oB;;MAIhBG,YAAA,CAQqBC,6BAAA;IAPFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,cAE3B,qBACAwB,YAAA,CAC6CgF,uBAAA;MAD/BC,OAAO,EAAC,OAAO;MAAEC,QAAQ,EAAElG,IAAA,CAAAmG,gBAAgB;MAAGC,KAAK,EAAE,IAAI;MACpElF,KAAK,EAAE;;;MAGdF,YAAA,CAQqBC,6BAAA;IAPFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,eAE3B,qBACAwB,YAAA,CAC8CgF,uBAAA;MADhCC,OAAO,EAAC,OAAO;MAAEC,QAAQ,EAAElG,IAAA,CAAAqG,YAAY;MAAGD,KAAK,EAAE,IAAI;MAChElF,KAAK,EAAE;;;6CAILlB,IAAA,CAAAU,MAAM,S,cAAjBhB,mBAAA,CAiHM,OAAA4G,WAAA,GAhHJtF,YAAA,CA4BqBC,6BAAA;IA3BFE,OAAO,EAAAC,QAAA,CACtB,MAwBM,CAxBNxB,mBAAA,CAwBM,OAxBN2G,WAwBM,GAvBJ3G,mBAAA,CAAiE;MAA3D4D,GAAG,EAAEC,OAAO;MAAwCC,GAAG,EAAC;0CAC9C1D,IAAA,CAAAwG,kBAAkB,IAAIxG,IAAA,CAAAwG,kBAAkB,CAAC1E,MAAM,S,cAA/DpC,mBAAA,CAUWI,SAAA;MAAAc,GAAA;IAAA,IATThB,mBAAA,CAIM,OAJN6G,WAIM,I,kBAHJ/G,mBAAA,CAEMI,SAAA,QAAAC,WAAA,CAFuBC,IAAA,CAAAwG,kBAAkB,GAAlCtG,IAAI,EAAEqB,KAAK;2BAAxB7B,mBAAA,CAEM;QAF4CkB,GAAG,EAAEW,KAAK;QAAGpB,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAqG,WAAW,CAACxG,IAAI;UACzEqB,KAAK,Q,cAAhB7B,mBAAA,CAAuE,OAAvEiH,WAAuE,EAAA/E,gBAAA,CAAnB1B,IAAI,CAACgB,KAAK,oB;sCAI1DlB,IAAA,CAAAwG,kBAAkB,CAAC1E,MAAM,UAAU9B,IAAA,CAAA4G,MAAM,gBAAgB5G,IAAA,CAAA4G,MAAM,iB,cADvElH,mBAAA,CAGM;;MAHDD,KAA6E,EAA7E;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA,CAA6E;MAE/EU,OAAK,EAAA0F,MAAA,QAAAA,MAAA,UAAAgB,IAAA,KAAExG,MAAA,CAAAyG,iBAAA,IAAAzG,MAAA,CAAAyG,iBAAA,IAAAD,IAAA,CAAiB;OAAE,KAC7B,K,gFAQFnH,mBAAA,CAIWI,SAAA;MAAAc,GAAA;IAAA,IAVX8E,mBAAA,yTAKU,E,4BAER9F,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAa,GAAC,QAEzB,oB;;MAKRwB,YAAA,CAoCqBC,6BAAA;IAnCFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,QAE3B,qBACAI,mBAAA,CAcM,OAdNmH,WAcM,GAbJnH,mBAAA,CAOM,OAPNoH,WAOM,G,4BANJpH,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAA+B,GAAC,UAE3C,qBACAI,mBAAA,CAEM,OAFNqH,WAEM,GADJrH,mBAAA,CAAwD,cAAAgC,gBAAA,CAA/C5B,IAAA,CAAAkH,yBAAyB,CAACC,WAAW,kB,6CAAU,IAC1D,oB,KAEFvH,mBAAA,CAIM,OAJNwH,WAIM,GAHQpH,IAAA,CAAAqH,wBAAwB,I,cAApCtF,YAAA,CAEmBuF,eAAA;;MADhBC,KAAK;QAAAC,UAAA,EAAgBxH,IAAA,CAAAyH,wBAAwB,CAACC,YAAY;QAAAhG,GAAA,EAAO1B,IAAA,CAAAyH,wBAAwB,CAAC/F,GAAG;QAAAiG,IAAA;MAAA;MAC9F1F,EAAE,EAAC;iFAGTrC,mBAAA,CAcM,OAdNgI,WAcM,GAbJhI,mBAAA,CAIM,c,4BAHJA,mBAAA,CAAW,WAAR,MAAI,qBACPA,mBAAA,CACkG,WAAAgC,gBAAA,CAD3F5B,IAAA,CAAAyH,wBAAwB,CAACI,YAAY,GAAG7H,IAAA,CAAAyH,wBAAwB,CAACK,aAAa,GAAsB9H,IAAA,CAAAyH,wBAAwB,CAACI,YAAY,GAAG7H,IAAA,CAAAyH,wBAAwB,CAACK,aAAa,0B,GAG3LlI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAW,WAAR,MAAI,qBACPA,mBAAA,CAAmG,WAAAgC,gBAAA,CAA7F5B,IAAA,CAAAyH,wBAAwB,CAACI,YAAY,GAAG7H,IAAA,CAAAyH,wBAAwB,CAACI,YAAY,0B,GAErFjI,mBAAA,CAGM,c,4BAFJA,mBAAA,CAAW,WAAR,MAAI,qBACPA,mBAAA,CAAqG,WAAAgC,gBAAA,CAA/F5B,IAAA,CAAAyH,wBAAwB,CAACK,aAAa,GAAG9H,IAAA,CAAAyH,wBAAwB,CAACK,aAAa,0B;;MAK7F9G,YAAA,CAOqBC,6BAAA;IANFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,SAE3B,qBACWQ,IAAA,CAAA+H,WAAW,I,cAAtBhG,YAAA,CAA2DiG,cAAA;;MAAlC9F,IAAI,EAAElC,IAAA,CAAAiI,OAAO;MAAEhG,EAAE,EAAC;;;MAG/CjB,YAAA,CAmBqBC,6BAAA;IAlBFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,QAE3B,sB,kBACAE,mBAAA,CAYMI,SAAA,QAAAC,WAAA,CAZqCC,IAAA,CAAAkI,uBAAuB,GAAtChI,IAAI,EAAEqB,KAAK;4CAAvC7B,mBAAA,CAYM;QAZDF,KAAK,EAAC,SAAS;QAAiDoB,GAAG,EAAEW;UACxE3B,mBAAA,CAKM,OALNuI,WAKM,GAJJvI,mBAAA,CAEuB;QAFlBJ,KAAK,EAAC,OAAO;QACfC,KAAK,EAAA+B,eAAA;UAAA,SAAaD,KAAK,oBAAoBA,KAAK,oBAAoBA,KAAK;QAAA;0BACvEA,KAAK,6B,iBAAa,GACvB,GAAAK,gBAAA,CAAG1B,IAAI,iB,GAIDqB,KAAK,Y,cAFb7B,mBAAA,CAIM;;QAJDF,KAAK,EAAC,eAAe;QACvBC,KAAK,EAAA+B,eAAA;UAAA,cAAkBD,KAAK,oBAAoBA,KAAK;QAAA;SACjC,KAEvB,oB,gDAXuFA,KAAK,M;;;MAelGP,YAAA,CAQqBC,6BAAA;IAPFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,YAE3B,qBACAwB,YAAA,CAC+CgF,uBAAA;MADjCC,OAAO,EAAC,OAAO;MAAEC,QAAQ,EAAElG,IAAA,CAAAoI,0BAA0B;MAChElH,KAAK,EAAE;;;MAGdF,YAAA,CAQqBC,6BAAA;IAPFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,YAE3B,qBACAwB,YAAA,CAC+CgF,uBAAA;MADjCC,OAAO,EAAC,OAAO;MAAEC,QAAQ,EAAElG,IAAA,CAAAqI,8BAA8B;MACpEnH,KAAK,EAAE;;;6CAILlB,IAAA,CAAAU,MAAM,S,cAAjBhB,mBAAA,CAmDM,OAAA4I,WAAA,GAlDJtH,YAAA,CAiBqBC,6BAAA;IAhBFE,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAe,GAAC,SAE3B,qBACAI,mBAAA,CAKM,OALN2I,WAKM,GAJJ3I,mBAAA,CACiE;MAD3DJ,KAAK,EAAAe,eAAA;QAAAiI,uBAAA;QAAAC,yBAAA,EAA8DzI,IAAA,CAAA0I,QAAQ,IAAI1I,IAAA,CAAA2I;MAAK;MACvFxI,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAAuI,iBAAiB,KAAKC,IAAI,GAAGC,WAAW;OAAK,MAAI,kBAC3DlJ,mBAAA,CAC0C;MADpCJ,KAAK,EAAAe,eAAA;QAAAiI,uBAAA;QAAAC,yBAAA,EAA8DzI,IAAA,CAAA0I,QAAQ,IAAI1I,IAAA,CAAA2I;MAAK;MACvFxI,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAAuI,iBAAiB;OAAM,KAAG,iB,GAEtC5H,YAAA,CAC6CgF,uBAAA;MAD/BC,OAAO,EAAC,OAAO;MAAC8C,IAAI,EAAC,YAAY;MAAE7C,QAAQ,EAAElG,IAAA,CAAAgJ,WAAW;MACnE9H,KAAK,EAAE;2CACVtB,mBAAA,CAEM;MAFDJ,KAAK,EAAC,oBAAoB;MAAEW,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAA4I,MAAM,CAACC,IAAI;OAA2B,QAE9E,E;;MAGJlI,YAAA,CAcqBC,6BAAA;IAbFE,OAAO,EAAAC,QAAA,CACtB,MAGM,CAHNxB,mBAAA,CAGM,OAHNuJ,WAGM,G,6CAHqB,SAEzB,qBAAAnI,YAAA,CAA6E+E,mBAAA;MAAnElF,IAAI,EAAC,YAAY;MAACY,KAAK,EAAC,SAAS;MAAC2H,IAAI,EAAC,IAAI;MAAEjJ,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEJ,IAAA,CAAAqJ,IAAI;UAEpEzJ,mBAAA,CAKM,OALN0J,WAKM,GAJJ1J,mBAAA,CAC2D;MADrDJ,KAAK,EAAAe,eAAA;QAAAiI,uBAAA;QAAAC,yBAAA,EAA8DzI,IAAA,CAAAuJ,YAAY,IAAIvJ,IAAA,CAAA2I;MAAK;MAC3FxI,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAAmJ,WAAW,KAAKX,IAAI,GAAGC,WAAW;OAAK,MAAI,kBACrDlJ,mBAAA,CACoC;MAD9BJ,KAAK,EAAAe,eAAA;QAAAiI,uBAAA;QAAAC,yBAAA,EAA8DzI,IAAA,CAAAuJ,YAAY,IAAIvJ,IAAA,CAAA2I;MAAK;MAC3FxI,OAAK,EAAA0F,MAAA,QAAAA,MAAA,MAAAzF,MAAA,IAAEC,MAAA,CAAAmJ,WAAW;OAAM,KAAG,iB,GAEhCxI,YAAA,CAAuGgF,uBAAA;MAAzFC,OAAO,EAAC,QAAQ;MAAEC,QAAQ,EAAElG,IAAA,CAAAyJ,eAAe;MAAGvI,KAAK,EAAE;;;MAGvEF,YAAA,CAgBqBC,6BAAA;IAfFE,OAAO,EAAAC,QAAA,CACtB,MASM,CATNxB,mBAAA,CASM,OATN8J,WASM,G,6CATqB,WAEzB,qBAAA9J,mBAAA,CAMM,cALJoB,YAAA,CAIc2I,sBAAA;MAJON,IAAI,EAAErJ,IAAA,CAAA4J,WAAW;2DAAX5J,IAAA,CAAA4J,WAAW,GAAAxJ,MAAA;MAAEyJ,QAAQ,EAAC,YAAY;MAAEC,OAAO,EAAE9J,IAAA,CAAA+J,KAAK;MAAGC,QAAM,EAAE3J,MAAA,CAAA2J;;MAC3EC,SAAS,EAAA7I,QAAA,CAClB,MAAmF,CAAnFxB,mBAAA,CAAmF,Y,kCAA7EI,IAAA,CAAAkK,WAAW,IAAG,GAAC,iBAAAlJ,YAAA,CAA0D+E,mBAAA;QAAhDlF,IAAI,EAAC,MAAM;QAACpB,KAAiC,EAAjC;UAAA;QAAA;;;4DAKnDuB,YAAA,CAC6CgF,uBAAA;MAD/B+C,IAAI,EAAC,YAAY;MAAC9C,OAAO,EAAC,OAAO;MAAEC,QAAQ,EAAElG,IAAA,CAAAmK,eAAe;MACvEjJ,KAAK,EAAE;2CACVtB,mBAAA,CAAyG;MAApGJ,KAAK,EAAC,SAAS;MAACC,KAAmC,EAAnC;QAAA;QAAA;MAAA,CAAmC;MAAC2K,SAAqB,EAAbpK,IAAA,CAAAqK,OAAO,CAAC1C,IAAI;MAAGxH,OAAK,EAAA0F,MAAA,SAAAA,MAAA,OAAAzF,MAAA,IAAEC,MAAA,CAAAiK,QAAQ;;;6CAI1FtK,IAAA,CAAAU,MAAM,S,cAAjBhB,mBAAA,CAuJM,OAAA6K,WAAA,G,4BAtJJ3K,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAqB,GAAC,MAEjC,qBACAwB,YAAA,CA4BqBC,6BAAA;IA5BDC,KAAK,EAAC;EAAM;IACbC,OAAO,EAAAC,QAAA,CACtB,MAwBM,CAxBNxB,mBAAA,CAwBM,OAxBN4K,WAwBM,GAvBJ5K,mBAAA,CAEM,OAFN6K,WAEM,GADSzK,IAAA,CAAA0K,QAAQ,CAAChJ,GAAG,I,cAAzBK,YAAA,CAA0F4I,gBAAA;;MAA9DpD,KAAK;QAAA7F,GAAA,EAAS1B,IAAA,CAAA0K,QAAQ,CAAChJ,GAAG;QAAAiG,IAAA;MAAA;MAAkB1F,EAAE,EAAC;+EAE7ErC,mBAAA,CAmBM,OAnBNgL,WAmBM,GAlBJhL,mBAAA,CASM,OATNiL,WASM,GARJjL,mBAAA,CAGI,Y,6CAHD,SACD,qBAAAA,mBAAA,CACS;MADFH,KAAK,EAAA+B,eAAA;QAAA,SAAaxB,IAAA,CAAA0K,QAAQ,CAACI,OAAO;MAAA;wBAAqC9K,IAAA,CAAA0K,QAAQ,CAACK,aAAa,wB,GAGtGnL,mBAAA,CAGI;MAHAH,KAAK,EAAA+B,eAAA;QAAA,SAAaxB,IAAA,CAAA0K,QAAQ,CAACI,OAAO;MAAA;QAAmC9J,YAAA,CACjC+E,mBAAA;MAD2ClF,IAAI,EAAC,MAAM;MAC1FpB,KAAiC,EAAjC;QAAA;MAAA;yBAAoC,MAAI,GAAAmC,gBAAA,CAAG5B,IAAA,CAAA0K,QAAQ,CAACI,OAAO,wBAAAlJ,gBAAA,CACzD5B,IAAA,CAAA0K,QAAQ,CAACM,aAAa,iB,oBAG9BpL,mBAAA,CAOM,OAPNqL,WAOM,GANJrL,mBAAA,CAGI,Y,6CAHD,SACD,qBAAAA,mBAAA,CACoC;MAD7BH,KAAK,EAAA+B,eAAA;QAAA,SAAaxB,IAAA,CAAA0K,QAAQ,CAACQ,QAAQ;MAAA;wBACxClL,IAAA,CAAA0K,QAAQ,CAACS,eAAe,wB,GAE5BvL,mBAAA,CACkF;MAD9EH,KAAK,EAAA+B,eAAA;QAAA,SAAaxB,IAAA,CAAA0K,QAAQ,CAACQ,QAAQ;MAAA;QAAkClK,YAAA,CAAwB+E,mBAAA;MAAdlF,IAAI,EAAC;IAAM,I,iBAAG,MAC5F,GAAAe,gBAAA,CAAG5B,IAAA,CAAA0K,QAAQ,CAACQ,QAAQ,wBAAAtJ,gBAAA,CAA0B5B,IAAA,CAAA0K,QAAQ,CAACU,eAAe,iB;;MAMrFpK,YAAA,CAUqBC,6BAAA;IAVDC,KAAK,EAAC;EAAM;IACbmK,GAAG,EAAAjK,QAAA,CAClB,MAGM,CAHNxB,mBAAA,CAGM,OAHN0L,WAGM,I,kBAFJ5L,mBAAA,CACgGI,SAAA,QAAAC,WAAA,CAA/EC,IAAA,CAAAuL,UAAU,EAAlBrL,IAAI;2BADbR,mBAAA,CACgG;QAD1FF,KAAK,EAAAe,eAAA;UAAAiL,gBAAA;UAAAC,uBAAA,EAAqDzL,IAAA,CAAA0L,SAAS,IAAIxL,IAAI,CAAC+B;QAAE;QACtDrB,GAAG,EAAEV,IAAI,CAAC+B,EAAE;QAAG9B,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsL,OAAO,CAACzL,IAAI,CAAC+B,EAAE;0BAAW/B,IAAI,CAACW,IAAI,gCAAA+K,WAAA;;IAG1EzK,OAAO,EAAAC,QAAA,CACtB,MAAwG,CAAtDpB,IAAA,CAAA6L,kBAAkB,CAAC/J,MAAM,I,cAA3EC,YAAA,CAAwG+J,eAAA;;MAAlG7J,EAAE,EAAC,OAAO;MAAEC,IAAI,EAAElC,IAAA,CAAA6L,kBAAkB;MAAoCE,MAAM,EAAE/L,IAAA,CAAA0L;;;kCAG1F9L,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAqB,GAAC,SAEjC,qBACAwB,YAAA,CAcqBC,6BAAA;IAdDC,KAAK,EAAC;EAAK;IACZC,OAAO,EAAAC,QAAA,CACtB,MAUM,CAVNxB,mBAAA,CAUM,OAVNoM,WAUM,I,kBATJtM,mBAAA,CAQMI,SAAA,QAAAC,WAAA,CAR2DC,IAAA,CAAAiM,OAAO,GAAtB/L,IAAI,EAAEqB,KAAK;2BAA7D7B,mBAAA,CAQM;QARDF,KAAK,EAAC,+BAA+B;QAAiCoB,GAAG,EAAEW;UAC9E3B,mBAAA,CAGM;QAHDJ,KAAK,EAAC,mCAAmC;QAC3CC,KAAK,EAAA+B,eAAA;UAAAC,KAAA,EAAWF,KAAK,oBAAoBA,KAAK;QAAA;0BAC5CrB,IAAI,CAACwB,GAAG,yBAEb9B,mBAAA,CAEM,OAFNsM,WAEM,EAAAtK,gBAAA,CADD1B,IAAI,CAACgB,KAAK,iB;;;MAMvBF,YAAA,CASqBC,6BAAA;IATDC,KAAK,EAAC,UAAU;IAACO,KAAK,EAAC;;IACxBN,OAAO,EAAAC,QAAA,CACtB,MAE+C,CAF/CJ,YAAA,CAE+CgF,uBAAA;MAFjCC,OAAO,EAAC,QAAQ;MAACxE,KAAK,EAAC,SAAS;MAC3CyE,QAAQ,EAAElG,IAAA,CAAAmM,WAAW,GAAGnM,IAAA,CAAAoM,yBAAyB,GAAGpM,IAAA,CAAAoM,yBAAyB,CAACC,KAAK,IAAIrM,IAAA,CAAAsM,SAAS;MAChGpL,KAAK,EAAE;2CACVtB,mBAAA,CAEM;MAFDJ,KAAK,EAAC,oBAAoB;MAAEW,OAAK,EAAA0F,MAAA,SAAAA,MAAA,WAAAgB,IAAA,KAAExG,MAAA,CAAAkM,kBAAA,IAAAlM,MAAA,CAAAkM,kBAAA,IAAA1F,IAAA,CAAkB;wBACrD7G,IAAA,CAAAmM,WAAW,mC;;MAIpBnL,YAAA,CAwBqBC,6BAAA;IAxBDC,KAAK,EAAC;EAAY;IACnBmK,GAAG,EAAAjK,QAAA,CAClB,MAGM,CAHNxB,mBAAA,CAGM,OAHN4M,WAGM,I,kBAFJ9M,mBAAA,CACgGI,SAAA,QAAAC,WAAA,CAA/EC,IAAA,CAAAuL,UAAU,EAAlBrL,IAAI;2BADbR,mBAAA,CACgG;QAD1FF,KAAK,EAAAe,eAAA;UAAAiL,gBAAA;UAAAC,uBAAA,EAAqDzL,IAAA,CAAAyM,SAAS,IAAIvM,IAAI,CAAC+B;QAAE;QACtDrB,GAAG,EAAEV,IAAI,CAAC+B,EAAE;QAAG9B,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsL,OAAO,CAACzL,IAAI,CAAC+B,EAAE;0BAAW/B,IAAI,CAACW,IAAI,gCAAA6L,WAAA;;IAG1EvL,OAAO,EAAAC,QAAA,CACtB,MAEM,C,4BAFNxB,mBAAA,CAEM;MAFDJ,KAAK,EAAC,eAAe;MAACC,KAAyB,EAAzB;QAAA;MAAA;OAA0B,WAErD,qBAC0BO,IAAA,CAAA2M,0BAA0B,CAAC7K,MAAM,I,cAA3DC,YAAA,CAC8C6K,iBAAA;;MADtC3K,EAAE,EAAC,QAAQ;MAA2C8J,MAAM,EAAE/L,IAAA,CAAAyM,SAAS;MAC5EvK,IAAI,EAAElC,IAAA,CAAA2M;kHACT/M,mBAAA,CAEM;MAFDJ,KAAK,EAAC,eAAe;MAACC,KAAyB,EAAzB;QAAA;MAAA;OAA0B,OAErD,qBACuBO,IAAA,CAAA6M,sBAAsB,CAAC/K,MAAM,I,cAApDC,YAAA,CACO+J,eAAA;;MADD7J,EAAE,EAAC,OAAO;MAAuC8J,MAAM,EAAE/L,IAAA,CAAAyM,SAAS;MAAGvK,IAAI,EAAElC,IAAA,CAAA6M;kHAEjFjN,mBAAA,CAEM;MAFDJ,KAAK,EAAC,eAAe;MAACC,KAAyB,EAAzB;QAAA;MAAA;OAA0B,YAErD,qBACuBO,IAAA,CAAA8M,0BAA0B,CAAChL,MAAM,I,cAAxDC,YAAA,CAC4C+J,eAAA;;MADtC7J,EAAE,EAAC,OAAO;MAA2C8J,MAAM,EAAE/L,IAAA,CAAAyM,SAAS;MACzEvK,IAAI,EAAElC,IAAA,CAAA8M;;;MAGb9L,YAAA,CAeqBC,6BAAA;IAfDC,KAAK,EAAC,UAAU;IAACO,KAAK,EAAC;;IACxB4J,GAAG,EAAAjK,QAAA,CAClB,MAGM,CAHNxB,mBAAA,CAGM,OAHNmN,WAGM,I,kBAFJrN,mBAAA,CACkGI,SAAA,QAAAC,WAAA,CAAjFC,IAAA,CAAAuL,UAAU,EAAlBrL,IAAI;2BADbR,mBAAA,CACkG;QAD5FF,KAAK,EAAAe,eAAA;UAAAiL,gBAAA;UAAAC,uBAAA,EAAqDzL,IAAA,CAAAgN,aAAa,IAAI9M,IAAI,CAAC+B;QAAE;QAC1DrB,GAAG,EAAEV,IAAI,CAAC+B,EAAE;QAAG9B,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsL,OAAO,CAACzL,IAAI,CAAC+B,EAAE;SAAQ,IAAE,GAAAL,gBAAA,CAAG1B,IAAI,CAACW,IAAI,gCAAAoM,WAAA;;IAG5E9L,OAAO,EAAAC,QAAA,CACtB,MAE+C,CAF/CJ,YAAA,CAE+CgF,uBAAA;MAFjCC,OAAO,EAAC,QAAQ;MAC3BC,QAAQ,EAAElG,IAAA,CAAAkN,WAAW,GAAGlN,IAAA,CAAAmN,wBAAwB,GAAGnN,IAAA,CAAAmN,wBAAwB,CAACd,KAAK,IAAIrM,IAAA,CAAAsM,SAAS;MAC9FpL,KAAK,EAAE;2CACVtB,mBAAA,CAEM;MAFDJ,KAAK,EAAC,oBAAoB;MAAEW,OAAK,EAAA0F,MAAA,SAAAA,MAAA,WAAAgB,IAAA,KAAExG,MAAA,CAAA+M,kBAAA,IAAA/M,MAAA,CAAA+M,kBAAA,IAAAvG,IAAA,CAAkB;wBACrD7G,IAAA,CAAAkN,WAAW,mC;;kCAIpBtN,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAqB,GAAC,SAEjC,qBACAwB,YAAA,CASqBC,6BAAA;IATDC,KAAK,EAAC,QAAQ;IAACO,KAAK,EAAC;;IACtBN,OAAO,EAAAC,QAAA,CACtB,MAE8C,CAF9CJ,YAAA,CAE8CgF,uBAAA;MAFhCC,OAAO,EAAC,QAAQ;MAACxE,KAAK,EAAC,SAAS;MAC3CyE,QAAQ,EAAElG,IAAA,CAAAqN,UAAU,GAAGrN,IAAA,CAAAsN,WAAW,GAAGtN,IAAA,CAAAsN,WAAW,CAACjB,KAAK,IAAIrM,IAAA,CAAAsM,SAAS;MACnEpL,KAAK,EAAE;2CACVtB,mBAAA,CAEM;MAFDJ,KAAK,EAAC,oBAAoB;MAAEW,OAAK,EAAA0F,MAAA,SAAAA,MAAA,WAAAgB,IAAA,KAAExG,MAAA,CAAAkN,iBAAA,IAAAlN,MAAA,CAAAkN,iBAAA,IAAA1G,IAAA,CAAiB;wBACpD7G,IAAA,CAAAqN,UAAU,mC;;MAInBrM,YAAA,CAUqBC,6BAAA;IAVDC,KAAK,EAAC;EAAS;IAChBmK,GAAG,EAAAjK,QAAA,CAClB,MAGM,CAHNxB,mBAAA,CAGM,OAHN4N,WAGM,I,kBAFJ9N,mBAAA,CACiGI,SAAA,QAAAC,WAAA,CAAhFC,IAAA,CAAAuL,UAAU,EAAlBrL,IAAI;2BADbR,mBAAA,CACiG;QAD3FF,KAAK,EAAAe,eAAA;UAAAiL,gBAAA;UAAAC,uBAAA,EAAqDzL,IAAA,CAAAyN,YAAY,IAAIvN,IAAI,CAAC+B;QAAE;QACzDrB,GAAG,EAAEV,IAAI,CAAC+B,EAAE;QAAG9B,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsL,OAAO,CAACzL,IAAI,CAAC+B,EAAE;SAAQ,GAAC,GAAAL,gBAAA,CAAG1B,IAAI,CAACW,IAAI,gCAAA6M,WAAA;;IAG3EvM,OAAO,EAAAC,QAAA,CACtB,MAAwG,CAA9EpB,IAAA,CAAA2N,cAAc,CAAC7L,MAAM,I,cAA/CC,YAAA,CAAwG6K,iBAAA;;MAAhG3K,EAAE,EAAC,QAAQ;MAA+B8J,MAAM,EAAE/L,IAAA,CAAAyN,YAAY;MAAGvL,IAAI,EAAElC,IAAA,CAAA2N;;;MAGnF3M,YAAA,CAaqBC,6BAAA;IAbDC,KAAK,EAAC;EAAQ;IACfmK,GAAG,EAAAjK,QAAA,CAClB,MAKM,CALNxB,mBAAA,CAKM,OALNgO,WAKM,I,kBAJJlO,mBAAA,CAGMI,SAAA,QAAAC,WAAA,CAFWC,IAAA,CAAAuL,UAAU,EAAlBrL,IAAI;2BADbR,mBAAA,CAGM;QAHAF,KAAK,EAAAe,eAAA;UAAAiL,gBAAA;UAAAC,uBAAA,EAAqDzL,IAAA,CAAA6N,gBAAgB,IAAI3N,IAAI,CAAC+B;QAAE;QAC7DrB,GAAG,EAAEV,IAAI,CAAC+B,EAAE;QAAG9B,OAAK,EAAAC,MAAA,IAAEC,MAAA,CAAAsL,OAAO,CAACzL,IAAI,CAAC+B,EAAE;UACjErC,mBAAA,CAA2B,aAAtB,GAAC,GAAAgC,gBAAA,CAAG1B,IAAI,CAACW,IAAI,iB;;IAIPM,OAAO,EAAAC,QAAA,CACtB,MAC0C,CADnBpB,IAAA,CAAA8N,wBAAwB,CAAChM,MAAM,I,cAAtDC,YAAA,CAC0C+J,eAAA;;MADpC7J,EAAE,EAAC,OAAO;MAAyC8J,MAAM,EAAE/L,IAAA,CAAA6N,gBAAgB;MAC9E3L,IAAI,EAAElC,IAAA,CAAA8N;;;6CAIJ9N,IAAA,CAAAU,MAAM,S,cAAjBhB,mBAAA,CAGM,OAAAqO,WAAA,EAAAlI,MAAA,SAAAA,MAAA,QAFJjG,mBAAA,CACiH;IADzG4D,GAAG,EAAC,uCAAuC;IAACwK,WAAW,EAAC,GAAG;IACjEvO,KAAqG,EAArG;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;qEAEJuB,YAAA,CAAaiN,eAAA,GACbjN,YAAA,CAOYkN,oBAAA;IAPD,YAAU,EAAC,OAAO;IAACC,KAAK,EAAL,EAAK;IAAS9E,IAAI,EAAErJ,IAAA,CAAAqJ,IAAI;2DAAJrJ,IAAA,CAAAqJ,IAAI,GAAAjJ,MAAA;IAAEgO,SAAS,EAAT,EAAS;IAAE3O,KAAK,EAAE;MAAA4O,MAAA;MAAAC,KAAA;IAAA;;sBACxE,MAKMzI,MAAA,SAAAA,MAAA,QALNjG,mBAAA,CAKM;MALDJ,KAAK,EAAC;IAAW,IACpBI,mBAAA,CAEM;MAFDJ,KAAK,EAAC;IAAiB,GAAC,MAE7B,GACAI,mBAAA,CAA0C;MAArCJ,KAAK,EAAC;IAAM,GAAC,oBAAkB,E", "ignoreList": []}]}
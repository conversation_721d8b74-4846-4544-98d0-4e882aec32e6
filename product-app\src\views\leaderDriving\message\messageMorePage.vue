<template>
  <div class="MessageInfoPage">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="noticeDetailsBox"
             v-for="item in dataList"
             @click="MessagePage(item)"
             :key="item.id">
          <div class="noticeTitle">{{item.title}}</div>
          <div class="noticeInfo">
            <div>{{item.createDate}}</div>
            <div>{{item.name}}</div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview } from 'vant'
import { useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'MessageInfoPage',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    // const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '详情',
      id: route.query.id,
      loading: false,
      finished: false,
      refreshing: false,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getList()
    })
    const onRefresh = () => {
      getList()
    }
    // 列表请求
    const getList = async () => {
      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })
      var { data: list, total } = res
      // list.forEach(item => {
      //   item.content = item.content ? DeleteHtmlFromStartToEnd(item.content, '<!--', '-->').replace(/<.*?>/g, '').replace(/&nbsp;/ig, '') : ''
      // })
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const MessagePage = async (_item) => {
      window.location.href = `http://**************:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, MessagePage, onClickLeft }
  }
}
</script>
<style lang="less">
.MessageInfoPage {
  width: 100%;
  min-height: 100%;
  background: #eee;
  // background: #f8f8f8;
  .noticeDetailsBox {
    width: 100%;
    padding: 10px 16px;
    background-color: #fff;
    margin-bottom: 2px;
    .noticeTitle {
      font-size: 16px;
      line-height: 24px;
      color: #333333;
    }
    .noticeInfo {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 0;
      div {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #999999;
      }
    }
  }
}
</style>

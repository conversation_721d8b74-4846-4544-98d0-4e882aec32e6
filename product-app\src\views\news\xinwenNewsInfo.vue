<template>
  <div class="newsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft">
        <!-- <template #right>{{playText}}
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="'font-size:20px; '"
           v-html="details.infoTitle"> </div>
      <div class="n_details_more_box flex_box">
        <div class="flex_placeholder"></div>
        <div class=" flex_box flex_align_center flex_justify-content_end">
          <div class="n_details_name"
               :style="'font-size:12px;'+'color:'+appTheme+';'">{{details.infoSource}}</div>
          <div class="n_details_time">{{ details.pubTime? dayjs(details.pubTime).format('YYYY-MM-DD') : '' }}</div>
        </div>
      </div>
      <div class="n_details_content"
           v-html="content"></div>
      <!-- <div class="n_details_more_box">
        <div v-if="browerCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">{{browerCount.hint}}{{browerCount.value}}</div>
        <div v-if="shareCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">分享：{{shareCount.value}}</div>
        <div v-if="commentCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">评论：{{commentCount.value}}</div>
        <div v-if="dataTime.show"
             class="n_details_time"
             :style="'font-size:12px;'">{{dataTime.value}}</div>
        <div style="clear: both;"></div>
      </div> -->
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { NavBar, Sticky, ImagePreview, Image as VanImage, Dialog } from 'vant'
export default {
  name: 'newsDetails',
  components: {
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      ifIike: route.query.ifIike || '',
      id: route.query.id,
      details: {},
      content: '',
      urlShow: false // 是否打开了外部链接
    })
    onMounted(() => {
      browseSave()
      newsInfo()
    })
    const onRefresh = () => {
      newsInfo()
    }
    const browseSave = async () => {
      await $api.general.saveBrowse({
        keyId: data.id,
        type: data.type
      })
    }
    // 详情
    const newsInfo = async () => {
      getDetails()
    }
    // 获取详情
    const getDetails = async () => {
      const res = await $api.news.getNewsDetail2({
        id: data.id
      })
      // var interlinkage = sessionStorage.getItem('interlinkage') || ''
      // if (interlinkage === res.data.externalLinks) {
      //   router.push({ path: '/newsList5', query: { title: '资讯' } })
      //   return
      // }
      if (res.data.linkUrl !== null) {
        // if (data.urlShow) {
        //   return
        // }
        data.urlShow = true
        sessionStorage.setItem('interlinkage', res.data.linkUrl)
        window.location.replace(res.data.linkUrl)
        // window.location.href = res.data.linkUrl
        return
      }
      data.details = res.data
      const newHtml = res.data.infoContent.replace(/<img([^>]+)>/g, function (match, p1) {
        const style = 'style="width:100%; height:100%;"'
        if (/style="/.test(p1)) {
          // 如果img标签中已经包含style属性，则替换为新的style
          return match.replace(/style="[^"]*"/, style)
        } else {
          // 如果img标签中不包含style属性，则添加新的style
          return match.replace(/<img/, '<img ' + style)
        }
      })
      data.content = newHtml
    }

    const onClickLeft = () => {
      // 判断，如果是打开了外部链接，点击返回键就回退2，否则就正常回退
      if (data.urlShow) {
        router.go(-2)
      } else {
        history.back()
      }
    }
    return { ...toRefs(data), $general, route, dayjs, onRefresh, onClickLeft }
  }
}
</script>
<style lang="less">
.newsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }
  .n_details_survey {
    width: 100%;
    .n_details_survey_bg {
      width: 100%;
      height: 350px;
      background: #000;
      background-size: 100% 100% !important;
      overflow: hidden;
      position: relative;
      .n_details_survey_bgooo {
        overflow: hidden;
        width: 100%;
        height: 100%;
        background: #0000005b;
      }
      .n_details_survey_content {
        width: 100%;
        height: 30%;
        background: #fff;
        border-radius: 20px 20px 0 0;
        position: absolute;
        bottom: 0;
        padding: 10px;
        box-sizing: border-box;
      }
      .n_details_survey_top {
        width: 100%;
        height: 30px;
        color: #a8a8a8;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .n_details_survey_top_time {
          font-size: 14px;
        }
        .n_details_survey_top_text {
          font-size: 14px;
        }
      }
      .n_details_survey_title {
        width: 100%;
        height: 30px;
        font-size: 20px;
        font-weight: 700;
        color: #fff;
        margin: 40px 10px 0;
      }
    }
  }
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }
  .n_details_content {
    margin: 10px;
    img {
      width: 100%;
      height: 100%;
    }
    > p {
      margin: 15px 0;
      font-size: 16px !important;
      line-height: 28px !important;
      span {
        font-size: 16px !important;
        line-height: 28px !important;
      }
    }
  }
  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }
  .n_details_more_box > .flex_box {
    width: 40%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    text-align: right !important;
    align-items: right;
  }
  .n_details_time {
    font-size: 12px;
    color: #666;
    width: 100%;
    margin-top: 10px;
  }
  .n_details_name {
    color: #666;
    width: 100%;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .n_details_nextImg {
  }
  .representativeCircle_box_li {
    width: 100%;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e5e5;
    .representativeCircle_box_top {
      width: 100%;
      height: 35px;
      margin: 5px 0;
      display: flex;
      align-items: center;
      position: relative;
      .attention {
        text-align: center;
        position: absolute;
        top: 0;
        right: 10px;
        width: 80px;
        height: 80%;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40px;
        color: #3894ff;
        border: 1px solid #3894ff;
      }
      .attentionDel {
        color: #666;
        border: 1px solid #666;
      }
      .representativeCircle_box_top_headImg {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin: 5px;
      }
      .representativeCircle_box_name {
        font-size: 16px;
        .representativeCircle_box_congressStr {
          font-size: 14px;
          color: #4c4c4c;
        }
      }
    }
    .representativeCircle_box_center {
      box-sizing: border-box;
      .representativeCircle_box_center_content {
        padding-left: 13px;
        margin: 5px 0;
      }
      .representativeCircle_box_center_attachmentList {
        width: 95%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        .van-image {
          margin: 5px;
        }
      }
    }
  }
}
.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    padding-left: 10px;
    color: #a8a8a8;
  }
  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .representativeCircle_box_buttom_conmment {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > span {
        font-size: 14px;
        margin-right: 4px;
      }
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
    .representativeCircle_box_buttom_link {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;
      padding-right: 10px;
      > span {
        margin-right: 10px;
        font-size: 14px;
      }
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
    img {
      margin: 15px 0;
      width: 100% !important;
    }
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
}
.likeComment_box {
  background: #f7f7f7;
  margin: 0 5px 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;
  .comment_box {
    margin: 0 5px 0px;
  }
  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }
  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;
    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;
      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }
      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }
      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}
.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>

<template>
  <div class="newsMore">
    <template v-if="pageType === 'survey'">
      <van-tabs v-model:active="active" animated @click-tab="onClickTab">
        <van-tab v-for="index in ['进行中', '已结束']" :title="index" :key="index">
          <div>
            <van-search v-model="keyword" @search="search" @clear="search" placeholder="请输入搜索关键词" />
          </div>
          <!-- 意见征集 -->
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <van-cell v-for="item in dataList" :key="item.id">
              <template v-if="item.BGurl">
                <div class="survey_box" @click="skipDetails(pageType, item.id)">
                  <div class="survey_left">
                    <div class="survey_title">{{ item.title }} </div>
                    <div class="survey_time">{{ dayjs(item.starTime).format('YYYY-MM-DD') }}</div>
                  </div>
                  <div class="survey_right" v-if="item.BGurl">
                    <img :src="item.BGurl" alt="">
                  </div>
                  <div class="isRead" v-if="item.isRead == '0'"></div>
                </div>
              </template>
              <template v-else>
                <div class="survey_box2" @click="skipDetails(pageType, item.id)">
                  <div class="survey_left2">
                    <div class="survey_title2">{{ item.title }} </div>
                    <div class="survey_time2">{{ dayjs(item.starTime).format('YYYY-MM-DD') }}</div>
                  </div>
                  <div class="isRead" v-if="item.isRead == '0'"></div>
                </div>
              </template>
            </van-cell>
          </van-list>
        </van-tab>
      </van-tabs>
    </template>
    <template v-if="pageType !== 'survey'">
      <van-pull-refresh v-model="refreshing" success-text="刷新成功" @refresh="onRefresh">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad"
          :immediate-check="false">
          <van-cell v-for="item in dataList" :key="item.id">
            <!-- 数据列表 -->
            <div class="situation_li" @click="skipDetails(pageType, item.relateRecordId)" v-if="pageType !== 'community' && pageType !== 'representative' && pageType !== 'countryside'
              && pageType !== 'numberApp' && pageType !== 'announcement' && pageType !== 'columnList'">
              <p class="situation_li_title">{{ item.title }} <span v-if="item.isRead == '0'"
                  class="situation_li_title_sp"></span></p>
              <p class="situation_li_time">{{ item.createDate ? dayjs(item.createDate).format('YYYY-MM-DD') : '' }}</p>
            </div>
            <div class="situation_li" @click="skipDetails(pageType, item.id)" v-if="pageType === 'community'">
              <p class="situation_li_title">{{ item.title }} <span v-if="item.isRead == '0'"
                  class="situation_li_title_sp"></span></p>
              <p class="situation_li_time">{{ item.createDate ? dayjs(item.createDate).format('YYYY-MM-DD') : '' }}</p>
            </div>
            <div class="representative_box_li" :key="item.id"
              @click="skipDetails(pageType, item.id, item.externalLinks)" v-if="pageType === 'representative' || pageType === 'countryside' || pageType === 'numberApp' || pageType === 'columnList'
              ">
              <div class="representative_box_right">
                <p class="representative_title">{{ item.title }}</p>
                <p class="representative_time">{{ item.createDate ? dayjs(item.createDate).format('YYYY-MM-DD') : '' }}
                </p>
              </div>
              <div class="representative_box_left" v-if="pageType !== 'columnList' && item.imgObj.fullUrl != null">
                <img class="representative_img" :src="item.imgObj.fullUrl" alt="">
              </div>
              <div class="representative_box_left" v-if="pageType === 'columnList' && item.leftImage">
                <img class="representative_img" :src="item.leftImage" alt="">
              </div>
            </div>
            <template v-if="pageType === 'announcement'">
              <div class="announcement" @click="skipDetails('announcement', item)">
                <div class="announcement_title">{{ item.content }}</div>
                <div class="announcement_text">{{ item.moduleView }}</div>
              </div>
            </template>
          </van-cell>
        </van-list>
      </van-pull-refresh>
    </template>

    <div v-if="showSkeleton" class="notText">
      <van-skeleton v-for="(item, index) in 3" :key="index" title :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog } from 'vant'
export default {
  name: 'newsMore',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      id: route.query.id || '',
      pageType: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 20,
      total: 0,
      module: 6,
      dataList: [],
      switchs: { value: '', data: [] },
      themeImg: { url: '' },
      active: '',
      menuList: [],
      identification: 0,
      topic: ['498377074449317888', '498377147954495488', '498631879772078080', '498632215421255680', '583913427601195008', '583913456667721728', '583913561495961600', '583913588691828736', '583913702919503872', '583913728316014592', '583913823384109056', '583913848705122304', '588587230944034816', '588587379388841984']
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      console.log('pageType===>', data.pageType)
      if (window.location.origin === 'http://localhost:8080' || window.location.origin === 'http://**************') {
        data.topic = route.query.topic ? route.query.topic.split(',') : ['497281301590573056', '497281406292983808', '497911551798280192', '497911588330668032', '583535616667418624', '583535685554667520', '583576120553635840', '583576176543399936', '583581969791582208', '583582056441708544', '583586658427863040', '583586693840371712', '588606723766353920', '588606750186274816']
        // data.topic = route.query.topic ? route.query.topic.split(',') : ['498377074449317888', '498377147954495488', '498631879772078080', '498632215421255680', '583913427601195008', '583913456667721728', '583913561495961600', '583913588691828736', '583913702919503872', '583913728316014592', '583913823384109056', '583913848705122304', '588587230944034816', '588587379388841984']
      } else {
        data.topic = route.query.topic ? route.query.topic.split(',') : ['497281301590573056', '497281406292983808', '497911551798280192', '497911588330668032', '583535616667418624', '583535685554667520', '583576120553635840', '583576176543399936', '583581969791582208', '583582056441708544', '583586658427863040', '583586693840371712', '588606723766353920', '588606750186274816']
      }
      // data.active = Number(sessionStorage.getItem('isSolicitation')) === 1 ? 0 : 1 || 0
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    watch(() => data.switchs.value, (newName, oldName) => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    })
    const search = async () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      if (data.identification === 0) {
        var { data: list, total } = await $api.news.getMoreTopics({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          keyword: data.keyword
        })
        data.identification = 0
        data.dataList = data.dataList.concat(list)
        data.total = total
        // data.dataList = list
        data.showSkeleton = false
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        if (data.dataList.length >= total) {
          data.finished = true
        }
      } else if (data.identification === 1) {
        var { data: lists, totals } = await $api.news.getMoreTopics({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          keyword: data.keyword,
          isFollow: 1
        })
        data.identification = 1
        data.dataList = data.dataList.concat(lists)
        data.total = totals
        // data.dataList = list
        data.showSkeleton = false
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        data.finished = true
      } else {
        var { data: lista, totala } = await $api.news.getMoreTopics({
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          keyword: data.keyword,
          publishBy: data.user.id
        })
        data.identification = 2
        data.dataList = data.dataList.concat(lista)
        data.total = totala
        // data.dataList = list
        data.showSkeleton = false
        data.loading = false
        data.refreshing = false
        // 数据全部加载完成
        if (data.dataList.length >= totala) {
          data.finished = true
        }
      }
    }
    const onLoad = () => {
      if (data.dataList.length >= data.total) {
        return false
      } else {
        data.pageNo = data.pageNo + 1
        getList(data.identification)
      }
    }
    // 跳详情
    const skipDetails = async (type, _id, url, isFollow, publishBy, isFabulous, fabulousCount) => {
      if (type === 'announcement') {
        if (_id.paramMap !== null) {
          if (_id.module === 'survey') {
            router.push({ path: '/survey', query: { type: 'survey', id: _id.paramMap.id } })
            return
          } else if (_id.module === 'notice') {
            router.push({ path: '/noticeDetails', query: { type: _id.module, id: _id.paramMap.id } })
            return
          } else {
            router.push({ path: '/newsDetails', query: { type, id: _id.paramMap.id } })
            return
          }
        } else {
          Dialog.alert({
            title: _id.moduleView,
            message: _id.content,
            confirmButtonColor: '#39a9ed'
          }).then(() => {
            // on close
          })
          return false
        }
      }
      if (type === 'survey') {
        router.push({ name: 'survey', query: { id: _id, type } })
        // await $api.general.updateState({ id: _id })
        return false
      }
      if (url != null) {
        window.location.href = url
      } else {
        router.push({ name: 'newsDetails', query: { id: _id, type, isFollow, publishBy, ifIike: isFabulous, fabulousCount: fabulousCount } })
      }
    }
    // 打开用户列表
    const openUserList = (_item) => {
      router.push({ path: '/committeesayUserList', query: { uId: _item } })
    }
    // 点击tab
    const onClickTab = async ({ title }) => {
      sessionStorage.setItem('isSolicitation', title === '进行中' ? 1 : 2)
      data.keyword = ''
      switch (data.pageType) {
        case 'survey':
          if (title === '进行中') {
            data.pageNo = 1
            data.dataList = []
            data.loading = true
            data.finished = false
            getSurvey(1)
          } else {
            data.pageNo = 1
            data.dataList = []
            data.loading = true
            data.finished = false
            getSurvey(2)
          }
          break
      }
    }
    // 政情快递
    const getPolitical = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1,
        columnId: data.topic[5],
        subjectId: data.topic[4]
      }
      var res = await $api.general.highlights(params)
      var { data: list } = res
      data.dataList = data.dataList.concat(list)
      data.total = res.total
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      console.log('data.dataList.length=>', data.dataList.length)
      console.log('data.total=>', res.total)
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    // 获取两院咨询
    const getTwoInstitutes = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.topic[12],
        columnId: data.topic[13],
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1
      }
      var res = await $api.general.zySpecialsubjectRelateinfo(params)
      var { data: list } = res
      data.dataList = data.dataList.concat(list)
      data.total = res.total
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    // 获取青岛社区民意厅
    const getCommunity = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.topic[6],
        isOpen: 1,
        showDirectional: 2
      }
      var res = await $api.general.zySpecialsubjectColumn(params)
      data.dataList = res.data[0].specialsubjectNews
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
    }
    // 获取代表风采
    const getRepresentative = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.topic[2],
        isOpen: 1,
        showDirectional: 2
      }
      var res = await $api.general.representative(params)
      data.dataList = res.data[0].specialsubjectNews || []
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
    }
    // 获取乡村振兴
    const getCountryside = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.topic[8],
        isOpen: 1,
        showDirectional: 2
      }
      var res = await $api.general.countryside(params)
      data.dataList = res.data[0].specialsubjectNews || []
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
    }
    // 获取数字应用
    const getNumberApp = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.topic[10],
        isOpen: 1,
        showDirectional: 2
      }
      var res = await $api.general.applicationplatform(params)
      data.dataList = res.data[0].specialsubjectNews || []
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      data.finished = true
    }
    // 获取公告栏
    const getMsgbox = async () => {
      var { data: list, total } = await $api.general.msgboxList({ pageNo: data.pageNo, pageSize: data.pageSize })
      data.dataList = data.dataList.concat(list)
      data.total = total
      // data.dataList = list
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    // 获取意见征集
    const getSurvey = async (num = '1') => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        title: '',
        isSolicitation: sessionStorage.getItem('isSolicitation') || 1 // 1 进行中 2 已完成
      }
      var res = await $api.news.getAurveyList(params)
      var { data: list } = res
      list.forEach(item => {
        var attachmentList = item.attachmentList || []
        var bgList = attachmentList.filter(function (v) {
          return v.moduleType === 'splashBackGround'
        })
        var coverList = attachmentList.filter(function (v) {
          return v.moduleType === 'splashImg'
        })
        if (bgList.length) {
          item.BGurl = bgList[0].filePath
        }
        if (!bgList.length && coverList.length) {
          item.BGurl = coverList[0].filePath
        }
        if (coverList.length) {
          item.url = coverList[0].filePath
        }
      })
      data.dataList = data.dataList.concat(list)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    // 栏目列表
    const columnList = async () => {
      var params = {
        module: 3,
        structureId: data.id,
        pageNo: data.pageNo,
        pageSize: data.pageSize
      }
      var res = await $api.news.getColumnList(params)
      data.dataList = res.data
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    // 列表请求
    const getList = async (type) => {
      console.log('data.pageType==>', data.pageType)
      switch (data.pageType) {
        case 'political':// political 政情快递
          getPolitical()
          break
        case 'twoInstitutes':// twoInstitutes 两院咨询
          getTwoInstitutes()
          break
        case 'countryside':// countryside 乡村振兴
          getCountryside()
          break
        case 'community':// community 青岛社区民意
          getCommunity()
          break
        case 'representative':// representative 代表风采
          getRepresentative()
          break
        case 'numberApp':// numberApp 数字应用
          getNumberApp()
          break
        case 'announcement': // 公告
          getMsgbox()
          break
        case 'survey': // 意见征集
          getSurvey()
          break
        case 'columnList': // 履职学习栏目列表
          columnList()
          break
      }
    }
    return { ...toRefs(data), $general, openUserList, onClickTab, search, dayjs, skipDetails, onLoad, onRefresh }
  }
}
</script>

<style lang="less" scoped>
.newsMore {
  width: 100%;
  min-height: 100%;
  background: #fff;

  .isRead {
    width: 5px;
    height: 5px;
    background: red;
    border-radius: 20px;
    position: absolute;
    top: 5px;
    right: 0px;
  }

  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }

  .survey_box2 {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    .survey_left2 {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      box-sizing: border-box;

      .survey_title2 {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .survey_time2 {
        font-size: 14px;
      }
    }

    .survey_right2 {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;

      >img {
        width: 100%;
        height: 80%;
      }
    }
  }

  .survey_box {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    .survey_left {
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      box-sizing: border-box;

      .survey_title {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
      }

      .survey_time {
        font-size: 14px;
      }
    }

    .survey_right {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;

      >img {
        width: 100%;
        height: 80%;
      }
    }
  }

  .issue {
    width: 50px;
    height: 50px;
    color: #fff;
    text-align: center;
    line-height: 50px;
    background: #3894ff;
    position: fixed;
    border-radius: 50%;
    z-index: 9999;
    bottom: 30px;
    left: 50%;
    transform: translate(-50%, 0);
  }

  .announcement {
    width: 100%;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .announcement_title {
      font-weight: 700;
    }

    .announcement_text {
      color: #666;
      text-align: right;
      font-size: 14px;
    }
  }

  ::v-deep .van-tabs__line {
    background: #000 !important;
  }

  ::v-deep .van-tab__text--ellipsis {}

  ::v-deep .van-tab--active {
    color: #000 !important;
  }

  .representative_box_li {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .representative_box_right {
      width: 65%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .representative_title {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .representative_time {
        font-size: 14px;
        color: #a8a8a8;
        margin: 5px 0;
      }
    }

    .representative_box_left {
      width: 35%;
      height: 100%;

      .representative_img {
        width: 90%;
        height: 90%;
        margin: 5px;
      }
    }
  }

  .van-image {

    // margin-bottom: 10px;
    .van-image_img {
      border-radius: 5px !important;
    }
  }

  .situation_li {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .situation_li_title {
      margin: 10px 0 0px 0;
      position: relative;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .situation_li_title_sp {
        position: absolute;
        bottom: 0px;
        right: 10px;
        display: inline-block;
        width: 8px;
        height: 8px;
        background: red;
        border-radius: 50%;
      }
    }

    .situation_li_time {
      font-size: 14px;
      color: #a8a8a8;
      margin: 5px 0;
    }
  }

  .menu {
    background: #fff;
    padding: 8px 0 25px 0;
  }

  .menu_warp {}

  .menu .menu_item {
    position: relative;
    width: 25%;
    padding: 10px 0;
  }

  .menu .menu_item p {
    color: #3e3e3e;
    margin-top: 3px;
    text-align: center;
  }

  .menu .menu_item .footer_item_hot {
    position: absolute;
    top: 4px;
    right: 25%;
    width: 10px;
    height: 10px;
    background: #f92323;
    border-radius: 50%;
  }

  .menu .menu_item .footer_item_hot_big {
    position: absolute;
    top: 1px;
    right: 20%;
    width: 20px;
    height: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
  }
}

.representativeCircle_box_li {
  width: 100%;
  padding-bottom: 5px;

  .representativeCircle_box_top {
    width: 100%;
    height: 35px;
    margin: 5px 0;
    display: flex;
    align-items: center;
    position: relative;

    .attention {
      text-align: center;
      position: absolute;
      top: 0;
      right: 10px;
      width: 80px;
      height: 80%;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40px;
      color: #3894ff;
      border: 1px solid #3894ff;
    }

    .attentionDel {
      color: #666;
      border: 1px solid #666;
    }

    .representativeCircle_box_top_headImg {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin: 5px;
    }

    .representativeCircle_box_name {
      font-size: 16px;

      .representativeCircle_box_congressStr {
        font-size: 14px;
        color: #4c4c4c;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
      }
    }
  }

  .representativeCircle_box_center {
    box-sizing: border-box;

    .representativeCircle_box_center_content {
      padding-left: 13px;
      margin: 5px 0;
    }

    .representativeCircle_box_center_attachmentList {
      width: 95%;
      margin: auto;
      display: flex;
      flex-wrap: wrap;

      // justify-content: space-between;
      .van-image {
        margin: 5px;
      }
    }
  }
}

.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    color: #a8a8a8;
  }

  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .representativeCircle_box_buttom_comment {
      display: flex;
      align-items: center;
      justify-content: space-between;

      >img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }

    .representativeCircle_box_buttom_like {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;

      >img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
}

.likeComment_box {
  background: #f7f7f7;
  margin: 0 5px 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;

  .comment_box {
    margin: 0 5px 0px;
  }

  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }

  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;

    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;

      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }

      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }

      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}

.T-flex-flow-row-wrap-Liaison {
  width: 100%;
  // margin: 0 auto;
  display: flex;
  align-items: center;
  height: 140px;
  overflow: hidden;

  .T-flex-flow-row-wrap-Liaison-img {
    width: 30%;
    height: 100px;
    border-radius: 10px;
    margin-right: 10px;
  }

  .T-flex-flow-row-wrap-Liaison-right {
    width: 70%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .T-flex-flow-row-wrap-Liaison-title {
      width: 100%;
      margin-bottom: 50px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .T-flex-flow-row-wrap-Liaison-but {
      width: 100%;
      display: flex;
      align-items: center;
      height: 100%;
      justify-content: space-between;
      overflow: hidden;

      .T-flex-flow-row-wrap-Liaison-time {
        color: #a8a8a8;
        font-size: 16px;
        width: 50%;
      }

      .T-flex-flow-row-wrap-Liaison-text {
        font-size: 16px;
        width: 50%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.house_content_bottom {
  width: 100%;
  background: #ffffff;
  padding: 8px 14px;
}
</style>

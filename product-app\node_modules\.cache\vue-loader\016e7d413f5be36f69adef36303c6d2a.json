{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue", "mtime": 1756438117302}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue"], "names": [], "mappings": ";AAwiBA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACvJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd;UACE,CAAC,CAAC,CAAC,EAAE,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,EAAE,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,EAAE;QACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,EAAE;MACP,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACzB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC3B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC;IACD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;IACA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;IACA,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B;IACA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;IACA,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACxB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACvB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACzB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC3B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC5B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3B;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC;MACR;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1D;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACjC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACzF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1D;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb;MACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACnC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACpC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACrC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5G,CAAC,EAAE;IACL;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC/D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5F;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACvE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC;UACF;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC;UACF;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1D;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC5C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxG;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACf;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACxC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1G;UACF;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACrC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACxC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACrC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACtC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACpE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACzC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;MACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF,CAAC;IACH;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC7C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MAC/B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAChB;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACnC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;MACF,CAAC;IACH;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACnC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC3E,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpI;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACjC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;MACjC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC5C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5I,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC5C,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACpD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7G,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACpD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAClD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACxC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAClF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAClD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;MACH;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9G;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,EAAE,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB;IACF;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC;IACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACjF;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC5Q;AACF", "file": "D:/zy/xm/h5/qdrd_h5/product-app/src/views/leaderDriving/leaderDriving.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"leaderDriving\">\r\n    <div class=\"leaderDriving_top\">\r\n    </div>\r\n    <div class=\"leaderDriving_tab\">\r\n      <div @click=\"tabClick(item)\"\r\n        :class=\"{ leaderDriving_tab_item: true, leaderDriving_tab_item_active: active == item.value }\"\r\n        v-for=\"item in tabList\" :key=\"item.value\">{{ item.name }}</div>\r\n    </div>\r\n    <div v-if=\"active == 1\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市概括\r\n      </div>\r\n      <leader-driving-box title=\"组织概括\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in generalize\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总用户量\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar1\" v-if=\"representativeVos.length\" :list=\"representativeVos\"></Bar>\r\n          <Bar :color=\"'rgba(255, 123, 49)'\" id=\"bar2\" v-if=\"officeVos.length\" :list=\"officeVos\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"市代表变动情况\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in representative\" :key=\"index\"\r\n              @click=\"representativeClick(item)\">\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                <span class=\"leaderDriving_generalize_item_title_span\">{{ item.title }}</span>\r\n                <span v-if=\"index == 0\">\r\n                  <el-icon style=\"color: #41ce81;\">\r\n                    <Bottom style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n                <span v-else>\r\n                  <el-icon style=\"color: #ff6da2;\">\r\n                    <Top style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_num\" :style=\"{ color: index == 0 ? '#41ce81' : '#ff6da2' }\">\r\n                {{ index == 0 ? '-' : '+' }}{{ item.num }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各代表团人数\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(255, 110, 110)'\" id=\"bar3\" v-if=\"representerTeam.length\" :list=\"representerTeam\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表学历分析\">\r\n        <template v-slot:content>\r\n          <Radar id=\"radar1\" v-if=\"memberEducationData.length\" :list=\"memberEducationData\"></Radar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表年龄分析\">\r\n        <template v-slot:content>\r\n          <Pie :id=\"'pie1'\" :list=\"birthday\" v-if=\"birthday.length\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表性别分析\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie\">\r\n            <div class=\"box_left\">\r\n              <Pie :id=\"'pie2'\" v-if=\"sex.length\" :list=\"sex\"></Pie>\r\n            </div>\r\n            <div class=\"box_right\" v-if=\"sex.length\">\r\n              <div class=\"top\">\r\n                <div><img :src=\"require('../../assets/img/man.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[0].name }}性{{ sex[0].value }}名</span>\r\n                <span style=\"color: #3894ff;\">{{ parseInt(sex[0].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <div><img :src=\"require('../../assets/img/woman.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[1].name }}性{{ sex[1].value }}名</span>\r\n                <span style=\"color: #ff8197;\">{{ parseInt(sex[1].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表结构分析\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar4\" v-if=\"representerElement.length\" :list=\"representerElement\">\r\n          </Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 2\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              今日提交总额\r\n            </div>\r\n            <div class=\"suggest_num\">\r\n              <span style=\"font-weight: 700;\">{{ AdviceByToday }}</span>\r\n              件\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议总数\r\n          </div>\r\n          <div class=\"suggest_box\">\r\n            <div :class=\"{ suggest_meet: index == 0, suggest_flat: index == 1 }\" v-for=\"item, index in AdviceByDomain\"\r\n              :key=\"index\">\r\n              <div class=\"meet_num\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1')\">\r\n                <span>{{ item.adviceCount }}</span>\r\n                件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1020')\">\r\n                正在办理<span>{{ item.transacting }}</span>件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1100')\">\r\n                已办结<span>{{ item.transactAccomplish }}</span>件\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"suggest_title\">\r\n            类别占比\r\n          </div>\r\n          <Pie :id=\"'pie3'\" v-if=\"currentCategoryData.length\" :list=\"currentCategoryData\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in keywords\" :key=\"index\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              满意度\r\n            </div>\r\n            <div class=\"suggest_satisfaction\">\r\n              <div class=\"satisfaction_item\" v-for=\"item, index in ['满意', '基本满意', '不满意']\" :key=\"index\">\r\n                <span :style=\"{ 'background': index == 0 ? '#40cd80' : index == 1 ? '#ffd055' : '#ff6d6d' }\"></span>\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_title\">\r\n            <p>建议满意度</p>\r\n            <template v-if=\"BySatisfaction.length\">\r\n              <memory-bar v-for=\"item, index in BySatisfaction\" :key=\"index\" :item=\"item\"></memory-bar>\r\n            </template>\r\n          </div>\r\n          <div class=\"satisfaction_title\" style=\"border: 0;\">\r\n            <p>类别满意度</p>\r\n            <div class=\"satisfaction_item\" v-for=\"item, index in SatisfactionByData\" :key=\"index\">\r\n              <!-- <span>{{ item.name }}</span> -->\r\n              <memory-bar :item=\"item\"></memory-bar>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_all\">\r\n            <p v-if=\"satisfactionStatus\" @click=\"satisfactionAll(false)\"><van-icon name=\"arrow-up\" />\r\n              收起</p>\r\n            <p v-if=\"!satisfactionStatus\" @click=\"satisfactionAll(true)\"><van-icon name=\"arrow-down\" />\r\n              点击展开查看更多</p>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByRepresentative\" :click=\"true\"\r\n            :title=\"['排行', '姓名', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByDelegation\" :click=\"true\"\r\n            :title=\"['排行', '代表团', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 3\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"message_box\">\r\n            <img :src=\"require('../../assets/img/ldjsc_message.png')\" alt=\"\">\r\n            <template v-if=\"findWygzsTitleData && findWygzsTitleData.length != 0\">\r\n              <div class=\"message\">\r\n                <div v-for=\"(item, index) in findWygzsTitleData\" :key=\"index\" @click=\"MessagePage(item)\">\r\n                  <div v-if=\"index < 2\" class=\"news_text_box_item\">{{ item.title }}</div>\r\n                </div>\r\n              </div>\r\n              <div style=\"color: #7e7d7d;padding: 0.1rem;position: absolute;right: 10px;top: 0;\"\r\n                v-if=\"findWygzsTitleData.length >= 2 && (areaId == '370215' || areaId == '370200')\"\r\n                @click=\"massMessagesClick\"> >\r\n              </div>\r\n            </template>\r\n            <!-- <div class=\"message\"\r\n                 v-if=\"findWygzsTitleData.length\">\r\n              <p v-for=\"item,index in findWygzsTitleData\"\r\n                 :key=\"index\"\r\n                 v-show=\"index < 2\"><span>{{ item.title }}</span><span v-if=\"index == 0\">></span></p>\r\n            </div> -->\r\n            <template v-else>\r\n              <div class=\"messageNull\">\r\n                暂无数据\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            数据概括\r\n          </div>\r\n          <div class=\"interface_location_box\">\r\n            <div class=\"interface_location_left\">\r\n              <div class=\"interface_location_left_title\">\r\n                联络站总数量\r\n              </div>\r\n              <div class=\"interface_location_left_bot\">\r\n                <span>{{ findStudioCountByCityData.studioCount }}</span>个\r\n              </div>\r\n            </div>\r\n            <div class=\"interface_location_right\">\r\n              <Pie2 v-if=\"findWygzsTitlesCountShow\"\r\n                :datas=\"{ percentage: findWygzsTitlesCountData.responseRate, num: findWygzsTitlesCountData.num, text: '回复率', }\"\r\n                id=\"pie2\"></Pie2>\r\n            </div>\r\n          </div>\r\n          <div class=\"interface_location_box_bot\">\r\n            <div>\r\n              <p>总留言数</p>\r\n              <p>{{ (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) ?\r\n                (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>已回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.repliedCount ? findWygzsTitlesCountData.repliedCount : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>未回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.noRreplyCount ? findWygzsTitlesCountData.noRreplyCount : '暂无数据' }}</p>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            联络站分布\r\n          </div>\r\n          <Map v-if=\"mapListShow\" :list=\"mapList\" id=\"maplist\"></Map>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in findHotspotKeywordsData\" :key=\"index\" v-show=\"index < 5\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            区市联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsTitlesRankingData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsStudioTitlesCountData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 4\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            市代表标兵\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear == years }\"\r\n              @click=\"representativeTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear != years }\"\r\n              @click=\"representativeTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"medal\" type=\"resumption\" :dataList=\"dutynumList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"router.push('/performanceFilesList')\">\r\n            查看更多\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团排行\r\n            <van-icon name=\"question-o\" color=\"#d5d5d5\" size=\"24\" @click=\"show = true\" />\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear == years }\"\r\n              @click=\"dumplingTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear != years }\"\r\n              @click=\"dumplingTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"trophy\" :dataList=\"delegationScore\" :title=\"['排行', '代表团', '得分']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区市代表标兵\r\n            <div>\r\n              <van-popover v-model:show=\"showPopover\" placemen=\"bottom-end\" :actions=\"areas\" @select=\"onSelect\">\r\n                <template #reference>\r\n                  <p>{{ actionsText }} <van-icon name=\"play\" style=\"transform: rotate(90deg);\" /></p>\r\n                </template>\r\n              </van-popover>\r\n            </div>\r\n          </div>\r\n          <ranking-list type=\"resumption\" urlType=\"medal\" :dataList=\"dutynumCityList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"notText\" style=\"font-size:14px;color: #ccc;\" v-html=\"pageNot.text\" @click=\"loadMore()\"></div>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 5\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市\r\n      </div>\r\n      <leader-driving-box title=\"总安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie1\">\r\n            <div class=\"box_left\">\r\n              <pie-2 v-if=\"appToday.num\" :datas=\"{ num: appToday.num, text: '总安装率' }\" id=\"pie1\"></pie-2>\r\n            </div>\r\n            <div class=\"box_right\">\r\n              <div class=\"top\">\r\n                <p>今日登录人数\r\n                  <span :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\">{{ appToday.todayLoginNum\r\n                  }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\"> <van-icon name=\"down\"\r\n                    style=\"transform: rotate(-90deg)\" /> 较昨日{{ appToday.rorfNum === 1 ? '增加' : '下降' }}{{\r\n                      appToday.riseOrFallNum }}\r\n                </p>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <p>今日登录人次\r\n                  <span :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\">{{\r\n                    appToday.todayLoginTimes }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\"><van-icon name=\"down\" />\r\n                  较昨日{{ appToday.rorfTime === 1 ? '增加' : '下降' }}{{ appToday.riseOrFallTimes }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: dynamicId == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '1')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line1\" :list=\"appLoginActivation\" v-if=\"appLoginActivation.length\" :status=\"dynamicId\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in install\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团安装率排行\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded2 ? memberCMemTeamInstallount : memberCMemTeamInstallount.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll2\">\r\n            {{ isExpanded2 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"青岛市本级活跃度分析\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: subactive == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '2')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            登录人数/人次\r\n          </div>\r\n          <line-2 id=\"lines1\" v-if=\"appLoginActivationByNumTim.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByNumTim\"></line-2>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            活跃度\r\n          </div>\r\n          <Line id=\"line2\" v-if=\"appLoginActivationCity.length\" :status=\"subactive\" :list=\"appLoginActivationCity\">\r\n          </Line>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            机关、代表活跃度\r\n          </div>\r\n          <Line id=\"line3\" v-if=\"appLoginActivationByMemOff.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByMemOff\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团活跃度排行\" color=\"#e2eeff\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: groupActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '3')\"> 本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\"\r\n            :dataList=\"isExpanded1 ? appLoginActivationByTeam : appLoginActivationByTeam.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '活跃度']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll1\">\r\n            {{ isExpanded1 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市各区\r\n      </div>\r\n      <leader-driving-box title=\"总安装率排名\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded ? areaInstall : areaInstall.slice(0, showCount)\"\r\n            :title=\"['排行', '区市', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll\">\r\n            {{ isExpanded ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市登录情况\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: istrictEntry == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '4')\">本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <line-2 id=\"lines2\" v-if=\"appLoginByArea.length\" :status=\"istrictEntry\" :list=\"appLoginByArea\"></line-2>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: districtActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '5')\">\r\n              <div>本{{ item.name }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line4\" v-if=\"appLoginActivationByArea.length\" :status=\"districtActivity\"\r\n            :list=\"appLoginActivationByArea\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 6\">\r\n      <iframe src=\"http://120.221.72.187:9003/cockpit/#/\" frameborder=\"0\"\r\n        style=\"width: 100%;height: 680px;z-index: 99999;-webkit-overflow-scrolling: touch; overflow: scroll;\"></iframe>\r\n    </div>\r\n    <demo></demo>\r\n    <van-popup close-icon=\"close\" round v-model:show=\"show\" closeable :style=\"{ height: '13%', width: '90%' }\">\r\n      <div class=\"popup_con\">\r\n        <div class=\"popup_con_title\">\r\n          提示\r\n        </div>\r\n        <div class=\"info\">代表团积分=代表团中代表之和/总人数</div>\r\n      </div>\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'\r\nimport LeaderDrivingBox from './components/leaderDrivingBox.vue'\r\nimport Bar from './components/bar.vue'\r\nimport Pie from './components/pie.vue'\r\nimport RankingList from './components/rankingList.vue'\r\nimport Map from './components/map.vue'\r\nimport Line from './components/line.vue'\r\nimport Pie2 from './components/pie2.vue'\r\nimport Line2 from './components/line2.vue'\r\nimport Radar from './components/radar.vue'\r\nimport MemoryBar from './components/memoryBar.vue'\r\nimport Demo from './components/demo.vue'\r\nexport default {\r\n  name: 'leaderDriving',\r\n  components: {\r\n    LeaderDrivingBox,\r\n    Bar,\r\n    Pie,\r\n    RankingList,\r\n    Map,\r\n    Line,\r\n    Pie2,\r\n    Line2,\r\n    Radar,\r\n    MemoryBar,\r\n    Demo,\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Circle.name]: Circle\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      pageNot: { text: '' },\r\n      pageNo: 1,\r\n      pageSize: 5,\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId')),\r\n      areas: [],\r\n      areaIdStatus: '',\r\n      years: new Date().getFullYear(),\r\n      showPopover: false,\r\n      actionsText: '',\r\n      active: sessionStorage.getItem('leaderActive') || '1',\r\n      tabList: [{\r\n        name: '组织情况',\r\n        value: '1'\r\n      }, {\r\n        name: '建议情况',\r\n        value: '2'\r\n      }, {\r\n        name: '联络站',\r\n        value: '3'\r\n      }, {\r\n        name: '履职报表',\r\n        value: '4'\r\n      }, {\r\n        name: '运行情况',\r\n        value: '5'\r\n      }, {\r\n        name: '信访情况',\r\n        value: '6'\r\n      }],\r\n      generalize: [\r\n        {\r\n          num: '',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      install: [\r\n        {\r\n          num: '0',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      representative: [\r\n        {\r\n          num: 1,\r\n          title: '出缺代表',\r\n          key: '1',\r\n          type: 'hasVacant'\r\n        },\r\n        {\r\n          num: 1,\r\n          title: '新增代表',\r\n          key: '2',\r\n          type: ''\r\n        }\r\n      ],\r\n      keywordsList: ['教育', '产业链'],\r\n      keywords: ['教育', '产业链', '农业'],\r\n      mapList: [],\r\n      mapListShow: false,\r\n      rate: 50,\r\n      cityYear: new Date().getFullYear(),\r\n      dumplingYear: 2025,\r\n      show: false,\r\n      dynamicTab: [{\r\n        name: '日',\r\n        id: '1'\r\n      }, {\r\n        name: '月',\r\n        id: '2'\r\n      }],\r\n      dynamicId: '1',\r\n      subactive: '1',\r\n      groupActivity: '1',\r\n      istrictEntry: '1',\r\n      districtActivity: '1',\r\n      representativeText: '点击查看更多',\r\n      satisfactionStatus: false,\r\n      sex: [],\r\n      birthday: [],\r\n      party: [],\r\n      representerElement: [],\r\n      representerTeam: [],\r\n      memberEducationData: [],\r\n      officeVos: [],\r\n      representativeVos: [],\r\n      AdviceByToday: '0',\r\n      AdviceByDomain: [],\r\n      currentCategoryData: [],\r\n      BySatisfaction: [],\r\n      SatisfactionBy: [],\r\n      SatisfactionByData: [],\r\n      ByRepresentative: [],\r\n      ByDelegation: [],\r\n      findWygzsTitleData: [],\r\n      findStudioCountByCityData: {},\r\n      findWygzsTitlesCountData: {},\r\n      findWygzsTitlesCountShow: false,\r\n      findHotspotKeywordsData: {},\r\n      findWygzsTitlesRankingData: [],\r\n      findWygzsStudioTitlesCountData: [],\r\n      dutynumList: [],\r\n      delegationScore: [],\r\n      dutynumCityList: [],\r\n      appToday: {\r\n        todayLoginNum: '', // 今日登录人数\r\n        rorfNum: '', // 较昨日上升或下降\r\n        riseOrFallNum: '', // 上升或下降数量\r\n        todayLoginTimes: '', // 今日登陆人次\r\n        rorfTime: '', // 较昨日上升或下降\r\n        riseOrFallTimes: '', // 上升或下降数量\r\n        num: 0\r\n      },\r\n      appLoginActivation: [],\r\n      appInstall: [],\r\n      areaInstall: [],\r\n      showCount: 5, // 控制展示的数据数量，默认为5\r\n      isExpanded: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded1: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded2: false, // 控制是否展开全部数据，默认为false\r\n      memberCMemTeamInstallount: [],\r\n      appLoginActivationByNumTim: [],\r\n      appLoginActivationCity: [],\r\n      appLoginActivationByMemOff: [],\r\n      appLoginActivationByTeam: [],\r\n      appLoginByArea: [],\r\n      appLoginActivationByArea: []\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      const areaList = JSON.parse(sessionStorage.getItem('areas'))\r\n      data.areas = areaList.map(item => {\r\n        return {\r\n          text: item.name,\r\n          id: item.id,\r\n          name: item.name\r\n        }\r\n      })\r\n      // data.areas.splice(0, 1)\r\n      data.actionsText = data.areas[0].name\r\n      data.areaIdStatus = data.areas[0].id\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    })\r\n    // 组织情况\r\n    const organization = () => {\r\n      getMemberCount()\r\n      memberEducation()\r\n      getOrganization()\r\n      memberChange()\r\n    }\r\n    // 建议情况\r\n    const recommendation = () => {\r\n      getAdviceByToday()\r\n      getAdviceByDomain()\r\n      currentCategory()\r\n      keywords()\r\n      getAdviceBySatisfaction()\r\n      getNumberByRepresentative()\r\n      getNumberByDelegation()\r\n    }\r\n    // 联络站\r\n    const interfaceLocation = () => {\r\n      getMapList()\r\n      findWygzsTitleList()\r\n      findStudioCountByCity()\r\n      findWygzsTitlesCount()\r\n      findHotspotKeywords()\r\n      findWygzsTitlesRanking()\r\n      findWygzsStudioTitlesCount()\r\n    }\r\n    // 履职报表\r\n    const PerformanceReport = () => {\r\n      dutynumList(2025)\r\n      delegationScore()\r\n      dutynumCityList()\r\n    }\r\n    // 运行情况\r\n    const runningCondition = () => {\r\n      appTodayLogin()\r\n      appAllInstall()\r\n      appLoginActivation()\r\n      appInstall()\r\n      memberCMemTeamInstallount()\r\n      appLoginActivationByNumTim()\r\n      appLoginActivationCity()\r\n      appLoginActivationByMemOff()\r\n      appLoginActivationByTeam()\r\n      areaInstall()\r\n      appLoginByArea()\r\n      appLoginActivationByArea()\r\n    }\r\n    const getMapList = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByDistrict({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.mapList = res.data\r\n      data.mapListShow = true\r\n    }\r\n    const representativeTab = (y) => {\r\n      data.cityYear = y\r\n      dutynumList(y)\r\n    }\r\n    const dumplingTab = (y) => {\r\n      data.dumplingYear = y\r\n      delegationScore(y)\r\n    }\r\n    const tabClick = (item) => {\r\n      sessionStorage.setItem('leaderActive', item.value)\r\n      data.active = sessionStorage.getItem('leaderActive')\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    }\r\n    const onSelect = (item) => {\r\n      data.actionsText = item.text\r\n      data.areaIdStatus = item.id\r\n      dutynumCityList()\r\n    }\r\n    const dynamic = (id, type) => {\r\n      switch (type) {\r\n        case '1':\r\n          data.dynamicId = id\r\n          appLoginActivation(id)\r\n          break\r\n        case '2':\r\n          data.subactive = id\r\n          appLoginActivationByNumTim(id)\r\n          appLoginActivationCity(id)\r\n          appLoginActivationByMemOff(id)\r\n          break\r\n        case '3':\r\n          data.groupActivity = id\r\n          appLoginActivationByTeam(id)\r\n          break\r\n        case '4':\r\n          data.istrictEntry = id\r\n          appLoginByArea(id)\r\n          break\r\n        case '5':\r\n          data.districtActivity = id\r\n          appLoginActivationByArea(id)\r\n          break\r\n      }\r\n    }\r\n    const satisfactionAll = (type) => {\r\n      data.satisfactionStatus = type\r\n      if (data.satisfactionStatus) {\r\n        data.SatisfactionByData = data.SatisfactionBy\r\n      } else {\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getMemberCount = async () => {\r\n      var res = await $api.leaderDriving.memberCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      if (res.data) {\r\n        data.sex = res.data.sex.map((item, index) => {\r\n          return {\r\n            name: item.name,\r\n            value: item.amount,\r\n            key: item.key,\r\n            itemStyle: { color: index === 0 ? '#3da2ff' : '#ff738c' }\r\n          }\r\n        })\r\n        data.birthday = res.data.birthday.map(item => {\r\n          return {\r\n            key: item.key,\r\n            name: item.name,\r\n            value: item.amount\r\n          }\r\n        })\r\n        data.party = res.data.party.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        })\r\n        data.representerElement = res.data.representerElement.map(item => {\r\n          return {\r\n            value: item.amount,\r\n            key: item.key,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n        data.representerTeam = res.data.representerTeam.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        }).reverse()\r\n      }\r\n      // console.log(data.birthday)\r\n      // console.log('getMemberCount', res.data)\r\n    }\r\n    const memberEducation = async () => {\r\n      var res = await $api.leaderDriving.memberEducation({})\r\n      if (res.data) {\r\n        data.memberEducationData = res.data.map(item => {\r\n          return {\r\n            value: item.value,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const getOrganization = async () => {\r\n      var res = await $api.leaderDriving.getOrganization({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.officeVos = res.data.officeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.representativeVos = res.data.representativeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.generalize[0].num = res.data.totalNumber\r\n      data.generalize[1].num = res.data.representativeNumber\r\n      data.generalize[2].num = res.data.officeNumber\r\n    }\r\n    const memberChange = async () => {\r\n      var res = await $api.leaderDriving.memberChange({})\r\n      data.representative[0].num = res.data.vacantNum\r\n      data.representative[1].num = res.data.repairNum\r\n    }\r\n    const getAdviceByToday = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByToday({ personCode: '' })\r\n      if (res.result) {\r\n        data.AdviceByToday = res.result\r\n      }\r\n    }\r\n    const getAdviceByDomain = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByDomain({})\r\n      if (res.result) {\r\n        data.AdviceByDomain = res.result.reverse()\r\n      }\r\n    }\r\n    const suggestGoLink = (type, mType) => {\r\n      // if (mType) {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&manageType=${mType}&token={{token}}`\r\n      // } else {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&token={{token}}`\r\n      // }\r\n    }\r\n    const currentCategory = async () => {\r\n      var res = await $api.leaderDriving.currentCategory({ type: '' })\r\n      if (res.result) {\r\n        data.currentCategoryData = res.result.map(item => {\r\n          return {\r\n            name: item.name,\r\n            proportion: item.proportion,\r\n            value: item.value,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_cate?type=${item.code}&token={{token}}`\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const keywords = async () => {\r\n      var res = await $api.leaderDriving.keywords({})\r\n      if (res) {\r\n        data.keywordsList = res.data.filter(item => item !== '青岛市')\r\n      }\r\n    }\r\n    const getAdviceBySatisfaction = async () => {\r\n      var res = await $api.leaderDriving.getAdviceBySatisfaction({ type: '' })\r\n      var ress = await $api.leaderDriving.getSatisfactionByCategory({ type: '' })\r\n      if (res) {\r\n        data.BySatisfaction = [res.result].map(item => {\r\n          return {\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionBy = ress.result.map(item => {\r\n          return {\r\n            name: item.suggestName,\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getNumberByRepresentative = async () => {\r\n      var res = await $api.leaderDriving.getNumberByRepresentative({ type: '' })\r\n      if (res) {\r\n        data.ByRepresentative = res.result.map(item => {\r\n          return {\r\n            num: item.issueCount,\r\n            name: item.name,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_mylist?personCode=${item.userCode}&token={{token}}`\r\n          }\r\n        }).slice(0, 5)\r\n      }\r\n    }\r\n    const getNumberByDelegation = async () => {\r\n      var res = await $api.leaderDriving.getNumberByDelegation({ type: '' })\r\n      if (res) {\r\n        data.ByDelegation = res.result.map(item => {\r\n          if (item.delegationName !== '解放军代表团') {\r\n            return {\r\n              num: item.adviceTotal,\r\n              name: item.delegationName,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_group?groupId=${item.delegationCode}&token={{token}}`\r\n            }\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const findWygzsTitleList = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })\r\n      data.findWygzsTitleData = res.data\r\n    }\r\n    const findStudioCountByCity = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByCity({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findStudioCountByCityData = res.data[0]\r\n    }\r\n    const findWygzsTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesCountData = res.data\r\n      data.findWygzsTitlesCountData.num = parseFloat(data.findWygzsTitlesCountData.responseRate.replace('%', ''))\r\n      data.findWygzsTitlesCountShow = true\r\n    }\r\n    const findHotspotKeywords = async () => {\r\n      var res = await $api.leaderDriving.findHotspotKeywords({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findHotspotKeywordsData = res.data.filter(item => item !== '测试')\r\n      // console.log('findHotspotKeywords', res.data)\r\n    }\r\n    const findWygzsTitlesRanking = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesRanking({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesRankingData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      })\r\n    }\r\n    const findWygzsStudioTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsStudioTitlesCount({})\r\n      data.findWygzsStudioTitlesCountData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      }).splice(0, 10)\r\n    }\r\n    const dutynumList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: '1',\r\n        pageSize: '5',\r\n        year: y,\r\n        areaId: data.areaId\r\n      })\r\n      data.dutynumList = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username,\r\n          year: y,\r\n          userid: item.userid\r\n        }\r\n      }).splice(0, 5)\r\n    }\r\n    const delegationScore = async (y) => {\r\n      var res = await $api.leaderDriving.delegationScore({\r\n        pageNo: '1',\r\n        pageSize: '10',\r\n        year: data.years\r\n      })\r\n      data.delegationScore = res.data.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.delegationview\r\n        }\r\n      })\r\n    }\r\n    const dutynumCityList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        year: new Date().getFullYear(),\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.pageNot.text = res && res.errcode !== 200 ? res.errmsg || res.data : ''\r\n      var a = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username\r\n        }\r\n      })\r\n      data.dutynumCityList = data.dutynumCityList.concat(a)\r\n      var LOAD_MORE = '点击加载更多'\r\n      var LOAD_ALL = '已加载完'\r\n      data.pageNot.text = data.dutynumCityList.length === 0 ? '' : res.data.dutyNumListVos.length >= data.pageSize ? LOAD_MORE : LOAD_ALL\r\n    }\r\n    const appTodayLogin = async (y) => {\r\n      var res = await $api.leaderDriving.appTodayLogin({\r\n        areaId: data.areaId\r\n      })\r\n      data.appToday.todayLoginNum = Number(res.data.todayLoginNum) // 今日登录人数\r\n      data.appToday.rorfNum = Number(res.data.rorfNum) // 较昨日上升或下降\r\n      data.appToday.riseOrFallNum = Number(res.data.riseOrFallNum) // 上升或下降数量\r\n      data.appToday.todayLoginTimes = Number(res.data.todayLoginTimes) // 今日登陆人次\r\n      data.appToday.rorfTime = Number(res.data.rorfTime) // 较昨日上升或下降\r\n      data.appToday.riseOrFallTimes = Number(res.data.riseOrFallTimes) // 上升或下降数量\r\n    }\r\n    const appAllInstall = async (y) => {\r\n      var res = await $api.leaderDriving.appAllInstall({\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.appToday.num = Number(res.data.rate.replace('%', ''))\r\n    }\r\n    const appLoginActivation = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId == '370215' ? data.areaId : '' }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivation = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appInstall = async () => {\r\n      var res = await $api.leaderDriving.appInstall({ areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.install[0].num = res.data.totalInstall\r\n        data.install[1].num = res.data.memberInstall\r\n        data.install[2].num = res.data.officeInstall\r\n      }\r\n    }\r\n    const areaInstall = async () => {\r\n      var res = await $api.leaderDriving.areaInstall({ areaId: data.areaId })\r\n      if (res) {\r\n        data.areaInstall = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const memberCMemTeamInstallount = async () => {\r\n      var res = await $api.leaderDriving.memberCMemTeamInstallount({ areaId: data.areaId })\r\n      if (res) {\r\n        data.memberCMemTeamInstallount = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByNumTim = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByNumTim({ type: t, areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivationByNumTim = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            nums: item.times\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationCity = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationCity = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByMemOff = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByMemOff({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationByMemOff = res.data.map(item => {\r\n          return {\r\n            numMem: item.numMem,\r\n            numOff: item.numOff,\r\n            name: item.time,\r\n            activationOff: item.activationOff,\r\n            activationMem: item.activationMem\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByTeam = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByTeam({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByTeam = res.data.map(item => {\r\n          return {\r\n            num: item.activation,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            times: item.times\r\n          }\r\n        })\r\n        console.log('data.appLoginByArea===>', data.appLoginByArea)\r\n      }\r\n    }\r\n    const appLoginActivationByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const MessagePage = async (_item) => {\r\n      window.location.href = `http://120.221.72.187:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`\r\n    }\r\n    const massMessagesClick = async () => {\r\n      router.push({ name: 'messageMorePage' })\r\n    }\r\n    const loadMore = async () => {\r\n      var LOAD_MORE = '点击加载更多'\r\n      var NET_ERR = '网络不小心断开了'\r\n      var LOAD_ING = '加载中，请稍候...'\r\n      if ((data.pageNot.text === LOAD_MORE || data.pageNot.text === NET_ERR) && data.pageNo !== 1) {\r\n        data.pageNot.text = LOAD_ING\r\n        data.pageNo++\r\n        dutynumCityList()\r\n      } else {\r\n        data.pageNo = data.pageNo + 1\r\n        dutynumCityList()\r\n      }\r\n    }\r\n    const representativeAll = () => {\r\n      data.isExpanded = !data.isExpanded\r\n    }\r\n    const representativeAll1 = () => {\r\n      data.isExpanded1 = !data.isExpanded1\r\n    }\r\n    const representativeAll2 = () => {\r\n      data.isExpanded2 = !data.isExpanded2\r\n    }\r\n    const representativeClick = (_item) => {\r\n      router.push({ name: 'peopleList', query: { key: _item.key, type: _item.type } })\r\n    }\r\n    return { ...toRefs(data), loadMore, MessagePage, massMessagesClick, representativeAll, representativeAll1, representativeAll2, suggestGoLink, general, confirm, tabClick, representativeTab, dumplingTab, onSelect, dynamic, router, satisfactionAll, representativeClick }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.leaderDriving {\r\n  background: #f8f8f8;\r\n  box-sizing: border-box;\r\n  padding: 15px 10px 10px 10px;\r\n  height: 100%;\r\n\r\n  .satisfaction_title {\r\n    width: 95%;\r\n    margin: 10px 10px 0 10px;\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid #d8d8d8;\r\n  }\r\n\r\n  .satisfaction_item {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    >span {\r\n      font-size: 14px;\r\n      display: inline-block;\r\n      width: 25%;\r\n    }\r\n  }\r\n\r\n  .satisfaction_all {\r\n    text-align: center;\r\n    color: #3894ff;\r\n    margin: 15px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .dynamic_tab {\r\n    width: 100%;\r\n    height: 100%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n\r\n    .dynamic_tab_item {\r\n      width: 50%;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .dynamic_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .sex_pie1 {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 50%;\r\n      height: 120px;\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // justify-content: space-around;\r\n      font-size: 16px;\r\n\r\n      .top {\r\n        display: flex;\r\n        // align-items: center;\r\n        flex-direction: column;\r\n        margin-bottom: 10px;\r\n        font-size: 16px;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        // align-items: center;\r\n        font-size: 16px;\r\n        flex-direction: column;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .popup_con {\r\n    margin: 0px 10px;\r\n\r\n    .popup_con_title {\r\n      text-align: center;\r\n      font-size: 20px;\r\n      margin: 10px 0;\r\n      font-weight: 700;\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .representative_all {\r\n    width: 100%;\r\n    text-align: center;\r\n    color: #a2a2a2;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .representative_tab {\r\n    width: 94%;\r\n    height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n    margin: 0 10px;\r\n\r\n    .representative_tab_item {\r\n      flex: 1;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      color: #3894ff;\r\n      text-align: center;\r\n    }\r\n\r\n    .representative_tab_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .interface_location_box_bot {\r\n    width: 100%;\r\n    height: 80px;\r\n    background: #f8fbfe;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    text-align: center;\r\n\r\n    >div {\r\n      flex: 1;\r\n\r\n      >p {\r\n        margin: 10px 0;\r\n      }\r\n\r\n      p:nth-child(1) {\r\n        color: #8c9fb7;\r\n      }\r\n\r\n      p:nth-child(2) {\r\n        font-weight: 700;\r\n      }\r\n    }\r\n  }\r\n\r\n  .interface_location_box {\r\n    width: 100%;\r\n    height: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 10px;\r\n\r\n    .interface_location_left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      height: 100%;\r\n\r\n      .interface_location_left_title {\r\n        color: #747474;\r\n      }\r\n\r\n      .interface_location_left_bot {\r\n        >span {\r\n          font-weight: 700;\r\n          color: #3894ff;\r\n          font-size: 45px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .interface_location_right {\r\n      width: 37%;\r\n      height: 90px;\r\n      position: relative;\r\n      margin-right: 30px;\r\n\r\n      .text {\r\n        position: absolute;\r\n        top: 26px;\r\n        left: 22px;\r\n        text-align: center;\r\n\r\n        >p:nth-child(1) {\r\n          font-weight: 700;\r\n          font-size: 20px;\r\n        }\r\n\r\n        >p:nth-child(2) {\r\n          font-size: 12px;\r\n          color: #a2a2a2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_satisfaction {\r\n    width: 65%;\r\n    margin: 0 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .satisfaction_item {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 14px;\r\n\r\n      >span {\r\n        width: 14px;\r\n        height: 14px;\r\n        display: inline-block;\r\n        margin: 0 5px;\r\n        border-radius: 7px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message_box {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    >img {\r\n      height: 50px;\r\n      width: 50px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .message {\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    margin: 0 10px;\r\n\r\n    .news_text_box_item {\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 1;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 2px 0;\r\n      font-size: 15px;\r\n    }\r\n\r\n    p:nth-child(1) {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n\r\n  .messageNull {\r\n    text-align: center;\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #aeaeae;\r\n  }\r\n\r\n  .content_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .hotWord {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    height: 35px;\r\n    padding: 5px 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    .hotWord_item {\r\n      display: flex;\r\n      width: 70%;\r\n      align-items: center;\r\n\r\n      .index {\r\n        margin: 0 10px 0 0;\r\n      }\r\n    }\r\n\r\n    .hotWord_right {\r\n      color: #fff;\r\n      // padding: 3px;\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 3px;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .suggest_box {\r\n    display: flex;\r\n    color: #fff;\r\n\r\n    .suggest_transaction {\r\n      margin-left: 10px;\r\n      margin-bottom: 5px;\r\n\r\n      >span {\r\n        font-size: 22px;\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .suggest_meet {\r\n      flex: 1;\r\n      margin: 0 5px;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg1.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .suggest_flat {\r\n      margin: 0 5px;\r\n      flex: 1;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg2.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_title {\r\n    height: 24px;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    margin: 5px 10px 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    >div {\r\n      width: 29%;\r\n      color: #3894ff;\r\n    }\r\n  }\r\n\r\n  .suggest_num {\r\n    color: #3894ff;\r\n    margin-right: 10px;\r\n\r\n    >span {\r\n      font-size: 28px;\r\n    }\r\n  }\r\n\r\n  .sex_pie {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 60%;\r\n      height: 120px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      font-size: 18px;\r\n\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .leaderDriving_top {\r\n    width: 100%;\r\n    height: 100px;\r\n    // margin: 15px 10px 0;\r\n    background: url(\"../../assets/img/ldjsc_head_bg.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  .leaderDriving_tab {\r\n    margin-top: 10px;\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_tab_item {\r\n      width: 60px;\r\n      height: 60px;\r\n      background: #fff;\r\n      border-radius: 30px;\r\n      text-align: center;\r\n      box-sizing: border-box;\r\n      padding: 10px;\r\n      box-shadow: 0px 5px 15px -3px rgba(138, 138, 138, 0.1);\r\n    }\r\n\r\n    .leaderDriving_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .leaderDriving_title {\r\n    width: 100%;\r\n    height: 30px;\r\n    margin: 10px 0;\r\n    color: #3894ff;\r\n    padding-left: 10px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    position: relative;\r\n  }\r\n\r\n  .leaderDriving_title::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    height: 18px;\r\n    width: 4px;\r\n    top: 4px;\r\n    left: 0px;\r\n    background: #3894ff;\r\n    border-radius: 1px;\r\n  }\r\n\r\n  .leaderDriving_generalize {\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_generalize_item {\r\n      // width: 32%;\r\n      flex: 1;\r\n      height: 100%;\r\n      // padding-left: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .leaderDriving_generalize_item_num {\r\n        font-size: 28px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title {\r\n        color: #8196af;\r\n        // display: flex;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title_span {}\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
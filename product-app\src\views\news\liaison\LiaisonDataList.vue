<template>
  <div class="LiaisonDataList">
    <!-- 基层履职平台 -->
    <van-list v-model:loading="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="onLoad">
      <van-cell v-for="item in dataList"
                :key="item.id">
        <!-- 基层履职列表 -->
        <div class="representativeCircle_box_li"
             @click="skipDetails('LiaisonData', item.id, null, item.isFollow, item.publishBy,item.ifIike,item.fabulousCount)">
          <div class="T-flex-flow-row-wrap-Liaison">
            <van-image position="contain"
                width="2.5rem"
                fit="cover"
                height="2.5rem"
                style="border-radius: .2rem;overflow: hidden;margin-right: 0.1rem;"
                :src="item.url"/>
            <div class="T-flex-flow-row-wrap-Liaison-right">
              <div class="T-flex-flow-row-wrap-Liaison-title">
                {{item.title}}
              </div>
              <div class="T-flex-flow-row-wrap-Liaison-but">
                <div class="T-flex-flow-row-wrap-Liaison-time">
                  {{dayjs(item.time).format('YYYY-MM-DD')}}
                </div>
                <div class="T-flex-flow-row-wrap-Liaison-text">
                  {{item.studioName}}
                </div>
              </div>
            </div>
          </div>
          <div class="house_content_bottom">
            <div class="flex_box flex_align_center">
              <div class="flex_placeholder"></div>
              <div class="house_content_bottom_comment flex_box flex_align_center flex_justify-content_end"
                   @click.stop="replyClick(item)">
                <img :src="require('../../../assets/img/icon_comments.png')"
                     alt=""
                     style="width: 15px;">
                <span :style="'margin-left:5px;'+$general.loadConfiguration(-2)">{{item.commentCount}}</span>
              </div>
              <div class="house_content_bottom_like flex_box flex_align_center flex_justify-content_end"
                   style="margin-left:18px;"
                   @click.stop="downLikecontact(item)">
                <img :src="require( !item.ifIike ? '../../../assets/img/icon_likes.png': '../../../assets/img/icon_likes_on.png')"
                     alt=""
                     style="width: 15px;">
                <span :style="'margin-left:5px;'+$general.loadConfiguration(-2)+';color:'+(item.ifIike?'#FE7530':'#333333')+';'">{{item.fabulousCount}}</span>
              </div>
            </div>
          </div>
          <div class="likeComment_box">
            <div class="like_box"
                 :style="$general.loadConfiguration(-4)"
                 v-if="item.fabulousList != null && item.fabulousList.length">
              <van-icon name="like-o"
                        v-if="item.fabulousList != null && item.fabulousList.length" />
              <span :style="$general.loadConfiguration(-4)+'margin-bottom: 2px;' + 'line-height: 18px;'"
                    v-for="(it,ind) in item.fabulousList"
                    :key="ind">
                {{ind>0?',':''}} {{it.userName}} </span>
            </div>
            <div class="comment_box"
                 v-if="item.comments.length!=0">
              <div v-for="(items,indexs) in item.comments"
                   :key="indexs">
                <p style="display: flex;align-items: center;">
                  <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'">{{items.userName?items.userName:items.userType}}: </span>
                  <span :style="$general.loadConfiguration(-4)+'flex:1;'"
                        @click.stop="replyClick(item,items,indexs)"> {{items.content}} </span>
                </p>
                <p v-for="(ite,inde) in items.children"
                   :key="inde"
                   :style="$general.loadConfiguration(-4)">
                  <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'"
                        @click.stop="openUserList(ite.createBy)">{{ite.userName}}</span> 回复
                  <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'"
                        @click.stop="openUserList(items.createBy)">{{items.userName?items.userName:items.userType}}: </span>
                  <span :style="$general.loadConfiguration(-4)"
                        @click.stop="replyClick(item,items,inde)"> {{ite.content}} </span>
                </p>
              </div>
            </div>
            <div class="reply_box"
                 @click.stop=""
                 v-show="item.inputObj.replyShow">
              <div class="reply_box_item">
                <input type="text"
                       v-model="item.commentObj.content"
                       :style="$general.loadConfiguration(-4)"
                       class="reply_box_inp"
                       :placeholder="item.inputObj.replyName">
                <button :class="item.commentObj.content!='' ? 'reply_box_but' : 'reply_box_buts'"
                        :style="$general.loadConfiguration(-4)"
                        @click="transmitClick(item.commentObj,item.inputObj)">发送</button>
              </div>
            </div>
          </div>
        </div>
      </van-cell>
    </van-list>
    <!-- 发布 -->
    <div class="issue"
         @click="issueAdd">
      发布
    </div>
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog, Toast } from 'vant'
export default {
  name: 'LiaisonDataList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      id: route.query.id || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      onRefresh()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getLiaisonData()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getLiaisonData()
    }
    // 跳详情
    const skipDetails = (type, _id, url, isFollow, publishBy, isFabulous, fabulousCount) => {
      router.push({ name: 'LiaisonDataDetails', query: { id: _id, type, isFollow, publishBy, ifIike: isFabulous, fabulousCount: fabulousCount } })
    }
    // 发布
    const issueAdd = () => {
      // LiaisonDataAdd
      router.push({ name: 'LiaisonDataAdd', query: { paramType: 'resumption', topicId: JSON.stringify(data.topic) } })
    }
    // 打开用户列表
    const openUserList = (_item) => {
      router.push({ path: '/committeesayUserList', query: { uId: _item } })
    }
    // 回复
    const replyClick = (_item, _items) => {
      _item.inputObj.replyShow = true
      _item.inputObj.replyName = '发送评论'
      if (_items) {
        _item.commentObj.commentPid = _items.id
        _item.inputObj.replyName = _items.userName ? '回复' + _items.userName : '回复'
      }
    }
    // 发送
    const transmitClick = async (_item, _items) => {
      if (_item.content === '') {
        return false
      }
      _items.replyShow = false
      var url = 'comment/save'
      var params = _item
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        if (data.pageType === 'LiaisonData') {
          data.dataList = []
          getLiaisonData()
        }
      } else {
        Toast('请求失败。')
      }
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id, type = '25') => {
      var relateType = type
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: relateType
      }
      await $api.general.fabulous({ url, params })
    }
    // 获取基层履职平台
    const getLiaisonData = async (type) => {
      var { data: list, total } = await $api.general.findWygzsWorkDynamic({
        pageNo: data.pageNo,
        pageSize: data.pageSize
      })
      if (list && list.length !== 0) {
        for (var i = 0; i < list.length; i++) {
          var item = {}
          var itemData = list[i]
          item.id = itemData.id || ''
          item.isTop = itemData.isTop || '0'
          item.title = itemData.name || ''
          item.url = itemData.imgPath || ''
          item.externalLinks = itemData.externalLinks || ''
          item.time = itemData.createDate
          item.studioName = itemData.studioName
          item.key = 1
          // 点赞数
          item.fabulousCount = itemData.fabulousNumber || 0
          // 点赞人名称
          item.likes = itemData.likes || ''
          if (itemData.fabulousUser != null) {
            item.fabulousList = itemData.fabulousUser.map(items => {
              return {
                userName: items.userName,
                id: items.id
              }
            })
          } else {
            item.fabulousList = []
          }
          // 评论集合
          item.comments = itemData.children || []
          item.commentCount = itemData.commentNumber || 0
          item.ifFollow = itemData.isFollow || 0
          item.ifIike = itemData.isFabulous || false
          item.relateType = 'interfaceLocation'
          item.commentObj = {
            content: '',
            createBy: data.user.id,
            commentPid: '',
            keyId: item.id,
            attach: '',
            extend: '1',
            type: '71',
            isCheck: '1',
            areaId: data.actionsId || '370200'
          }
          item.inputObj = {
            replyShow: false,
            replyName: '发送评论'
          }
          data.dataList.push(item)
          data.total = total
          data.showSkeleton = false
          data.loading = false
          data.refreshing = false
          // 数据全部加载完成
          if (data.dataList.length >= total) {
            data.finished = true
          }
        }
      }
    }
    // 基层履职点赞
    const downLikecontact = (_item) => {
      if (_item.ifIike) { // 当前是已点赞状态数量-1
        if (_item.fabulousCount > 0) {
          _item.fabulousCount--
          _item.fabulousList = _item.fabulousList.filter(item => item.id !== data.user.id)
        }
      } else {
        _item.fabulousCount++
        _item.likes = _item.likes + (_item.likes ? ',' : '') + data.user.userName
        _item.fabulousList.push({ id: data.user.id, userName: data.user.userName })
      }
      _item.ifIike = !_item.ifIike
      fabulousInfo(_item.ifIike, _item.id, '71')
    }
    return { ...toRefs(data), $general, downLikecontact, openUserList, issueAdd, dayjs, skipDetails, onLoad, replyClick, transmitClick, onRefresh }
  }
}
</script>

<style lang="less" scoped>
.LiaisonDataList {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
    position: absolute;
    top: 0;
    right: 10px;
  }
  .survey_box2 {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .survey_left2 {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      box-sizing: border-box;
      .survey_title2 {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .survey_time2 {
        font-size: 14px;
      }
    }
    .survey_right2 {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      > img {
        width: 100%;
        height: 80%;
      }
    }
  }
  .survey_box {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .survey_left {
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0;
      box-sizing: border-box;
      .survey_title {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .survey_time {
        font-size: 14px;
      }
    }
    .survey_right {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      > img {
        width: 100%;
        height: 80%;
      }
    }
  }
  .issue {
    width: 50px;
    height: 50px;
    color: #fff;
    text-align: center;
    line-height: 50px;
    background: #3894ff;
    position: fixed;
    border-radius: 50%;
    z-index: 9999;
    bottom: 30px;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .announcement {
    width: 100%;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .announcement_title {
      font-weight: 700;
    }
    .announcement_text {
      color: #666;
      text-align: right;
      font-size: 14px;
    }
  }
  ::v-deep .van-tabs__line {
    background: #000 !important;
  }
  ::v-deep .van-tab__text--ellipsis {
  }
  ::v-deep .van-tab--active {
    color: #000 !important;
  }
  .representative_box_li {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .representative_box_right {
      width: 65%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .representative_title {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .representative_time {
        font-size: 14px;
        color: #a8a8a8;
        margin: 5px 0;
      }
    }
    .representative_box_left {
      width: 35%;
      height: 100%;
      .representative_img {
        width: 90%;
        height: 90%;
        margin: 5px;
      }
    }
  }
  .van-image {
    // margin-bottom: 10px;
    .van-image_img {
      border-radius: 5px !important;
    }
  }
  .situation_li {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .situation_li_title {
      margin: 10px 0 0px 0;
      position: relative;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .situation_li_title_sp {
        position: absolute;
        bottom: 0px;
        right: 10px;
        display: inline-block;
        width: 8px;
        height: 8px;
        background: red;
        border-radius: 50%;
      }
    }
    .situation_li_time {
      font-size: 14px;
      color: #a8a8a8;
      margin: 5px 0;
    }
  }
  .menu {
    background: #fff;
    padding: 8px 0 25px 0;
  }
  .menu_warp {
  }
  .menu .menu_item {
    position: relative;
    width: 25%;
    padding: 10px 0;
  }
  .menu .menu_item p {
    color: #3e3e3e;
    margin-top: 3px;
    text-align: center;
  }
  .menu .menu_item .footer_item_hot {
    position: absolute;
    top: 4px;
    right: 25%;
    width: 10px;
    height: 10px;
    background: #f92323;
    border-radius: 50%;
  }
  .menu .menu_item .footer_item_hot_big {
    position: absolute;
    top: 1px;
    right: 20%;
    width: 20px;
    height: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
  }
}
.representativeCircle_box_li {
  width: 100%;
  padding-bottom: 5px;
  .representativeCircle_box_top {
    width: 100%;
    height: 35px;
    margin: 5px 0;
    display: flex;
    align-items: center;
    position: relative;
    .attention {
      text-align: center;
      position: absolute;
      top: 0;
      right: 10px;
      width: 80px;
      height: 80%;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40px;
      color: #3894ff;
      border: 1px solid #3894ff;
    }
    .attentionDel {
      color: #666;
      border: 1px solid #666;
    }
    .representativeCircle_box_top_headImg {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin: 5px;
    }
    .representativeCircle_box_name {
      font-size: 16px;
      .representativeCircle_box_congressStr {
        font-size: 14px;
        color: #4c4c4c;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
      }
    }
  }
  .representativeCircle_box_center {
    box-sizing: border-box;
    .representativeCircle_box_center_content {
      padding-left: 13px;
      margin: 5px 0;
    }
    .representativeCircle_box_center_attachmentList {
      width: 95%;
      margin: auto;
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-between;
      .van-image {
        margin: 5px;
      }
    }
  }
}
.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    color: #a8a8a8;
  }
  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .representativeCircle_box_buttom_comment {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
    .representativeCircle_box_buttom_like {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;
      > img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
}
.likeComment_box {
  background: #f7f7f7;
  margin: 0 5px 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;
  .comment_box {
    margin: 0 5px 0px;
  }
  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }
  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;
    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;
      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }
      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }
      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}
.T-flex-flow-row-wrap-Liaison {
  width: 100%;
  // margin: 0 auto;
  display: flex;
  align-items: center;
  height: 140px;
  overflow: hidden;
  .T-flex-flow-row-wrap-Liaison-img {
    width: 30%;
    height: 100px;
    border-radius: 10px;
    margin-right: 10px;
  }
  .T-flex-flow-row-wrap-Liaison-right {
    width: 70%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .T-flex-flow-row-wrap-Liaison-title {
      width: 100%;
      margin-bottom: 50px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .T-flex-flow-row-wrap-Liaison-but {
      width: 100%;
      display: flex;
      align-items: center;
      height: 100%;
      justify-content: space-between;
      overflow: hidden;
      .T-flex-flow-row-wrap-Liaison-time {
        color: #a8a8a8;
        font-size: 16px;
        width: 50%;
      }
      .T-flex-flow-row-wrap-Liaison-text {
        font-size: 16px;
        width: 50%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.house_content_bottom {
  width: 100%;
  background: #ffffff;
  padding: 8px 14px;
}
</style>

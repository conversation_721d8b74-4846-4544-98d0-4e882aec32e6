<template>
  <div class="homeAdministratorDetails">
      <div class="RandomClappingUpload-box">
        <van-cell-group inset>
          <van-cell title="评论人" :value="details.userName" />
          <van-cell title="评论时间" :value="details.messageDate" />
          <van-field class="newContent"
                     v-model="content"
                     name="content"
                     label="内容"
                     rows="6"
                     maxlength="200"
                     type="textarea"
                     placeholder="请输入不超过200字的问题描述" />
        </van-cell-group>
      </div>
      <div class="footer">
        <div class="RandomClappingUpload-submit"
           @click="commentSubmit">提交</div>
           <div class="RandomClappingUpload-submits"
           @click="router.back()">取消</div>
      </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Uploader, Image as VanImage, Step, Steps, RadioGroup, Radio, ImagePreview, Toast, Button, Popup, Cell, CellGroup } from 'vant'
// import SelectMap from '@/components/SelectMap/index.vue'
export default ({
  name: 'commentEdit',
  props: {},
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [VanImage.name]: VanImage,
    [Step.name]: Step,
    [Steps.name]: Steps,
    [RadioGroup.name]: RadioGroup,
    [Button.name]: Button,
    [Popup.name]: Popup,
    [Uploader.name]: Uploader,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Radio.name]: Radio
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    // const $general = inject('$general')
    const data = reactive({
      id: route.query.id || '',
      loading: false,
      defImg: require('../../../assets/img/icon_def_head_img.png'),
      finished: false,
      refreshing: false,
      popupshow: false,
      pageNo: 1,
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      total: 0,
      Uploadmax: 3,
      content: '',
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      footerList: [
        {
          title: '评论',
          path: '/commentList',
          id: '1',
          icon: 'comment'
        },
        {
          title: '编辑',
          path: '',
          id: '2',
          icon: 'column'
        },
        {
          title: '回复',
          path: '',
          id: '3',
          icon: 'todo-list'
        },
        {
          title: '转办',
          path: '',
          id: '4',
          icon: 'share'
        }
      ],
      details: {
      },
      dataList: [
      ],
      scheduleData: [
      ],
      replyData: [
      ],
      evaluateId: '1',
      evaluateData: [
        {
          id: '1',
          name: '满意'
        },
        {
          id: '2',
          name: '基本满意'
        },
        {
          id: '3',
          name: '不满意'
        }
      ]
    })
    onMounted(() => { getInfo() })

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
    }
    const getInfo = async () => {
      const { data: List } = await $api.RandomClapping.representativecommentInfo(data.id)
      data.details = List
      data.content = data.details.messageMessage
      if (data.details.imageVo.length) {
        data.details.imageVo.forEach(e => { e.url = e.fullUrl })
      }
      console.log(data.details)
    }
    // 提交评价
    const Submit = async () => {
      const res = await $api.RandomClapping.replyEvaluate({
        messageId: data.id,
        areaId: data.areaId,
        status: data.evaluateId
      })
      if (res.errcode === 200) {
        Toast('评价成功')
        getInfo()
      }
    }

    const previewImg = (info) => {
      ImagePreview({
        images: [info.url || info.fullUrl],
        closeable: true
      })
    }
    // 提交评论
    const commentSubmit = async () => {
      var params = {
        userName: data.details.userName,
        messageDate: data.details.messageDate,
        messageMessage: data.content,
        userId: data.details.userId,
        messageId: data.details.messageId,
        id: data.id
      }
      const { errcode } = await $api.RandomClapping.representativecommentEdit(params)
      if (errcode === 200) {
        Toast('编辑成功')
        router.back()
      }
      // console.log(params, 'params')
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? 'representativelike/addLikeApp' : 'representativelike/cancelLike'
      var params = {
        messageId: _id,
        userId: data.user.id,
        areaId: data.areaId
      }
      await $api.general.fabulous({ url, params })
      // onRefresh()
    }
    const downLike = (item) => {
      item.isLike = !item.isLike
      if (item.isLike) {
        item.likeCount++
        fabulousInfo(item.isLike, item.id)
      } else {
        item.likeCount--
        fabulousInfo(item.isLike, item.id)
      }
    }
    const footerGo = (item) => {
      router.push({ path: item.path, query: { id: data.id } })
    }
    return { ...toRefs(data), dayjs, route, router, $api, footerGo, onRefresh, Submit, commentSubmit, previewImg, downLike, fabulousInfo }
  }
})
</script>
<style lang='less'>
.homeAdministratorDetails {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;
  padding-bottom: 90px;
  .footer {
    position: fixed;
    bottom: 15px;
    left: 0;
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .footer_list {
      flex:1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      .footer_list_top {
        width: 50px;
        height: 50px;
        border: 1px solid #bbdbfa;
        border-radius: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: inset 0px 0px 10px 5px #a3cffb;
        .footer_list_top_icon {
          font-size: 35px;
        }
      }
    }
  }
.footer {
  width: 90%;
    height: 56px;
  position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    display: flex;
      align-items: center;
      justify-content: space-between;
}
  .RandomClappingUpload-submit {
    width: 45%;
    height: 40px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }
  .RandomClappingUpload-submits {
    width: 45%;
    height: 40px;
    border: 1px solid #3088fe;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #3088fe;
    border-radius: 5px;
  }
  .RandomClappingUpload-box {
    background: #fff;
    border-radius: 8px;
    margin: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../../assets/font/PingFang Medium_downcc.otf");
  }

  .homeAdministratorDetails-detail {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 12px;

    .details-title {
      font-weight: 600;
      font-size: 19px;
      color: #333333;
      line-height: 26px;
    }

    .details-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 13px;

      .details-left {
        display: flex;
        align-items: center;

        img {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          margin-right: 10px;
        }

        .details-name {
          font-weight: 500;
          font-size: 15px;
          color: #999999;
        }
      }

      .details-time {
        font-size: 14px;
        color: #7e7e7e;
      }
    }

    .details-content {
      margin-top: 20px;
      font-weight: 500;
      font-size: 17px;
      color: #333333;
      line-height: 28px;
      text-indent: 2em;
    }
  }

  .imageBox {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .details-image {
      margin-right: 10px;
    }
  }

  .homeAdministratorDetails-bg {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px 12px;
  }

  .homeAdministratorDetails-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: PingFangSC-Medium;

    >span {
      font-weight: 600;
      font-size: 19px;
      color: #333333;
      flex: 1;
    }

    p {
      width: 2px;
      position: relative;
      height: 16px;
      background: #3894ff;
      margin-right: 8px;
      border-radius: 5px;
    }
  }

  .homeAdministratorDetails-schedule {
    .schedule-box {
      width: 100%;
      padding: 14px 10px;
      display: flex;
      // align-items: center;
      justify-content: space-between;
      background: #f4f6f8;
      border-radius: 10px;
      flex-direction: column;
      align-items: left;

      .schedule-state {
        font-weight: 600;
        font-size: 15px;
        color: #000000;
        font-family: PingFangSC-Medium;
        >span {
          // font-size: 12px;
        }
      }

      .schedule-time {
        font-size: 14px;
        margin-top: 8px;
        color: #7e7e7e;
      }
    }

    .van-hairline::after {
      border: none !important;
    }
  }

  .homeAdministratorDetails-reply {
    .replyBox {
      margin-top: 12px;

      .reply-name {
        font-weight: 600;
        font-size: 15px;
        color: #000000;
        font-family: PingFangSC-Medium;
        margin-bottom: 5px;

        span {
          font-weight: 500;
          font-size: 15px;
          color: #666;
        }
      }

      .reply-content {
        font-weight: 500;
        font-size: 17px;
        color: #333333;
        line-height: 28px;
        padding: 0px 2px;
        font-family: PingFangSC-Medium;
      }
    }
  }

  .homeAdministratorDetails-evaluate {
    .evaluate-radio {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 10px;
    }

    .evaluate-submit {
      width: 100%;
      height: 46px;
      background: #3088fe;
      box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 46px;
      text-align: center;
      color: #ffffff;
      border-radius: 5px;
    }
  }

  .homeAdministratorDetails-comment {
    .commentBox {
      background: #fff;

      .commentBox-li {
        padding: 12px 0xp;
        border-bottom: 1px solid #e8e8e8;

        .commentBox-li-top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .commentBox-title {
            width: 70%;
            font-size: 17px;
            color: #333333;
            font-weight: 700;
            font-family: PingFangSC-Medium;
          }

          .commentBox-state {
            width: 56px;
            height: 21px;
            text-align: center;
            line-height: 21px;
            background: linear-gradient(0deg, #3894ff, #38c3ff);
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
          }

          .commentBox-state2 {
            width: 56px;
            height: 21px;
            text-align: center;
            line-height: 21px;
            background: linear-gradient(0deg, #f6a531, #fddf2f);
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
          }
        }

        .commentBox-li-btm {
          display: flex;
          align-items: center;
          margin-top: 8px;
          height: 60px;

          .commentBox-li-btm-left {
            img {
              width: 45px;
              height: 55px;
              border-radius: 5px;
              overflow: hidden;
              margin: 0 5px;
            }
          }

          .commentBox-li-btm-right {
            margin-left: 3px;
            width: 100%;
            height: 100%;

            .commentBox-li-btm-right-top {
              display: flex;
              align-items: center;
              margin-bottom: 10px;
              margin-top: 5px;

              .commentBox-li-btm-right-top-name {
                font-size: 16px;
                color: #7e7e7e;
                margin-right: 10px;
              }

              .commentBox-li-btm-right-top-position {
                font-size: 13px;
                color: #3291ff;
                background: #e9f3ff;
                padding: 3px 6px;
                border-radius: 2px;
              }
            }

            .commentBox-li-btm-right-btm {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .commentBox-li-btm-right-btm-time {
                font-size: 14px;
                color: #7e7e7e;
              }

              .like-and-leave-message {
                display: flex;
                align-items: center;

                .like {
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                  color: #7e7e7e;

                  .van-icon {
                    font-size: 16px;
                  }
                }

                .leave-message {
                  margin-left: 10px;
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                  color: #7e7e7e;

                  .van-icon {
                    font-size: 16px;
                    margin-top: 2px;
                  }
                }
              }
            }
          }
        }

        .commentBox-content {
          font-weight: 500;
          font-size: 17px;
          color: #333333;
          line-height: 28px;
          font-family: PingFangSC-Medium;
          padding: 10px 0px;
        }

        .commentBox-reply {
          width: 100%;
          padding: 8px 12px;
          background: #f4f6f8;
          border-radius: 4px;

          .commentBox-reply-title {
            font-weight: 600;
            font-size: 15px;
            color: #000000;
            font-family: PingFangSC-Medium;
            margin-bottom: 5px;

            span {
              font-weight: 500;
              font-size: 15px;
              color: #666;
            }
          }

          .commentBox-reply-content {
            font-weight: 500;
            font-size: 17px;
            color: #333333;
            line-height: 28px;
            font-family: PingFangSC-Medium;
          }
        }
      }
    }
  }
}
</style>

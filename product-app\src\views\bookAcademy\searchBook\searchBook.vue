<template>
  <div class="searchBook">
    <van-sticky>
      <form action="/">
        <van-search v-model="seachText"
                    ref="searchBox"
                    show-action
                    placeholder="请输入搜索关键词"
                    @update:model-value="onSearchChange"
                    @search="onSearch"
                    @cancel="onCancel" />
      </form>
    </van-sticky>
    <!-- <div style="height:70px;"></div> -->
      <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="itemSex_box flex_box T-flex-flow-row-wrap box_yj">
          <div @click="openBookDetails(nItem)"
               v-for="(nItem) in listData"
               :key="nItem.id"
               class="itemSex_item click">
            <div :style="'width:100px;height:135px;margin:auto;position: relative;'">
              <img v-if="nItem.txt.bookType == '2'"
                   class="item_Sound"
                   :src="icon_hasSound" />
              <img v-if="nItem.txt.isAvailable == '0'"
                   class="item_overdue"
                   src="../../../assets/img/overdue.png" />
              <img :style="'width:100%;height:100%;object-fit: cover;border-radius: 2px;'"
                   :src="nItem.img.url" />
            </div>
            <div v-if="nItem.name"
                 class="itemSex_name text_one2"
                 :style="'font-size:14px'"
                 v-html="nItem.name"></div>
            <div v-if="nItem.author"
                 class="itemSex_author text_one2"
                 :style="'font-size:12px'"
                 v-html="nItem.author"></div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, inject, toRefs } from 'vue'
import { Toast, Sticky } from 'vant'
export default {
  components: {
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const $api = inject('$api')
    const $general = inject('$general')
    console.log($api)
    console.log(Toast)
    const data = reactive({
      appTheme: '#3A77FF',
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      userId: JSON.parse(sessionStorage.getItem('user')).id,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      seachText: '', // 搜索词
      searchBox: null, // 搜索框对象
      listData: [
        // { url: '../../../images/img_test8.png', name: '论语', author: '作者：张素玲', summary: '探秘妇女解放思想的启蒙' },
        // { url: '../../../images/img_test9.png', name: '韩非子', author: '作者：陈培永', summary: '无论你是否承认,社会主义,这个词,它都在你心中,占有重要的一席之地。' },
        // { url: '../../../images/img_test10.png', name: '论美国的民主', author: '作者：吴官正', summary: '吴书记的人生哲理和为政之道' }
      ], // 列表数据

      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部   btn为按钮

      searchPage: true, // 是否为搜索页 否则为
      history: [],
      allSearch: false, // 是否点击展开全部搜索
      popular: [{ name: '热门搜索', data: ['三字经', '社会主义的哲思', '习近平', '履职', '斗'] }],
      icon_hasSound: require('../../../assets/img/icon_hasSound.png'),
      icon_no_data: require('../../../assets/img/icon_no_data.png'),
      switchs: { value: '', data: [] },
      url: ''

    })
    onMounted(() => {
      onRefresh()
    })
    // const getTypeList = async () => {
    //   var { data: getTypeList } = await $api.bookAcademy.getTypeList({
    //     id: data.id
    //   })
    //   data.url = getTypeList.coverImgUrl || data.icon_no_data
    // }
    const onSearchChange = (val) => {
      data.seachText = val
      data.searchPage = true
      onRefresh()
    }
    const onSearch = (val) => {
      data.seachText = val
      data.searchPage = false
      console.log(data.seachText)
      data.history = data.history.filter(item => item !== data.seachText)// 删除之前有的
      data.history.unshift(data.seachText)
      onRefresh()
    }
    const getSyTypeTree = async () => {
      var { data: getSyTypeTree } = await $api.bookAcademy.getSyTypeTree({
        parentId: data.id
      })
      if (getSyTypeTree && getSyTypeTree.length !== 0) {
        var getSyTypeTreeData = getSyTypeTree[0].children || []
        var syTypeTreeDataNew = []
        data.switchs.data = [{ label: '所有', value: '' }]
        getSyTypeTreeData.forEach(function (_eItem, _eIndex, _eArr) { // item index 原数组对象
          syTypeTreeDataNew.push({ label: _eItem.name, value: _eItem.id })
        })
        data.switchs.data = data.switchs.data.concat(syTypeTreeDataNew)
      }
      if (!$general.getItemForKey(data.switchs.value, data.switchs.data, 'value')) {
        data.switchs.value = ''
        getList(true)
      }
    }
    const getBookList = async () => {
      var param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText
      }
      if (!data.switchs.value) {
        param.bookTypeFirstId = data.id
      } else {
        param.bookTypeSecondId = '338173723657895936'
      }
      var { data: list } = await $api.bookAcademy.getBookList(param)
      var dataListNew = []
      list.forEach((_eItem, _eIndex, _eArr) => { // item index 原数组对象
        var item = { img: { url: _eItem.coverImgUrl || '' }, txt: { isAvailable: _eItem.isAvailable, url: _eItem.bookContentUrl || '', state: 0, schedule: -1, name: _eItem.bookName || '', bookType: _eItem.bookType || '' } }
        item.id = _eItem.id || ''// 书本id
        item.name = (_eItem.bookName || '').replace(/(^\s*)|(\s*$)/g, '')// 书名
        item.author = (_eItem.authorName || '').replace(/(^\s*)|(\s*$)/g, '')// 书作者
        item.summary = (_eItem.bookDescription || '').replace(/(^\s*)|(\s*$)/g, '')// 书简单
        dataListNew.push(item)
      })
      data.listData = data.listData.concat(dataListNew)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (list.length < data.pageSize) {
        data.finished = true
      }
    }
    // 获取数据
    const getList = async (isGetType) => {
      if (!isGetType) {
        // getTypeList()
        getSyTypeTree()
      }
      getBookList()
    }

    // tab切换事件
    const tabClick = (_name, _title) => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getList(true)
    }

    const openBookDetails = row => {
      router.push({ name: 'bookDetail', query: { id: row.id } })
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.listData = []
      data.loading = true
      data.finished = false
      data.show = false
      getList()
    }

    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    const openSearch = () => { router.push({ name: 'searchBook', query: {} }) }
    const onCancel = () => { router.go(-1) }

    return {
      ...toRefs(data), openSearch, openBookDetails, getList, onRefresh, onLoad, tabClick, onSearchChange, onSearch, onCancel
    }
  }
}
</script>
<style lang="less" scoped>
// @import "./searchBook.less";
@import "./libraryDetails.less";
</style>

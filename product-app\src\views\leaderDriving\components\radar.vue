<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'radar',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: <PERSON><PERSON>
  },
  props: {
    color: String,
    id: String,
    list: Object
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      nextTick(() => {
        var chartDom = document.getElementById(props.id)
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        myChart = echarts.init(chartDom)
        setOptions()
      })
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
    })
    const setOptions = () => {
      var options = {
        color: ['#3894FF'],
        rotation: 10,
        radar: [
          {
            triggerEvent: true,
            indicator: [
              { name: '60%\nSales\r5', max: 300 },
              { name: 'Administration', max: 300 },
              { name: 'Information Technology', max: 300 },
              { name: 'Customer Support', max: 300 },
              { name: 'Development', max: 300 },
              { name: 'Marketing', max: 300 }
            ],
            center: ['50%', '50%'],
            radius: 80,
            startAngle: 80,
            splitNumber: 4,
            shape: 'circle',
            axisName: {
              formatter: '{value}',
              color: '#666666',
              fontSize: parseInt(data.viewportWidth * 0.025)
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(215, 224, 231, 1)'
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(215, 224, 231, 1)'
              }
            }
          }
        ],
        series: [
          {
            type: 'radar',
            data: [
              {
                value: [40, 50, 100, 200, 10, 30],
                areaStyle: {
                  color: 'rgba(56,148,255,0.5)'
                }
              }
            ]
          }
        ]
      }
      nextTick(() => {
        if (props.id === 'radar1') {
          options.radar[0].indicator = props.list.map(item => {
            return {
              name: item.proportion + '\n' + item.name + '\r' + item.value,
              max: 300
            }
          })
          options.series[0].data[0].value = props.list.map(item => item.value)
          myChart.setOption(options)
        }
      })
    }
    return { ...toRefs(data), general }
  }
}
</script>
<style lang="less" scoped>
#radar1 {
  width: 100%;
  height: 220px;
  // margin: 10px 0;
  box-sizing: border-box;
  background: #fff;
}
</style>

<template>
  <div class="replyAdd">
    <div class="RandomClappingUpload-box">
      <van-cell-group inset>
        <van-field v-model="selectHome.name"
                   :label="selectHome.name ? '反馈单位' : '反馈单位'"
                   maxlength="20"
                   input-align="right"
                   :placeholder="selectHome.name ? '反馈单位' : '反馈单位'" />
        <van-field v-model="messageDate"
                   name="回复时间"
                   label="回复时间"
                   disabled
                   input-align="right"
                   placeholder="回复时间" />
        <van-field class="newContent"
                   v-model="content"
                   name="content"
                   label="结果反馈"
                   rows="6"
                   maxlength="200"
                   type="textarea"
                   placeholder=" " />
      </van-cell-group>
    </div>
    <div class="RandomClappingUpload-box">
      <div class="picUploader">
        <div class="picUploader_title">
          上传图片(最多上传{{ Uploadmax }}张)
        </div>
        <div class="imgloager">
          <div class="img_box"
               v-for="(nItem, nIndex) in attachmentIds"
               :key="nIndex">
            <van-icon name="clear"
                      @click.stop="$general.delItemForKey(nIndex, attachmentIds)"
                      class="clear" />
            <van-image @click.stop="ImagePreview(setImg(nItem.url))"
                       width="2rem"
                       height="2rem"
                       fit="cover"
                       :src="setImg(nItem.url)" />
          </div>
          <!-- <van-uploader :preview-full-image="true"
                        ref="vanUploader"
                        accept="image/*"
                        capture="camera"
                        multiple
                        :disabled="attachmentIds.length == Uploadmax"
                        :after-read="imgUploader"
                        :max-count="Uploadmax">
          </van-uploader> -->
          <van-uploader :preview-full-image="true"
                        ref="vanUploader"
                        accept="image/*"
                        v-if="attachmentIds.length != Uploadmax"
                        :after-read="imgUploader"
                        :max-count="Uploadmax">
            <div class="photo">
              <van-icon name="add-o" />
            </div>
          </van-uploader>
        </div>
      </div>
    </div>
    <!-- <div class="RandomClappingUpload-submit"
         @click="Submit">提交</div> -->
    <div class="footer">
      <div class="RandomClappingUpload-submit"
           @click="Submit">提交</div>
      <div class="RandomClappingUpload-submits"
           @click="router.back()">取消</div>
    </div>
  </div>
  <van-popup v-model:show="mapVisible"
             :round="false"
             style="width: 100vw;height: 100vh;">
    <SelectMap @close="mapVisible = false"
               @confirm="mapConfirm"
               :dataList="selectTitleList" />
  </van-popup>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Uploader, Image as VanImage, Toast } from 'vant'
import SelectMap from '@/components/SelectMap/index.vue'
import { imgZip } from '../imgZip.js'
export default ({
  name: 'replyAdd',
  props: {},
  components: {
    SelectMap,
    [VanImage.name]: VanImage,
    [Uploader.name]: Uploader
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      module: route.query.module,
      id: route.query.id,
      title: '',
      content: '',
      address: '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      username: '',
      mobile: '',
      longitude: '',
      dimensionality: '',
      township: '',
      Uploadmax: 3,
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      mapVisible: false,
      selectTitleList: [],
      selectHome: {},
      areaTree: [],
      messageDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      standbyTwo: ''
    })
    onMounted(() => {
      console.log(data.user, 'user')
      data.username = data.user.userName
      data.mobile = data.user.mobile
      getInfo()
    })
    const getInfo = async () => {
      selectTitle()
      getareaTree()
      const { data: list } = await $api.RandomClapping.representativemessageInfo(data.id)
      data.selectHome.name = list.standbyFour ? '' : list.standbyTwo
      data.selectHome.id = list.standbyOne
    }
    const setImg = (url) => {
      if (url && !window.location.origin.includes('localhost') && !window.location.origin.includes('http://**************')) {
        console.log()
        return window.location.origin + '/lzt' + url.split('lzt')[1]
      } else {
        return url
      }
    }
    const selectTitle = async () => {
      const { data: list } = await $api.RandomClapping.representativehomeSelectTitle()
      data.selectTitleList = list.map(item => {
        return {
          name: item.title,
          id: item.id,
          longitude: item.longitude,
          latitude: item.dimension,
          standbyFour: item.standbyFour
        }
      })
    }
    const getareaTree = async () => {
      const { data: list } = await $api.RandomClapping.areaTree()
      data.areaTree = list
    }
    const imgUploader = async (file) => {
      imgZip(file, uploadFile)
    }
    const uploadFile = async (e) => {
      const item = { url: '', uploadUrl: '', name: '', uploadId: '', status: 'uploading', module: 'lzbl' }
      const formData = new FormData()
      formData.append('attachment', e)
      formData.append('module', 'lzbl')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.status = 'done'
        item.url = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.status = 'failed'
        item.error = ret.errmsg || ''
      }
      data.attachmentIds.push(item)
    }
    // 地图选点
    const mapConfirm = ({ positionObj }) => {
      data.address = positionObj.address
      data.longitude = positionObj.lon
      data.dimensionality = positionObj.lat
      data.township = findIdByName(data.areaTree, positionObj.township)
      data.adcode = positionObj.adcode
      console.log('地图选点', data.township)
      console.log('地图选点', data.adcode)
      data.mapVisible = false
      data.selectHome = data.selectTitleList.find(item => {
        return item.standbyFour == data.township // eslint-disable-line
      })
      console.log(data.selectHome)
      console.log(data.selectTitleList)
    }
    // 递归函数
    function findIdByName (data, name) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].name === name) {
          return data[i].id
        } else if (data[i].children.length > 0) {
          var foundId = findIdByName(data[i].children, name)
          if (foundId !== null) {
            return foundId
          }
        }
      }
      return null // 如果未找到匹配项，则返回 null
    }
    // 提交
    const Submit = async () => {
      if (data.selectHome.name === '') {
        Toast('请填写办理单位')
        return
      }
      if (data.content === '') {
        Toast('请填写内容')
        return
      }
      // if (data.attachmentIds.length === 0) {
      //   Toast('请上传图片')
      //   return
      // }
      // if (data.address === '') {
      //   Toast('请选择地址')
      //   return
      // }
      var params = {
        userName: data.selectHome.name,
        messageDate: data.messageDate,
        messageMessage: data.content,
        remarks: data.selectHome.name,
        // id: this.id,
        messageId: data.id,
        image: data.attachmentIds.map(item => item.uploadId).join(',')
      }
      console.log(params, 'params')
      const { errcode } = await $api.RandomClapping.representativereplyAdd(params)
      if (errcode === 200) {
        Toast('留言成功')
        router.back()
      }
    }
    const ImagePreview = (url) => {
      router.push({ path: '/openImg', query: { url } })
    }
    return { ...toRefs(data), dayjs, route, router, $api, imgUploader, setImg, ImagePreview, $general, mapConfirm, Submit }
  }
})
</script>
<style lang='less'>
.replyAdd {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;
  padding: 15px;

  .footer {
    width: 90%;
    height: 56px;
    position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .RandomClappingUpload-submit {
    width: 45%;
    height: 40px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }

  .RandomClappingUpload-submits {
    width: 45%;
    height: 40px;
    border: 1px solid #3088fe;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #3088fe;
    border-radius: 5px;
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../../assets/font/PingFang Medium_downcc.otf");
  }

  .RandomClappingUpload-box {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  // .RandomClappingUpload-submit {
  //   position: fixed;
  //   left: 50%;
  //   bottom: 2%;
  //   transform: translateX(-50%);
  //   width: 90%;
  //   height: 56px;
  //   background: #3088fe;
  //   box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
  //   font-size: 16px;
  //   font-family: PingFang SC;
  //   font-weight: 400;
  //   line-height: 56px;
  //   text-align: center;
  //   color: #ffffff;
  //   border-radius: 5px;
  // }
}
</style>

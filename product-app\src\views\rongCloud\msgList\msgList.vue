
<template>
  <div class="msgList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   title="交流"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--搜索-->
        <!-- <div id="search"
             class="search_box">
          <div @click="openSearch()"
               class="search_warp flex_box">
            <div class="search_btn img_btn flex_box flex_align_center flex_justify_content">
              <van-icon :size="16"
                        :color="'#757575'"
                        name="search"></van-icon>
            </div>
            <form class="flex_placeholder flex_box flex_align_center search_input"
                  action="javascript:return true;"><input readonly
                     :style="'font-size:13px;padding-left: 0;'"
                     :placeholder="seachPlaceholder"
                     maxlength="100"
                     type="search"
                     ref="btnSearch"
                     @keyup.enter="btnSearch()"
                     v-model="seachText" /></form>
          </div>
        </div> -->
        <!--数据列表-->
        <ul v-if="dataList.length != 0 || permanent.length != 0">
          <div v-if="permanent.length != 0 && !isMember"
               style="border-bottom:10px solid #F9F9F9;">
            <div class="flex_box">
              <div v-for="(item) in permanent"
                   :key="item.id"
                   @click="openDetails(item)"
                   class="btn_item T-flexbox-vertical flex_align_center flex_justify_content">
                <div :style="'width:46px;height:46px;border-radius:50%;margin-bottom:5px;position: relative;'"
                     class="flex_box flex_align_center flex_justify_content">
                  <p v-if="item.unreadMessageCount > 0"
                     class="flex_box flex_align_center flex_justify_content text_one"
                     :class="item.pointType == 'big'?'footer_item_hot_big':'footer_item_hot'"
                     :style="item.pointType == 'big'?'font-size:12px;width:20px;height20px;':'font-size:12px;'"
                     v-html="item.pointType == 'big'?(item.unreadMessageCount>99?'99+':item.unreadMessageCount):''"></p>
                  <img :src="item.url"
                       alt=""
                       srcset=""
                       :style="'width:46px;height:46px;'">
                  <!-- <van-image :style="'width:46px;height:46px;'"
                             round
                             fit="contain"
                             :src="item.url"></van-image> -->
                </div>
                <div class="chat_list_title text_one2 flex_placeholder"
                     :style="'font-size:15px;'">{{item.userName}}</div>
              </div>
            </div>
          </div>
          <van-swipe-cell v-for="(item,index) in dataList"
                          :key="item.id"
                          class="van-hairline--bottom">
            <p v-if="item.unreadMessageCount > 0"
               class="flex_box flex_align_center flex_justify_content text_one chat_list_hot_big"
               :style="'font-size:11px;width:20px;height:20px;'"
               v-html="(item.unreadMessageCount>99?'99+':item.unreadMessageCount)"></p>
            <van-cell clickable
                      class="chat_list_item"
                      :class="item.isTop?'chat_list_top':''"
                      @click="openDetails(item)">
              <li class="flex_box flex_align_center">
                <div class="chat_list_img_box flex_box flex_align_center">
                  <!--用那个会一闪而过白色的 不能用-->
                  <img :style="'width:46px;height:46px;object-fit: contain;border-radius:50%;'"
                       :src="item.img" />
                </div>
                <div class="flex_placeholder">
                  <div class="chat_list_box flex_box">
                    <div class="chat_list_title text_one2 flex_placeholder"
                         :style="'font-size:15px;'">{{(item.isTop?'【置顶】':'')+item.name}}</div>
                    <div class="chat_list_time"
                         :style="'font-size:12px;'">{{(item.sentTime)}}</div>
                  </div>
                  <div class="chat_list_more text_one2 flex_box flex_align_center">
                    <div class="flex_placeholder flex_box flex_align_center"
                         :style="'font-size:13px;'">
                      <!--是否有@信息-->
                      <!-- <span class="chat_span"
                            v-if="getAite(item)"
                            style="color: #e14948;">[有人@我] </span> -->
                      <template v-if="item.latestMessageId != -1">
                        <!--发送失败和发送中的图标-->
                        <van-icon v-if="item.sentStatus == 'SENDING'"
                                  :size="16"
                                  :name="loading_more"></van-icon>
                        <van-icon v-else-if="item.sentStatus == 'FAILED'"
                                  :size="16"
                                  :name="icon_fail"></van-icon>
                      </template>
                      <!--有草稿 没有@的时候-->
                      <template v-if="item.draft"><span class="chat_span"
                              style="color: #e14948;">[草稿] </span><span class="flex_placeholder chat_span text_one2"
                              v-html="item.draft"></span></template>
                      <span class="flex_placeholder text_one2"
                            v-else>
                        <!--展示群组发送人名字-->
                        <span class="chat_span"
                              v-html="item.latestMessage.name?item.latestMessage.name+'：':''"></span>
                        <span class="chat_span text_one"
                              v-if="item.objectName == 'RC:TxtMsg'"
                              v-html="item.latestMessage.content.content"></span>
                        <span class="chat_span"
                              v-else-if="item.objectName == 'RC:RcCmd'"
                              v-html="'撤回一条消息'"></span>
                        <span class="chat_span"
                              v-else-if="item.objectName == 'RC:VcMsg'"
                              v-html="'[语音]'"></span>
                        <span class="chat_span"
                              v-else-if="item.objectName == 'RC:ImgMsg'"
                              v-html="'[图片]'"></span>
                        <span class="chat_span"
                              v-else-if="item.objectName == 'RC:LBSMsg'"
                              v-html="'[位置]'"></span>
                        <span class="chat_span"
                              v-else-if="item.objectName == 'RC:ImgTextMsg'"
                              v-html="item.latestMessage.content.title || '&nbsp;'"></span>
                        <span class="chat_span"
                              v-else-if="item.objectName == 'RC:RcNtf'"
                              v-html="getRcNtf(item)"></span>
                        <span class="chat_span"
                              v-else
                              v-html="item.objectName || '&nbsp;'"></span>
                      </span>
                    </div>
                    <van-icon v-if="item.notificationStatus==1"
                              :size="16"
                              :name="icon_notificationStatus"></van-icon>
                  </div>
                </div>
              </li>
            </van-cell>
            <template v-slot:right>
              <span>
                <van-button @click="setConversationToTop(item)"
                            square
                            color="#c8c7cd"
                            :text="item.isTop?'取消':'置顶'"></van-button>
                <van-button @click="clearMessagesUnreadStatus(item)"
                            square
                            color="#ff9c00"
                            :text="'设为已读'"></van-button>
                <van-button @click="removeConversation(item,index)"
                            square
                            type="danger"
                            text="删除"></van-button>
              </span>
            </template>
          </van-swipe-cell>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, inject, toRefs, watch } from 'vue'
import { Empty, Overlay, Tag, Icon, Field, Dialog, Button, SwipeCell, NavBar, Sticky } from 'vant'
import { getConversionTime } from '../../../assets/js/date.js'
// import emo from '/src/assets/emotion/emotion.json'
// import emotion from '/src/components/emotion'
import * as RongIMLib from '@rongcloud/imlib-next'
export default {
  name: 'msgList',
  components: {
    [Button.name]: Button,
    [SwipeCell.name]: SwipeCell,
    [Empty.name]: Empty,
    [Tag.name]: Tag,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    // const route = useRoute()
    const router = useRouter()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      loading_more: require('../../../assets/img/loading_more.gif'),
      icon_fail: require('../../../assets/img/icon_fail.png'),
      icon_notificationStatus: require('../../../assets/img/icon_notificationStatus.png'),
      seachPlaceholder: '请输入搜索内容',
      active: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1, // 当前页码
      pageSize: 10, // 当前请求条数
      startTime: 0, // 可选项）以此时间戳为基准，根据 order 向前或向后查询，当值是 0 时为边界（order 值是 0 时为最大时间戳，order 值是 1 时为最小时间戳）
      count: 20, // （可选项）获取消息的数量，范围: 1-20
      order: 0, // 可选项）查询消息的方向，值为 0或1，0 为以 timestamp 为基准向前查询，1 为以 timestamp 为基准向后查询
      dataList: [],
      permanent: [
        // { _ajax: true, key: 'notice', latestMessage: { text: '' }, unreadMessageCount: 0, pointType: 'small', url: require('../../../assets/img/icon_book_notice.png'), userName: '通知', sentTime: '' },
        // { _ajax: true, key: 'readActivity', latestMessage: { text: '' }, unreadMessageCount: 0, url: require('../../../assets/img/icon_book_activity.png'), userName: '活动', sentTime: '' },
        { _ajax: true, key: 'user', latestMessage: { text: '' }, unreadMessageCount: 0, pointType: 'small', url: require('../../../assets/img/icon_book_user.png'), userName: '通讯录', sentTime: '' },
        { _ajax: true, key: 'group', latestMessage: { text: '' }, unreadMessageCount: 0, pointType: 'small', url: require('../../../assets/img/icon_book_group.png'), userName: '群组', sentTime: '' }
      ],
      isMember: false
    })
    watch(() => data.dataList, (newName, oldName) => {
      console.log(newName)
      setTimeout(() => {
        data.dataList.forEach((element, index) => {
          console.log(element.name)
          if (!element.name) {
            console.log('删除' + element.targetId)
            removeConversation(element, index)
            clearMessagesUnreadStatus(element)
          }
        })
      }, 1000)
    })
    onMounted(() => {
      const roleList = sessionStorage.getItem('roleList')
      if (roleList && roleList.indexOf('政协委员') >= 0) {
        data.isMember = true
      }
      // init()
    })

    const getHistory = () => {
      RongIMLib.getConversationList({
        count: data.count,
        startTime: data.startTime,
        order: data.order
      }).then(res => {
        if (res.code === 0) {
          console.log(res.code, res.data)
          data.loading = false
          const showDatas = []
          var allUnreadNum = 0
          res.data.forEach(element => {
            getUserMsg(element)
            element.latestMessage.content.extra = element.latestMessage.content.extra ? JSON.parse(element.latestMessage.content.extra) : ''
            getUserInfo(element.latestMessage)
            element.sentTime = getConversionTime(new Date(element.latestMessage.sentTime), new Date(), 1)
            element.objectName = element.latestMessage.messageType
            if (element.unreadMessageCount > 0) {
              allUnreadNum = allUnreadNum + Number(element.unreadMessageCount || 0)
            }
            showDatas.push(element)
          })
          console.log(allUnreadNum)
          if (allUnreadNum === 0) {
            RongIMLib.clearAllMessagesUnreadStatus()// 清除全部未读数
          }
          setTimeout(() => {
            data.dataList = data.dataList.concat(showDatas)
          }, 500)
          data.loading = false
          data.refreshing = false
          // 数据全部加载完成
          if (res.data.length < data.count) {
            data.finished = true
          }
        } else {
          console.log(res.code, res.msg)
        }
      })
    }

    const openDetails = (_item) => {
      if (_item.key) {
        var myParam = {}
        myParam.title = _item.userName// 页面名字
        switch (_item.key) {
          case 'readActivity':// 读书活动
            myParam = {}
            myParam.title = '活动'// 页面名字
            // T.openWin('readActivity_win' + myParam.pageOnly, 'widget://wgt/A00002/html/module/mo_readActivity/readActivity_win.html', myParam, true, false)
            break
          case 'user':
            myParam = {}
            myParam.title = '通讯录'// 页面名字
            myParam.onlyPage = '1'
            router.push({ name: 'addressBook', query: myParam })
            return
          case 'group':
            myParam = {}
            myParam.title = '群组'// 页面名字
            myParam.onlyPage = '2'
            router.push({ name: 'groupList', query: myParam })
            return
          case 'notice':
            myParam = {}
            myParam.title = '通知'// 页面名字
            router.push({ name: 'bookNotice', query: myParam })
            return
        }
      }
      router.push({ name: 'chatRoom', query: { id: _item.targetId, conversationType: _item.conversationType, name: _item.name } })
      clearMessagesUnreadStatus(_item)
      // RongIMLib.clearAllMessagesUnreadStatus()// 清除全部未读数
    }

    const getUserInfo = async (_item) => {
      var datas = {
        id: _item.senderUserId.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      var { data: userInfo } = await $api.rongCloud.getUserInfo(datas)
      _item.img = userInfo.fullImgUrl || ''
      _item.name = userInfo.userName || ''
    }
    // 获取当前的用户的信息
    const getUserMsg = async (_item, _index) => {
      // 每次请求 可放else里面减少请求
      var datas = {
        id: _item.targetId.split(sessionStorage.getItem('rongCloudIdPrefix'))[1]
      }
      // 1: 单聊3: 群聊4: 聊天室5: 客服会话6: 系统消息7: 默认关注的公众号8: 手动关注的公众号9: RTCLib 房间
      if (_item.conversationType === 1) {
        var { data: userInfo } = await $api.rongCloud.getUserInfo(datas)
        _item.img = userInfo.fullImgUrl || ''
        _item.name = userInfo.userName || ''
        _item.position = userInfo.position || ''
      } else if (_item.conversationType === 3) {
        var res = await $api.rongCloud.getGroupInfo(datas)
        var { data: gruopInfo } = res
        _item.img = gruopInfo.groupImgUrl || ''
        _item.name = gruopInfo.groupName || ''
        _item.groupOwner = gruopInfo.groupOwner || ''
        _item.groupType = gruopInfo.groupType || ''
        _item.openSay = gruopInfo.openSay || ''
      }
      // that.getChatInfo(_item, function (ret) {
      //   if (ret.isDel) {
      //     that.removeConversation(_item, _index)
      //   }
      // })
    }
    const clearMessagesUnreadStatus = (_item) => {
      const conversationType = _item.conversationType
      const targetId = _item.targetId
      RongIMLib.clearMessagesUnreadStatus({ conversationType, targetId }).then(res => {
        if (res.code === 0) {
          console.log(res.code)
        } else {
          console.log(res.code, res.msg)
        }
      })
    }
    const setConversationToTop = (_item) => {
      const conversationType = _item.conversationType
      const targetId = _item.targetId
      _item.isTop = !_item.isTop
      const isTop = _item.isTop

      RongIMLib.setConversationToTop({
        conversationType,
        targetId
      }, isTop).then(({ code }) => {
        // 设置会话置顶成功
        onRefresh()
        if (!code) {
        }
      })
    }

    const removeConversation = (_item, _index) => {
      const conversationType = _item.conversationType
      RongIMLib.removeConversation({
        conversationType,
        targetId: _item.targetId
      }).then(res => {
        // 删除指定会话成功
        if (res.code === 0) {
          data.dataList.splice(_index, 1)// 删除
        } else {
          console.log(res.code, res.msg)
        }
      })
    }
    // const init = () => {
    //   RongIMLib.init({ appkey: sessionStorage.getItem('appkey') })
    //   const Events = RongIMLib.Events
    //   /**
    //      * 正在链接的事件状态
    //      */
    //   RongIMLib.addEventListener(Events.CONNECTING, () => {
    //     console.log('正在链接...')
    //   })

    //   /**
    //      * 链接到服务器会触发这个事件
    //      */
    //   RongIMLib.addEventListener(Events.CONNECTED, () => {
    //     console.log('连接成功')
    //   })

    //   /**
    //      * 手动调用 disconnect 方法或者用户被踢下线 会触发这个事件
    //      */
    //   RongIMLib.addEventListener(Events.DISCONNECT, () => {
    //     console.log('连接中断，需要业务层进行重连处理')
    //   })

    //   /**
    //      * 链接出问题时，内部进行重新链接，会出发这个事件
    //      */
    //   RongIMLib.addEventListener(Events.SUSPEND, () => {
    //     console.log('链接中断，SDK 会尝试重连，业务层无需关心')
    //   })
    //   /** 接收消息监听器 */
    //   const callback = function (messages) {
    //     console.log(messages)
    //     // onRefresh()
    //     var element = messages.messages[0]
    //     data.dataList.forEach(item => {
    //       if (item.targetId === element.targetId) {
    //         item.latestMessage.content = element.content
    //         getUserInfo(item.latestMessage)
    //         item.sentTime = getConversionTime(new Date(element.sentTime), new Date(), 1)
    //         const conversationType = item.conversationType
    //         const targetId = item.targetId
    //         RongIMLib.getUnreadCount({ conversationType, targetId }).then(res => {
    //           if (res.code === 0) {
    //             console.log(res.code, res.data)
    //             item.unreadMessageCount = res.data
    //           } else {
    //             console.log(res.code, res.msg)
    //           }
    //         }).catch(error => {
    //           console.log(error)
    //         })
    //       }
    //     })
    //   }
    //   RongIMLib.addEventListener(Events.MESSAGES, callback)

    //   RongIMLib.connect(sessionStorage.getItem('rongCloudToken')).then((res) => {
    //     console.log(res)
    //     onRefresh()
    //   })
    // }
    const onRefresh = () => {
      data.order = 0
      data.startTime = 0
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      getHistory()
    }
    const onClickLeft = () => history.back()
    const onLoad = () => {
      // getHistory()
    }
    return { ...toRefs(data), onRefresh, onLoad, onClickLeft, openDetails, clearMessagesUnreadStatus, setConversationToTop, removeConversation }
  }
}
</script>
<style lang="less" scoped>
@import "./msgList.less";
</style>

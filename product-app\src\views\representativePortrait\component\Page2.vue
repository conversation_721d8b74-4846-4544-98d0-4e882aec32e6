<template>
  <div class="Pie2">
    <div class="container_swipe">
      <div class="pages2">
        <img src="../../../assets/img/timeAxis/g_bg02.png"
             alt=""
             style="width:100%;height:100%;">
        <div class="pages_item2">
          <div class="pages_item2_text0"
               :class="{ 'fade-in1': showText1 }">{{ year2 }}年{{ month2 }}月{{ day2 }}日</div>
          <div class="pages_item2_text1"
               :class="{ 'fade-in1': showText1 }">您第一次登录<span>青岛人大APP</span></div>
          <div class="pages_item2_text2"
               :class="{ 'fade-in1': showText2 }">全年</div>
          <div class="pages_item2_text3"
               :class="{ 'fade-in1': showText2 }">您登录青岛人大<span>{{loginTimes}}</span>次</div>
          <div class="pages_item2_text4"
               :class="{ 'fade-in1': showText3 }">您最晚在线时间为</div>
          <div class="pages_item2_text5"
               :class="{ 'fade-in1': showText3 }">{{ lastLogin }}{{ lastLoginTimeHour }}时{{ lastLoginTimeSecond }}分</div>
        </div>
        <div class="bottom_img">
          <img src="../../../assets/img/timeAxis/g_bg02_bottom.png"
               alt=""
               style="width:100%;height:100%;">
        </div>
        <div class="more">
          <div class="drop">︽</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, ref, onBeforeUnmount, onUpdated } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'Page2',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  props: {
    showText1: Boolean,
    showText2: Boolean,
    showText3: Boolean,
    pageData: Object
  },
  setup (props) {
    const router = useRouter()
    const route = useRoute()
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const touchStartY = ref(0)
    const touchMoveY = ref(0)
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      year2: '', // 第一次登录年
      month2: '', // 第一次登录月
      day2: '', // 第一次登录日
      loginTimes: 0, // 登录次数
      lastLogin: '', // 最晚时间 年月日
      lastLoginTimeHour: '', // 最晚时间 时
      lastLoginTimeSecond: '', // 最晚时间 秒
      showText1: false,
      showText2: false,
      showText3: false
    })
    onUpdated(() => {
      data.showText1 = props.showText1
      data.showText2 = props.showText2
      data.showText3 = props.showText3
      data.loginTimes = props.pageData.loginTimes
      data.lastLogin = props.pageData.lastLogin ? props.pageData.lastLogin.split(' ')[0] : ''
      data.lastLoginTime = props.pageData.lastLogin ? props.pageData.lastLogin.substring(11, 17) : ''
      data.lastLoginTimeHour = props.pageData.lastLogin ? props.pageData.lastLogin.substring(11, 14) : ''
      data.lastLoginTimeSecond = props.pageData.lastLogin ? props.pageData.lastLogin.substring(15, 17) : ''
      data.year2 = props.pageData.firstLogin ? props.pageData.firstLogin.substring(0, 4) : ''
      data.month2 = props.pageData.firstLogin ? props.pageData.firstLogin.substring(5, 7) : ''
      data.day2 = props.pageData.firstLogin ? props.pageData.firstLogin.substring(8, 10) : ''
    })
    onMounted(() => {
      // memberPortraitDutyTime()
      preventScroll()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleMove, { passive: false })
    }
    const handleMove = (event) => {
      event.preventDefault()
    }
    // 获取数据
    // const memberPortraitDutyTime = async () => {
    //   const res = await $api.representativePortrait.memberPortraitDutyTime()
    //   console.log('获取数据===>>', res)
    //   data.loginTimes = res.data.loginTimes
    //   data.lastLogin = res.data.lastLogin.split(' ')[0]
    //   data.lastLoginTime = res.data.lastLogin.substring(11, 17)
    //   data.lastLoginTimeHour = res.data.lastLogin.substring(11, 14)
    //   data.lastLoginTimeSecond = res.data.lastLogin.substring(15, 17)
    //   data.year2 = res.data.firstLogin.substring(0, 4)
    //   data.month2 = res.data.firstLogin.substring(5, 7)
    //   data.day2 = res.data.firstLogin.substring(8, 10)
    // }
    const handleTouchStart = (event) => {
      touchStartY.value = event.touches[0].clientY
    }
    const handleTouchMove = (event) => {
      touchMoveY.value = event.touches[0].clientY
      if (touchMoveY.value < touchStartY.value) {
        // 向上滑动
        console.log('向上滑动')
        setTimeout(() => {
          router.push('/Page3')
        }, 500) // 延迟500毫秒
      } else {
        // 向下滑动
        console.log('向下滑动')
        setTimeout(() => {
          router.push('/Page1')
        }, 500) // 延迟500毫秒
      }
    }
    return { ...toRefs(data), $general, handleTouchStart, handleTouchMove }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiHei-2";
  src: url("../../../assets/img/timeAxis/font/YouSheBiaoTiHei-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}
.Pie2 {
  width: 100%;
  min-height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages_item2_text0,
    .pages_item2_text1,
    .pages_item2_text2,
    .pages_item2_text3,
    .pages_item2_text4,
    .pages_item2_text5 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages_item2_text0.fade-in,
    .pages_item2_text1.fade-in,
    .pages_item2_text2.fade-in,
    .pages_item2_text3.fade-in,
    .pages_item2_text4.fade-in,
    .pages_item2_text5.fade-in {
      opacity: 1;
    }
    .pages2 {
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages_item2 {
        position: absolute;
        top: 30%;
        left: 0.6rem;
        .pages_item2_text0 {
          color: #634ea2;
          font-size: 0.65rem;
          font-family: "YouSheBiaoTiHei-2";
        }
        .pages_item2_text1 {
          color: #fff;
          font-size: 0.45rem;
          margin-top: 0.05rem;

          span {
            color: #fff;
            font-size: 0.5rem;
            font-weight: 800;
            margin-left: 0.1rem;
          }
        }
        .pages_item2_text2 {
          color: #fff;
          font-size: 0.45rem;
          margin-top: 0.1rem;
          letter-spacing: 3px;
        }
        .pages_item2_text3 {
          color: #fff;
          font-size: 0.45rem;
          letter-spacing: 2px;

          span {
            font-size: 0.8rem;
            color: #634ea2;
            margin: 0 0.1rem;
            font-family: "YouSheBiaoTiHei-2";
          }
        }
        .pages_item2_text4 {
          color: #fff;
          font-size: 0.45rem;
          margin-top: 0.1rem;
          letter-spacing: 2px;
        }
        .pages_item2_text5 {
          color: #634ea2;
          font-size: 0.65rem;
          font-family: "YouSheBiaoTiHei-2";
          margin-top: 0.1rem;
        }
      }
      .bottom_img {
        position: absolute;
        bottom: 0;
      }
    }
    .fade-in1 {
      opacity: 0;
      animation: fade-in-animation 3s forwards;
    }

    @keyframes fade-in-animation {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
    .more {
      position: absolute;
      bottom: 1rem;
      left: 4.5rem;
      .drop {
        font-size: 30px;
        animation: drop 1s linear infinite;
      }
      // .text {
      //   color: #454545;
      // }
    }
    @keyframes drop {
      0% {
        opacity: 0;
        margin-top: 0px;
      }

      25% {
        opacity: 0.5;
        margin-top: -10px;
      }

      50% {
        opacity: 1;
        margin-top: -20px;
      }

      75% {
        opacity: 0.5;
        margin-top: -30px;
      }

      100% {
        opacity: 0;
        margin-top: -40px;
      }
    }
  }
}
</style>

import axios from 'axios'
import Qs from 'qs'
import router from '../router'
import CryptoJs from 'crypto-js/crypto-js'
import {
  Toast
} from 'vant'

var loginUc = 'http://**************:22324/server'
var baseURL = 'http://**************:22325/lzt' // 河南政协平台版
// loginUc = 'https://lzpt.hnzx.gov.cn/platform-server'
// baseURL = 'https://lzpt.hnzx.gov.cn/platform-lzt'// 河南政协正数-测试环境域名
console.log(process.env.STAGE)
if (process.env.STAGE == 'zyrd_qingdao_test') { // eslint-disable-line
  loginUc = 'http://**************:22507/server'
  baseURL = 'http://**************:22508/lzt' // 正宇政协产品
  // loginUc = 'http://**************/server'
  // baseURL = 'http://**************/lzt'
  // loginUc = 'http://localhost:8080/server'
  // baseURL = 'http://localhost:8080/lzt'
} else if (process.env.STAGE == 'zyrd_qingdao_sdt') { // eslint-disable-line
  loginUc = 'http://************:22507/server'
  baseURL = 'http://************:22508/lzt' // 青岛人大山东通正式请求
} else if (process.env.STAGE == 'zyrd_platform_prod_81') { // eslint-disable-line
  loginUc = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/server`
  baseURL = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/lzt`
}

var timeout = 16000
axios.defaults.baseURL = baseURL

// 请求超时时间
axios.defaults.timeout = timeout
// 设置post请求头
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
// 请求拦截器
axios.interceptors.request.use(
  config => {
    if (config.url.indexOf('push/rongCloud') >= 0) {
      config.baseURL = sessionStorage.getItem('tomcatAddress')
      // } else if (config.url.indexOf('www.yozodcs.com') >= 0) {
    } else if (config.url.indexOf('rest/oagxh/personal_getdetail_v7') >= 0) {
      const token = sessionStorage.getItem('otherToken') || ''
      config.headers.Authorization = token
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
    } else {
      config.headers.certId = sessionStorage.getItem('useridN')
      if (config.header.token) {
        config.headers.token = config.header.token
      } else {
        // 自定义请求头参数
        const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token') || ''
        const areaId = JSON.parse(sessionStorage.getItem('areaId')) || ''
        config.headers.Authorization = token ? JSON.parse(token) : 'basic enlzb2Z0Onp5c29mdCo2MDc5'
        config.headers['u-login-areaId'] = areaId || ''
      }
      if (config.method === 'post') {
        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line
          if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {
            config.data = Qs.stringify(config.data)
          }
        }
      }
      var appSecret = '3d5ef53424f24852a8fc1d15a1072b42'
      var clientId = '816cc044c46247d987bbf611ced1cf5e'
      var clientTypeId = sessionStorage.getItem('BigDataUser') || 'qdrdplatform'
      var timestamp = Date.parse(new Date()) / 1000 // 时间戳，精确到秒
      config.headers.timestamp = timestamp
      config.headers.clientId = clientId
      config.headers.clientTypeId = clientTypeId
      config.headers.signature = getSignature(config.url, config.method, timestamp, clientId, appSecret)
      config.headers['u-app-user'] = true
      if (config.url.includes('datacenter/common') && process.env.STAGE == 'zyrd_platform_prod_81') {// eslint-disable-line
        config.baseURL = 'http://**************'
      } else if (config.url.includes('datacenter/common')) {
        config.baseURL = 'http://**************'
      }
    }
    return config
  }, error => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

function getSignature(url, method, timestamp, clientId, appSecret) {
  var HTTPMethod = method ? method.toUpperCase() : 'GET'
  var URI = ''
  var param = ''
  url = baseURL + url
  try {
    if (url.indexOf('?') >= 0) {
      URI = url.split('?')[0].split(url.split('/')[3])[1]
      param = HTTPMethod === 'POST' ? '' : (url.split('?')[1])
    } else {
      URI = url.split(url.split('/')[3])[1]
    }
  } catch (e) {
    console.error('语句异常：' + e.message)
  }
  var Message = timestamp + '-' + HTTPMethod + '-' + URI + '-' + getParam(HTTPMethod, param)
  var hash = '' + CryptoJs.HmacSHA256(Message, appSecret)
  return hash
}
/**
 * _httpMethod:请求方法（必须大写）
 * _param参数字符串
 * 将所有GET参数值的 char 值加和生成一个整数 POST方法返回0
 */
function getParam(_httpMethod, _param) {
  var jsonarr = _param.split('&')
  var signStr = ''
  for (var i in jsonarr) {
    var s = '' + jsonarr[i]
    var key = s.split('=')[0]
    var value = s.split('=')[1]
    // eslint-disable-next-line eqeqeq
    if (key == 'timestamp' || key == 'clientId' || key == 'signatrue') {
      continue
    }
    // eslint-disable-next-line eqeqeq
    if (!value || value == '' || value == undefined || value == null) {
      continue
    }
    signStr += key
  }
  // eslint-disable-next-line eqeqeq
  if (signStr == '') {
    return 0
  }
  var paramIntValue = 0
  for (var k = 0; k < signStr.length; k++) {
    paramIntValue += signStr.charCodeAt(k)
  }
  // eslint-disable-next-line eqeqeq
  if (_httpMethod == 'POST') {
    paramIntValue = 0
  }
  return paramIntValue
}

// 响应拦截器
axios.interceptors.response.use(
  response => {
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (response.data.errcode === 200) {
      return Promise.resolve(response)
    } else if (response.data.errcode === 302) {
      Toast.fail(response.data.errmsg || response.data.message)
      sessionStorage.clear()
      router.push({
        name: 'login'
      })
      return Promise.reject(response)
    } else if (response.data.errcode === undefined) { // undefind 为文件下载接口
      return Promise.resolve(response)
    } else {
      if (response.data.errmsg) {
        Toast.fail(response.data.errmsg || response.data.message)
      }
      return Promise.reject(response)
    }
  }, error => {
    if (error.message.includes('timeout')) {
      Toast.fail('请求超时，请稍后重试！')
      return Promise.reject(error)
    } else if (error.message.includes('404')) {
      Toast.fail('404了哟~')
    } else if (error && (error.response.data.errmsg || error.response.data.message)) {
      Toast.fail(error.response.data.errmsg || error.response.data.message)
      return Promise.reject(error)
    }
    return Promise.reject(error)
  }
)
class HTTP {
  request({
    url,
    data = {},
    method = 'post',
    header = {}
  }) {
    return new Promise((resolve, reject) => {
      this._request(url, this.filter(data), method, header, resolve, reject)
    })
  }

  _request(url, data = {}, method, header, resolve, reject) {
    axios({
      url: url,
      data: data,
      method: method,
      header: header
    }).then(res => {
      resolve(res.data)
    }).catch(err => {
      reject(err)
    })
  }

  filter(param) { // 参数过滤
    var data = param
    for (var key in data) {
      if (data[key] === null) {
        delete data[key]
      }
    }
    return data
  }

  file({
    url,
    data = {},
    type = 'blob',
    method = 'post',
    header = {}
  }) {
    return new Promise((resolve, reject) => {
      this._file(url, this.filter(data), method, header, type, resolve, reject)
    })
  }

  _file(url, params, method, header, type, resolve, reject) {
    axios({
      url: url,
      data: params,
      method: method,
      header: header,
      responseType: type
    }).then(res => {
      resolve(res.data)
    }).catch(err => {
      reject(err)
    })
  }
}

export {
  HTTP,
  loginUc
}

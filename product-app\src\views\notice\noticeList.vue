<template>
  <div class="noticeList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="noticeItemBox"
             v-for="item in dataList"
             @click="details(item)"
             :key="item.id">
          <div class="noticeTime">{{item.publishDate}}</div>
          <div class="noticeItem"
               :class="{noticeItemIcon:item.read}">
            <div class="noticeTitle">
              <span v-if="item.isTop">置顶</span>
              <div class="ellipsis">{{item.noticeTitle}}</div>
            </div>
            <div class="noticeContent ellipsis">{{item.content}}</div>
            <div class="noticeImg">
              <img v-if="item.type == '通知'"
                   src="../../assets/img/noticeImg.png"
                   alt="">
              <img v-if="item.type == '公告'"
                   src="../../assets/img/cys.png"
                   alt="">
              <img v-if="item.type == '倡仪书'"
                   src="../../assets/img/cys.png"
                   alt="">
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs } from 'vue'
import { NavBar, Sticky } from 'vant'
export default {
  name: 'noticeList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      type: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      noticeList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        noticeList()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      noticeList()
    }
    // 列表请求
    const noticeList = async () => {
      const res = await $api.notice.noticeList({
        pageNo: data.pageNo,
        pageSize: 5,
        noticeTitle: data.keyword,
        type: data.type,
        isAppShow: 1
      })
      var { data: list, total } = res
      list.forEach(item => {
        item.content = item.content ? DeleteHtmlFromStartToEnd(item.content, '<!--', '-->').replace(/<.*?>/g, '').replace(/&nbsp;/ig, '') : ''
      })
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const DeleteHtmlFromStartToEnd = (str, begin, end) => {
      str = str.replace(begin + end, '')
      if (str.indexOf(begin) === -1) {
        return str
      }
      var substr = str.substring(str.indexOf(begin) + begin.length, str.indexOf(end))
      str = str.replace(substr, '')
      return DeleteHtmlFromStartToEnd(str, begin, end)
    }
    const onClickLeft = () => history.back()
    const details = (row) => {
      router.push({ name: 'noticeDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), search, onClickLeft, onRefresh, onLoad, details }
  }
}
</script>
<style lang="less">
.noticeList {
  width: 100%;
  min-height: 100%;
  background: #f8f8f8;
  .noticeListHead {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3088fe;
    .van-search {
      width: 100%;
      // background-color: rgba(255, 255, 255, 0.3);
      .van-cell {
        padding: 3px 0;
      }
      .van-icon {
        color: #888;
      }
      .van-search__content {
        height: 30px;
        line-height: 30px;
        padding-right: 8px;
        // background-color: rgba(255, 255, 255, 0.3);
        .van-field__body {
          font-size: 14px;
          .van-field__control {
            color: #888;
            &::-webkit-input-placeholder {
              color: #888;
            }
          }
        }
      }
    }
  }
  .noticeItemBox {
    width: 100%;
    padding-top: 10px;
    .noticeTime {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 17px;
      color: #999999;
      padding: 10px 0;
      text-align: center;
    }
    .noticeItem {
      width: 343px;
      background: #ffffff;
      border-radius: 4px;
      margin: auto;
      padding: 10px;
      position: relative;
      &::after {
        content: "";
        position: absolute;
        top: 4px;
        right: 10px;
        width: 7px;
        height: 7px;
        background: #f33636;
        border-radius: 50%;
      }
      .noticeTitle {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 6px 0;
        div {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 600;
          color: #333333;
          line-height: 16px;
          flex: 1;
        }
        span {
          display: inline-block;
          background: #ffffff;
          border: 1px solid #f33636;
          border-radius: 2px;
          font-size: 10px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #f33636;
          line-height: 16px;
          padding: 0 6px;
          margin-right: 5px;
        }
      }
      .noticeContent {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 22px;
        color: #666666;
        padding-bottom: 6px;
      }
      .noticeImg {
        width: 323px;
        height: 107px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .noticeItemIcon {
      &::after {
        background: transparent;
      }
    }
  }
}
</style>

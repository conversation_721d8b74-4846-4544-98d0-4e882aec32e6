{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue?vue&type=template&id=53054bfa&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue", "mtime": 1756438117302}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImxlYWRlckRyaXZpbmciPg0KICAgIDxkaXYgY2xhc3M9ImxlYWRlckRyaXZpbmdfdG9wIj4NCiAgICA8L2Rpdj4NCiAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX3RhYiI+DQogICAgICA8ZGl2IEBjbGljaz0idGFiQ2xpY2soaXRlbSkiDQogICAgICAgIDpjbGFzcz0ieyBsZWFkZXJEcml2aW5nX3RhYl9pdGVtOiB0cnVlLCBsZWFkZXJEcml2aW5nX3RhYl9pdGVtX2FjdGl2ZTogYWN0aXZlID09IGl0ZW0udmFsdWUgfSINCiAgICAgICAgdi1mb3I9Iml0ZW0gaW4gdGFiTGlzdCIgOmtleT0iaXRlbS52YWx1ZSI+e3sgaXRlbS5uYW1lIH19PC9kaXY+DQogICAgPC9kaXY+DQogICAgPGRpdiB2LWlmPSJhY3RpdmUgPT0gMSI+DQogICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX3RpdGxlIj4NCiAgICAgICAg5YWo5biC5qaC5ousDQogICAgICA8L2Rpdj4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9Iue7hOe7h+amguaLrCI+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX2dlbmVyYWxpemUiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGVhZGVyRHJpdmluZ19nZW5lcmFsaXplX2l0ZW0iIHYtZm9yPSJpdGVtLCBpbmRleCBpbiBnZW5lcmFsaXplIiA6a2V5PSJpbmRleCI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImxlYWRlckRyaXZpbmdfZ2VuZXJhbGl6ZV9pdGVtX251bSINCiAgICAgICAgICAgICAgICA6c3R5bGU9InsgY29sb3I6IGluZGV4ID09IDAgPyAnIzM4OTRmZicgOiBpbmRleCA9PSAxID8gJyM0YWRiNDcnIDogJyNmZjZkYTInIH0iPg0KICAgICAgICAgICAgICAgIHt7IGl0ZW0ubnVtIH19DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX2dlbmVyYWxpemVfaXRlbV90aXRsZSI+DQogICAgICAgICAgICAgICAge3sgaXRlbS50aXRsZSB9fQ0KICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLmgLvnlKjmiLfph48iPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPEJhciA6Y29sb3I9IidyZ2JhKDYwLCAxNTAsIDI1NSknIiBpZD0iYmFyMSIgdi1pZj0icmVwcmVzZW50YXRpdmVWb3MubGVuZ3RoIiA6bGlzdD0icmVwcmVzZW50YXRpdmVWb3MiPjwvQmFyPg0KICAgICAgICAgIDxCYXIgOmNvbG9yPSIncmdiYSgyNTUsIDEyMywgNDkpJyIgaWQ9ImJhcjIiIHYtaWY9Im9mZmljZVZvcy5sZW5ndGgiIDpsaXN0PSJvZmZpY2VWb3MiPjwvQmFyPg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX3RpdGxlIj4NCiAgICAgICAg6Z2S5bKb5biC5pys57qnDQogICAgICA8L2Rpdj4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9IuW4guS7o+ihqOWPmOWKqOaDheWGtSI+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX2dlbmVyYWxpemUiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGVhZGVyRHJpdmluZ19nZW5lcmFsaXplX2l0ZW0iIHYtZm9yPSJpdGVtLCBpbmRleCBpbiByZXByZXNlbnRhdGl2ZSIgOmtleT0iaW5kZXgiDQogICAgICAgICAgICAgIEBjbGljaz0icmVwcmVzZW50YXRpdmVDbGljayhpdGVtKSI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImxlYWRlckRyaXZpbmdfZ2VuZXJhbGl6ZV9pdGVtX3RpdGxlIj4NCiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz0ibGVhZGVyRHJpdmluZ19nZW5lcmFsaXplX2l0ZW1fdGl0bGVfc3BhbiI+e3sgaXRlbS50aXRsZSB9fTwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8c3BhbiB2LWlmPSJpbmRleCA9PSAwIj4NCiAgICAgICAgICAgICAgICAgIDxlbC1pY29uIHN0eWxlPSJjb2xvcjogIzQxY2U4MTsiPg0KICAgICAgICAgICAgICAgICAgICA8Qm90dG9tIHN0eWxlPSJ3aWR0aDogMC40OHJlbTtoZWlnaHQ6IDAuNDhyZW07bWFyZ2luLWJvdHRvbTogLTAuMXJlbTsiIC8+DQogICAgICAgICAgICAgICAgICA8L2VsLWljb24+DQogICAgICAgICAgICAgICAgPC9zcGFuPg0KICAgICAgICAgICAgICAgIDxzcGFuIHYtZWxzZT4NCiAgICAgICAgICAgICAgICAgIDxlbC1pY29uIHN0eWxlPSJjb2xvcjogI2ZmNmRhMjsiPg0KICAgICAgICAgICAgICAgICAgICA8VG9wIHN0eWxlPSJ3aWR0aDogMC40OHJlbTtoZWlnaHQ6IDAuNDhyZW07bWFyZ2luLWJvdHRvbTogLTAuMXJlbTsiIC8+DQogICAgICAgICAgICAgICAgICA8L2VsLWljb24+DQogICAgICAgICAgICAgICAgPC9zcGFuPg0KICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGVhZGVyRHJpdmluZ19nZW5lcmFsaXplX2l0ZW1fbnVtIiA6c3R5bGU9InsgY29sb3I6IGluZGV4ID09IDAgPyAnIzQxY2U4MScgOiAnI2ZmNmRhMicgfSI+DQogICAgICAgICAgICAgICAge3sgaW5kZXggPT0gMCA/ICctJyA6ICcrJyB9fXt7IGl0ZW0ubnVtIH19DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9IuWQhOS7o+ihqOWbouS6uuaVsCI+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8QmFyIDpjb2xvcj0iJ3JnYmEoMjU1LCAxMTAsIDExMCknIiBpZD0iYmFyMyIgdi1pZj0icmVwcmVzZW50ZXJUZWFtLmxlbmd0aCIgOmxpc3Q9InJlcHJlc2VudGVyVGVhbSI+PC9CYXI+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9IuS7o+ihqOWtpuWOhuWIhuaekCI+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8UmFkYXIgaWQ9InJhZGFyMSIgdi1pZj0ibWVtYmVyRWR1Y2F0aW9uRGF0YS5sZW5ndGgiIDpsaXN0PSJtZW1iZXJFZHVjYXRpb25EYXRhIj48L1JhZGFyPg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLku6PooajlubTpvoTliIbmnpAiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPFBpZSA6aWQ9IidwaWUxJyIgOmxpc3Q9ImJpcnRoZGF5IiB2LWlmPSJiaXJ0aGRheS5sZW5ndGgiPjwvUGllPg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLku6PooajmgKfliKvliIbmnpAiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic2V4X3BpZSI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3hfbGVmdCI+DQogICAgICAgICAgICAgIDxQaWUgOmlkPSIncGllMiciIHYtaWY9InNleC5sZW5ndGgiIDpsaXN0PSJzZXgiPjwvUGllPg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3hfcmlnaHQiIHYtaWY9InNleC5sZW5ndGgiPg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJ0b3AiPg0KICAgICAgICAgICAgICAgIDxkaXY+PGltZyA6c3JjPSJyZXF1aXJlKCcuLi8uLi9hc3NldHMvaW1nL21hbi5wbmcnKSIgYWx0PSIiPjwvZGl2Pg0KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzZENzg3RTsiPnt7IHNleFswXS5uYW1lIH195oCne3sgc2V4WzBdLnZhbHVlIH195ZCNPC9zcGFuPg0KICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogIzM4OTRmZjsiPnt7IHBhcnNlSW50KHNleFswXS52YWx1ZSAvIChzZXhbMF0udmFsdWUgKyBzZXhbMV0udmFsdWUpICogMTAwKSB9fSU8L3NwYW4+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3QiPg0KICAgICAgICAgICAgICAgIDxkaXY+PGltZyA6c3JjPSJyZXF1aXJlKCcuLi8uLi9hc3NldHMvaW1nL3dvbWFuLnBuZycpIiBhbHQ9IiI+PC9kaXY+DQogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjNkQ3ODdFOyI+e3sgc2V4WzFdLm5hbWUgfX3mgKd7eyBzZXhbMV0udmFsdWUgfX3lkI08L3NwYW4+DQogICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiAjZmY4MTk3OyI+e3sgcGFyc2VJbnQoc2V4WzFdLnZhbHVlIC8gKHNleFswXS52YWx1ZSArIHNleFsxXS52YWx1ZSkgKiAxMDApIH19JTwvc3Bhbj4NCiAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveCB0aXRsZT0i5Luj6KGo57uT5p6E5YiG5p6QIj4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxCYXIgOmNvbG9yPSIncmdiYSg2MCwgMTUwLCAyNTUpJyIgaWQ9ImJhcjQiIHYtaWY9InJlcHJlc2VudGVyRWxlbWVudC5sZW5ndGgiIDpsaXN0PSJyZXByZXNlbnRlckVsZW1lbnQiPg0KICAgICAgICAgIDwvQmFyPg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgPC9kaXY+DQogICAgPGRpdiB2LWlmPSJhY3RpdmUgPT0gMiI+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0iY29udGVudF9ib3giPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZ2VzdF90aXRsZSI+DQogICAgICAgICAgICAgIOS7iuaXpeaPkOS6pOaAu+minQ0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X251bSI+DQogICAgICAgICAgICAgIDxzcGFuIHN0eWxlPSJmb250LXdlaWdodDogNzAwOyI+e3sgQWR2aWNlQnlUb2RheSB9fTwvc3Bhbj4NCiAgICAgICAgICAgICAg5Lu2DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveD4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdGl0bGUiPg0KICAgICAgICAgICAg5bu66K6u5oC75pWwDQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZ2VzdF9ib3giPg0KICAgICAgICAgICAgPGRpdiA6Y2xhc3M9Insgc3VnZ2VzdF9tZWV0OiBpbmRleCA9PSAwLCBzdWdnZXN0X2ZsYXQ6IGluZGV4ID09IDEgfSIgdi1mb3I9Iml0ZW0sIGluZGV4IGluIEFkdmljZUJ5RG9tYWluIg0KICAgICAgICAgICAgICA6a2V5PSJpbmRleCI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Im1lZXRfbnVtIiBAY2xpY2s9InN1Z2dlc3RHb0xpbmsoaXRlbS5zdWdnZXN0aW9uRmxhZyA9PSAn5bmzJyA/ICcyJyA6ICcxJykiPg0KICAgICAgICAgICAgICAgIDxzcGFuPnt7IGl0ZW0uYWR2aWNlQ291bnQgfX08L3NwYW4+DQogICAgICAgICAgICAgICAg5Lu2DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RyYW5zYWN0aW9uIiBAY2xpY2s9InN1Z2dlc3RHb0xpbmsoaXRlbS5zdWdnZXN0aW9uRmxhZyA9PSAn5bmzJyA/ICcyJyA6ICcxJywgJzEwMjAnKSI+DQogICAgICAgICAgICAgICAg5q2j5Zyo5Yqe55CGPHNwYW4+e3sgaXRlbS50cmFuc2FjdGluZyB9fTwvc3Bhbj7ku7YNCiAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdHJhbnNhY3Rpb24iIEBjbGljaz0ic3VnZ2VzdEdvTGluayhpdGVtLnN1Z2dlc3Rpb25GbGFnID09ICflubMnID8gJzInIDogJzEnLCAnMTEwMCcpIj4NCiAgICAgICAgICAgICAgICDlt7Llip7nu5M8c3Bhbj57eyBpdGVtLnRyYW5zYWN0QWNjb21wbGlzaCB9fTwvc3Bhbj7ku7YNCiAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIj4NCiAgICAgICAgICAgIOexu+WIq+WNoOavlA0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxQaWUgOmlkPSIncGllMyciIHYtaWY9ImN1cnJlbnRDYXRlZ29yeURhdGEubGVuZ3RoIiA6bGlzdD0iY3VycmVudENhdGVnb3J5RGF0YSI+PC9QaWU+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3g+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIj4NCiAgICAgICAgICAgIOW7uuiurueDreivjQ0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9ImhvdFdvcmQiIHYtZm9yPSJpdGVtLCBpbmRleCBpbiBrZXl3b3JkcyIgOmtleT0iaW5kZXgiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaG90V29yZF9pdGVtIj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5kZXgiDQogICAgICAgICAgICAgICAgOnN0eWxlPSJ7ICdjb2xvcic6IGluZGV4ID09IDAgPyAnI2ZiMmYyZicgOiBpbmRleCA9PSAxID8gJyNmZjgzM2QnIDogaW5kZXggPT0gMiA/ICcjZmZjZjU1JyA6ICcnIH0iPg0KICAgICAgICAgICAgICAgIHt7IGluZGV4ICsgMSB9fTwvZGl2Pg0KICAgICAgICAgICAgICB7eyBpdGVtIH19DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImhvdFdvcmRfcmlnaHQiDQogICAgICAgICAgICAgIDpzdHlsZT0ieyAnYmFja2dyb3VuZCc6IGluZGV4ID09IDAgPyAnI2ZiMmYyZicgOiBpbmRleCA9PSAxID8gJyNmZjgzM2QnIDogJyNmZmNmNTUnIH0iDQogICAgICAgICAgICAgIHYtaWY9ImluZGV4ICsgMSA8IDQiPg0KICAgICAgICAgICAgICDng60NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0iY29udGVudF9ib3giPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZ2VzdF90aXRsZSI+DQogICAgICAgICAgICAgIOa7oeaEj+W6pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3NhdGlzZmFjdGlvbiI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InNhdGlzZmFjdGlvbl9pdGVtIiB2LWZvcj0iaXRlbSwgaW5kZXggaW4gWyfmu6HmhI8nLCAn5Z+65pys5ruh5oSPJywgJ+S4jea7oeaEjyddIiA6a2V5PSJpbmRleCI+DQogICAgICAgICAgICAgICAgPHNwYW4gOnN0eWxlPSJ7ICdiYWNrZ3JvdW5kJzogaW5kZXggPT0gMCA/ICcjNDBjZDgwJyA6IGluZGV4ID09IDEgPyAnI2ZmZDA1NScgOiAnI2ZmNmQ2ZCcgfSI+PC9zcGFuPg0KICAgICAgICAgICAgICAgIHt7IGl0ZW0gfX0NCiAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzYXRpc2ZhY3Rpb25fdGl0bGUiPg0KICAgICAgICAgICAgPHA+5bu66K6u5ruh5oSP5bqmPC9wPg0KICAgICAgICAgICAgPHRlbXBsYXRlIHYtaWY9IkJ5U2F0aXNmYWN0aW9uLmxlbmd0aCI+DQogICAgICAgICAgICAgIDxtZW1vcnktYmFyIHYtZm9yPSJpdGVtLCBpbmRleCBpbiBCeVNhdGlzZmFjdGlvbiIgOmtleT0iaW5kZXgiIDppdGVtPSJpdGVtIj48L21lbW9yeS1iYXI+DQogICAgICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InNhdGlzZmFjdGlvbl90aXRsZSIgc3R5bGU9ImJvcmRlcjogMDsiPg0KICAgICAgICAgICAgPHA+57G75Yir5ruh5oSP5bqmPC9wPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic2F0aXNmYWN0aW9uX2l0ZW0iIHYtZm9yPSJpdGVtLCBpbmRleCBpbiBTYXRpc2ZhY3Rpb25CeURhdGEiIDprZXk9ImluZGV4Ij4NCiAgICAgICAgICAgICAgPCEtLSA8c3Bhbj57eyBpdGVtLm5hbWUgfX08L3NwYW4+IC0tPg0KICAgICAgICAgICAgICA8bWVtb3J5LWJhciA6aXRlbT0iaXRlbSI+PC9tZW1vcnktYmFyPg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic2F0aXNmYWN0aW9uX2FsbCI+DQogICAgICAgICAgICA8cCB2LWlmPSJzYXRpc2ZhY3Rpb25TdGF0dXMiIEBjbGljaz0ic2F0aXNmYWN0aW9uQWxsKGZhbHNlKSI+PHZhbi1pY29uIG5hbWU9ImFycm93LXVwIiAvPg0KICAgICAgICAgICAgICDmlLbotbc8L3A+DQogICAgICAgICAgICA8cCB2LWlmPSIhc2F0aXNmYWN0aW9uU3RhdHVzIiBAY2xpY2s9InNhdGlzZmFjdGlvbkFsbCh0cnVlKSI+PHZhbi1pY29uIG5hbWU9ImFycm93LWRvd24iIC8+DQogICAgICAgICAgICAgIOeCueWHu+WxleW8gOafpeeci+abtOWkmjwvcD4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveD4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdGl0bGUiPg0KICAgICAgICAgICAg5Luj6KGo5o+Q5Lqk5bu66K6u5oC75o6S6KGM5qacDQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPHJhbmtpbmctbGlzdCB1cmxUeXBlPSJtZWRhbCIgOmRhdGFMaXN0PSJCeVJlcHJlc2VudGF0aXZlIiA6Y2xpY2s9InRydWUiDQogICAgICAgICAgICA6dGl0bGU9Ilsn5o6S6KGMJywgJ+Wnk+WQjScsICfku7bmlbAnXSI+PC9yYW5raW5nLWxpc3Q+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3g+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIj4NCiAgICAgICAgICAgIOS7o+ihqOWbouaPkOS6pOW7uuiuruaAu+aOkuihjOamnA0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxyYW5raW5nLWxpc3QgdXJsVHlwZT0ibWVkYWwiIDpkYXRhTGlzdD0iQnlEZWxlZ2F0aW9uIiA6Y2xpY2s9InRydWUiDQogICAgICAgICAgICA6dGl0bGU9Ilsn5o6S6KGMJywgJ+S7o+ihqOWboicsICfku7bmlbAnXSI+PC9yYW5raW5nLWxpc3Q+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICA8L2Rpdj4NCiAgICA8ZGl2IHYtaWY9ImFjdGl2ZSA9PSAzIj4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3g+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXNzYWdlX2JveCI+DQogICAgICAgICAgICA8aW1nIDpzcmM9InJlcXVpcmUoJy4uLy4uL2Fzc2V0cy9pbWcvbGRqc2NfbWVzc2FnZS5wbmcnKSIgYWx0PSIiPg0KICAgICAgICAgICAgPHRlbXBsYXRlIHYtaWY9ImZpbmRXeWd6c1RpdGxlRGF0YSAmJiBmaW5kV3lnenNUaXRsZURhdGEubGVuZ3RoICE9IDAiPg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJtZXNzYWdlIj4NCiAgICAgICAgICAgICAgICA8ZGl2IHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIGZpbmRXeWd6c1RpdGxlRGF0YSIgOmtleT0iaW5kZXgiIEBjbGljaz0iTWVzc2FnZVBhZ2UoaXRlbSkiPg0KICAgICAgICAgICAgICAgICAgPGRpdiB2LWlmPSJpbmRleCA8IDIiIGNsYXNzPSJuZXdzX3RleHRfYm94X2l0ZW0iPnt7IGl0ZW0udGl0bGUgfX08L2Rpdj4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiAjN2U3ZDdkO3BhZGRpbmc6IDAuMXJlbTtwb3NpdGlvbjogYWJzb2x1dGU7cmlnaHQ6IDEwcHg7dG9wOiAwOyINCiAgICAgICAgICAgICAgICB2LWlmPSJmaW5kV3lnenNUaXRsZURhdGEubGVuZ3RoID49IDIgJiYgKGFyZWFJZCA9PSAnMzcwMjE1JyB8fCBhcmVhSWQgPT0gJzM3MDIwMCcpIg0KICAgICAgICAgICAgICAgIEBjbGljaz0ibWFzc01lc3NhZ2VzQ2xpY2siPiA+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgICAgICAgIDwhLS0gPGRpdiBjbGFzcz0ibWVzc2FnZSINCiAgICAgICAgICAgICAgICAgdi1pZj0iZmluZFd5Z3pzVGl0bGVEYXRhLmxlbmd0aCI+DQogICAgICAgICAgICAgIDxwIHYtZm9yPSJpdGVtLGluZGV4IGluIGZpbmRXeWd6c1RpdGxlRGF0YSINCiAgICAgICAgICAgICAgICAgOmtleT0iaW5kZXgiDQogICAgICAgICAgICAgICAgIHYtc2hvdz0iaW5kZXggPCAyIj48c3Bhbj57eyBpdGVtLnRpdGxlIH19PC9zcGFuPjxzcGFuIHYtaWY9ImluZGV4ID09IDAiPj48L3NwYW4+PC9wPg0KICAgICAgICAgICAgPC9kaXY+IC0tPg0KICAgICAgICAgICAgPHRlbXBsYXRlIHYtZWxzZT4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibWVzc2FnZU51bGwiPg0KICAgICAgICAgICAgICAgIOaaguaXoOaVsOaNrg0KICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvdGVtcGxhdGU+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3g+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIj4NCiAgICAgICAgICAgIOaVsOaNruamguaLrA0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9ImludGVyZmFjZV9sb2NhdGlvbl9ib3giPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW50ZXJmYWNlX2xvY2F0aW9uX2xlZnQiPg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbnRlcmZhY2VfbG9jYXRpb25fbGVmdF90aXRsZSI+DQogICAgICAgICAgICAgICAg6IGU57uc56uZ5oC75pWw6YePDQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbnRlcmZhY2VfbG9jYXRpb25fbGVmdF9ib3QiPg0KICAgICAgICAgICAgICAgIDxzcGFuPnt7IGZpbmRTdHVkaW9Db3VudEJ5Q2l0eURhdGEuc3R1ZGlvQ291bnQgfX08L3NwYW4+5LiqDQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpbnRlcmZhY2VfbG9jYXRpb25fcmlnaHQiPg0KICAgICAgICAgICAgICA8UGllMiB2LWlmPSJmaW5kV3lnenNUaXRsZXNDb3VudFNob3ciDQogICAgICAgICAgICAgICAgOmRhdGFzPSJ7IHBlcmNlbnRhZ2U6IGZpbmRXeWd6c1RpdGxlc0NvdW50RGF0YS5yZXNwb25zZVJhdGUsIG51bTogZmluZFd5Z3pzVGl0bGVzQ291bnREYXRhLm51bSwgdGV4dDogJ+WbnuWkjeeOhycsIH0iDQogICAgICAgICAgICAgICAgaWQ9InBpZTIiPjwvUGllMj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9ImludGVyZmFjZV9sb2NhdGlvbl9ib3hfYm90Ij4NCiAgICAgICAgICAgIDxkaXY+DQogICAgICAgICAgICAgIDxwPuaAu+eVmeiogOaVsDwvcD4NCiAgICAgICAgICAgICAgPHA+e3sgKGZpbmRXeWd6c1RpdGxlc0NvdW50RGF0YS5yZXBsaWVkQ291bnQgKyBmaW5kV3lnenNUaXRsZXNDb3VudERhdGEubm9ScmVwbHlDb3VudCkgPw0KICAgICAgICAgICAgICAgIChmaW5kV3lnenNUaXRsZXNDb3VudERhdGEucmVwbGllZENvdW50ICsgZmluZFd5Z3pzVGl0bGVzQ291bnREYXRhLm5vUnJlcGx5Q291bnQpIDogJ+aaguaXoOaVsOaNricgfX08L3A+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDxkaXY+DQogICAgICAgICAgICAgIDxwPuW3suWbnuWkjeaVsDwvcD4NCiAgICAgICAgICAgICAgPHA+e3sgZmluZFd5Z3pzVGl0bGVzQ291bnREYXRhLnJlcGxpZWRDb3VudCA/IGZpbmRXeWd6c1RpdGxlc0NvdW50RGF0YS5yZXBsaWVkQ291bnQgOiAn5pqC5peg5pWw5o2uJyB9fTwvcD4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPGRpdj4NCiAgICAgICAgICAgICAgPHA+5pyq5Zue5aSN5pWwPC9wPg0KICAgICAgICAgICAgICA8cD57eyBmaW5kV3lnenNUaXRsZXNDb3VudERhdGEubm9ScmVwbHlDb3VudCA/IGZpbmRXeWd6c1RpdGxlc0NvdW50RGF0YS5ub1JyZXBseUNvdW50IDogJ+aaguaXoOaVsOaNricgfX08L3A+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveD4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdGl0bGUiPg0KICAgICAgICAgICAg6IGU57uc56uZ5YiG5biDDQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPE1hcCB2LWlmPSJtYXBMaXN0U2hvdyIgOmxpc3Q9Im1hcExpc3QiIGlkPSJtYXBsaXN0Ij48L01hcD4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveD4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdGl0bGUiPg0KICAgICAgICAgICAg5bu66K6u54Ot6K+NDQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0iaG90V29yZCIgdi1mb3I9Iml0ZW0sIGluZGV4IGluIGZpbmRIb3RzcG90S2V5d29yZHNEYXRhIiA6a2V5PSJpbmRleCIgdi1zaG93PSJpbmRleCA8IDUiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaG90V29yZF9pdGVtIj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaW5kZXgiDQogICAgICAgICAgICAgICAgOnN0eWxlPSJ7ICdjb2xvcic6IGluZGV4ID09IDAgPyAnI2ZiMmYyZicgOiBpbmRleCA9PSAxID8gJyNmZjgzM2QnIDogaW5kZXggPT0gMiA/ICcjZmZjZjU1JyA6ICcnIH0iPg0KICAgICAgICAgICAgICAgIHt7IGluZGV4ICsgMSB9fTwvZGl2Pg0KICAgICAgICAgICAgICB7eyBpdGVtIH19DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImhvdFdvcmRfcmlnaHQiDQogICAgICAgICAgICAgIDpzdHlsZT0ieyAnYmFja2dyb3VuZCc6IGluZGV4ID09IDAgPyAnI2ZiMmYyZicgOiBpbmRleCA9PSAxID8gJyNmZjgzM2QnIDogJyNmZmNmNTUnIH0iDQogICAgICAgICAgICAgIHYtaWY9ImluZGV4ICsgMSA8IDQiPg0KICAgICAgICAgICAgICDng60NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZ2VzdF90aXRsZSI+DQogICAgICAgICAgICDljLrluILogZTnu5znq5nmtLvot4PluqYNCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8cmFua2luZy1saXN0IHVybFR5cGU9Im1lZGFsIiA6ZGF0YUxpc3Q9ImZpbmRXeWd6c1RpdGxlc1JhbmtpbmdEYXRhIg0KICAgICAgICAgICAgOnRpdGxlPSJbJ+aOkuihjCcsICfogZTnu5znq5knLCAn5rS76LeD5bqmJ10iPjwvcmFua2luZy1saXN0Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZ2VzdF90aXRsZSI+DQogICAgICAgICAgICDlkITljLrogZTnu5znq5nmtLvot4PluqYNCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8cmFua2luZy1saXN0IHVybFR5cGU9Im1lZGFsIiA6ZGF0YUxpc3Q9ImZpbmRXeWd6c1N0dWRpb1RpdGxlc0NvdW50RGF0YSINCiAgICAgICAgICAgIDp0aXRsZT0iWyfmjpLooYwnLCAn6IGU57uc56uZJywgJ+a0u+i3g+W6piddIj48L3JhbmtpbmctbGlzdD4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgIDwvZGl2Pg0KICAgIDxkaXYgdi1pZj0iYWN0aXZlID09IDQiPg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveD4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdGl0bGUiPg0KICAgICAgICAgICAg5biC5Luj6KGo5qCH5YW1DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0icmVwcmVzZW50YXRpdmVfdGFiIj4NCiAgICAgICAgICAgIDxkaXYgOmNsYXNzPSJ7IHJlcHJlc2VudGF0aXZlX3RhYl9pdGVtOiB0cnVlLCByZXByZXNlbnRhdGl2ZV90YWJfYWN0aXZlOiBjaXR5WWVhciA9PSB5ZWFycyB9Ig0KICAgICAgICAgICAgICBAY2xpY2s9InJlcHJlc2VudGF0aXZlVGFiKG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSkiPuW5tOW6puenr+WIhjwvZGl2Pg0KICAgICAgICAgICAgPGRpdiA6Y2xhc3M9InsgcmVwcmVzZW50YXRpdmVfdGFiX2l0ZW06IHRydWUsIHJlcHJlc2VudGF0aXZlX3RhYl9hY3RpdmU6IGNpdHlZZWFyICE9IHllYXJzIH0iDQogICAgICAgICAgICAgIEBjbGljaz0icmVwcmVzZW50YXRpdmVUYWIoJycpIj7mgLvnp6/liIY8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8cmFua2luZy1saXN0IHVybFR5cGU9Im1lZGFsIiB0eXBlPSJyZXN1bXB0aW9uIiA6ZGF0YUxpc3Q9ImR1dHludW1MaXN0Ig0KICAgICAgICAgICAgOnRpdGxlPSJbJ+aOkuihjCcsICflp5PlkI0nLCAn5b6X5YiGJ10iPjwvcmFua2luZy1saXN0Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InJlcHJlc2VudGF0aXZlX2FsbCIgQGNsaWNrPSJyb3V0ZXIucHVzaCgnL3BlcmZvcm1hbmNlRmlsZXNMaXN0JykiPg0KICAgICAgICAgICAg5p+l55yL5pu05aSaDQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3g+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIj4NCiAgICAgICAgICAgIOS7o+ihqOWbouaOkuihjA0KICAgICAgICAgICAgPHZhbi1pY29uIG5hbWU9InF1ZXN0aW9uLW8iIGNvbG9yPSIjZDVkNWQ1IiBzaXplPSIyNCIgQGNsaWNrPSJzaG93ID0gdHJ1ZSIgLz4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJyZXByZXNlbnRhdGl2ZV90YWIiPg0KICAgICAgICAgICAgPGRpdiA6Y2xhc3M9InsgcmVwcmVzZW50YXRpdmVfdGFiX2l0ZW06IHRydWUsIHJlcHJlc2VudGF0aXZlX3RhYl9hY3RpdmU6IGR1bXBsaW5nWWVhciA9PSB5ZWFycyB9Ig0KICAgICAgICAgICAgICBAY2xpY2s9ImR1bXBsaW5nVGFiKG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKSkiPuW5tOW6puenr+WIhjwvZGl2Pg0KICAgICAgICAgICAgPGRpdiA6Y2xhc3M9InsgcmVwcmVzZW50YXRpdmVfdGFiX2l0ZW06IHRydWUsIHJlcHJlc2VudGF0aXZlX3RhYl9hY3RpdmU6IGR1bXBsaW5nWWVhciAhPSB5ZWFycyB9Ig0KICAgICAgICAgICAgICBAY2xpY2s9ImR1bXBsaW5nVGFiKCcnKSI+5oC756ev5YiGPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPHJhbmtpbmctbGlzdCB1cmxUeXBlPSJ0cm9waHkiIDpkYXRhTGlzdD0iZGVsZWdhdGlvblNjb3JlIiA6dGl0bGU9Ilsn5o6S6KGMJywgJ+S7o+ihqOWboicsICflvpfliIYnXSI+PC9yYW5raW5nLWxpc3Q+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3g+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIj4NCiAgICAgICAgICAgIOWQhOWMuuW4guS7o+ihqOagh+WFtQ0KICAgICAgICAgICAgPGRpdj4NCiAgICAgICAgICAgICAgPHZhbi1wb3BvdmVyIHYtbW9kZWw6c2hvdz0ic2hvd1BvcG92ZXIiIHBsYWNlbWVuPSJib3R0b20tZW5kIiA6YWN0aW9ucz0iYXJlYXMiIEBzZWxlY3Q9Im9uU2VsZWN0Ij4NCiAgICAgICAgICAgICAgICA8dGVtcGxhdGUgI3JlZmVyZW5jZT4NCiAgICAgICAgICAgICAgICAgIDxwPnt7IGFjdGlvbnNUZXh0IH19IDx2YW4taWNvbiBuYW1lPSJwbGF5IiBzdHlsZT0idHJhbnNmb3JtOiByb3RhdGUoOTBkZWcpOyIgLz48L3A+DQogICAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgICAgICAgICAgPC92YW4tcG9wb3Zlcj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxyYW5raW5nLWxpc3QgdHlwZT0icmVzdW1wdGlvbiIgdXJsVHlwZT0ibWVkYWwiIDpkYXRhTGlzdD0iZHV0eW51bUNpdHlMaXN0Ig0KICAgICAgICAgICAgOnRpdGxlPSJbJ+aOkuihjCcsICflp5PlkI0nLCAn5b6X5YiGJ10iPjwvcmFua2luZy1saXN0Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9Im5vdFRleHQiIHN0eWxlPSJmb250LXNpemU6MTRweDtjb2xvcjogI2NjYzsiIHYtaHRtbD0icGFnZU5vdC50ZXh0IiBAY2xpY2s9ImxvYWRNb3JlKCkiPjwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgPC9kaXY+DQogICAgPGRpdiB2LWlmPSJhY3RpdmUgPT0gNSI+DQogICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX3RpdGxlIj4NCiAgICAgICAg5YWo5biCDQogICAgICA8L2Rpdj4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9IuaAu+WuieijheeOhyI+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzZXhfcGllMSI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3hfbGVmdCI+DQogICAgICAgICAgICAgIDxwaWUtMiB2LWlmPSJhcHBUb2RheS5udW0iIDpkYXRhcz0ieyBudW06IGFwcFRvZGF5Lm51bSwgdGV4dDogJ+aAu+WuieijheeOhycgfSIgaWQ9InBpZTEiPjwvcGllLTI+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImJveF9yaWdodCI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InRvcCI+DQogICAgICAgICAgICAgICAgPHA+5LuK5pel55m75b2V5Lq65pWwDQogICAgICAgICAgICAgICAgICA8c3BhbiA6c3R5bGU9InsgJ2NvbG9yJzogYXBwVG9kYXkucm9yZk51bSA9PT0gMSA/ICcjZmY2ZDZkJyA6ICcjNDBjZDgwJyB9Ij57eyBhcHBUb2RheS50b2RheUxvZ2luTnVtDQogICAgICAgICAgICAgICAgICB9fTwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L3A+DQogICAgICAgICAgICAgICAgPHAgOnN0eWxlPSJ7ICdjb2xvcic6IGFwcFRvZGF5LnJvcmZOdW0gPT09IDEgPyAnI2ZmNmQ2ZCcgOiAnIzQwY2Q4MCcgfSI+IDx2YW4taWNvbiBuYW1lPSJkb3duIg0KICAgICAgICAgICAgICAgICAgICBzdHlsZT0idHJhbnNmb3JtOiByb3RhdGUoLTkwZGVnKSIgLz4g6L6D5pio5pele3sgYXBwVG9kYXkucm9yZk51bSA9PT0gMSA/ICflop7liqAnIDogJ+S4i+mZjScgfX17ew0KICAgICAgICAgICAgICAgICAgICAgIGFwcFRvZGF5LnJpc2VPckZhbGxOdW0gfX0NCiAgICAgICAgICAgICAgICA8L3A+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJib3QiPg0KICAgICAgICAgICAgICAgIDxwPuS7iuaXpeeZu+W9leS6uuasoQ0KICAgICAgICAgICAgICAgICAgPHNwYW4gOnN0eWxlPSJ7ICdjb2xvcic6IGFwcFRvZGF5LnJvcmZUaW1lID09PSAxID8gJyNmZjZkNmQnIDogJyM0MGNkODAnIH0iPnt7DQogICAgICAgICAgICAgICAgICAgIGFwcFRvZGF5LnRvZGF5TG9naW5UaW1lcyB9fTwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L3A+DQogICAgICAgICAgICAgICAgPHAgOnN0eWxlPSJ7ICdjb2xvcic6IGFwcFRvZGF5LnJvcmZUaW1lID09PSAxID8gJyNmZjZkNmQnIDogJyM0MGNkODAnIH0iPjx2YW4taWNvbiBuYW1lPSJkb3duIiAvPg0KICAgICAgICAgICAgICAgICAg6L6D5pio5pele3sgYXBwVG9kYXkucm9yZlRpbWUgPT09IDEgPyAn5aKe5YqgJyA6ICfkuIvpmY0nIH19e3sgYXBwVG9kYXkucmlzZU9yRmFsbFRpbWVzIH19PC9wPg0KICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLmgLvmtLvot4PluqYiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OnRhYj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJkeW5hbWljX3RhYiI+DQogICAgICAgICAgICA8ZGl2IDpjbGFzcz0ieyBkeW5hbWljX3RhYl9pdGVtOiB0cnVlLCBkeW5hbWljX3RhYl9pdGVtX2FjdGl2ZTogZHluYW1pY0lkID09IGl0ZW0uaWQgfSINCiAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gZHluYW1pY1RhYiIgOmtleT0iaXRlbS5pZCIgQGNsaWNrPSJkeW5hbWljKGl0ZW0uaWQsICcxJykiPnt7IGl0ZW0ubmFtZSB9fTwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPExpbmUgaWQ9ImxpbmUxIiA6bGlzdD0iYXBwTG9naW5BY3RpdmF0aW9uIiB2LWlmPSJhcHBMb2dpbkFjdGl2YXRpb24ubGVuZ3RoIiA6c3RhdHVzPSJkeW5hbWljSWQiPjwvTGluZT4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGRpdiBjbGFzcz0ibGVhZGVyRHJpdmluZ190aXRsZSI+DQogICAgICAgIOmdkuWym+W4guacrOe6pw0KICAgICAgPC9kaXY+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLlronoo4XnjociPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0ibGVhZGVyRHJpdmluZ19nZW5lcmFsaXplIj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImxlYWRlckRyaXZpbmdfZ2VuZXJhbGl6ZV9pdGVtIiB2LWZvcj0iaXRlbSwgaW5kZXggaW4gaW5zdGFsbCIgOmtleT0iaW5kZXgiPg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX2dlbmVyYWxpemVfaXRlbV9udW0iDQogICAgICAgICAgICAgICAgOnN0eWxlPSJ7IGNvbG9yOiBpbmRleCA9PSAwID8gJyMzODk0ZmYnIDogaW5kZXggPT0gMSA/ICcjNGFkYjQ3JyA6ICcjZmY2ZGEyJyB9Ij4NCiAgICAgICAgICAgICAgICB7eyBpdGVtLm51bSB9fQ0KICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGVhZGVyRHJpdmluZ19nZW5lcmFsaXplX2l0ZW1fdGl0bGUiPg0KICAgICAgICAgICAgICAgIHt7IGl0ZW0udGl0bGUgfX0NCiAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveCB0aXRsZT0i5Luj6KGo5Zui5a6J6KOF546H5o6S6KGMIiBjb2xvcj0iI2ZmZWJjZiI+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8cmFua2luZy1saXN0IHVybFR5cGU9InRyb3BoeSIgY29sb3I9IiNmZGY2ZjIiDQogICAgICAgICAgICA6ZGF0YUxpc3Q9ImlzRXhwYW5kZWQyID8gbWVtYmVyQ01lbVRlYW1JbnN0YWxsb3VudCA6IG1lbWJlckNNZW1UZWFtSW5zdGFsbG91bnQuc2xpY2UoMCwgc2hvd0NvdW50KSINCiAgICAgICAgICAgIDp0aXRsZT0iWyfmjpLooYwnLCAn5Luj6KGo5ZuiJywgJ+WuieijheeOhyddIj48L3JhbmtpbmctbGlzdD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJyZXByZXNlbnRhdGl2ZV9hbGwiIEBjbGljaz0icmVwcmVzZW50YXRpdmVBbGwyIj4NCiAgICAgICAgICAgIHt7IGlzRXhwYW5kZWQyID8gJ+aUtui1tycgOiAn54K55Ye75p+l55yL5pu05aSaJyB9fQ0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLpnZLlspvluILmnKznuqfmtLvot4PluqbliIbmnpAiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OnRhYj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJkeW5hbWljX3RhYiI+DQogICAgICAgICAgICA8ZGl2IDpjbGFzcz0ieyBkeW5hbWljX3RhYl9pdGVtOiB0cnVlLCBkeW5hbWljX3RhYl9pdGVtX2FjdGl2ZTogc3ViYWN0aXZlID09IGl0ZW0uaWQgfSINCiAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gZHluYW1pY1RhYiIgOmtleT0iaXRlbS5pZCIgQGNsaWNrPSJkeW5hbWljKGl0ZW0uaWQsICcyJykiPnt7IGl0ZW0ubmFtZSB9fTwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZ2VzdF90aXRsZSIgc3R5bGU9ImZvbnQtd2VpZ2h0OiA0MDA7Ij4NCiAgICAgICAgICAgIOeZu+W9leS6uuaVsC/kurrmrKENCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8bGluZS0yIGlkPSJsaW5lczEiIHYtaWY9ImFwcExvZ2luQWN0aXZhdGlvbkJ5TnVtVGltLmxlbmd0aCIgOnN0YXR1cz0ic3ViYWN0aXZlIg0KICAgICAgICAgICAgOmxpc3Q9ImFwcExvZ2luQWN0aXZhdGlvbkJ5TnVtVGltIj48L2xpbmUtMj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdnZXN0X3RpdGxlIiBzdHlsZT0iZm9udC13ZWlnaHQ6IDQwMDsiPg0KICAgICAgICAgICAg5rS76LeD5bqmDQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPExpbmUgaWQ9ImxpbmUyIiB2LWlmPSJhcHBMb2dpbkFjdGl2YXRpb25DaXR5Lmxlbmd0aCIgOnN0YXR1cz0ic3ViYWN0aXZlIiA6bGlzdD0iYXBwTG9naW5BY3RpdmF0aW9uQ2l0eSI+DQogICAgICAgICAgPC9MaW5lPg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2dlc3RfdGl0bGUiIHN0eWxlPSJmb250LXdlaWdodDogNDAwOyI+DQogICAgICAgICAgICDmnLrlhbPjgIHku6PooajmtLvot4PluqYNCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8TGluZSBpZD0ibGluZTMiIHYtaWY9ImFwcExvZ2luQWN0aXZhdGlvbkJ5TWVtT2ZmLmxlbmd0aCIgOnN0YXR1cz0ic3ViYWN0aXZlIg0KICAgICAgICAgICAgOmxpc3Q9ImFwcExvZ2luQWN0aXZhdGlvbkJ5TWVtT2ZmIj48L0xpbmU+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICA8L2xlYWRlci1kcml2aW5nLWJveD4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9IuS7o+ihqOWboua0u+i3g+W6puaOkuihjCIgY29sb3I9IiNlMmVlZmYiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OnRhYj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJkeW5hbWljX3RhYiI+DQogICAgICAgICAgICA8ZGl2IDpjbGFzcz0ieyBkeW5hbWljX3RhYl9pdGVtOiB0cnVlLCBkeW5hbWljX3RhYl9pdGVtX2FjdGl2ZTogZ3JvdXBBY3Rpdml0eSA9PSBpdGVtLmlkIH0iDQogICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIGR5bmFtaWNUYWIiIDprZXk9Iml0ZW0uaWQiIEBjbGljaz0iZHluYW1pYyhpdGVtLmlkLCAnMycpIj4g5pyse3sgaXRlbS5uYW1lIH19PC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvdGVtcGxhdGU+DQogICAgICAgIDx0ZW1wbGF0ZSB2LXNsb3Q6Y29udGVudD4NCiAgICAgICAgICA8cmFua2luZy1saXN0IHVybFR5cGU9InRyb3BoeSINCiAgICAgICAgICAgIDpkYXRhTGlzdD0iaXNFeHBhbmRlZDEgPyBhcHBMb2dpbkFjdGl2YXRpb25CeVRlYW0gOiBhcHBMb2dpbkFjdGl2YXRpb25CeVRlYW0uc2xpY2UoMCwgc2hvd0NvdW50KSINCiAgICAgICAgICAgIDp0aXRsZT0iWyfmjpLooYwnLCAn5Luj6KGo5ZuiJywgJ+a0u+i3g+W6piddIj48L3JhbmtpbmctbGlzdD4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJyZXByZXNlbnRhdGl2ZV9hbGwiIEBjbGljaz0icmVwcmVzZW50YXRpdmVBbGwxIj4NCiAgICAgICAgICAgIHt7IGlzRXhwYW5kZWQxID8gJ+aUtui1tycgOiAn54K55Ye75p+l55yL5pu05aSaJyB9fQ0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8ZGl2IGNsYXNzPSJsZWFkZXJEcml2aW5nX3RpdGxlIj4NCiAgICAgICAg6Z2S5bKb5biC5ZCE5Yy6DQogICAgICA8L2Rpdj4NCiAgICAgIDxsZWFkZXItZHJpdmluZy1ib3ggdGl0bGU9IuaAu+WuieijheeOh+aOkuWQjSIgY29sb3I9IiNmZmViY2YiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPHJhbmtpbmctbGlzdCB1cmxUeXBlPSJ0cm9waHkiIGNvbG9yPSIjZmRmNmYyIg0KICAgICAgICAgICAgOmRhdGFMaXN0PSJpc0V4cGFuZGVkID8gYXJlYUluc3RhbGwgOiBhcmVhSW5zdGFsbC5zbGljZSgwLCBzaG93Q291bnQpIg0KICAgICAgICAgICAgOnRpdGxlPSJbJ+aOkuihjCcsICfljLrluIInLCAn5a6J6KOF546HJ10iPjwvcmFua2luZy1saXN0Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InJlcHJlc2VudGF0aXZlX2FsbCIgQGNsaWNrPSJyZXByZXNlbnRhdGl2ZUFsbCI+DQogICAgICAgICAgICB7eyBpc0V4cGFuZGVkID8gJ+aUtui1tycgOiAn54K55Ye75p+l55yL5pu05aSaJyB9fQ0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgPC9sZWFkZXItZHJpdmluZy1ib3g+DQogICAgICA8bGVhZGVyLWRyaXZpbmctYm94IHRpdGxlPSLlkITljLrluILnmbvlvZXmg4XlhrUiPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OnRhYj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJkeW5hbWljX3RhYiI+DQogICAgICAgICAgICA8ZGl2IDpjbGFzcz0ieyBkeW5hbWljX3RhYl9pdGVtOiB0cnVlLCBkeW5hbWljX3RhYl9pdGVtX2FjdGl2ZTogaXN0cmljdEVudHJ5ID09IGl0ZW0uaWQgfSINCiAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gZHluYW1pY1RhYiIgOmtleT0iaXRlbS5pZCIgQGNsaWNrPSJkeW5hbWljKGl0ZW0uaWQsICc0JykiPuacrHt7IGl0ZW0ubmFtZSB9fTwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3RlbXBsYXRlPg0KICAgICAgICA8dGVtcGxhdGUgdi1zbG90OmNvbnRlbnQ+DQogICAgICAgICAgPGxpbmUtMiBpZD0ibGluZXMyIiB2LWlmPSJhcHBMb2dpbkJ5QXJlYS5sZW5ndGgiIDpzdGF0dXM9ImlzdHJpY3RFbnRyeSIgOmxpc3Q9ImFwcExvZ2luQnlBcmVhIj48L2xpbmUtMj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgICAgPGxlYWRlci1kcml2aW5nLWJveCB0aXRsZT0i5ZCE5Yy65biC5rS76LeD5bqmIj4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDp0YWI+DQogICAgICAgICAgPGRpdiBjbGFzcz0iZHluYW1pY190YWIiPg0KICAgICAgICAgICAgPGRpdiA6Y2xhc3M9InsgZHluYW1pY190YWJfaXRlbTogdHJ1ZSwgZHluYW1pY190YWJfaXRlbV9hY3RpdmU6IGRpc3RyaWN0QWN0aXZpdHkgPT0gaXRlbS5pZCB9Ig0KICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBkeW5hbWljVGFiIiA6a2V5PSJpdGVtLmlkIiBAY2xpY2s9ImR5bmFtaWMoaXRlbS5pZCwgJzUnKSI+DQogICAgICAgICAgICAgIDxkaXY+5pyse3sgaXRlbS5uYW1lIH19PC9kaXY+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgICAgPHRlbXBsYXRlIHYtc2xvdDpjb250ZW50Pg0KICAgICAgICAgIDxMaW5lIGlkPSJsaW5lNCIgdi1pZj0iYXBwTG9naW5BY3RpdmF0aW9uQnlBcmVhLmxlbmd0aCIgOnN0YXR1cz0iZGlzdHJpY3RBY3Rpdml0eSINCiAgICAgICAgICAgIDpsaXN0PSJhcHBMb2dpbkFjdGl2YXRpb25CeUFyZWEiPjwvTGluZT4NCiAgICAgICAgPC90ZW1wbGF0ZT4NCiAgICAgIDwvbGVhZGVyLWRyaXZpbmctYm94Pg0KICAgIDwvZGl2Pg0KICAgIDxkaXYgdi1pZj0iYWN0aXZlID09IDYiPg0KICAgICAgPGlmcmFtZSBzcmM9Imh0dHA6Ly8xMjAuMjIxLjcyLjE4Nzo5MDAzL2NvY2twaXQvIy8iIGZyYW1lYm9yZGVyPSIwIg0KICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCU7aGVpZ2h0OiA2ODBweDt6LWluZGV4OiA5OTk5OTstd2Via2l0LW92ZXJmbG93LXNjcm9sbGluZzogdG91Y2g7IG92ZXJmbG93OiBzY3JvbGw7Ij48L2lmcmFtZT4NCiAgICA8L2Rpdj4NCiAgICA8ZGVtbz48L2RlbW8+DQogICAgPHZhbi1wb3B1cCBjbG9zZS1pY29uPSJjbG9zZSIgcm91bmQgdi1tb2RlbDpzaG93PSJzaG93IiBjbG9zZWFibGUgOnN0eWxlPSJ7IGhlaWdodDogJzEzJScsIHdpZHRoOiAnOTAlJyB9Ij4NCiAgICAgIDxkaXYgY2xhc3M9InBvcHVwX2NvbiI+DQogICAgICAgIDxkaXYgY2xhc3M9InBvcHVwX2Nvbl90aXRsZSI+DQogICAgICAgICAg5o+Q56S6DQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJpbmZvIj7ku6Pooajlm6Lnp6/liIY95Luj6KGo5Zui5Lit5Luj6KGo5LmL5ZKML+aAu+S6uuaVsDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgPC92YW4tcG9wdXA+DQogIDwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5G,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACnG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzG,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzG,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxG,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzD;YACF,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;gBACnG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;cACpB;YACF,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACtF,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxE,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnG,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtG,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;gBACnG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;cACpB;YACF,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC9E,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC;cACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC;gBACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;sBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9B,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC;gBACH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC9F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACnF,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjG,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC/E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjG,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnG,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClG,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClH,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdrd_h5/product-app/src/views/leaderDriving/leaderDriving.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"leaderDriving\">\r\n    <div class=\"leaderDriving_top\">\r\n    </div>\r\n    <div class=\"leaderDriving_tab\">\r\n      <div @click=\"tabClick(item)\"\r\n        :class=\"{ leaderDriving_tab_item: true, leaderDriving_tab_item_active: active == item.value }\"\r\n        v-for=\"item in tabList\" :key=\"item.value\">{{ item.name }}</div>\r\n    </div>\r\n    <div v-if=\"active == 1\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市概括\r\n      </div>\r\n      <leader-driving-box title=\"组织概括\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in generalize\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总用户量\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar1\" v-if=\"representativeVos.length\" :list=\"representativeVos\"></Bar>\r\n          <Bar :color=\"'rgba(255, 123, 49)'\" id=\"bar2\" v-if=\"officeVos.length\" :list=\"officeVos\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"市代表变动情况\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in representative\" :key=\"index\"\r\n              @click=\"representativeClick(item)\">\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                <span class=\"leaderDriving_generalize_item_title_span\">{{ item.title }}</span>\r\n                <span v-if=\"index == 0\">\r\n                  <el-icon style=\"color: #41ce81;\">\r\n                    <Bottom style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n                <span v-else>\r\n                  <el-icon style=\"color: #ff6da2;\">\r\n                    <Top style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_num\" :style=\"{ color: index == 0 ? '#41ce81' : '#ff6da2' }\">\r\n                {{ index == 0 ? '-' : '+' }}{{ item.num }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各代表团人数\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(255, 110, 110)'\" id=\"bar3\" v-if=\"representerTeam.length\" :list=\"representerTeam\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表学历分析\">\r\n        <template v-slot:content>\r\n          <Radar id=\"radar1\" v-if=\"memberEducationData.length\" :list=\"memberEducationData\"></Radar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表年龄分析\">\r\n        <template v-slot:content>\r\n          <Pie :id=\"'pie1'\" :list=\"birthday\" v-if=\"birthday.length\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表性别分析\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie\">\r\n            <div class=\"box_left\">\r\n              <Pie :id=\"'pie2'\" v-if=\"sex.length\" :list=\"sex\"></Pie>\r\n            </div>\r\n            <div class=\"box_right\" v-if=\"sex.length\">\r\n              <div class=\"top\">\r\n                <div><img :src=\"require('../../assets/img/man.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[0].name }}性{{ sex[0].value }}名</span>\r\n                <span style=\"color: #3894ff;\">{{ parseInt(sex[0].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <div><img :src=\"require('../../assets/img/woman.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[1].name }}性{{ sex[1].value }}名</span>\r\n                <span style=\"color: #ff8197;\">{{ parseInt(sex[1].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表结构分析\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar4\" v-if=\"representerElement.length\" :list=\"representerElement\">\r\n          </Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 2\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              今日提交总额\r\n            </div>\r\n            <div class=\"suggest_num\">\r\n              <span style=\"font-weight: 700;\">{{ AdviceByToday }}</span>\r\n              件\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议总数\r\n          </div>\r\n          <div class=\"suggest_box\">\r\n            <div :class=\"{ suggest_meet: index == 0, suggest_flat: index == 1 }\" v-for=\"item, index in AdviceByDomain\"\r\n              :key=\"index\">\r\n              <div class=\"meet_num\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1')\">\r\n                <span>{{ item.adviceCount }}</span>\r\n                件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1020')\">\r\n                正在办理<span>{{ item.transacting }}</span>件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1100')\">\r\n                已办结<span>{{ item.transactAccomplish }}</span>件\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"suggest_title\">\r\n            类别占比\r\n          </div>\r\n          <Pie :id=\"'pie3'\" v-if=\"currentCategoryData.length\" :list=\"currentCategoryData\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in keywords\" :key=\"index\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              满意度\r\n            </div>\r\n            <div class=\"suggest_satisfaction\">\r\n              <div class=\"satisfaction_item\" v-for=\"item, index in ['满意', '基本满意', '不满意']\" :key=\"index\">\r\n                <span :style=\"{ 'background': index == 0 ? '#40cd80' : index == 1 ? '#ffd055' : '#ff6d6d' }\"></span>\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_title\">\r\n            <p>建议满意度</p>\r\n            <template v-if=\"BySatisfaction.length\">\r\n              <memory-bar v-for=\"item, index in BySatisfaction\" :key=\"index\" :item=\"item\"></memory-bar>\r\n            </template>\r\n          </div>\r\n          <div class=\"satisfaction_title\" style=\"border: 0;\">\r\n            <p>类别满意度</p>\r\n            <div class=\"satisfaction_item\" v-for=\"item, index in SatisfactionByData\" :key=\"index\">\r\n              <!-- <span>{{ item.name }}</span> -->\r\n              <memory-bar :item=\"item\"></memory-bar>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_all\">\r\n            <p v-if=\"satisfactionStatus\" @click=\"satisfactionAll(false)\"><van-icon name=\"arrow-up\" />\r\n              收起</p>\r\n            <p v-if=\"!satisfactionStatus\" @click=\"satisfactionAll(true)\"><van-icon name=\"arrow-down\" />\r\n              点击展开查看更多</p>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByRepresentative\" :click=\"true\"\r\n            :title=\"['排行', '姓名', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByDelegation\" :click=\"true\"\r\n            :title=\"['排行', '代表团', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 3\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"message_box\">\r\n            <img :src=\"require('../../assets/img/ldjsc_message.png')\" alt=\"\">\r\n            <template v-if=\"findWygzsTitleData && findWygzsTitleData.length != 0\">\r\n              <div class=\"message\">\r\n                <div v-for=\"(item, index) in findWygzsTitleData\" :key=\"index\" @click=\"MessagePage(item)\">\r\n                  <div v-if=\"index < 2\" class=\"news_text_box_item\">{{ item.title }}</div>\r\n                </div>\r\n              </div>\r\n              <div style=\"color: #7e7d7d;padding: 0.1rem;position: absolute;right: 10px;top: 0;\"\r\n                v-if=\"findWygzsTitleData.length >= 2 && (areaId == '370215' || areaId == '370200')\"\r\n                @click=\"massMessagesClick\"> >\r\n              </div>\r\n            </template>\r\n            <!-- <div class=\"message\"\r\n                 v-if=\"findWygzsTitleData.length\">\r\n              <p v-for=\"item,index in findWygzsTitleData\"\r\n                 :key=\"index\"\r\n                 v-show=\"index < 2\"><span>{{ item.title }}</span><span v-if=\"index == 0\">></span></p>\r\n            </div> -->\r\n            <template v-else>\r\n              <div class=\"messageNull\">\r\n                暂无数据\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            数据概括\r\n          </div>\r\n          <div class=\"interface_location_box\">\r\n            <div class=\"interface_location_left\">\r\n              <div class=\"interface_location_left_title\">\r\n                联络站总数量\r\n              </div>\r\n              <div class=\"interface_location_left_bot\">\r\n                <span>{{ findStudioCountByCityData.studioCount }}</span>个\r\n              </div>\r\n            </div>\r\n            <div class=\"interface_location_right\">\r\n              <Pie2 v-if=\"findWygzsTitlesCountShow\"\r\n                :datas=\"{ percentage: findWygzsTitlesCountData.responseRate, num: findWygzsTitlesCountData.num, text: '回复率', }\"\r\n                id=\"pie2\"></Pie2>\r\n            </div>\r\n          </div>\r\n          <div class=\"interface_location_box_bot\">\r\n            <div>\r\n              <p>总留言数</p>\r\n              <p>{{ (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) ?\r\n                (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>已回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.repliedCount ? findWygzsTitlesCountData.repliedCount : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>未回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.noRreplyCount ? findWygzsTitlesCountData.noRreplyCount : '暂无数据' }}</p>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            联络站分布\r\n          </div>\r\n          <Map v-if=\"mapListShow\" :list=\"mapList\" id=\"maplist\"></Map>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in findHotspotKeywordsData\" :key=\"index\" v-show=\"index < 5\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            区市联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsTitlesRankingData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsStudioTitlesCountData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 4\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            市代表标兵\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear == years }\"\r\n              @click=\"representativeTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear != years }\"\r\n              @click=\"representativeTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"medal\" type=\"resumption\" :dataList=\"dutynumList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"router.push('/performanceFilesList')\">\r\n            查看更多\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团排行\r\n            <van-icon name=\"question-o\" color=\"#d5d5d5\" size=\"24\" @click=\"show = true\" />\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear == years }\"\r\n              @click=\"dumplingTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear != years }\"\r\n              @click=\"dumplingTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"trophy\" :dataList=\"delegationScore\" :title=\"['排行', '代表团', '得分']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区市代表标兵\r\n            <div>\r\n              <van-popover v-model:show=\"showPopover\" placemen=\"bottom-end\" :actions=\"areas\" @select=\"onSelect\">\r\n                <template #reference>\r\n                  <p>{{ actionsText }} <van-icon name=\"play\" style=\"transform: rotate(90deg);\" /></p>\r\n                </template>\r\n              </van-popover>\r\n            </div>\r\n          </div>\r\n          <ranking-list type=\"resumption\" urlType=\"medal\" :dataList=\"dutynumCityList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"notText\" style=\"font-size:14px;color: #ccc;\" v-html=\"pageNot.text\" @click=\"loadMore()\"></div>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 5\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市\r\n      </div>\r\n      <leader-driving-box title=\"总安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie1\">\r\n            <div class=\"box_left\">\r\n              <pie-2 v-if=\"appToday.num\" :datas=\"{ num: appToday.num, text: '总安装率' }\" id=\"pie1\"></pie-2>\r\n            </div>\r\n            <div class=\"box_right\">\r\n              <div class=\"top\">\r\n                <p>今日登录人数\r\n                  <span :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\">{{ appToday.todayLoginNum\r\n                  }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\"> <van-icon name=\"down\"\r\n                    style=\"transform: rotate(-90deg)\" /> 较昨日{{ appToday.rorfNum === 1 ? '增加' : '下降' }}{{\r\n                      appToday.riseOrFallNum }}\r\n                </p>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <p>今日登录人次\r\n                  <span :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\">{{\r\n                    appToday.todayLoginTimes }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\"><van-icon name=\"down\" />\r\n                  较昨日{{ appToday.rorfTime === 1 ? '增加' : '下降' }}{{ appToday.riseOrFallTimes }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: dynamicId == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '1')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line1\" :list=\"appLoginActivation\" v-if=\"appLoginActivation.length\" :status=\"dynamicId\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in install\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团安装率排行\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded2 ? memberCMemTeamInstallount : memberCMemTeamInstallount.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll2\">\r\n            {{ isExpanded2 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"青岛市本级活跃度分析\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: subactive == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '2')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            登录人数/人次\r\n          </div>\r\n          <line-2 id=\"lines1\" v-if=\"appLoginActivationByNumTim.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByNumTim\"></line-2>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            活跃度\r\n          </div>\r\n          <Line id=\"line2\" v-if=\"appLoginActivationCity.length\" :status=\"subactive\" :list=\"appLoginActivationCity\">\r\n          </Line>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            机关、代表活跃度\r\n          </div>\r\n          <Line id=\"line3\" v-if=\"appLoginActivationByMemOff.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByMemOff\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团活跃度排行\" color=\"#e2eeff\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: groupActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '3')\"> 本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\"\r\n            :dataList=\"isExpanded1 ? appLoginActivationByTeam : appLoginActivationByTeam.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '活跃度']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll1\">\r\n            {{ isExpanded1 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市各区\r\n      </div>\r\n      <leader-driving-box title=\"总安装率排名\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded ? areaInstall : areaInstall.slice(0, showCount)\"\r\n            :title=\"['排行', '区市', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll\">\r\n            {{ isExpanded ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市登录情况\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: istrictEntry == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '4')\">本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <line-2 id=\"lines2\" v-if=\"appLoginByArea.length\" :status=\"istrictEntry\" :list=\"appLoginByArea\"></line-2>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: districtActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '5')\">\r\n              <div>本{{ item.name }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line4\" v-if=\"appLoginActivationByArea.length\" :status=\"districtActivity\"\r\n            :list=\"appLoginActivationByArea\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 6\">\r\n      <iframe src=\"http://120.221.72.187:9003/cockpit/#/\" frameborder=\"0\"\r\n        style=\"width: 100%;height: 680px;z-index: 99999;-webkit-overflow-scrolling: touch; overflow: scroll;\"></iframe>\r\n    </div>\r\n    <demo></demo>\r\n    <van-popup close-icon=\"close\" round v-model:show=\"show\" closeable :style=\"{ height: '13%', width: '90%' }\">\r\n      <div class=\"popup_con\">\r\n        <div class=\"popup_con_title\">\r\n          提示\r\n        </div>\r\n        <div class=\"info\">代表团积分=代表团中代表之和/总人数</div>\r\n      </div>\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'\r\nimport LeaderDrivingBox from './components/leaderDrivingBox.vue'\r\nimport Bar from './components/bar.vue'\r\nimport Pie from './components/pie.vue'\r\nimport RankingList from './components/rankingList.vue'\r\nimport Map from './components/map.vue'\r\nimport Line from './components/line.vue'\r\nimport Pie2 from './components/pie2.vue'\r\nimport Line2 from './components/line2.vue'\r\nimport Radar from './components/radar.vue'\r\nimport MemoryBar from './components/memoryBar.vue'\r\nimport Demo from './components/demo.vue'\r\nexport default {\r\n  name: 'leaderDriving',\r\n  components: {\r\n    LeaderDrivingBox,\r\n    Bar,\r\n    Pie,\r\n    RankingList,\r\n    Map,\r\n    Line,\r\n    Pie2,\r\n    Line2,\r\n    Radar,\r\n    MemoryBar,\r\n    Demo,\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Circle.name]: Circle\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      pageNot: { text: '' },\r\n      pageNo: 1,\r\n      pageSize: 5,\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId')),\r\n      areas: [],\r\n      areaIdStatus: '',\r\n      years: new Date().getFullYear(),\r\n      showPopover: false,\r\n      actionsText: '',\r\n      active: sessionStorage.getItem('leaderActive') || '1',\r\n      tabList: [{\r\n        name: '组织情况',\r\n        value: '1'\r\n      }, {\r\n        name: '建议情况',\r\n        value: '2'\r\n      }, {\r\n        name: '联络站',\r\n        value: '3'\r\n      }, {\r\n        name: '履职报表',\r\n        value: '4'\r\n      }, {\r\n        name: '运行情况',\r\n        value: '5'\r\n      }, {\r\n        name: '信访情况',\r\n        value: '6'\r\n      }],\r\n      generalize: [\r\n        {\r\n          num: '',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      install: [\r\n        {\r\n          num: '0',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      representative: [\r\n        {\r\n          num: 1,\r\n          title: '出缺代表',\r\n          key: '1',\r\n          type: 'hasVacant'\r\n        },\r\n        {\r\n          num: 1,\r\n          title: '新增代表',\r\n          key: '2',\r\n          type: ''\r\n        }\r\n      ],\r\n      keywordsList: ['教育', '产业链'],\r\n      keywords: ['教育', '产业链', '农业'],\r\n      mapList: [],\r\n      mapListShow: false,\r\n      rate: 50,\r\n      cityYear: new Date().getFullYear(),\r\n      dumplingYear: 2025,\r\n      show: false,\r\n      dynamicTab: [{\r\n        name: '日',\r\n        id: '1'\r\n      }, {\r\n        name: '月',\r\n        id: '2'\r\n      }],\r\n      dynamicId: '1',\r\n      subactive: '1',\r\n      groupActivity: '1',\r\n      istrictEntry: '1',\r\n      districtActivity: '1',\r\n      representativeText: '点击查看更多',\r\n      satisfactionStatus: false,\r\n      sex: [],\r\n      birthday: [],\r\n      party: [],\r\n      representerElement: [],\r\n      representerTeam: [],\r\n      memberEducationData: [],\r\n      officeVos: [],\r\n      representativeVos: [],\r\n      AdviceByToday: '0',\r\n      AdviceByDomain: [],\r\n      currentCategoryData: [],\r\n      BySatisfaction: [],\r\n      SatisfactionBy: [],\r\n      SatisfactionByData: [],\r\n      ByRepresentative: [],\r\n      ByDelegation: [],\r\n      findWygzsTitleData: [],\r\n      findStudioCountByCityData: {},\r\n      findWygzsTitlesCountData: {},\r\n      findWygzsTitlesCountShow: false,\r\n      findHotspotKeywordsData: {},\r\n      findWygzsTitlesRankingData: [],\r\n      findWygzsStudioTitlesCountData: [],\r\n      dutynumList: [],\r\n      delegationScore: [],\r\n      dutynumCityList: [],\r\n      appToday: {\r\n        todayLoginNum: '', // 今日登录人数\r\n        rorfNum: '', // 较昨日上升或下降\r\n        riseOrFallNum: '', // 上升或下降数量\r\n        todayLoginTimes: '', // 今日登陆人次\r\n        rorfTime: '', // 较昨日上升或下降\r\n        riseOrFallTimes: '', // 上升或下降数量\r\n        num: 0\r\n      },\r\n      appLoginActivation: [],\r\n      appInstall: [],\r\n      areaInstall: [],\r\n      showCount: 5, // 控制展示的数据数量，默认为5\r\n      isExpanded: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded1: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded2: false, // 控制是否展开全部数据，默认为false\r\n      memberCMemTeamInstallount: [],\r\n      appLoginActivationByNumTim: [],\r\n      appLoginActivationCity: [],\r\n      appLoginActivationByMemOff: [],\r\n      appLoginActivationByTeam: [],\r\n      appLoginByArea: [],\r\n      appLoginActivationByArea: []\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      const areaList = JSON.parse(sessionStorage.getItem('areas'))\r\n      data.areas = areaList.map(item => {\r\n        return {\r\n          text: item.name,\r\n          id: item.id,\r\n          name: item.name\r\n        }\r\n      })\r\n      // data.areas.splice(0, 1)\r\n      data.actionsText = data.areas[0].name\r\n      data.areaIdStatus = data.areas[0].id\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    })\r\n    // 组织情况\r\n    const organization = () => {\r\n      getMemberCount()\r\n      memberEducation()\r\n      getOrganization()\r\n      memberChange()\r\n    }\r\n    // 建议情况\r\n    const recommendation = () => {\r\n      getAdviceByToday()\r\n      getAdviceByDomain()\r\n      currentCategory()\r\n      keywords()\r\n      getAdviceBySatisfaction()\r\n      getNumberByRepresentative()\r\n      getNumberByDelegation()\r\n    }\r\n    // 联络站\r\n    const interfaceLocation = () => {\r\n      getMapList()\r\n      findWygzsTitleList()\r\n      findStudioCountByCity()\r\n      findWygzsTitlesCount()\r\n      findHotspotKeywords()\r\n      findWygzsTitlesRanking()\r\n      findWygzsStudioTitlesCount()\r\n    }\r\n    // 履职报表\r\n    const PerformanceReport = () => {\r\n      dutynumList(2025)\r\n      delegationScore()\r\n      dutynumCityList()\r\n    }\r\n    // 运行情况\r\n    const runningCondition = () => {\r\n      appTodayLogin()\r\n      appAllInstall()\r\n      appLoginActivation()\r\n      appInstall()\r\n      memberCMemTeamInstallount()\r\n      appLoginActivationByNumTim()\r\n      appLoginActivationCity()\r\n      appLoginActivationByMemOff()\r\n      appLoginActivationByTeam()\r\n      areaInstall()\r\n      appLoginByArea()\r\n      appLoginActivationByArea()\r\n    }\r\n    const getMapList = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByDistrict({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.mapList = res.data\r\n      data.mapListShow = true\r\n    }\r\n    const representativeTab = (y) => {\r\n      data.cityYear = y\r\n      dutynumList(y)\r\n    }\r\n    const dumplingTab = (y) => {\r\n      data.dumplingYear = y\r\n      delegationScore(y)\r\n    }\r\n    const tabClick = (item) => {\r\n      sessionStorage.setItem('leaderActive', item.value)\r\n      data.active = sessionStorage.getItem('leaderActive')\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    }\r\n    const onSelect = (item) => {\r\n      data.actionsText = item.text\r\n      data.areaIdStatus = item.id\r\n      dutynumCityList()\r\n    }\r\n    const dynamic = (id, type) => {\r\n      switch (type) {\r\n        case '1':\r\n          data.dynamicId = id\r\n          appLoginActivation(id)\r\n          break\r\n        case '2':\r\n          data.subactive = id\r\n          appLoginActivationByNumTim(id)\r\n          appLoginActivationCity(id)\r\n          appLoginActivationByMemOff(id)\r\n          break\r\n        case '3':\r\n          data.groupActivity = id\r\n          appLoginActivationByTeam(id)\r\n          break\r\n        case '4':\r\n          data.istrictEntry = id\r\n          appLoginByArea(id)\r\n          break\r\n        case '5':\r\n          data.districtActivity = id\r\n          appLoginActivationByArea(id)\r\n          break\r\n      }\r\n    }\r\n    const satisfactionAll = (type) => {\r\n      data.satisfactionStatus = type\r\n      if (data.satisfactionStatus) {\r\n        data.SatisfactionByData = data.SatisfactionBy\r\n      } else {\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getMemberCount = async () => {\r\n      var res = await $api.leaderDriving.memberCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      if (res.data) {\r\n        data.sex = res.data.sex.map((item, index) => {\r\n          return {\r\n            name: item.name,\r\n            value: item.amount,\r\n            key: item.key,\r\n            itemStyle: { color: index === 0 ? '#3da2ff' : '#ff738c' }\r\n          }\r\n        })\r\n        data.birthday = res.data.birthday.map(item => {\r\n          return {\r\n            key: item.key,\r\n            name: item.name,\r\n            value: item.amount\r\n          }\r\n        })\r\n        data.party = res.data.party.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        })\r\n        data.representerElement = res.data.representerElement.map(item => {\r\n          return {\r\n            value: item.amount,\r\n            key: item.key,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n        data.representerTeam = res.data.representerTeam.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        }).reverse()\r\n      }\r\n      // console.log(data.birthday)\r\n      // console.log('getMemberCount', res.data)\r\n    }\r\n    const memberEducation = async () => {\r\n      var res = await $api.leaderDriving.memberEducation({})\r\n      if (res.data) {\r\n        data.memberEducationData = res.data.map(item => {\r\n          return {\r\n            value: item.value,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const getOrganization = async () => {\r\n      var res = await $api.leaderDriving.getOrganization({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.officeVos = res.data.officeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.representativeVos = res.data.representativeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.generalize[0].num = res.data.totalNumber\r\n      data.generalize[1].num = res.data.representativeNumber\r\n      data.generalize[2].num = res.data.officeNumber\r\n    }\r\n    const memberChange = async () => {\r\n      var res = await $api.leaderDriving.memberChange({})\r\n      data.representative[0].num = res.data.vacantNum\r\n      data.representative[1].num = res.data.repairNum\r\n    }\r\n    const getAdviceByToday = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByToday({ personCode: '' })\r\n      if (res.result) {\r\n        data.AdviceByToday = res.result\r\n      }\r\n    }\r\n    const getAdviceByDomain = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByDomain({})\r\n      if (res.result) {\r\n        data.AdviceByDomain = res.result.reverse()\r\n      }\r\n    }\r\n    const suggestGoLink = (type, mType) => {\r\n      // if (mType) {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&manageType=${mType}&token={{token}}`\r\n      // } else {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&token={{token}}`\r\n      // }\r\n    }\r\n    const currentCategory = async () => {\r\n      var res = await $api.leaderDriving.currentCategory({ type: '' })\r\n      if (res.result) {\r\n        data.currentCategoryData = res.result.map(item => {\r\n          return {\r\n            name: item.name,\r\n            proportion: item.proportion,\r\n            value: item.value,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_cate?type=${item.code}&token={{token}}`\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const keywords = async () => {\r\n      var res = await $api.leaderDriving.keywords({})\r\n      if (res) {\r\n        data.keywordsList = res.data.filter(item => item !== '青岛市')\r\n      }\r\n    }\r\n    const getAdviceBySatisfaction = async () => {\r\n      var res = await $api.leaderDriving.getAdviceBySatisfaction({ type: '' })\r\n      var ress = await $api.leaderDriving.getSatisfactionByCategory({ type: '' })\r\n      if (res) {\r\n        data.BySatisfaction = [res.result].map(item => {\r\n          return {\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionBy = ress.result.map(item => {\r\n          return {\r\n            name: item.suggestName,\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getNumberByRepresentative = async () => {\r\n      var res = await $api.leaderDriving.getNumberByRepresentative({ type: '' })\r\n      if (res) {\r\n        data.ByRepresentative = res.result.map(item => {\r\n          return {\r\n            num: item.issueCount,\r\n            name: item.name,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_mylist?personCode=${item.userCode}&token={{token}}`\r\n          }\r\n        }).slice(0, 5)\r\n      }\r\n    }\r\n    const getNumberByDelegation = async () => {\r\n      var res = await $api.leaderDriving.getNumberByDelegation({ type: '' })\r\n      if (res) {\r\n        data.ByDelegation = res.result.map(item => {\r\n          if (item.delegationName !== '解放军代表团') {\r\n            return {\r\n              num: item.adviceTotal,\r\n              name: item.delegationName,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_group?groupId=${item.delegationCode}&token={{token}}`\r\n            }\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const findWygzsTitleList = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })\r\n      data.findWygzsTitleData = res.data\r\n    }\r\n    const findStudioCountByCity = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByCity({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findStudioCountByCityData = res.data[0]\r\n    }\r\n    const findWygzsTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesCountData = res.data\r\n      data.findWygzsTitlesCountData.num = parseFloat(data.findWygzsTitlesCountData.responseRate.replace('%', ''))\r\n      data.findWygzsTitlesCountShow = true\r\n    }\r\n    const findHotspotKeywords = async () => {\r\n      var res = await $api.leaderDriving.findHotspotKeywords({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findHotspotKeywordsData = res.data.filter(item => item !== '测试')\r\n      // console.log('findHotspotKeywords', res.data)\r\n    }\r\n    const findWygzsTitlesRanking = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesRanking({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesRankingData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      })\r\n    }\r\n    const findWygzsStudioTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsStudioTitlesCount({})\r\n      data.findWygzsStudioTitlesCountData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      }).splice(0, 10)\r\n    }\r\n    const dutynumList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: '1',\r\n        pageSize: '5',\r\n        year: y,\r\n        areaId: data.areaId\r\n      })\r\n      data.dutynumList = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username,\r\n          year: y,\r\n          userid: item.userid\r\n        }\r\n      }).splice(0, 5)\r\n    }\r\n    const delegationScore = async (y) => {\r\n      var res = await $api.leaderDriving.delegationScore({\r\n        pageNo: '1',\r\n        pageSize: '10',\r\n        year: data.years\r\n      })\r\n      data.delegationScore = res.data.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.delegationview\r\n        }\r\n      })\r\n    }\r\n    const dutynumCityList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        year: new Date().getFullYear(),\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.pageNot.text = res && res.errcode !== 200 ? res.errmsg || res.data : ''\r\n      var a = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username\r\n        }\r\n      })\r\n      data.dutynumCityList = data.dutynumCityList.concat(a)\r\n      var LOAD_MORE = '点击加载更多'\r\n      var LOAD_ALL = '已加载完'\r\n      data.pageNot.text = data.dutynumCityList.length === 0 ? '' : res.data.dutyNumListVos.length >= data.pageSize ? LOAD_MORE : LOAD_ALL\r\n    }\r\n    const appTodayLogin = async (y) => {\r\n      var res = await $api.leaderDriving.appTodayLogin({\r\n        areaId: data.areaId\r\n      })\r\n      data.appToday.todayLoginNum = Number(res.data.todayLoginNum) // 今日登录人数\r\n      data.appToday.rorfNum = Number(res.data.rorfNum) // 较昨日上升或下降\r\n      data.appToday.riseOrFallNum = Number(res.data.riseOrFallNum) // 上升或下降数量\r\n      data.appToday.todayLoginTimes = Number(res.data.todayLoginTimes) // 今日登陆人次\r\n      data.appToday.rorfTime = Number(res.data.rorfTime) // 较昨日上升或下降\r\n      data.appToday.riseOrFallTimes = Number(res.data.riseOrFallTimes) // 上升或下降数量\r\n    }\r\n    const appAllInstall = async (y) => {\r\n      var res = await $api.leaderDriving.appAllInstall({\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.appToday.num = Number(res.data.rate.replace('%', ''))\r\n    }\r\n    const appLoginActivation = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId == '370215' ? data.areaId : '' }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivation = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appInstall = async () => {\r\n      var res = await $api.leaderDriving.appInstall({ areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.install[0].num = res.data.totalInstall\r\n        data.install[1].num = res.data.memberInstall\r\n        data.install[2].num = res.data.officeInstall\r\n      }\r\n    }\r\n    const areaInstall = async () => {\r\n      var res = await $api.leaderDriving.areaInstall({ areaId: data.areaId })\r\n      if (res) {\r\n        data.areaInstall = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const memberCMemTeamInstallount = async () => {\r\n      var res = await $api.leaderDriving.memberCMemTeamInstallount({ areaId: data.areaId })\r\n      if (res) {\r\n        data.memberCMemTeamInstallount = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByNumTim = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByNumTim({ type: t, areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivationByNumTim = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            nums: item.times\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationCity = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationCity = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByMemOff = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByMemOff({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationByMemOff = res.data.map(item => {\r\n          return {\r\n            numMem: item.numMem,\r\n            numOff: item.numOff,\r\n            name: item.time,\r\n            activationOff: item.activationOff,\r\n            activationMem: item.activationMem\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByTeam = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByTeam({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByTeam = res.data.map(item => {\r\n          return {\r\n            num: item.activation,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            times: item.times\r\n          }\r\n        })\r\n        console.log('data.appLoginByArea===>', data.appLoginByArea)\r\n      }\r\n    }\r\n    const appLoginActivationByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const MessagePage = async (_item) => {\r\n      window.location.href = `http://120.221.72.187:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`\r\n    }\r\n    const massMessagesClick = async () => {\r\n      router.push({ name: 'messageMorePage' })\r\n    }\r\n    const loadMore = async () => {\r\n      var LOAD_MORE = '点击加载更多'\r\n      var NET_ERR = '网络不小心断开了'\r\n      var LOAD_ING = '加载中，请稍候...'\r\n      if ((data.pageNot.text === LOAD_MORE || data.pageNot.text === NET_ERR) && data.pageNo !== 1) {\r\n        data.pageNot.text = LOAD_ING\r\n        data.pageNo++\r\n        dutynumCityList()\r\n      } else {\r\n        data.pageNo = data.pageNo + 1\r\n        dutynumCityList()\r\n      }\r\n    }\r\n    const representativeAll = () => {\r\n      data.isExpanded = !data.isExpanded\r\n    }\r\n    const representativeAll1 = () => {\r\n      data.isExpanded1 = !data.isExpanded1\r\n    }\r\n    const representativeAll2 = () => {\r\n      data.isExpanded2 = !data.isExpanded2\r\n    }\r\n    const representativeClick = (_item) => {\r\n      router.push({ name: 'peopleList', query: { key: _item.key, type: _item.type } })\r\n    }\r\n    return { ...toRefs(data), loadMore, MessagePage, massMessagesClick, representativeAll, representativeAll1, representativeAll2, suggestGoLink, general, confirm, tabClick, representativeTab, dumplingTab, onSelect, dynamic, router, satisfactionAll, representativeClick }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.leaderDriving {\r\n  background: #f8f8f8;\r\n  box-sizing: border-box;\r\n  padding: 15px 10px 10px 10px;\r\n  height: 100%;\r\n\r\n  .satisfaction_title {\r\n    width: 95%;\r\n    margin: 10px 10px 0 10px;\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid #d8d8d8;\r\n  }\r\n\r\n  .satisfaction_item {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    >span {\r\n      font-size: 14px;\r\n      display: inline-block;\r\n      width: 25%;\r\n    }\r\n  }\r\n\r\n  .satisfaction_all {\r\n    text-align: center;\r\n    color: #3894ff;\r\n    margin: 15px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .dynamic_tab {\r\n    width: 100%;\r\n    height: 100%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n\r\n    .dynamic_tab_item {\r\n      width: 50%;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .dynamic_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .sex_pie1 {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 50%;\r\n      height: 120px;\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // justify-content: space-around;\r\n      font-size: 16px;\r\n\r\n      .top {\r\n        display: flex;\r\n        // align-items: center;\r\n        flex-direction: column;\r\n        margin-bottom: 10px;\r\n        font-size: 16px;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        // align-items: center;\r\n        font-size: 16px;\r\n        flex-direction: column;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .popup_con {\r\n    margin: 0px 10px;\r\n\r\n    .popup_con_title {\r\n      text-align: center;\r\n      font-size: 20px;\r\n      margin: 10px 0;\r\n      font-weight: 700;\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .representative_all {\r\n    width: 100%;\r\n    text-align: center;\r\n    color: #a2a2a2;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .representative_tab {\r\n    width: 94%;\r\n    height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n    margin: 0 10px;\r\n\r\n    .representative_tab_item {\r\n      flex: 1;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      color: #3894ff;\r\n      text-align: center;\r\n    }\r\n\r\n    .representative_tab_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .interface_location_box_bot {\r\n    width: 100%;\r\n    height: 80px;\r\n    background: #f8fbfe;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    text-align: center;\r\n\r\n    >div {\r\n      flex: 1;\r\n\r\n      >p {\r\n        margin: 10px 0;\r\n      }\r\n\r\n      p:nth-child(1) {\r\n        color: #8c9fb7;\r\n      }\r\n\r\n      p:nth-child(2) {\r\n        font-weight: 700;\r\n      }\r\n    }\r\n  }\r\n\r\n  .interface_location_box {\r\n    width: 100%;\r\n    height: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 10px;\r\n\r\n    .interface_location_left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      height: 100%;\r\n\r\n      .interface_location_left_title {\r\n        color: #747474;\r\n      }\r\n\r\n      .interface_location_left_bot {\r\n        >span {\r\n          font-weight: 700;\r\n          color: #3894ff;\r\n          font-size: 45px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .interface_location_right {\r\n      width: 37%;\r\n      height: 90px;\r\n      position: relative;\r\n      margin-right: 30px;\r\n\r\n      .text {\r\n        position: absolute;\r\n        top: 26px;\r\n        left: 22px;\r\n        text-align: center;\r\n\r\n        >p:nth-child(1) {\r\n          font-weight: 700;\r\n          font-size: 20px;\r\n        }\r\n\r\n        >p:nth-child(2) {\r\n          font-size: 12px;\r\n          color: #a2a2a2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_satisfaction {\r\n    width: 65%;\r\n    margin: 0 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .satisfaction_item {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 14px;\r\n\r\n      >span {\r\n        width: 14px;\r\n        height: 14px;\r\n        display: inline-block;\r\n        margin: 0 5px;\r\n        border-radius: 7px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message_box {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    >img {\r\n      height: 50px;\r\n      width: 50px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .message {\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    margin: 0 10px;\r\n\r\n    .news_text_box_item {\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 1;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 2px 0;\r\n      font-size: 15px;\r\n    }\r\n\r\n    p:nth-child(1) {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n\r\n  .messageNull {\r\n    text-align: center;\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #aeaeae;\r\n  }\r\n\r\n  .content_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .hotWord {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    height: 35px;\r\n    padding: 5px 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    .hotWord_item {\r\n      display: flex;\r\n      width: 70%;\r\n      align-items: center;\r\n\r\n      .index {\r\n        margin: 0 10px 0 0;\r\n      }\r\n    }\r\n\r\n    .hotWord_right {\r\n      color: #fff;\r\n      // padding: 3px;\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 3px;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .suggest_box {\r\n    display: flex;\r\n    color: #fff;\r\n\r\n    .suggest_transaction {\r\n      margin-left: 10px;\r\n      margin-bottom: 5px;\r\n\r\n      >span {\r\n        font-size: 22px;\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .suggest_meet {\r\n      flex: 1;\r\n      margin: 0 5px;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg1.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .suggest_flat {\r\n      margin: 0 5px;\r\n      flex: 1;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg2.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_title {\r\n    height: 24px;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    margin: 5px 10px 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    >div {\r\n      width: 29%;\r\n      color: #3894ff;\r\n    }\r\n  }\r\n\r\n  .suggest_num {\r\n    color: #3894ff;\r\n    margin-right: 10px;\r\n\r\n    >span {\r\n      font-size: 28px;\r\n    }\r\n  }\r\n\r\n  .sex_pie {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 60%;\r\n      height: 120px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      font-size: 18px;\r\n\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .leaderDriving_top {\r\n    width: 100%;\r\n    height: 100px;\r\n    // margin: 15px 10px 0;\r\n    background: url(\"../../assets/img/ldjsc_head_bg.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  .leaderDriving_tab {\r\n    margin-top: 10px;\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_tab_item {\r\n      width: 60px;\r\n      height: 60px;\r\n      background: #fff;\r\n      border-radius: 30px;\r\n      text-align: center;\r\n      box-sizing: border-box;\r\n      padding: 10px;\r\n      box-shadow: 0px 5px 15px -3px rgba(138, 138, 138, 0.1);\r\n    }\r\n\r\n    .leaderDriving_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .leaderDriving_title {\r\n    width: 100%;\r\n    height: 30px;\r\n    margin: 10px 0;\r\n    color: #3894ff;\r\n    padding-left: 10px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    position: relative;\r\n  }\r\n\r\n  .leaderDriving_title::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    height: 18px;\r\n    width: 4px;\r\n    top: 4px;\r\n    left: 0px;\r\n    background: #3894ff;\r\n    border-radius: 1px;\r\n  }\r\n\r\n  .leaderDriving_generalize {\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_generalize_item {\r\n      // width: 32%;\r\n      flex: 1;\r\n      height: 100%;\r\n      // padding-left: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .leaderDriving_generalize_item_num {\r\n        font-size: 28px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title {\r\n        color: #8196af;\r\n        // display: flex;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title_span {}\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}
<template>
  <div class="Pie0">
    <div class="container_swipe">
      <div class="pages0">
        <div class="pages0_item">
          <div class="flex_box flex_align_center pages0_item_text1"
               :class="{ 'fade-in1': showText1 }">
            <img src="../../assets/img/qingdao/hui.png"
                 alt=""
                 style="width: 36px;height: 36px;">
            <span class="pages0_item_span">2023年</span>
          </div>
          <div class="pages0_item_text2"
               :class="{ 'fade-in1': showText2 }">代表履职报告</div>
        </div>
        <div class="pages0_openBtn">
          <div class="open_item"
               @click="openPage1()">
            <div class="open_text">点击开启</div>
            <img src="../../assets/img/timeAxis/g_open.png"
                 alt=""
                 class="open_img">
          </div>
        </div>
      </div>
    </div>
    <van-overlay :show="isShowLoading">
      <div style="text-align: center; padding-top: 100%">
        <van-loading size="24px"
                     vertical
                     text-color="#0094ff"
                     color="#0094ff">年度报告生成中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, onBeforeUnmount } from 'vue'
import { Image as VanImage, Loading, Overlay } from 'vant'
export default {
  name: 'Page0',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [VanImage.name]: VanImage
  },
  setup () {
    const $api = inject('$api')
    const router = useRouter()
    const route = useRoute()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      title: route.query.title || '',
      isShowLoading: true,
      userName: '',
      showText1: null,
      showText2: null,
      page2Show: 0,
      page3Show: 0,
      page4Show: 0,
      page5Show: 0,
      page6Show: 0,
      page7Show: 0,
      dutyListData: [],
      randerData: [],
      rankVal: 0
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      data.isShowLoading = true
      const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token')
      if (!token) {
        setTimeout(() => {
          sessionStorage.setItem('Sys_token', JSON.stringify(api.getPrefs({ sync: true, key: 'Sys_token' }))) // eslint-disable-line
          sessionStorage.setItem('areaId', JSON.stringify(api.getPrefs({ sync: true, key: 'SYS_SiteID' }))) // eslint-disable-line
          api.setInterfaceStyle({ style: 'light' }) // eslint-disable-line
          const user = {
            userName: api.getPrefs({ sync: true, key: 'Sys_UserName' }), // eslint-disable-line
            position: api.getPrefs({ sync: true, key: 'Sys_Position' }), // eslint-disable-line
            headImg: window.localStorage.getItem('Sys_AppPhoto')
          }
          data.userName = user.userName
          sessionStorage.setItem('user', JSON.stringify(user))
        }, 300)
      } else {
        setTimeout(() => {
          console.log('走到else了')
          // const user = {
          //   userName: api.getPrefs({ sync: true, key: 'Sys_UserName' }) || JSON.parse(sessionStorage.getItem('user')).userName || '', // eslint-disable-line
          //   position: api.getPrefs({ sync: true, key: 'Sys_Position' }), // eslint-disable-line
          //   headImg: window.localStorage.getItem('Sys_AppPhoto')
          // }
          // data.userName = user.userName
          data.userName = JSON.parse(sessionStorage.getItem('user')).userName
          data.isShowLoading = false
        }, 2000)
      }
      setTimeout(() => {
        data.showText1 = true
      }, 2000)
      setTimeout(() => {
        data.showText2 = true
      }, 3000)
      preventScroll()
      memberPortraitDutyTime()
      memberPortraitDutyData()
      getPages3Recommendation()
      getAppDutyDetail()
    })
    onBeforeUnmount(() => {
      preventScroll()
    })
    const preventScroll = () => {
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
    }
    const handleTouchMove = (event) => {
      event.preventDefault()
    }
    // 获取数据
    const memberPortraitDutyTime = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyTime()
      data.pageData = res.data
      data.page2Show = res.data.loginTimes
      data.page3Show = res.data.participateServer
      data.page4Show = res.data.publishDuty
      data.page6Show = (res.data.watchVideoTime && res.data.readBookTime)
      data.rankVal = res.data.dutyRank
    }
    // 获取雷达图数据
    const memberPortraitDutyData = async () => {
      const res = await $api.representativePortrait.memberPortraitDutyData()
      data.randerData = res.data
    }
    // 获取亚威建议数据
    const getPages3Recommendation = async () => {
      const res = await $api.representativePortrait.getPersonSubmitInfo({ personCode: data.user.id })
      console.log('获取代表建议===>>', res)
      data.page5Data = res.result
      data.page5Show = res.result.submitCount
    }
    // 获取履职足迹
    const getAppDutyDetail = async () => {
      const param = {
        pageNo: 1,
        pageSize: 99,
        year: 2023,
        userId: data.user.id,
        areaId: sessionStorage.getItem('areaId')
      }
      const res = await $api.performanceFiles.getAppDutyDetail(param)
      console.log('获取履职足迹===>', res)
      const list = res.data
      list.forEach(item => {
        var date = new Date(item.date)
        var month = date.getMonth() + 1
        var day = date.getDate()
        var formattedDate = month + '月' + day + '日'
        item.time = formattedDate
        switch (item.type) {
          case 'suggest': // 建议
            item.typeName = '建议'
            break
          case 'proposal': // 提案
            item.typeName = '提案'
            break
          case 'officeOnline': // 委员值班
            item.typeName = '委员值班'
            break
          case 'social': // 社情民意
            item.typeName = '社情民意'
            break
          case 'activity': // 活动
          case '49': // 活动
            item.typeName = '活动'
            break
          case 'survey': // 意见征集
            item.typeName = '意见征集'
            break
          case 'learning': // 考试
            item.typeName = '学习培训'
            break
          case 'meet': // 会议
            item.typeName = '会议'
            break
          case 'bill': // 议案
            item.typeName = '议案'
            break
          case '211': // 履职补录
            item.typeName = '活动'
            break
        }
      })
      data.dutyListData = data.dutyListData.concat(list)
      data.page7Show = data.dutyListData.length
    }
    const openPage1 = () => {
      if (!data.page2Show && !data.page3Show && !data.page4Show && !data.page5Show && !data.page6Show && !data.page7Show) {
        router.push({ name: 'twoDataPage', query: { typePlay: true } })
      } else {
        router.push({ name: 'annualReport', query: { typePlay: true } })
      }
    }
    return { ...toRefs(data), $general, openPage1 }
  }

}
</script>
<style lang="less" scoped>
@font-face {
  font-family: "YouSheBiaoTiYuan-2";
  src: url("../../assets/img/timeAxis/font/YouSheBiaoTiYuan-2.ttf")
    format("truetype");
  /* 其他字体格式和属性 */
}

.Pie0 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }

  * {
    padding: 0;
    margin: 0;
  }
  .container_swipe {
    height: 100vh;
    width: 100vw;
    .pages0_item_text2,
    .pages0_item_text1 {
      opacity: 0;
      transition: opacity 1s;
    }

    .pages0_item_text2.fade-in,
    .pages0_item_text1.fade-in {
      opacity: 1;
    }
    .pages0 {
      background-image: url(../../assets/img/timeAxis/g_bg00.png);
      background-size: 100% 100%;
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
      position: relative;
      .pages0_item {
        position: absolute;
        top: 2.8rem;
        left: 2rem;
        .pages0_item_span {
          font-size: 40px;
          font-weight: 400;
          color: #ffffff;
          line-height: 47px;
          font-family: "YouSheBiaoTiYuan-2";
          letter-spacing: 1px;
          margin-left: 5px;
        }
        .pages0_item_text2 {
          font-size: 40px;
          font-weight: 400;
          color: #ffffff;
          line-height: 47px;
          letter-spacing: 1px;
          font-family: "YouSheBiaoTiYuan-2";
        }
      }
      .pages0_openBtn {
        position: absolute;
        bottom: 1.5rem;
        left: 3.2rem;
        .open_item {
          background-image: url(../../assets/img/timeAxis/g_openBg.png);
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          padding: 10px 20px;
          .open_text {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
          }
          .open_img {
            width: 17px;
            height: 17px;
            margin-left: 10px;
            margin-top: 3px;
          }
        }
      }
    }
  }
  .fade-in1 {
    opacity: 0;
    animation: fade-in-animation 3s forwards;
  }

  @keyframes fade-in-animation {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}
</style>

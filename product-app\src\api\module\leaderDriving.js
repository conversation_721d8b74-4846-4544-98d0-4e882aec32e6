import { HTTP } from '../http.js'
// import axios from 'axios'
class leaderDriving extends HTTP {
  // 地图
  findStudioCountByDistrict (params) {
    return this.request({ url: '/wygzsStudioDocking/findStudioCountByDistrict', data: params })
  }

  // 信息统计
  memberCount (params) {
    return this.request({ url: '/member/count', data: params })
  }

  // 联络站群众留言总数（全市总数）
  getOrganization (params) {
    return this.request({ url: '/conferenceInfoStatistics/getOrganization', data: params })
  }

  // 代表学历信息
  memberEducation (params) {
    return this.request({ url: '/member/education', data: params })
  }

  // 出缺
  memberChange (params) {
    return this.request({ url: '/member/change', data: params })
  }

  // 总安装率
  appAllInstall (params) {
    return this.request({ url: '/wholeuser/appAllInstall', data: params })
  }

  // 获取登录人数和人次
  appTodayLogin (params) {
    return this.request({ url: '/appTodayLogin', data: params })
  }

  // 总活跃度
  appLoginActivation (params) {
    return this.request({ url: '/appLoginActivation', data: params })
  }

  // 登录人数人次
  appLoginActivationByNumTim (params) {
    return this.request({ url: '/appLoginActivationByNumTim', data: params })
  }

  // 机关代表活跃度
  appLoginActivationByMemOff (params) {
    return this.request({ url: '/appLoginActivationByMemOff', data: params })
  }

  // 联络站总数量（全市总数）
  findStudioCountByCity (params) {
    return this.request({ url: '/wygzsStudioDocking/findStudioCountByCity', data: params })
  }

  // 联络站群众留言总数（全市总数）
  findWygzsTitlesCount (params) {
    return this.request({ url: '/wygzsStudioDocking/findWygzsTitlesCount', data: params })
  }

  // 代表团活跃度排名
  appLoginActivationByTeam (params) {
    return this.request({ url: '/appLoginActivationByTeam', data: params })
  }

  // 各区市登录情况
  appLoginByArea (params) {
    return this.request({ url: '/appLoginByArea', data: params })
  }

  // 各区市活跃度
  appLoginActivationByArea (params) {
    return this.request({ url: '/appLoginActivationByArea', data: params })
  }

  // 群众热词（全市的
  findHotspotKeywords (params) {
    return this.request({ url: '/wygzsStudioDocking/findHotspotKeywords', data: params })
  }

  // 群众热词（全市的
  // findHotspotKeywords (params) {
  //   return this.request({ url: '/wygzsStudioDocking/findHotspotKeywords', data: params })
  // }

  // 各区市群众留言数排名（各区数量及排名
  findWygzsTitlesRanking (params) {
    return this.request({ url: '/wygzsStudioDocking/findWygzsTitlesRanking', data: params })
  }

  // 各联络站活跃度
  findWygzsStudioTitlesCount (params) {
    return this.request({ url: '/wygzsStudioDocking/findWygzsStudioTitlesCount', data: params })
  }

  // 青岛市本级安装率
  appInstall (params) {
    return this.request({ url: '/wholeuser/appInstall', data: params })
  }

  // 青岛市本级代表团安装率
  memberCMemTeamInstallount (params) {
    return this.request({ url: '/wholeuser/MemTeamInstall', data: params })
  }

  // 各区市总安装率排名
  areaInstall (params) {
    return this.request({ url: '/wholeuser/areaInstall', data: params })
  }

  // 全市标兵/各区市
  dutynumList (params) {
    return this.request({ url: '/dutynum/list', data: params })
  }

  // 代表团
  delegationScore (params) {
    return this.request({ url: '/dutynum/delegationScore', data: params })
  }

  // 群众留言
  findWygzsTitleList (params) {
    return this.request({ url: '/wygzsStudioDocking/findWygzsTitleList', data: params })
  }

  // 履职列表
  generateDuty (params) {
    return this.request({ url: '/duty/generateDuty', data: params })
  }

  // 青岛市本级代表团安装率
  MemTeamInstall (params) {
    return this.request({ url: '/wholeuser/MemTeamInstall', data: params })
  }

  // 建议统计图
  currentCategory (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/currentCategory', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 建议总数
  getAdviceByDomain (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getAdviceByDomain', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 代表团
  getNumberByDelegation (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getNumberByDelegation', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 代表
  getNumberByRepresentative (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getNumberByRepresentative', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 今日提交
  getAdviceByToday (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getAdviceByToday', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 建议热词
  keywords (params) {
    return this.request({ url: 'http://120.221.72.187:81/datacenter/inner/keywords', data: params })
  }

  // 建议满意度
  getAdviceBySatisfaction (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getAdviceBySatisfaction', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 类别满意度
  getSatisfactionByCategory (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getSatisfactionByCategory', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 通知公告详情
  noticeInfo (params) {
    return this.request({ url: `/notice/info/${params}` })
  }
}
export {
  leaderDriving
}

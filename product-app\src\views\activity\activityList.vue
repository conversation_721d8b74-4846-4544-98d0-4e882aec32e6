<template>
  <div class="activityList">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div :style="$general.loadConfiguration(1)">
      <van-tabs v-model:active="switchs.value"
                :color="appTheme"
                swipeable
                sticky
                :offset-top="isShowHead?'46px':'0'"
                :title-active-color="appTheme"
                :ellipsis="false">
        <van-tab v-for="(item,index) in switchs.data"
                 :key="index"
                 :title="item.label"
                 :name="item.value">
          <!--搜索-->
          <van-pull-refresh v-model="refreshing"
                            @refresh="onRefresh">
            <van-list v-model:loading="loading"
                      :finished="finished"
                      finished-text="没有更多了"
                      offset="52"
                      @load="onLoad">
              <div v-if="showSearch"
                   id="search"
                   class="search_box"
                   :style="$general.loadConfiguration()">
                <div class="search_warp flex_box">
                  <div @click="search();"
                       class="search_btn flex_box flex_align_center flex_justify_content">
                    <van-icon :size="((appFontSize)*0.01)+'px'"
                              :color="'#666'"
                              :name="'search'"></van-icon>
                  </div>
                  <form class="flex_placeholder flex_box flex_align_center search_input"
                        action="javascript:return true;"> <input id="searchInput"
                           class="flex_placeholder"
                           :style="$general.loadConfiguration(-1)"
                           :placeholder="seachPlaceholder"
                           maxlength="100"
                           type="text"
                           ref="btnSearch"
                           @keyup.enter="search()"
                           v-model="seachText" />
                    <div v-if="seachText"
                         @click="seachText='';search();"
                         class="search_btn flex_box flex_align_center flex_justify_content">
                      <van-icon :size="((appFontSize)*0.01)+'px'"
                                :color="'#999'"
                                :name="'clear'"></van-icon>
                    </div>
                  </form>
                </div>
              </div>
              <!--数据列表-->
              <ul class="vue_newslist_ul">
                <li v-for="(item,index) in dataList"
                    :key="index"
                    class="vue_newslist2_box">
                  <van-cell clickable
                            class="vue_newslist2_item "
                            @click="openDetails(item)">
                    <div style="position: relative;">
                      <div v-if="item.isRead == '0'"
                           style="top:5px;right:5px;"
                           class="notRead"></div>
                    </div>
                    <div class="flex_box vue_newslist2_warp">
                      <div class="flex_placeholder vue_newslist2_con">
                        <div class="vue_newslist2_title text_two"
                             :style="$general.loadConfiguration(-1)+'margin-top:14px;'">
                          <span v-if="item.areaName"
                                class="vue_newslist_top"
                                :style="$general.loadConfiguration(-3)">
                            <van-tag plain
                                     :color="appTheme">{{item.areaName}}</van-tag>
                          </span>
                          <font class="inherit"
                                v-html="item.title"></font>
                        </div>
                        <div v-if="item.state"
                             class="flex_box flex_align_center">
                          <div v-if="item.showClock"
                               style="margin-right: 5px;padding-top: 1px;"
                               class="flex_box flex_align_center flex_justify_content">
                            <van-icon :color="$general.getHeadThemeRelatively()"
                                      :size="((appFontSize-3)*0.01)+'px'"
                                      :name="'clock-o'"></van-icon>
                          </div>
                          <div :style="$general.loadConfiguration(-3)+'color:#333;font-weight: 600;'"
                               v-html="item.showText"></div>
                          <div :style="$general.loadConfiguration(-3)+'color:#F6931C;font-weight: 600;'"
                               v-html="item.addTime"></div>
                        </div>
                        <div class="flex_box flex_align_center"
                             style="margin: 5px 0 5px;">
                          <div class="flex_placeholder text_one2"
                               :style="$general.loadConfiguration(-3)+'color:#333;'">组织部门：{{item.organizer}}</div>
                        </div>
                        <div class="flex_box flex_align_center"
                             style="margin: 5px 0 10px;">
                          <div class="flex_placeholder text_one2"
                               :style="$general.loadConfiguration(-3)+'color:#333;'">地点：{{item.address}}</div>
                        </div>
                        <ul v-if="(item.btns||[]).length != 0"
                            class="vue_newslist2_btns flex_box flex_align_center van-hairline--top">
                          <li v-for="(nItem,nIndex) in item.btns"
                              :key="nIndex"
                              @click.stop="listBtnClick(nItem,item)"
                              :style="$general.loadConfiguration(-1)+'font-weight: '+(nItem.important?'600':'400')+';color:'+(nItem.color||(nItem.important?appTheme:'#333'))"
                              class="flex_box flex_align_center flex_justify_content"
                              :class="nIndex!=0?'van-hairline--left':''"
                              v-html="nItem.name"></li>
                        </ul>
                      </div>
                    </div>
                  </van-cell>
                </li>
              </ul>

              <!--加载中提示 首次为骨架屏-->
              <div v-if="showSkeleton"
                   class="notText">
                <van-skeleton v-for="(item,index) in 3"
                              :key="index"
                              title
                              :row="3"></van-skeleton>
              </div>
              <template v-else-if="dataList.length == 0">
                <van-empty :style="$general.loadConfiguration(-2)">
                  <!-- <template #description>
                    <div class="van-empty__description_text"
                         :style="$general.loadConfiguration(-1)"
                         v-html="'暂无数据'"></div>
                  </template> -->
                </van-empty>
              </template>
            </van-list>
          </van-pull-refresh>
        </van-tab>
      </van-tabs>
      <div class="newProposal"
           v-if="switchs.value ==='notStart,signUp'"
           @click="newClick"
           :style="'background:'+appTheme">发起活动</div>
    </div>
  </div>
  <van-overlay :show="dialogShow"
               @click="show = false">
    <van-dialog v-model:show="dialogShow"
                :title="dialogTitle"
                :width="'288px'"
                :overlay="false"
                @confirm="confirm"
                show-cancel-button>
      <div class="inherit"
           style="padding: 20px 0 20px 0;">
        <!-- 密码输入框 -->
        <van-password-input :value="value"
                            :mask="false"
                            :length="signlength"
                            :focused="showKeyboard"
                            @focus="showKeyboard = true" />
      </div>

    </van-dialog>
    <!-- 数字键盘 -->
    <van-number-keyboard v-model="value"
                         :show="showKeyboard"
                         :z-index="'99'"
                         @blur="showKeyboard = false" />
  </van-overlay>
  <van-action-sheet v-model:show="show"
                    :actions="actions"
                    :description="description"
                    cancel-text="取消"
                    @select="onSelect"
                    close-on-click-action />
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Toast, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'activityList',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      seachPlaceholder: '搜索',
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      switchs: { value: 'notStart,signUp', data: [{ label: '未开始', value: 'notStart,signUp' }, { label: '进行中', value: 'ongoing' }, { label: '已结束', value: 'end' }, { label: '我的', value: 'my' }] },
      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用
      filters: [
      ], // 筛选集合

      showSearch: true,
      description: '',
      show: false,
      actions: [],
      dialogShow: false,
      dialogTitle: '',
      showKeyboard: true,
      value: '',
      signlength: 4,
      nowItem: ''
    })
    onMounted(() => {
      onRefresh()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })
    watch(() => data.switchs.value, (newName, oldName) => {
      onRefresh()
    })
    watch(() => data.value, (newVal) => {
      console.log(newVal)
      if (newVal.length > data.signlength) {
        Toast(`签到口令为${data.signlength}位`)
        data.value = data.value.slice(0, 4)
      }
    })
    const search = () => {
      onRefresh()
    }
    // 列表请求
    const getList = async () => {
      if (data.pageNo > 1 && data.dataList.length === 0) {
        return
      }
      const param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.seachText,
        meetName: data.seachText,
        isAppShow: '1'
      }
      var res = {}
      if (data.switchs.value === 'my') {
        res = await $api.activity.myActivityList(param)
      } else {
        param.meetState = data.switchs.value
        res = await $api.activity.activityList(param)
      }

      var { data: list, total } = res
      const newData = []
      list.forEach(item => {
        const itemData = item
        var signBeginTime = itemData.signBeginTime || ''// 报名开始
        var signEndTime = itemData.signEndTime || ''// 报名截止
        var meetSignBeginTime = itemData.meetSignBeginTime || ''// 签到开始
        var meetSignEndTime = itemData.meetSignEndTime || ''// 签到结束
        var meetStartTime = itemData.meetStartTime || ''// 活动开始时间
        var meetEndTime = itemData.meetEndTime || ''// 活动结束时间

        var formatNowTime = Math.round(new Date().getTime() * 0.001)// 当前时间的时间戳
        var formatSignBeginTime = Math.round(new Date(signBeginTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化报名开始成时间戳
        var formatSignEndTime = Math.round(new Date(signEndTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化报名截止成时间戳
        var formatMeetSignBeginTime = Math.round(new Date(meetSignBeginTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化签到开始成时间戳
        var formatMeetSignEndTime = Math.round(new Date(meetSignEndTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化签到结束成时间戳
        var formatMeetStartTime = Math.round(new Date(meetStartTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化开始成时间戳
        var formatMeetEndTime = Math.round(new Date(meetEndTime.replace(/-/g, '/')).getTime() * 0.001)// 格式化结束成时间戳
        // var isPublish = itemData.isPublish || '0'// 是否公开 1是0否
        var isPublishbm = itemData.isPublishbm || '0'// 是否公开报名 1是0否
        var isInviter = itemData.isInviter || '0'// 是否邀请，1是0否
        var isSignUp = itemData.isSignUp || '0'// 是否报名，1是0否
        var isSignIn = itemData.isSignIn || '0'// 是否签到，1是0否
        var isLeave = itemData.isLeave || '0'// 是否请假，1是0否
        var leaveStatus = itemData.leaveStatus || '0'// 请假状态 0未审核 1审核通过 2审核未通过
        // item.time = meetStartTime ? meetStartTime.substring(0, meetStartTime.lastIndexOf(':')) : '未设置活动开始时间';
        item.id = itemData.id || ''// id
        item.ifSignInPostion = itemData.ifSignInPostion || ''// 是否开启定位签到
        item.isRead = itemData.isRead || '0'// 已读为1
        item.title = itemData.meetName || ''// 标题
        if ($general.trim(data.seachText)) {
          item.title = item.title.replace(new RegExp(data.seachText, 'g'), '<span style="color:' + data.appTheme + ';" class="inherit">' + data.seachText + '</span>')
        }
        item.state = itemData.state || ''// 标签
        item.areaName = itemData.areaName || ''// 地区 1是0否
        item.organizer = itemData.organizer || ''
        item.address = itemData.address || ''
        item.btns = []
        item.addTime = ''
        item.showClock = false
        if (formatSignBeginTime > formatNowTime) { // 报名没开始
          item.showClock = true
          item.showText = '离开始报名还有：'
          item.addTime = $general.formatSeconds(formatSignBeginTime - formatNowTime)
          // eslint-disable-next-line eqeqeq
          if (isInviter == '1' || isPublishbm == '1') {
            // eslint-disable-next-line eqeqeq
            item.btns.push({ name: isSignUp == '1' ? '已报名' : '报名', click: 'signUp', color: '#ccc', isClick: false })
            // eslint-disable-next-line eqeqeq
            if (isInviter == '1') {
              // eslint-disable-next-line eqeqeq
              item.btns.push({ name: isLeave == '1' ? (leaveStatus == 0 ? '请假审核中' : leaveStatus == 2 ? '请假未通过' : '请假通过') : '请假', click: 'leave', color: '#ccc', isClick: false })
            }
          }
          item.btns.push({ name: '详情', click: 'details', isClick: true })
        } else if (formatSignEndTime > formatNowTime) { // 报名没结束
          item.showClock = true
          item.showText = '报名倒计时：'
          item.addTime = $general.formatSeconds(formatSignEndTime - formatNowTime)
          // eslint-disable-next-line eqeqeq
          if (isInviter == '1') {
            // eslint-disable-next-line eqeqeq
            if (isSignUp == '1') {
              item.btns.push({ name: '已报名', click: 'signUp', color: data.appTheme, isClick: false })
              item.btns.push({ name: '请假', click: 'leave', color: '#ccc', isClick: false })
              // eslint-disable-next-line eqeqeq
            } else if (isLeave == '1') {
              // eslint-disable-next-line eqeqeq
              item.btns.push({ name: '报名', click: 'signUp', important: leaveStatus == 2, color: leaveStatus == 2 ? data.appTheme : '#ccc', isClick: leaveStatus == 2 })
              // eslint-disable-next-line eqeqeq
              item.btns.push({ name: leaveStatus == 0 ? '请假审核中' : leaveStatus == 2 ? '请假未通过' : '请假通过', color: data.appTheme, click: 'leave', isClick: false })
            } else {
              item.btns.push({ name: '报名', click: 'signUp', important: true, isClick: true })
              item.btns.push({ name: '请假', click: 'leave', important: true, isClick: true })
            }
            // eslint-disable-next-line eqeqeq
          } else if (isPublishbm == '1') {
            // eslint-disable-next-line eqeqeq
            item.btns.push({ name: isSignUp == '1' ? '已报名' : '报名', click: 'signUp', important: isSignUp != '1', color: data.appTheme, isClick: isSignUp != '1' })
          }
          item.btns.push({ name: '详情', click: 'details', isClick: true })
        } else if (formatMeetStartTime > formatMeetSignBeginTime && formatMeetSignEndTime > formatNowTime) { // 签到时间大于开始时间 并且没有结束签到
          item.showClock = true
          if (formatMeetSignBeginTime > formatNowTime) { // 没到签到时间
            item.showText = '离开始签到还有：'
            item.addTime = $general.formatSeconds(formatMeetSignBeginTime - formatNowTime)
          } else { // 签到倒计时
            item.showText = '签到倒计时：'
            item.addTime = $general.formatSeconds(formatMeetSignEndTime - formatNowTime)
          }
          showSignInBtn(1)
        } else if (formatMeetStartTime > formatNowTime) { // 没到活动开始时间
          item.showText = '开始时间：'
          item.addTime = dayjs(meetStartTime).format('YYYY-MM-DD HH:mm')
          showSignInBtn()
        } else if (formatMeetSignBeginTime > formatNowTime) { // 没到签到时间
          item.showClock = true
          item.showText = '离开始签到还有：'
          item.addTime = $general.formatSeconds(formatMeetSignBeginTime - formatNowTime)
          showSignInBtn(1)
        } else if (formatMeetSignEndTime > formatNowTime) { // 签到没结束
          item.showClock = true
          item.showText = '签到倒计时：'
          item.addTime = $general.formatSeconds(formatMeetSignEndTime - formatNowTime)
          showSignInBtn(1)
        } else if (formatMeetEndTime > formatNowTime) { // 活动没结束
          item.showClock = true
          item.showText = (formatMeetSignEndTime > formatMeetStartTime ? '签到已截止，' : '') + '活动倒计时：'// 签到截止在活动开始后 就有截止提示
          item.addTime = $general.formatSeconds(formatMeetEndTime - formatNowTime)
          showSignInBtn(1)
        } else {
          var addEndTime = ''
          // eslint-disable-next-line eqeqeq
          if (dayjs(meetStartTime).year() != dayjs(meetEndTime).year()) {
            addEndTime = dayjs(meetEndTime).format('YYYY-MM-DD HH:mm')
            // eslint-disable-next-line eqeqeq
          } else if (dayjs(meetStartTime).month() != dayjs(meetEndTime).month() || dayjs(meetStartTime).date() != dayjs(meetEndTime).date()) {
            addEndTime = dayjs(meetEndTime).format('MM-DD HH:mm')
          } else {
            addEndTime = dayjs(meetEndTime).format('HH:mm')
          }
          item.showText = '起止时间：' + dayjs(meetStartTime).format('YYYY-MM-DD HH:mm') + '至' + addEndTime
          // eslint-disable-next-line eqeqeq
          item.btns.push({ name: '查看活动结果', click: isInviter == '1' || isSignUp == '1' ? 'activityReport' : 'details', isClick: true })
        }
        // 很多状态都是一个判断
        function showSignInBtn (_type) {
          var hasDetails = true
          // eslint-disable-next-line eqeqeq
          if (isInviter == '1' || isPublishbm == '1') { // 邀请人 公开报名的
            // eslint-disable-next-line eqeqeq
            if (isSignUp == '1') {
              // eslint-disable-next-line eqeqeq
              item.btns.push({ name: isSignIn == '1' ? '已签到' : '签到', important: isSignIn != '1' && formatNowTime > formatMeetSignBeginTime && formatNowTime < formatMeetSignEndTime, color: formatNowTime < formatMeetSignBeginTime || (formatNowTime > formatMeetSignEndTime && isSignIn != '1') ? '#ccc' : data.appTheme, click: 'signIn', isClick: isSignIn != '1' && formatNowTime > formatMeetSignBeginTime && formatNowTime < formatMeetSignEndTime })
              if (_type) {
                hasDetails = false
                item.btns.push({ name: '活动资料', click: 'activityMateria', important: true, isClick: true })
                item.btns.push({ name: '日程行程', click: 'activitySchedule', important: true, isClick: true })
              }
              // eslint-disable-next-line eqeqeq
            } else if (isLeave == '1') {
              // eslint-disable-next-line eqeqeq
              item.btns.push({ name: leaveStatus == 0 ? '请假审核中' : leaveStatus == 2 ? '请假未通过' : '请假通过', color: data.appTheme, click: 'leave', isClick: false })
            } else {
              item.btns.push({ name: '未报名', click: 'signUp', color: '#ccc', isClick: false })
            }
          }
          if (hasDetails) { item.btns.push({ name: '详情', click: 'details', isClick: true }) }
        }
        item.relateType = data.relateType
        newData.push(item)
      })
      data.dataList = data.dataList.concat(newData)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }

    const onSelect = (item) => {
      // 默认情况下点击选项时不会自动收起
      // 可以通过 close-on-click-action 属性开启自动收起
      data.show = false
      // var touchItem = data.dataList[data.onTouchIndex]
      switch (item.name) {
        case '签到口令':
          data.dialogTitle = '活动签到码'
          data.dialogShow = true
          data.showKeyboard = true
          break
      }
    }
    const confirm = async () => {
      console.log(data.value)
      if (!data.value || data.value.length !== 4) {
        Toast('签到口令不正确')
        return
      }
      const res = await $api.activity.addSignInUser({ userId: data.user.id, dataId: data.nowItem.id, type: 'signIn', signInCommand: data.value })
      var code = res.errcode || 0
      if (code === 200) {
        Toast('签到成功')
        onRefresh()
      } else {
        Toast(res.errmsg || res.data)
      }
    }

    const listBtnClick = (_nItem, _item) => {
      if (!_nItem.isClick) return
      switch (_nItem.click) {
        case 'signUp':
          Toast({ type: 'loading', message: '报名中', duration: 0 })
          Dialog.confirm({
            message:
              '确定要报名吗？'
          }).then(async () => {
            const res = await $api.activity.addSignInUser({ userId: data.user.id, dataId: _item.id, type: 'signUp' })
            var code = res.errcode || 0
            if (code === 200) {
              Toast({ type: 'success', message: '报名成功', duration: 1500 })
              _nItem.name = '已报名'
              _nItem.isClick = false
              $general.delItemForKey('leave', _item.btns, 'click')
            } else {
              Toast({ type: 'fail', message: res.errmsg, duration: 1500 })
            }
          }).catch(() => {
            Toast({ type: 'fail', message: '取消报名', duration: 1500 })
            // on cancel
          })
          break
        case 'signIn':
          data.description = '签到方式'
          data.actions = [{ name: '签到口令', color: '#ee0a24' }]
          data.show = true
          data.nowItem = _item
          break
        case 'leave':
          router.push({ name: 'activityLeave', query: { title: '请假', id: _item.id, paramType: 'addLeave' } })
          break
        case 'details':
          openDetails(_item)
          break
        case 'activityMateria':// 活动资料
        case 'activitySchedule':// 日程行程
        case 'activityReport':// 活动报告
          router.push({ name: 'activityOtherInformation', query: { relateType: _nItem.click, id: _item.id, title: _nItem.name } })
          break
      }
    }
    const openDetails = rows => {
      router.push({ path: 'activityDetails', query: { id: rows.id, title: rows.title } })
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }

    const onClickLeft = () => history.back()

    const newClick = () => {
      router.push({ name: 'activityNew' })
    }

    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, search, openDetails, listBtnClick, confirm, onSelect, newClick }
  }
}
</script>
<style lang="less" scoped>
.activityList {
  background: #f8f8f8;
  .van-tabs {
    background: #fff;
  }

  .newProposal {
    position: fixed;
    left: 50%;
    bottom: 10%;
    transform: translateX(-50%);
    width: 76px;
    height: 76px;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 76px;
    text-align: center;
    color: #ffffff;
  }
}
</style>

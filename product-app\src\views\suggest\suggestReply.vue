<template>
  <div class="suggestReply">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <div :key="item.id"
           class="suggestReply_box"
           v-for="item in dataList">
        <div class="suggestReply_box_title">{{ item.groupName }}</div>
        <div v-html="item.content"></div>
        <div class="suggestReply_box_title_date">
          <p>{{ item.createDate }}</p>
          <p>{{ item.answerTypeView }}</p>
        </div>
        <template v-if="item.attachmentList">
          <div class="proposalText"
               v-if="item.attachmentList.length">附件</div>
          <div class="attachmentBox">
            <div class="attachmentItem"
                 v-for="file in item.attachmentList"
                 @click="download(file)"
                 :key="file.id">{{ file.fileName }}</div>
          </div>
        </template>
      </div>
    </van-pull-refresh>
  </div>
</template>
<script>
// import { Toast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'suggestReply',
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const data = reactive({
      id: route.query.id,
      activeNames: [],
      dataList: [],
      refreshing: false
    })
    onMounted(() => {
      answerList()
    })
    const onRefresh = () => {
      setTimeout(() => {
        answerList()
      }, 520)
    }
    // 列表请求
    const answerList = async () => {
      flowAnswerDetail()
      data.refreshing = false
    }
    // 列表请求
    const flowAnswerDetail = async (id) => {
      const res = await $api.suggest.suggestUrl('suggest/transactSuggestDetail', {
        suggestId: data.id
      })
      data.dataList = res.data.flowAnswerListVoList
      data.dataList.forEach(item => {
        suggestAnswerDetail(item)
      })
    }
    const suggestAnswerDetail = async (item) => {
      const res = await $api.suggest.suggestUrl('suggest/suggestAnswerDetail', {
        flowAnswerId: item.id
      })
      item.attachmentList = res.data.attachmentList
    }
    const download = (item) => {
      if (item.fileType === 'pdf') {
        if (window.location.origin === 'http://**************') {
          window.open('http://**************/pdf/web/viewer.html?file=' + item.filePath)
        } else {
          window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.filePath)
        }
      } else {
        var param = {
          id: item.id,
          url: item.filePath,
          name: item.fileName
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    return { ...toRefs(data), onRefresh, download }
  }
}
</script>
<style lang="less">
.suggestReply {
  width: 100%;
  min-height: 100%;
  background: #fff;

  .suggestReply_box {
    margin: 10px;
    background: #fff;
    padding: 0 10px;
    border-bottom: 1px solid #eee;
  }

  .suggestReply_box_title {
    font-size: 18px;
    margin: 5px 0;
    font-weight: 700;
  }

  .suggestReply_box_title_date {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
  }

  .communicationText {
    padding: 6px 0;
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 22px;
    color: #666666;

    span {
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 600;
      line-height: 22px;
      color: #333333;
    }
  }

  .proposalText {
    font-size: 15px;
    font-family: PingFang SC;
    font-weight: 600;
    line-height: 20px;
    color: #333333;
    position: relative;
    padding-left: 6px;
    margin-bottom: 6px;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      width: 2px;
      height: 15px;
      background: #3088fe;
      opacity: 1;
      border-radius: 10px;
    }
  }

  .attachmentBox {
    width: 100%;
    padding-bottom: 16px;

    .attachmentItem {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 18px;
      color: #3088fe;
      padding: 3px 0;
    }
  }
}
</style>

<template>
  <div class="wrap" v-loading="loading">
    <vue-office-docx v-if="fileInfo.fileType === 'docx'" :src="fileInfo.filePath" style="height: 100%"
      @rendered="rendered" @error="HandlError">
    </vue-office-docx>

    <vue-office-pdf v-if="fileInfo.fileType === 'pdf'" :src="fileInfo.filePath" style="height: 100%"
      @rendered="rendered" @error="HandlError">
    </vue-office-pdf>

    <div v-if="fileInfo.fileType === 'image'" class="imageBox">
      <img :src="fileInfo.filePath" alt="">
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from 'vue'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficePdf from '@vue-office/pdf'
import { useRoute } from 'vue-router'
const route = useRoute()
const loading = ref(true)
const fileInfo = ref({})

const rendered = () => {
  loading.value = false
}

const HandlError = (errorInfo) => {
  alert('该文件暂不支持在线预览')
  loading.value = false
}

onMounted(() => {
  fileInfo.value = route.query
  console.log('fileInfo.value', fileInfo.value)
})

</script>
<style>
.wrap {
  background: #e8e8e8 !important;
  height: 100vh !important;
}

.imageBox {
  background: #e8e8e8 !important;
  width: 100%;
  text-align: center;
}

.vue-office-pdf-wrapper {
  background: #e8e8e8 !important;
}
</style>

<template>
  <div class="activityLeave">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <van-cell title="请假理由"
              :required="true">
      <!-- 使用 right-icon 插槽来自定义右侧图标 -->
      <template #right-icon>
        <van-dropdown-menu :active-color="appTheme">
          <van-dropdown-item v-model="value"
                             :options="option" />
        </van-dropdown-menu>
      </template>
    </van-cell>
    <van-cell title="请假说明"
              :required="true">
      <van-field v-model="content"
                 rows="5"
                 autosize
                 type="textarea"
                 maxlength="1000"
                 placeholder="请输入请假说明"
                 show-word-limit />
    </van-cell>
    <van-uploader v-model="fileList"
                  :after-read="afterRead"
                  :before-delete="beforeDelete"
                  multiple
                  :max-count="9" />
    <div style="height:55px;"></div>
    <footer class="footerBox">
      <van-button @click="submit"
                  type="parmary"
                  :color="appTheme"
                  block>确定</van-button>
    </footer>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, DropdownMenu, DropdownItem, Uploader, Toast } from 'vant'
export default {
  name: 'activityLeave',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Uploader.name]: Uploader,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appFontSize: 16,
      safeAreaTop: 0,
      SYS_IF_ZX: $ifzx,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      keyword: '',
      seachText: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      fileList: [],
      attachmentIds: [],
      content: '',
      value: '',
      option: []
    })
    onMounted(() => {
      getData()
    })
    watch(() => data.dataList, (newName, oldName) => {

    })

    // 列表请求
    const getData = async () => {
      var { data: info } = await $api.general.pubkvs({ types: 'act_leave_type' })
      console.log(info.act_leave_type)
      data.option = []
      const newData = [{ text: '请选择', value: '' }]
      info.act_leave_type.forEach(element => {
        newData.push({ text: element.value, value: element.id })
      })
      data.option = data.option.concat(newData)
    }

    const submit = async () => {
      if (!data.value) {
        Toast('请选择请假理由')
        return
      }
      if (!data.content) {
        Toast('请输入请假说明')
        return
      }
      const myParam = {
        meetId: data.id,
        userId: data.user.id,
        userNames: data.user.userName,
        status: data.status || 0,
        reasonChoose: data.value,
        reason: data.content,
        attachmentIds: data.attachmentIds.join(',')
      }
      var res = await $api.activity.addLeave(myParam)
      console.log(res)
      if (res.errcode === 200) {
        Toast('请假成功')
        setTimeout(() => {
          onClickLeft()
        }, 1000)
      } else {
        Toast(res.errmsg || res.data)
      }
    }
    const beforeDelete = (file) => {
      data.fileList.forEach((element, index) => {
        if (element.content === file.content) {
          data.fileList.splice(index, 1)
          data.attachmentIds.splice(index, 1)
        }
      })
    }
    const afterRead = async (file) => {
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'leaveFile')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        data.attachmentIds.push(info.id)
      }
    }
    const onClickLeft = () => history.back()

    return { ...toRefs(data), onClickLeft, $general, afterRead, beforeDelete, submit }
  }
}
</script>
<style lang="less" scoped>
.activityLeave {
  background: #fff;
  .van-button {
    width: calc(100% - 20px);
    margin: 10px;
  }
}
</style>

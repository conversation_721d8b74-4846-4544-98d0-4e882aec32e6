<template>
  <router-view v-slot="{ Component }">
    <!-- <transition name="fade"
                mode="out-in"
                appear> -->
    <keep-alive :include="keepAlive">
      <component :is="Component" />
    </keep-alive>
    <!-- </transition> -->
  </router-view>
  <speechContent />
  <div v-if="scrollTop >= 100"
       class="goTop"
       @click="goTop">
    <van-icon :size="41"
              name="upgrade"></van-icon>

  </div>
</template>
<script>
import api from './api'
import utils from './assets/js/utils'
import general from './assets/js/general'
import speechContent from './components/speechContent/speechContent'
import { onMounted, provide, reactive, toRefs, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
export default {
  name: 'App',
  components: { speechContent },
  setup () {
    const SYS_IF_ZX = true // 是否是政协项目
    provide('$ifzx', SYS_IF_ZX)
    provide('$api', api)
    provide('$utils', utils)
    provide('$general', general)
    provide('$appTheme', SYS_IF_ZX ? '#3088FE' : '#C61414')// 主题颜色
    provide('$isShowHead', false)// 是否显示头部
    const route = useRoute()
    const store = useStore()
    const data = reactive({
      scrollTop: 0,
      keepAlive: [],
      whiteList: ['homeAdministratorDetails', 'RandomClappingDetails', 'homeAdministratorEdit']
    })
    onMounted(() => {
      general.setWaterMark()
      general.appGrayscale()
    })
    watch(() => store.state.eliminateStatus, (val) => {
      if (val === true) {
        data.keepAlive = ['module', 'suggestList']
      } else {
        data.keepAlive = ['RandomClapping', 'homeAdministratorList', 'module', 'suggestList']
      }
    }, {
      deep: true,
      immediate: true
    })
    window.onscroll = function () {
      if (data.whiteList.includes(route.name)) return
      data.scrollTop = document.documentElement.scrollTop || document.body.scrollTop
    }
    const goTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
    return { ...toRefs(data), goTop }
  }
}
</script>
<style lang="less">
.goTop {
  position: fixed;
  text-align: center;
  width: 40px;
  // left: 0;
  right: 40px;
  // margin: auto;
  bottom: 120px;
}

#app {
  width: 100%;
  min-height: 100vh;
  background: #f8f8f8;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0.3;
}
</style>

<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'pie',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: <PERSON><PERSON>
  },
  props: {
    color: String,
    id: String,
    list: Array
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      nextTick(() => {
        var chartDom = document.getElementById(props.id)
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        myChart = echarts.init(chartDom)
        setOptions()
      })
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
    })
    const setOptions = () => {
      // console.log(parseInt(data.viewportWidth * 0.04))
      var options = {
        legend: {
          orient: 'horizontal', // 或 'horizontal'
          bottom: -parseInt(data.viewportWidth * 0.013),
          left: 'center',
          width: parseInt(data.viewportWidth * 0.9),
          height: parseInt(data.viewportWidth * 0.4),
          padding: [parseInt(data.viewportWidth * 0.1), parseInt(data.viewportWidth * 0.01)],
          // 设置图例文字的样式
          textStyle: {
            fontSize: parseInt(data.viewportWidth * 0.03),
            color: '#ADADAD'
          },
          formatter: function (name) { // 该函数用于设置图例显示后的百分比
            var total = 0
            var value
            props.list.map(v => {
              return {
                value: v.value,
                name: v.name
              }
            }).forEach((item) => {
              total += Number(item.value)
              if (item.name === name) {
                value = item.value
              }
            })
            var p = Math.round(((value / total) * 100)) // 求出百分比
            return `${name} | ${p}%` // 返回出图例所显示的内容是名称+百分比
          },
          itemWidth: parseInt(data.viewportWidth * 0.04),
          itemHeight: parseInt(data.viewportWidth * 0.03)
        },
        color: ['#3893ff', '#a938fb', '#ffd434', '#ff7389', '#17c78a', '#ff7a2d'],
        series: [
          {
            name: '代表年龄分析',
            type: 'pie',
            startAngle: 20, // 调整起始角度
            radius: [parseInt(data.viewportWidth * 0.04) + '%', parseInt(data.viewportWidth * 0.1) + '%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: parseInt(data.viewportWidth * 0.015),
              borderColor: '#fff',
              borderWidth: parseInt(data.viewportWidth * 0.009)
            },
            label: {
              bleedMargin: 10,
              show: true,
              position: 'outside',
              // data: ['f'],
              formatter: function (params) {
                return `${params.name}\n${params.value}人`
              },
              fontSize: parseInt(data.viewportWidth * 0.03),
              alignTo: 'labelLine'
            },
            labelLine: {
              show: true,
              length: parseInt(data.viewportWidth * 0.05),
              length2: parseInt(data.viewportWidth * 0.12)
            },
            emphasis: {
            },
            data: [
              { value: 1048, name: 'Search Engine' },
              { value: 735, name: 'Direct' },
              { value: 580, name: 'Email' },
              { value: 484, name: 'Union Ads' },
              { value: 300, name: 'Video Ads' },
              { value: 200, name: 'ghjkgjkAds' }
            ]
          }
        ]
      }
      var options2 = {
        series: [
          {
            name: 'Access From',
            type: 'pie',
            startAngle: 60, // 调整起始角度
            radius: [parseInt(data.viewportWidth * 0.11) + '%', parseInt(data.viewportWidth * 0.18) + '%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: parseInt(data.viewportWidth * 0.015),
              borderColor: '#fff',
              borderWidth: parseInt(data.viewportWidth * 0.01)
            },
            label: {
              show: false
            },
            emphasis: {
            },
            data: [
              { value: 1048, name: 'Search Engine', itemStyle: { color: '#3da2ff' } },
              { value: 735, name: 'Direct', itemStyle: { color: '#ff738c' } }
            ]
          }
        ]
      }
      var options3 = {
        color: ['#67DBFF', '#FF61C9', '#64A2FF', '#FF9567', '#BCFF87', '#FF6D6D', '#61E89F', '#BC87FF', '#FFD056'],
        series: [
          {
            name: '类型占比',
            type: 'pie',
            minAngle: 30,
            // startAngle: 90, // 调整起始角度
            radius: [parseInt(data.viewportWidth * 0.06) + '%', parseInt(data.viewportWidth * 0.12) + '%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: parseInt(data.viewportWidth * 0.0),
              borderColor: '#fff',
              borderWidth: parseInt(data.viewportWidth * 0.008)
            },
            label: {
              show: true,
              position: 'outside',
              // data: ['f'],
              formatter: function (params) {
                return `${params.data.proportion}%\n${params.name}`
              },
              fontSize: parseInt(data.viewportWidth * 0.024),
              alignTo: 'labelLine',
              rich: {
                name: {
                  fontSize: 14,
                  color: '#333',
                  align: 'center'
                },
                value: {
                  fontSize: 12,
                  color: '#999'
                }
              }
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < myChart.getWidth() / 2
              const points = params.labelLinePoints
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            },
            labelLine: {
              show: true,
              length: parseInt(data.viewportWidth * 0.08),
              length2: parseInt(data.viewportWidth * 0.1)
            },
            emphasis: {
            },
            data: [
              { value: 1048, name: 'Search Engine' },
              { value: 735, name: 'Direct' },
              { value: 580, name: 'Email' },
              { value: 484, name: 'Union Ads' },
              { value: 300, name: 'Video Ads' },
              { value: 200, name: 'ghjkgjkAds' }
            ]
          }
        ]
      }
      nextTick(() => {
        if (props.id === 'pie1') {
          options.series[0].data = props.list
          myChart.setOption(options)
        } else if (props.id === 'pie2') {
          options2.series[0].data = props.list
          myChart.setOption(options2)
        } else if (props.id === 'pie3') {
          options3.series[0].data = props.list
          myChart.on('click', (e) => {
            // console.log(e)
            // window.location.href = e.data.url
          })
          myChart.setOption(options3)
        }
      })
    }
    return { ...toRefs(data), general }
  }
}
</script>
<style lang="less" scoped>
#pie1 {
  background: #fff;
  width: 100%;
  height: 260px;
  box-sizing: border-box;
}

#pie2 {
  width: 100%;
  height: 100%;
  // margin: 10px 0;
  box-sizing: border-box;
  background: #fff;
}

#pie3 {
  background: #fff;
  width: 100%;
  height: 220px;
  margin: 10px 0;
  box-sizing: border-box;
}

#pie4 {
  background: #fff;
  width: 100%;
  height: 100%;
  // margin: 10px 0;
  box-sizing: border-box;
}
</style>

<template>
  <div class="suggestList">
    <van-nav-bar v-if="isShowHead"
                 :title="title"
                 fixed
                 placeholder
                 safe-area-inset-top
                 left-text=""
                 left-arrow
                 @click-left="onClickLeft" />
    <div class="suggestListtHead">
      <van-search v-model="keyword"
                  show-action
                  placeholder="请输入搜索关键词"
                  @clear="onRefresh"
                  @search="onRefresh">
        <template #action>
          <div class="screeningButton"
               @click="show = !show">
            <van-icon name="filter-o" />筛选
          </div>
        </template>
      </van-search>
    </div>
    <van-tabs v-model:active="active"
              @change="onRefresh"
              offset-top="44"
              :color="appTheme"
              swipeable
              sticky>
      <van-tab v-for="item in activeData"
               :key="item.id"
               :name="item.id"
               :title="item.value">
        <van-pull-refresh v-model="refreshing"
                          @refresh="onRefresh">
          <van-list v-model:loading="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    offset="52"
                    @load="onLoad">
            <div class="proposalListBox">
              <dataList v-for="item in dataList"
                        :key="item.id"
                        :title="item.title"
                        @click="details(item)">
                <div class="state">{{item.processStateView}}</div>
                <div>{{item.mainSubmitUserName}}</div>
                <div>{{item.submitDate.slice(0,10)}}</div>
              </dataList>
            </div>
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
    <van-popup v-model:show="show"
               position="top">
      <screening more
                 cancel
                 title="年份"
                 v-model="year"
                 :data="yearData"
                 @more-click="moreClick"></screening>
      <screening title="届"
                 v-model="circles"
                 :data="circlesData"
                 :props="{label: 'value',id: 'value'}">届</screening>
      <screening title="次"
                 v-model="bout"
                 :data="boutData"
                 :props="{label: 'value',id: 'value'}"></screening>
      <screening cancel
                 title="建议类型"
                 v-model="submitType"
                 :data="submitTypeData"></screening>
      <screening cancel
                 title="会议类型"
                 v-model="meetType"
                 :data="meetTypeData"></screening>
      <div class="buttonBox">
        <van-button type="primary"
                    @click="reset"
                    size="mini">重置</van-button>
        <van-button type="primary"
                    @click="onRefresh"
                    size="mini">确定</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon } from 'vant'
import dataList from '../homePage/dataList'
import screening from './components/screening'
export default {
  name: 'suggestList',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    dataList,
    screening
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      active: '1',
      activeData: [],
      year: '',
      yearData: [],
      circles: '',
      bout: '',
      circlesData: [],
      boutData: [],
      submitType: '',
      submitTypeData: [
        { id: '1', label: '代表建议' },
        { id: '2', label: '代表团建议' }
      ],
      meetType: '',
      meetTypeData: [
        { id: '1', label: '大会建议' },
        { id: '2', label: '平时建议' }
      ],
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      dataList: [],
      show: false
    })
    onMounted(() => {
      crrentcircles()
      pubkvs()
      getNf(6)
    })
    /**
     * 当前届次
     */
    const crrentcircles = async (type) => {
      // const res = await $api.general.crrentcircles({ memberType: 1 })
      // var { data: list } = res
      // data.circles = list.circlesName.slice(0, list.circlesName.length - 1)
      // data.bout = list.boutName.slice(0, list.boutName.length - 1)
      // if (type) {
      //   onRefresh()
      // }
    }
    const moreClick = () => {
      getNf(data.yearData.length + 3)
    }
    const getNf = (number) => {
      var yearsArr = []
      var years = new Date().getFullYear()
      for (var i = years; i >= years - number; i--) {
        yearsArr.push({ id: i + '', label: i + '' })
      }
      data.yearData = yearsArr
    }
    const pubkvs = async () => {
      const res = await $api.general.pubkvs({ types: 'proposal_state_all,bout_type,circles_type' })
      var { data: list } = res
      // data.activeData = [{ id: '1', value: '所有' }, ...list.proposal_state_all]
      data.activeData = [{ id: '1', value: '所有' }, { id: '2', value: '办理中' }, { id: '3', value: '已答复' }]
      data.circlesData = list.circles_type
      data.boutData = list.bout_type
    }
    // 建议列表
    const suggestList = async () => {
      var datas = {
        pageNo: data.pageNo,
        pageSize: 10,
        // processState: data.active === '1' ? '' : data.active,
        processState: '',
        keyword: data.keyword,
        year: data.year, // 提案所属年份
        circles: data.circles, // 届
        bout: data.bout, // 次
        submitType: data.submitType === 'queryAgreeJoin' ? '' : data.submitType,
        meetType: data.meetType // 1 大会提案 2 平时提案
      }
      if (data.submitType === 'queryAgreeJoin') {
        datas.queryAgreeJoin = '1'
      }
      if (data.active === '2') {
        datas.queryTransactInfo = 1
      }
      if (data.active === '3') {
        datas.ifCompleteEvaluateInfo = 1
      }
      var res = ''
      if (data.active === '1') {
        res = await $api.suggest.suggestList(datas)
      } else if (data.active === '2') {
        res = await $api.suggest.allTransactList(datas)
      } else if (data.active === '3') {
        res = await $api.suggest.allAnsweredSuggestList(datas)
      }
      var { data: list, total } = res
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      data.show = false
      suggestList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      suggestList()
    }
    const reset = () => {
      getNf(6)
      data.year = ''
      data.keyword = ''
      data.meetType = ''
      data.submitType = ''
      crrentcircles(1)
    }
    const details = (row) => {
      console.log(row)
      router.push({ name: 'suggestDetails', query: { id: row.id } })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), onClickLeft, moreClick, onRefresh, onLoad, reset, details }
  }
}
</script>
<style lang="less">
.suggestList {
  width: 100%;
  // padding-top: 44px;
  .suggestListHead {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    height: 44px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .van-search {
      width: 100%;
      padding: 5px 0 5px 12px;
      .van-cell {
        padding: 3px 0;
      }
      .van-icon {
        color: #888;
      }
      .van-search__content {
        height: 30px;
        line-height: 30px;
        padding-right: 8px;
        .van-field__body {
          font-size: 14px;
          .van-field__control {
            color: #888;
            &::-webkit-input-placeholder {
              color: #888;
            }
          }
        }
      }
      .screeningButton {
        font-size: 14px;
      }
    }
  }
  .proposalListBox {
    margin: auto;
    padding: 16px;
  }
  .van-popup {
    width: 100%;
    padding-top: 44px;
    background-color: #fff;
    .buttonBox {
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 22px 0;
      .van-button {
        width: 90px;
        height: 28px;
        background: #bc1d1d;
        border: 1px solid #bc1d1d;
        .van-button__content {
          height: 26px;
          .van-button__text {
            font-size: 14px;
          }
        }
      }
      .van-button + .van-button {
        margin-left: 20px;
      }
    }
  }
}
</style>

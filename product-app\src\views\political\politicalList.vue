<template>
  <div class="newsZTList">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <van-cell v-for="item in dataList" :key="item.id"  >
          <div class="representative_box_li"  :key="item.id" @click="skipDetails(item)">
            <div class="representative_box_right">
              <p class="representative_title">{{ item.title }}</p>
              <p class="representative_time">{{ item.createDate?dayjs(item.createDate).format('YYYY-MM-DD'): '' }}</p>
            </div>
            <div class="representative_box_left" v-if="item.imageObj.fullUrl!=null">
              <img  class="representative_img" :src="item.imageObj.fullUrl" alt="">
            </div>
          </div>
        </van-cell>
      </van-list>
    <div v-if="showSkeleton"
         class="notText">
         <van-skeleton v-for="(item,index) in 3"
                            :key="index"
                            title
                            :row="3"></van-skeleton>
     </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog } from 'vant'
export default {
  name: 'newsZTList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      id: route.query.id || '',
      pageType: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      dataList: [],
      switchs: { value: '', data: [] },
      themeImg: { url: '' },
      active: '',
      menuList: [],
      identification: 0,
      topic: ['498377074449317888', '498377147954495488', '498631879772078080', '498632215421255680', '583913427601195008', '583913456667721728', '583913561495961600', '583913588691828736', '583913702919503872', '583913728316014592', '583913823384109056', '583913848705122304', '588587230944034816', '588587379388841984']
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      browseSave()
      // getTree()
      getList()
    })
    const browseSave = async () => {
      // const res = await $api.notice.browseSave({
      //   keyId: data.id,
      //   type: 5
      // })
    }
    watch(() => data.switchs.value, (newName, oldName) => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    })
    // const onRefresh = () => {
    //   data.pageNo = 1
    //   data.dataList = []
    //   data.showSkeleton = true
    //   data.loading = true
    //   data.finished = false
    //   getList()
    // }
    const onLoad = () => {
      if (data.dataList.length >= data.total) {
        return false
      } else {
        data.pageNo = data.pageNo + 1
        getList(data.identification)
      }
    }
    // 跳详情
    const skipDetails = (item) => {
      router.push({ name: 'politicalDetails', query: { id: item.relateType === '53' ? item.relateRecordId : item.relateRecordId, relateType: item.relateType } })
    }
    // 政情快递
    const getPolitical = async () => {
      var params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1,
        columnId: data.topic[5],
        subjectId: data.topic[4]
      }
      var res = await $api.general.highlights(params)
      data.dataList = res.data
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= res.total) {
        data.finished = true
      }
    }
    // 列表请求
    const getList = async (type) => {
      getPolitical()
    }
    const onClickLeft = () => history.back()
    const itemClick = (row) => {
      router.push({ name: 'newsZTListTwo', query: { id: data.id, columnId: row.id, title: row.name } })
    }
    const details = (row) => {
      // eslint-disable-next-line eqeqeq
      if (row.relateType == '5' || row.relateType == '53') {
        router.push({ name: 'newsDetails', query: { id: row.id, relateType: row.relateType, title: row.title } })
        // eslint-disable-next-line eqeqeq
      } else if (row.relateType == '27') {
        router.push({ name: 'noticeDetails', query: { id: row.id, title: row.title } })
      }
    }
    return { ...toRefs(data), dayjs, skipDetails, onClickLeft, onLoad, details, itemClick }
  }
}
</script>

<style lang="less" scoped>
.newsZTList {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
        position: absolute;
        top: 0;
        right: 10px;
     }
  .survey_box {
    width: 98%;
    margin: 0 auto;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .survey_left {
      width: 70%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 10px 0;
      box-sizing: border-box;
      .survey_title {
        font-weight: 700;
        width: 100%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .survey_time {
        font-size: 14px;
      }
    }
    .survey_right {
      width: 30%;
      height: 100%;
      display: flex;
      align-items: center;
      >img {
        width: 100%;
        height: 80%;
      }
    }
  }
  .issue{
    width: 50px;
    height: 50px;
    color: #fff;
    text-align: center;
    line-height: 50px;
    background: #3894ff;
    position: fixed;
    border-radius: 50%;
    z-index: 9999;
    bottom: 30px ;
    left: 50%;
    transform: translate(-50%, 0);
  }
  .announcement {
    width: 100%;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .announcement_title {
      font-weight: 700;
    }
    .announcement_text {
      color: #666;
      text-align: right;
      font-size: 14px;
    }
  }
  ::v-deep .van-tabs__line {
    background: #000 !important;
  }
  ::v-deep .van-tab__text--ellipsis {
  }
  ::v-deep .van-tab--active {
    color: #000 !important;

  }
  .representative_box_li {
        width: 100%;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .representative_box_right {
          width: 65%;
          height: 100%;
          display: flex;
          flex-direction: column;;
          justify-content: space-between;
          .representative_title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .representative_time {
            font-size: 14px;
            color: #a8a8a8;
            margin: 5px 0;
          }
        }
        .representative_box_left {
          width: 35%;
          height: 100%;
          .representative_img {
            width: 90%;
            height: 90%;
            margin: 5px;
          }
        }
      }
    .van-image {
      // margin-bottom: 10px;
      .van-image_img {
        border-radius: 5px !important;
      }
    }
  .situation_li {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .situation_li_title {
      margin: 10px 0 0px 0;
      position: relative;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      .situation_li_title_sp {
        position: absolute;
        bottom: 0px;
        right: 10px;
        display: inline-block;
        width: 8px;
        height: 8px;
        background: red;
        border-radius: 50%;
      }
    }
    .situation_li_time {
      font-size: 14px;
      color: #a8a8a8;
      margin: 5px 0;
    }
  }
  .menu {
    background: #fff;
    padding: 8px 0 25px 0;
  }
  .menu_warp {
  }
  .menu .menu_item {
    position: relative;
    width: 25%;
    padding: 10px 0;
  }
  .menu .menu_item p {
    color: #3e3e3e;
    margin-top: 3px;
    text-align: center;
  }
  .menu .menu_item .footer_item_hot {
    position: absolute;
    top: 4px;
    right: 25%;
    width: 10px;
    height: 10px;
    background: #f92323;
    border-radius: 50%;
  }
  .menu .menu_item .footer_item_hot_big {
    position: absolute;
    top: 1px;
    right: 20%;
    width: 20px;
    height: 20px;
    background: #f92323;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
  }
}
.representativeCircle_box_li {
   width: 100%;
   padding-bottom: 5px;
   .representativeCircle_box_top {
     width: 100%;
     height: 35px;
     margin: 5px 0;
     display: flex;
     align-items: center;
     position: relative;
      .attention {
        text-align: center;
        position: absolute;
        top: 0;
        right: 10px;
        width: 80px;
        height: 80%;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 40px;
        color: #3894ff;
        border: 1px solid #3894ff;
      }
      .attentionDel {
        color: #666;
        border: 1px solid #666;
      }
     .representativeCircle_box_top_headImg {
       width: 30px;
       height: 30px;
       border-radius: 50%;
       margin: 5px;
     }
     .representativeCircle_box_name {
       font-size: 16px;
       .representativeCircle_box_congressStr {
         font-size: 14px;
         color: #4c4c4c;
       }
     }
   }
   .representativeCircle_box_center {
     box-sizing: border-box;
     .representativeCircle_box_center_content {
       padding-left: 13px;
       margin: 5px 0;
     }
     .representativeCircle_box_center_attachmentList {
       width: 95%;
       margin: auto;
       display: flex;
       flex-wrap: wrap;
       // justify-content: space-between;
       .van-image {
         margin: 5px;
       }
     }
   }
 }

.representativeCircle_box_buttom {
 width: 100%;
 height: 35px;
 display: flex;
 align-items: center;
 justify-content: space-between;
 .representativeCircle_box_buttom_time {
   width: 70%;
   font-size: 14px;
   color: #a8a8a8;
 }
 .representativeCircle_box_buttom_cont {
   width: 25% !important;
   display: flex;
   align-items: center;
   justify-content: space-between;
   .representativeCircle_box_buttom_comment {
     display: flex;
     align-items: center;
     justify-content: space-between;
     >img {
       width: 16px;
       height: 16px;
       margin-right: 5px;
     }
   }
   .representativeCircle_box_buttom_like {
     // display: flex;
     // align-items: center;
     // justify-content: space-between;
     line-height: 100%;
     >img {
       width: 16px;
       height: 16px;
       margin-right: 5px;
     }
   }
 }
}
</style>

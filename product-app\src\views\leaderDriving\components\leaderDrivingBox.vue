<template>
  <div class="leaderDrivingBox">
    <div class="leaderDrivingBox_title" :style="`background: linear-gradient(to bottom, ${color ? color : '#fff'}, #fff)`"
      v-if="title">
      {{ title }}
      <div class="tab">
        <slot name="tab"></slot>
      </div>
    </div>
    <div class="leaderDrivingBox_content">
      <slot name="content"></slot>
    </div>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'leaderDrivingBox',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: ['title', 'color'],
  setup () {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user'))
    })
    onMounted(() => {
    })
    return { ...toRefs(data), general }
  }
}
</script>
<style lang="less" scoped>
.leaderDrivingBox {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  width: 100%;
  // padding: 5px;
  margin: 10px 0;
  box-shadow: 6px 10px 15px -3px rgba(0, 0, 0, 0.1);

  .leaderDrivingBox_title {
    width: 100%;
    font-weight: 700;
    font-size: 16px;
    position: relative;
    height: 50px;
    padding: 10px 10px 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .tab {
      width: 120px;
      height: 30px;
      font-size: 16px;
    }
  }

  .leaderDrivingBox_title::before {
    content: '';
    position: absolute;
    top: 19px;
    left: 6px;
    width: 3px;
    height: 2px;
    border-top: 5px solid #ffcc48;
    border-bottom: 7px solid #3894ff;
  }

  .leaderDrivingBox_content {
    width: 100%;
  }
}
</style>

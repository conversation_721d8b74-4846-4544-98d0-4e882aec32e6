
const RandomClapping = () => import('@/views/RandomClapping/RandomClapping')
const selectMap = () => import('@/views/RandomClapping/selectMap')
const RandomClappingUpload = () => import('@/views/RandomClapping/RandomClappingUpload')
const RandomClappingMy = () => import('@/views/RandomClapping/RandomClappingMy')
const RandomClappingDetails = () => import('@/views/RandomClapping/RandomClappingDetails')
// 管理
const homeAdministratorList = () => import('@/views/RandomClapping/homeAdministrator/homeAdministratorList')
const homeAdministratorDetails = () => import('@/views/RandomClapping/homeAdministrator/homeAdministratorDetails')
const homeAdministratorEdit = () => import('@/views/RandomClapping/homeAdministrator/homeAdministratorEdit')
const commentList = () => import('@/views/RandomClapping/homeAdministrator/commentList')
const commentEdit = () => import('@/views/RandomClapping/homeAdministrator/commentEdit')
const replyAdd = () => import('@/views/RandomClapping/homeAdministrator/replyAdd')
const turnToHold = () => import('@/views/RandomClapping/homeAdministrator/turnToHold')
const assign = () => import('@/views/RandomClapping/homeAdministrator/assign')
const openImg = () => import('@/components/openImg/openImg.vue')
const statistical = () => import('@/views/RandomClapping/statistical')
const seachTurnToHold = () => import('@/views/RandomClapping/components/seachTurnToHold')

const RandomClapp = [{
  path: '/RandomClapping',
  name: 'RandomClapping',
  component: RandomClapping,
  meta: {
    title: '人大代表随手拍',
    keepAlive: true
  }
}, {
  path: '/statistical',
  name: 'statistical',
  component: statistical,
  meta: {
    title: '统计分析',
    keepAlive: true
  }
}, {
  path: '/selectMap',
  name: 'selectMap',
  component: selectMap,
  meta: {
    title: '地图',
    keepAlive: true
  }
}, {
  path: '/openImg',
  name: 'openImg',
  component: openImg,
  meta: {
    title: '图片',
    keepAlive: true
  }
}, {
  path: '/RandomClappingUpload',
  name: 'RandomClappingUpload',
  component: RandomClappingUpload,
  meta: {
    title: '上传',
    keepAlive: true
  }
}, {
  path: '/RandomClappingMy',
  name: 'RandomClappingMy',
  component: RandomClappingMy,
  meta: {
    title: '我的',
    keepAlive: true
  }
}, {
  path: '/RandomClappingDetails',
  name: 'RandomClappingDetails',
  component: RandomClappingDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/homeAdministratorList',
  name: 'homeAdministratorList',
  component: homeAdministratorList,
  meta: {
    title: '管理',
    keepAlive: true
  }
}, {
  path: '/homeAdministratorDetails',
  name: 'homeAdministratorDetails',
  component: homeAdministratorDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/homeAdministratorEdit',
  name: 'homeAdministratorEdit',
  component: homeAdministratorEdit,
  meta: {
    title: '查看',
    keepAlive: true
  }
}, {
  path: '/commentList',
  name: 'commentList',
  component: commentList,
  meta: {
    title: '评论列表',
    keepAlive: true
  }
}, {
  path: '/commentEdit',
  name: 'commentEdit',
  component: commentEdit,
  meta: {
    title: '评论编辑',
    keepAlive: true
  }
}, {
  path: '/replyAdd',
  name: 'replyAdd',
  component: replyAdd,
  meta: {
    title: '回复',
    keepAlive: true
  }
}, {
  path: '/turnToHold',
  name: 'turnToHold',
  component: turnToHold,
  meta: {
    title: '转办',
    keepAlive: true
  }
}, {
  path: '/assign',
  name: 'assign',
  component: assign,
  meta: {
    title: '交办',
    keepAlive: true
  }
}, {
  path: '/seachTurnToHold',
  name: 'seachTurnToHold',
  component: seachTurnToHold,
  meta: {
    title: '市区选择',
    keepAlive: true
  }
}]
export default RandomClapp

<template>
  <div class="RandomClapping">
    <div class="RandomClapping-top">
      <p>人大代表随手拍</p>
      <div class="RandomClapping-upload" @click="uploadClick">
        <span>点我上传</span>
      </div>
      <div class="RandomClapping-statistical" @click="statisticalClick" v-if="statisticalShow">
        <span>统计分析</span>
      </div>
    </div>
    <div class="seach-box">
      <van-search v-model="keyword" placeholder="搜索关键词" @clear="onRefresh" @search="onRefresh">
      </van-search>
      <div class="seach_sele" @click="toCity"><span>{{ seachArea.text || '青岛市' }}</span><van-icon name="arrow-down" />
      </div>
      <div class="seach_seles" @click="show = !show"><span> 筛选
        </span><van-icon name="filter-o" size="20" /> </div>
    </div>
    <div class="RandomClapping-switch">
      <div class="switch-box">
        <div class="switch" v-for="item in switchData" :key="item.id"
          :class="switchAction == item.id ? 'switch-action' : ''" @click="switchClick(item.id)">{{ item.name }}</div>
      </div>
    </div>

    <!-- 列表区 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="300" @load="onLoad"
        :immediate-check="false">
        <div class="RandomClappingBox">
          <ul>
            <li class="RandomClappingBox-li" v-for="item in dataList" :key="item.id" @click="openDetails(item)">
              <div class="RandomClappingBox-li-top">
                <div class="RandomClappingBox-title">{{ item.title }}</div>
                <div :class="stateListClass[item.state]">
                  {{ stateList[item.state] }}
                </div>
              </div>
              <div class="RandomClappingBox-li-btm">
                <div class="RandomClappingBox-li-btm-left">
                  <img :src="setImg(item.userImage) || defImg" alt="">
                </div>
                <div class="RandomClappingBox-li-btm-right">
                  <div class="RandomClappingBox-li-btm-right-top">
                    <div class="RandomClappingBox-li-btm-right-top-name">{{ item.userName }}</div>
                    <div class="RandomClappingBox-li-btm-right-top-position">
                      <span v-if="item.congressStr.length">
                        {{ item.congressStr.join('|') + '人大代表' + (item.identity && item.congressStr.includes('青岛市') ?
                          '(' + item.identity + ')' : '') }}
                      </span>
                      <span v-else>
                        {{ item.identity || '用户' }}
                      </span>
                    </div>
                  </div>
                  <div class="RandomClappingBox-li-btm-right-btm">
                    <div class="RandomClappingBox-li-btm-right-btm-time">{{ item.messageDate }}</div>
                    <div class="like-and-leave-message">
                      <div class="like">
                        <van-icon name="good-job-o" :color="item.isLike ? '#1989fa' : ''"
                          @click.stop="downLike(item)" /> {{ item.likeCount }}
                      </div>
                      <div class="leave-message">
                        <van-icon name="comment-o" /> {{ item.comment }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <template v-if="item.replyListVos.length != 0 && switchAction == 2">
                <div class="RandomClappingBox-reply" v-for="(unit, index) in item.replyListVos" :key="index">
                  <div class="RandomClappingBox-reply-title">{{ unit.userName }} <span>回复</span> </div>
                  <div class="RandomClappingBox-reply-content">{{ unit.messageMessage }}</div>
                  <div class="imageBox">
                    <div class="details-image" v-for="item in unit.imageVo" :key="item.id">
                      <van-image width="2rem" height="2rem" fit="cover" @click.stop="ImagePreview(unit.imageVo)"
                        :src="setImg(item.fullUrl)" />
                    </div>
                  </div>
                  <div class="result RandomClappingBox-reply-title" v-if="unit.standbyOne">
                    评价结果: <span>{{ unit.standbyOne == '1' ? '满意' : unit.standbyOne == '2' ? '基本满意' : '不满意' }}</span>
                  </div>
                </div>
              </template>
            </li>
          </ul>
        </div>
      </van-list>
    </van-pull-refresh>

    <div class="RandomClapping-my" @click="newClick" v-if="!adminShow">{{ myText }}</div>
    <div class="RandomClapping-my" @click="administration" v-else>{{ myText }}</div>
    <!-- <van-action-sheet v-model:show="show"
                      :actions="stateLists"
                      @select="onSelect" /> -->
    <van-action-sheet style="height: 80%;" v-model:show="show" title="筛选">
      <div class="handleContent">
        <SearchCriteria v-if="show" @callback="callbackscreen"></SearchCriteria>
      </div>
    </van-action-sheet>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Image as VanImage, ActionSheet } from 'vant'
import { useStore } from 'vuex'
import SearchCriteria from './components/searchCriteria.vue'
export default ({
  name: 'RandomClapping',
  props: {},
  components: {
    SearchCriteria,
    [VanImage.name]: VanImage,
    [ActionSheet.name]: ActionSheet
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      module: route.query.module,
      searchShow: false,
      defImg: require('../../assets/img/icon_def_head_img.png'),
      switchData: [
        {
          id: 1,
          name: '最新发布'
        },
        {
          id: 2,
          name: '最新回复'
        }
      ],
      stateList: {
        1: '待办理',
        2: '已转办',
        3: '已办结',
        4: '退回',
        5: '二次交办',
        6: '已评价',
        11: '正在办理'
      },
      stateListClass: {
        1: 'RandomClappingBox-state2',
        2: 'RandomClappingBox-state3',
        3: 'RandomClappingBox-state',
        4: 'RandomClappingBox-state',
        5: 'RandomClappingBox-state3',
        6: 'RandomClappingBox-state',
        11: 'RandomClappingBox-state3'
      },
      show: false,
      seachStatus: '',
      seachStatusTiele: '状态',
      stateLists: [
        { name: '待办理', value: '1' },
        { name: '已转办', value: '2' },
        { name: '已办结', value: '3' },
        { name: '退回', value: '4' },
        { name: '二次交办', value: '5' },
        { name: '已评价', value: '6' },
        { name: '取消', value: '' }
      ],
      seachCity: '青岛市',
      switchAction: sessionStorage.getItem('switchAction') || 1,
      keyword: '',
      loading: false,
      finished: false,
      user: JSON.parse(sessionStorage.getItem('user')) || '',
      areaId: sessionStorage.getItem('areaId') || '',
      refreshing: false,
      pageNo: 1,
      pageSize: 5,
      total: 0,
      dataList: [],
      userName: '',
      roleList: [],
      adminShow: false,
      toCityShow: false,
      seachArea: {},
      myText: '',
      statisticalShow: false
    })
    onMounted(async () => {
      console.log('进入h5页面')
      const token = sessionStorage.getItem('Sys_token') || sessionStorage.getItem('token')
      if (!token) {
        setTimeout(() => {
          sessionStorage.setItem('Sys_token', JSON.stringify(api.getPrefs({ sync: true, key: 'Sys_token' }))) // eslint-disable-line
          sessionStorage.setItem('areaId', JSON.stringify(api.getPrefs({ sync: true, key: 'SYS_SiteID' }))) // eslint-disable-line
          api.setInterfaceStyle({ style: 'light' }) // eslint-disable-line
          const user = {
            userName: api.getPrefs({ sync: true, key: 'Sys_UserName' }), // eslint-disable-line
            position: api.getPrefs({ sync: true, key: 'Sys_Position' }), // eslint-disable-line
            headImg: window.localStorage.getItem('Sys_AppPhoto')
          }
          // data.userName = user.userName
          sessionStorage.setItem('user', JSON.stringify(user))
        }, 300)
      } else {
        const res = await $api.general.changearea()
        var {
          data: {
            token: tokens,
            user,
            menus,
            areas
          }
        } = res
        sessionStorage.setItem('menus', JSON.stringify(menus))
        sessionStorage.setItem('token', JSON.stringify(tokens))
        sessionStorage.setItem('Sys_token', JSON.stringify(tokens))
        sessionStorage.setItem('user', JSON.stringify(user))
        sessionStorage.setItem('areas', JSON.stringify(areas))
        sessionStorage.setItem('areaId', user.areaId)
        data.seachArea = JSON.parse(sessionStorage.getItem('seachArea')) || {}
        findRolesByUserId()
        onRefresh()
      }
    })
    const setImg = (url) => {
      if (url && !window.location.origin.includes('localhost') && !window.location.origin.includes('http://**************')) {
        return window.location.origin + '/lzt' + url.split('lzt')[1]
      } else {
        return url
      }
    }
    const findRolesByUserId = async () => {
      const { data: roleList } = await $api.general.findRolesByUserId()
      sessionStorage.setItem('roleList', JSON.stringify(roleList))
      data.roleList = roleList
      console.log('roleList===>', roleList.includes('随手拍统计分析查看'))
      if (roleList.includes('随手拍统计分析查看')) data.statisticalShow = true
      else data.statisticalShow = false
      if (roleList.includes('代表之家管理员') || roleList.includes('青岛市代表之家管理员') || roleList.includes('区级代表之家管理员')) {
        data.adminShow = true
        data.myText = '管理'
      } else {
        data.myText = '我的'
      }
    }
    const getList = async () => {
      var searchList = store.state.searchList
      console.log(searchList)
      var param = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        selectStatus: data.switchAction,
        stateQuery: searchList.seachStatus,
        messageType: searchList.typeStatus,
        areaQuery: ''
      }
      if (Object.keys(data.seachArea).length) {
        console.log(data.seachArea)
        if (data.seachArea.id) {
          param.areaQuery = data.seachArea.id.length === 6 ? data.seachArea.id + 'Key' : data.seachArea.standbyFour
        } else {
          param.areaQuery = data.seachArea.areaId
        }
      } else {
        param.areaQuery = '370200'
      }
      if (param.areaQuery === '370200') {
        param.areaQuery = ''
        param.isDefArea = '1'
      } else {
        param.isDefArea = '0'
      }
      const { data: List, total } = await $api.RandomClapping.representativemessageList(param)
      data.dataList = data.dataList.concat(List)
      data.dataList.forEach(item => {
        item.imageVo.forEach(e => {
          e.fullUrl = setImg(e.fullUrl)
        })
      })
      // data.dataList = yiguanzhuLists
      data.total = total
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
        // if ()
      }
    }
    const switchClick = (id) => {
      sessionStorage.setItem('switchAction', id)
      data.switchAction = sessionStorage.getItem('switchAction')
      onRefresh()
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 打开详情
    const openDetails = (item) => {
      store.commit('eliminate', false)
      // sessionStorage.setItem('pageNo', data.pageNo)
      router.push({ name: 'RandomClappingDetails', query: { id: item.id } })
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? 'representativelike/addLikeApp' : 'representativelike/cancelLike'
      var params = {
        messageId: _id,
        userId: data.user.id,
        areaId: data.areaId
      }
      await $api.general.fabulous({ url, params })
      // onRefresh()
    }
    const downLike = (item) => {
      console.log(item)
      item.isLike = !item.isLike
      if (item.isLike) {
        item.likeCount++
        fabulousInfo(item.isLike, item.id)
      } else {
        item.likeCount--
        fabulousInfo(item.isLike, item.id)
      }
    }
    const uploadClick = () => {
      store.commit('eliminate', true)
      router.push({ name: 'RandomClappingUpload' })
    }
    const statisticalClick = () => {
      router.push({ name: 'statistical' })
    }

    const newClick = () => {
      store.commit('eliminate', true)
      router.push({ name: 'RandomClappingMy' })
    }
    const administration = () => {
      store.commit('eliminate', true)
      router.push({ path: '/homeAdministratorList', query: { status: data.roleList.includes('青岛市代表之家管理员') || data.roleList.includes('区级代表之家管理员') ? '1' : '0' } })
    }
    const ImagePreview = (e) => {
      store.commit('eliminate', false)
      router.push({ path: '/openImg', query: { url: e.map(e => e.fullUrl).join(',') } })
    }
    const toCity = () => {
      router.push({ path: '/seachTurnToHold' })
    }
    const onSelect = (e) => {
      data.show = false
      data.seachStatus = e.value
      data.seachStatusTiele = e.name === '取消' ? '状态' : e.name
      onRefresh()
    }
    const callbackscreen = (e) => {
      data.show = e
      onRefresh()
    }
    return { ...toRefs(data), dayjs, route, callbackscreen, router, onSelect, $api, toCity, setImg, administration, switchClick, onRefresh, uploadClick, newClick, openDetails, onLoad, downLike, ImagePreview, statisticalClick }
  }
})
</script>
<style lang='less'>
.RandomClapping {
  width: 100%;
  background: #f0f6fb;
  overflow: hidden;

  // .seach-box {
  //   width: 100%;
  // }

  .imageBox {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .details-image {
      margin-right: 10px;
    }
  }

  // @font-face {
  //   font-family: "PingFangSC-Semibold";
  //   src: url("../../assets/font/PingFang-SC-Semibold.otf");
  // }

  // @font-face {
  //   font-family: "PingFangSC-Medium";
  //   src: url("../../assets/font/PingFang Medium_downcc.otf");
  // }

  .RandomClapping-top {
    width: 100%;
    height: 235px;
    background: url("../../assets/img/RandomClapping/001.png") no-repeat;
    background-size: 100% 100%;
    position: relative;

    >p {
      position: absolute;
      top: 30px;
      left: 50%;
      transform: translate(-50%, 0);
      color: #fff;
      font-weight: 600;
      font-size: 26px;
    }

    .RandomClapping-upload {
      position: absolute;
      top: 50%;
      left: 20px;
      width: 139px;
      height: 43px;
      background: #2c87c7;
      border-radius: 22px;
      // opacity: 0.22;
      text-align: center;
      line-height: 43px;

      span {
        font-size: 23px;
        font-weight: 600;
        font-family: PingFangSC-Semibold;
        color: #0d0d0d;
        // text-shadow: 0px 2px 4px rgba(20, 60, 141, 0.4);
        background: linear-gradient(0deg, #abfafb 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .RandomClapping-statistical {
      position: absolute;
      top: 70%;
      left: 20px;
      width: 139px;
      height: 43px;
      background: #2c87c7;
      border-radius: 22px;
      // opacity: 0.22;
      text-align: center;
      line-height: 43px;

      span {
        font-size: 23px;
        font-weight: 600;
        font-family: PingFangSC-Semibold;
        color: #0d0d0d;
        // text-shadow: 0px 2px 4px rgba(20, 60, 141, 0.4);
        background: linear-gradient(0deg, #abfafb 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .RandomClapping-switch {
    background: #fff;
    display: flex;
    // justify-content: ;
    align-items: center;
    padding: 12px 0px 12px 12px;
  }

  .switch-box {
    display: flex;
    align-items: center;

    .switch {
      width: 98px;
      height: 34px;
      text-align: center;
      line-height: 34px;
      background: #ffffff;
      box-shadow: 0px 0px 4px 0px rgba(180, 181, 182, 0.4);
      border-radius: 7px;
      color: #666;
      margin-right: 10px;
    }
  }

  .switch-action {
    width: 98px;
    height: 34px;
    background: #3894ff !important;
    box-shadow: 0px 4px 5px 0px rgba(56, 148, 255, 0.4) !important;
    border-radius: 7px;
    text-align: center;
    line-height: 34px;
    font-size: 17px;
    font-weight: 600;
    color: #fff !important;
  }

  .seach-box {
    width: 100%;
    background: #fff;
    padding: 12px 12px 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .seach_sele {
      height: 35px;
      width: 100px;
      background: #f7f8fa;
      border-radius: 30px;
      box-sizing: border-box;
      padding: 10px;
      font-size: 14px;
      display: flex;
      color: #adadad;
      align-items: center;
      justify-content: space-between;

      >span {
        width: 80px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .seach_seles {
      height: 35px;
      width: 70px;
      background: #f7f8fa;
      border-radius: 30px;
      box-sizing: border-box;
      padding: 10px;
      font-size: 14px;
      display: flex;
      color: #adadad;
      align-items: center;
      justify-content: space-between;

      >span {
        width: 80px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .seach_sele:nth-child(1) {
      margin: 0 5px;
    }

    .van-search {
      width: 160px;
      padding: 0px !important;

      .van-search__content {
        border-radius: 17px;
      }

      .van-field__control {
        font-size: 14px;
      }

      .van-field__left-icon {
        margin-right: 2px !important;
      }
    }
  }

  .RandomClapping-my {
    position: fixed;
    left: 50%;
    bottom: 5%;
    transform: translateX(-50%);
    width: 56px;
    height: 56px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    text-align: center;
    color: #ffffff;
  }

  .RandomClappingBox {
    background: #fff;

    .RandomClappingBox-li {
      padding: 12px;
      border-bottom: 1px solid #e8e8e8;

      .RandomClappingBox-li-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .RandomClappingBox-title {
          width: 70%;
          font-size: 17px;
          color: #333333;
          font-weight: 700;
          font-family: PingFangSC-Medium;
        }

        .RandomClappingBox-state {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 24px;
          background: linear-gradient(0deg, #3894ff, #38c3ff);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }

        .RandomClappingBox-state2 {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 24px;
          background: linear-gradient(0deg, #f6a531, #fddf2f);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }

        .RandomClappingBox-state3 {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 24px;
          background: linear-gradient(0deg, #1fb64d, #00c74d);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }
      }

      .RandomClappingBox-li-btm {
        display: flex;
        align-items: center;
        margin-top: 8px;
        margin-bottom: 8px;
        height: 80px;

        .RandomClappingBox-li-btm-left {
          img {
            width: 45px;
            height: 55px;
            border-radius: 5px;
            overflow: hidden;
            margin: 0 5px;
          }
        }

        .RandomClappingBox-li-btm-right {
          margin-left: 3px;
          width: 100%;
          height: 100%;

          .RandomClappingBox-li-btm-right-top {
            // display: flex;
            align-items: center;
            margin-bottom: 10px;
            margin-top: 5px;

            .RandomClappingBox-li-btm-right-top-name {
              font-size: 16px;
              color: #7e7e7e;
              margin-right: 10px;
            }

            .RandomClappingBox-li-btm-right-top-position {
              font-size: 13px;
              // max-width: 200px;
              display: inline-block;
              color: #3291ff;
              background: #e9f3ff;
              padding: 3px 3px;
              margin-top: 5px;
              margin-right: 40px;
              border-radius: 2px;
              // white-space: nowrap;
              // overflow: hidden;
              // text-overflow: ellipsis;

              >span {
                // white-space: nowrap;
                // /* 防止文字换行 */
                // overflow: hidden;
                // /* 溢出隐藏 */
                // display: inline-block;
                // /* 行内块元素 */
                // text-overflow: ellipsis;
                /* 文字溢出显示省略号 */
              }
            }
          }

          .RandomClappingBox-li-btm-right-btm {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .RandomClappingBox-li-btm-right-btm-time {
              font-size: 14px;
              color: #7e7e7e;
            }

            .like-and-leave-message {
              display: flex;
              align-items: center;

              .like {
                font-size: 14px;
                display: flex;
                align-items: center;
                color: #7e7e7e;

                .van-icon {
                  font-size: 16px;
                }
              }

              .leave-message {
                margin-left: 10px;
                font-size: 14px;
                display: flex;
                align-items: center;
                color: #7e7e7e;

                .van-icon {
                  font-size: 16px;
                  margin-top: 2px;
                }
              }
            }
          }
        }
      }

      .RandomClappingBox-reply {
        width: 100%;
        padding: 8px 12px;
        background: #f4f6f8;
        border-radius: 4px;

        .RandomClappingBox-reply-title {
          font-weight: 600;
          font-size: 15px;
          color: #000000;
          font-family: PingFangSC-Medium;
          margin-bottom: 5px;

          span {
            font-weight: 500;
            font-size: 15px;
            color: #666;
          }
        }

        .RandomClappingBox-reply-content {
          font-weight: 500;
          font-size: 17px;
          color: #333333;
          line-height: 28px;
          font-family: PingFangSC-Medium;
        }
      }
    }
  }
}
</style>

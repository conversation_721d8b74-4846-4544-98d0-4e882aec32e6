<template>
  <div class="newsDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="play">
        <!-- <template #right>{{playText}}
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_header_box">
      <div class="n_details_title"
           :style="'font-size:20px; '"
           v-html="title"
          > </div>
      <div class="n_details_more_box flex_box">
        <div class=" flex_box flex_align_center flex_justify-content_end" >
          <div class="n_details_name"
               :style="'font-size:12px;'+'color:'+appTheme+';'">{{politicalData.source}}</div>
          <div class="n_details_time" >{{ politicalData.publishDate? dayjs(politicalData.publishDate).format('YYYY-MM-DD') : '' }}</div>
        </div>
      </div>
      <div class="n_details_content" v-html="contents"></div>
      <!-- <div class="n_details_more_box">
        <div v-if="browerCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">{{browerCount.hint}}{{browerCount.value}}</div>
        <div v-if="shareCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">分享：{{shareCount.value}}</div>
        <div v-if="commentCount.show"
             class="n_details_item"
             :style="'font-size:12px;'">评论：{{commentCount.value}}</div>
        <div v-if="dataTime.show"
             class="n_details_time"
             :style="'font-size:12px;'">{{dataTime.value}}</div>
        <div style="clear: both;"></div>
      </div> -->
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, computed, watch } from 'vue'
import { NavBar, Sticky, ImagePreview, Image as VanImage, Dialog } from 'vant'
import { useStore } from 'vuex'
export default {
  name: 'newsDetails',
  components: {
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const store = useStore()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      isFollow: route.query.isFollow || '',
      publishBy: route.query.publishBy || '',
      pageType: route.query.type || '',
      userName: route.query.userName || '',
      contents: route.query.content || '',
      createDate: route.query.createDate || '',
      details: {},
      type: 25,
      refreshing: false,
      show: false,
      browerCount: { show: false, value: '0', hint: '阅读：' }, // 阅读数
      shareCount: { show: false, value: '0' }, // 分享数
      commentCount: { show: false, value: '0' }, // 评论数
      dataTime: { show: false, value: '' }, // 时间
      IBSName: { show: false, value: '' }, // 资讯类型
      source: '', // 来源
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] }, // 附件对象
      playText: '播放',
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      commentList: null,
      inputBox: null,
      commentObj: {},
      representativeDetails: {},
      politicalData: {},
      twoInstitutesData: {},
      communityData: {},
      representativeData: {},
      countrysideData: {},
      numberAppData: {},
      surveyComment: [], // 意见征集评论列表
      surveyDetails: {}, // 意见征集详情
      attentionStatus: false,
      conmmentList: []
    })
    const getShow = computed(() => {
      // 返回的是ref对象
      return store.state.speechShow
    })
    const getStatus = computed(() => {
      // 返回的是ref对象
      return store.state.speechStauts
    })
    watch(getShow, (newName, oldName) => {
      data.show = newName
      if (!data.show) {
        data.playText = '播放'
      }
    })
    watch(getStatus, (newName, oldName) => {
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '继续'
        } else {
          data.playText = '暂停'
        }
      }
    })
    const play = () => {
      if (data.playText === '播放') {
        data.playText = '暂停'
        store.commit('setSpeechShow', true)
      } else {
        store.commit('setStatus', !store.state.speechStauts)
      }
    }
    onMounted(() => {
      browseSave()
      newsInfo()
      if (data.pageType === 'resumption') {
        data.inputBox.showComment = false
        data.inputBox.showLike = false
      }
      console.log(route)
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '暂停'
        } else {
          data.playText = '继续'
        }
      }
    })
    const onRefresh = () => {
      newsInfo()
    }
    const browseSave = async () => {
      await $api.general.saveBrowse({
        keyId: data.id,
        type: data.type
      })
    }
    const getDetails = async () => {
      const res = await $api.news.getAnnouncement({ id: data.id })
      data.politicalData = res.data
    }
    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    const newsInfo = async () => {
      getDetails()
    }
    const addCommentEvent = (value) => {
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      data.inputBox.getStats()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), dayjs, previewCalback, onRefresh, onClickLeft, play, addCommentEvent, openInputBoxEvent, freshState, annexClick }
  }
}
</script>
<style lang="less">
.newsDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .representativeCircle_box_del {
        position: absolute;
        top: 0;
        right: 10px;
     }
  .n_details_survey {
    width: 100%;
    .n_details_survey_bg {
      width: 100%;
      height: 350px;
      background: #000;
      background-size: 100% 100% !important;
      overflow: hidden;
      position: relative;
      .n_details_survey_bgooo {
        overflow: hidden;
        width: 100%;
        height: 100%;
        background: #0000005b;
      }
      .n_details_survey_content {
        width: 100%;
        height: 30%;
        background: #fff;
        border-radius: 20px 20px 0 0 ;
        position: absolute;
        bottom: 0;
        padding: 10px;
        box-sizing: border-box;
      }
      .n_details_survey_top {
        width: 100%;
        height: 30px;
        color: #a8a8a8;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .n_details_survey_top_time {
          font-size: 14px;
        }
        .n_details_survey_top_text {
          font-size: 14px;
        }
      }
      .n_details_survey_title {
        width: 100%;
        height: 30px;
        font-size: 20px;
        font-weight: 700;
        color: #fff;
        margin: 40px 10px 0;
      }
    }
  }
  .n_details_header_box {
    width: 100%;
    padding: 20px 10px 15px 10px;
    box-sizing: border-box;
    position: relative;
  }
  .n_details_content {
    img {
      width: 100%;
    }
    >p {
      margin: 15px 0;
      font-size: 16px !important;
      line-height: 28px !important;
      span {
        font-size: 16px !important;
        line-height: 28px !important;
      }
    }
  }
  .n_details_title {
    font-weight: bold;
    line-height: 1.5;
  }
  .n_details_more_box {
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }
  .n_details_more_box >.flex_box {
    width: 40%;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    text-align: right !important;
    align-items: right;
  }
  .n_details_time {
    font-size: 12px;
    color: #666;
    width: 100%;
    margin-top: 10px;
  }
  .n_details_name {
    color: #666;
    width: 100%;
  }
  .n_details_item {
    color: #666;
    float: left;
    margin-right: 10px;
  }
  .n_details_time {
    color: #666;
    float: right;
  }
  .n_details_nextImg {
  }
      .representativeCircle_box_li {
        width: 100%;
        padding-bottom: 5px;
        border-bottom: 1px solid #e5e5e5;
        .representativeCircle_box_top {
          width: 100%;
          height: 35px;
          margin: 5px 0;
          display: flex;
          align-items: center;
          position: relative;
          .attention {
            text-align: center;
            position: absolute;
            top: 0;
            right: 10px;
            width: 80px;
            height: 80%;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 40px;
            color: #3894ff;
            border: 1px solid #3894ff;
          }
          .attentionDel {
            color: #666;
            border: 1px solid #666;
          }
          .representativeCircle_box_top_headImg {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin: 5px;
          }
          .representativeCircle_box_name {
            font-size: 16px;
            .representativeCircle_box_congressStr {
              font-size: 14px;
              color: #4c4c4c;
            }
          }
        }
        .representativeCircle_box_center {
          box-sizing: border-box;
          .representativeCircle_box_center_content {
            padding-left: 13px;
            margin: 5px 0;
          }
          .representativeCircle_box_center_attachmentList {
            width: 95%;
            margin: auto;
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            .van-image {
              margin: 5px;
            }
          }
        }
      }
    }
    .representativeCircle_box_buttom {
      width: 100%;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .representativeCircle_box_buttom_time {
        width: 70%;
        font-size: 14px;
        padding-left: 10px;
        color: #a8a8a8;
      }
      .representativeCircle_box_buttom_cont {
        width: 25% !important;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .representativeCircle_box_buttom_conmment {
          display: flex;
          align-items: center;
          justify-content: space-between;
          >span {
            font-size: 14px;
            margin-right: 10px;
          }
          >img {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }
        .representativeCircle_box_buttom_link {
          // display: flex;
          // align-items: center;
          // justify-content: space-between;
          line-height: 100%;
          padding-right: 10px;
          >span {
            margin-right: 10px;
            font-size: 14px;
          }
          >img {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }
      }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    padding-top: 0;
    img {
      margin: 15px 0;
      width: 100% !important;
    }
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
    word-break: normal !important;
    text-align: justify;
  }
}
.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>

import { HTTP, loginUc } from '../http.js'
class general extends HTTP {
  general (url, params) {
    return this.request({ url: url, data: params })
  }

  // 获取背景图
  findByAreas (params) {
    return this.request({ url: '/appimage/findByAreas', data: params })
  }

  // 获取个人详情信息
  memberLook (params) {
    return this.request({ url: '/member/look', data: params })
  }

  // 更换头像
  fileMyHeadimg (params) {
    return this.request({ url: '/file/myHeadimg', data: params })
  }

  // 电子工作证
  QrCodeInfo (params) {
    return this.request({ url: '/duty/QrCodeInfo', data: params })
  }

  // 收藏列表
  favoriteList (params) {
    return this.request({ url: '/favorite/list', data: params })
  }

  // 回显个人信息
  existsinfo (params) {
    return this.request({ url: '/useraudit/existsinfo', data: params })
  }

  // 提交个人信息
  userauditAdd (params) {
    return this.request({ url: '/useraudit/add', data: params })
  }

  // 获取首页基层代表联络站
  findWygzsWorkDynamic (params) {
    return this.request({ url: '/app/wygzsApp/findWygzsWorkDynamic', data: params })
  }

  // 获取公开的配置管理
  getPersonal (params) {
    return this.request({ url: 'http://180.101.232.130/oarest9V7-cs/rest/oagxh/personal_getdetail_v7', data: 'params={}' })
  }

  systemfeedbackAdd (params) {
    return this.request({ url: '/systemfeedback/add', data: params })
  }

  systemfeedbackList (params) {
    return this.request({ url: '/systemfeedback/list', data: params })
  }

  systemfeedbackInfo (params) {
    return this.request({ url: `/systemfeedback/info/${params}` })
  }

  // 获取公开的配置管理
  nologin (params) {
    return this.request({ url: '/readonfig/nologin', data: params })
  }

  authenticationLogin (params) {
    return this.request({ url: 'shandongAccess/authenticationLogin?code=' + params.code })
  }

  // 登录
  calogin (params) {
    return this.request({ url: `${loginUc}/calogin`, data: params })
  }

  // 用户中心登录
  loginUc (params) {
    return this.request({ url: `${loginUc}/login?`, data: params })
  }

  // 切换系统登录
  changearea (params) {
    return this.request({ url: '/changearea', data: params })
  }

  ddingGetUserToken (params) {
    return this.request({ url: '/dding/getUserToken', data: params })
  }

  // 查询树列表
  treelist (params) {
    return this.request({
      url: '/tree/list',
      data: params
    })
  }

  // 获取菜单模块
  appList (params) {
    return this.request({ url: '/module/appList', data: params })
  }

  // 获取公告栏
  msgboxList (params) {
    return this.request({ url: '/msgbox/list?', data: params })
  }

  // 获取全文检索
  searchData (params) {
    return this.request({
      url: '/datacenter/common/searchData?page=' + params.page + '&pageSize=' + params.pageSize + '&index=' + params.index + '&title=' + params.title,
      data: params,
      method: 'GET'
    })
  }

  // 全文检索详情
  searchDataDetail (params) {
    return this.request({ url: '/datacenter/common/searchDataDetail', data: params.data })
  }

  // 去红点
  updateState (params) {
    return this.request({ url: 'msgbox/updateState?', data: params })
  }

  // 去红点
  saveBrowseQingdao (params) {
    return this.request({ url: 'browse/saveBrowseQingdao', data: params })
  }

  // 履职圈红点
  committeesayUnread (params) {
    return this.request({ url: 'committeesay/unread', data: params })
  }

  // 分级红点
  browseNotCount (params) {
    return this.request({ url: 'browse/notCount', data: params })
  }

  // 会议活动红点
  getConferenceAgentNumbe (params) {
    return this.request({ url: 'conferenceAgent/getConferenceAgentNumber', data: params })
  }

  // 获取地区id
  Location (url) {
    return this.request({ url })
  }

  // 获取天气预报
  weather (url) {
    return this.request({ url })
  }

  // 获取代表履职圈
  representativeCircle (params) {
    return this.request({ url: 'committeesay/app/list', data: params })
  }

  // 点赞取消
  fabulous ({ url, params }) {
    return this.request({ url, data: params })
  }

  // 获取工作专题
  specialsubjectinfo (params) {
    return this.request({ url: 'specialsubjectinfo/details?', data: params })
  }

  // 获取两院咨询
  zySpecialsubjectRelateinfo (params) {
    return this.request({ url: 'zySpecialsubjectRelateinfo/list', data: params })
  }

  // 获取青岛社区民意厅
  zySpecialsubjectColumn (params) {
    return this.request({ url: 'zySpecialsubjectColumn/list', data: params })
  }

  // 获取代表风采
  representative (params) {
    return this.request({ url: 'zySpecialsubjectColumn/list', data: params })
  }

  // 获取乡村振兴数据
  countryside (params) {
    return this.request({ url: 'zySpecialsubjectColumn/list', data: params })
  }

  // 获取数字应用
  applicationplatform (params) {
    return this.request({ url: 'zySpecialsubjectColumn/list', data: params })
  }

  // 获取政情快递
  highlights (params) {
    return this.request({ url: 'zySpecialsubjectRelateinfo/list', data: params })
  }

  // work/station/manage/list
  station (params) {
    return this.request({ url: '/app/wygzsApp/findWygzsWorkDynamic', data: params })
  }

  stations (params) {
    return this.request({ url: '/app/wygzsApp/findWygzsWorkDynamicInfo?id=' + params.id })
  }

  // 获取字典
  pubkvs (params) {
    return this.request({ url: '/dictionary/pubkvs?', data: params })
  }

  // 获取当前届次
  crrentcircles (params) {
    return this.request({ url: '/member/crrentcircles?', data: params })
  }

  // 选人点获取机构
  pointrees (params) {
    return this.request({ url: `/pointrees/${params}` })
  }

  // 选人点根据机构获取用户
  users (params) {
    return this.request({ url: '/pointree/users', data: params })
  }

  // 选人点根据用户id获取用户
  poinexistsids (params) {
    return this.request({ url: `/poinexistsids/${params}` })
  }

  // 获取办理单位
  chooseList (params) {
    return this.request({ url: '/flowgroup/chooseList', data: params })
  }

  // 获取背景图片
  getbgimg (params) {
    return this.request({ url: '/library/user/mainpage', data: params })
  }

  // 获取系统背景图片
  getsysbgimg (params) {
    return this.request({ url: '/appimage/list', data: params })
  }

  // 上传笔记
  uploadFile (params) {
    return this.request({ url: '/attachment/uploadFile', data: params })
  }

  // 添加笔记
  add (params) {
    return this.request({ url: '/syReadingNotes/add', data: params })
  }

  // 是否公开
  delsOpen (params) {
    return this.request({ url: '/syReadingNotes/delsOpen', data: params })
  }

  // 附件在线查看
  fcscloudFile (params) {
    return this.request({ url: 'https://www.yozodcs.com/fcscloud/file/http?', data: params })
  }

  // 附件在线查看
  fcscloudCompositeConvert (params) {
    return this.request({ url: 'https://www.yozodcs.com/fcscloud/composite/convert', data: params })
  }

  // 增加阅读数
  saveBrowse (params) {
    return this.request({ url: '/browse/save', data: params })
  }

  // 获取评论、点赞等5种数据
  getCommentStats (params) {
    return this.request({ url: '/comment/stats', data: params })
  }

  // 点赞
  saveFabulous (params) {
    return this.request({ url: '/fabulous/save', data: params })
  }

  // 取消点赞
  delFabulous (params) {
    return this.request({ url: '/fabulous/del', data: params })
  }

  // 收藏
  addFavorite (params) {
    return this.request({ url: '/favorite/add', data: params })
  }

  // 取消收藏
  delFavorite (params) {
    return this.request({ url: '/favorite/del', data: params })
  }

  // 新增分享
  saveShare (params) {
    return this.request({ url: '/share/save', data: params })
  }

  // 新增评论
  saveComment (params) {
    return this.request({ url: '/comment/save', data: params })
  }

  // 评论列表
  getCommentList (params) {
    return this.request({ url: '/comment/list', data: params })
  }

  // 获取点赞列表
  getFabulousList (params) {
    return this.request({ url: '/fabulous/list', data: params })
  }

  // 删除评论
  delsComment (params) {
    return this.request({ url: '/comment/dels', data: params })
  }

  // 获取用户角色
  findRolesByUserId (params) {
    return this.request({ url: '/role/findRolesByUserId', data: params })
  }
}
export {
  general
}

<template>
  <div class="keyWorkDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
      </div>
    </van-sticky>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <li class="vue_newslist_li flex_box">
        <div class="flex_placeholder vue_newslist_warp T-flexbox-vertical">
          <div class="vue_newslist_title"
               :style="$general.loadConfiguration(2)">{{details.title}}</div>
          <div class="T-flex-item"></div>
          <div class="">
            <div class="n_details_item"
                 :style="$general.loadConfiguration(-3)">{{details.org}}&nbsp;&nbsp;{{details.dataTime}}</div>
            <div style="height:20px;"></div>
            <div v-if="details.expectCompleteDate"
                 class="info-item">
              <div class="info-item-name">预计完成时间</div>
              <div class="info-item-value">{{details.expectCompleteDate}}</div>
            </div>
            <div v-if="details.completeDate"
                 class="info-item">
              <div class="info-item-name">实际完成时间</div>
              <div class="info-item-value">{{details.completeDate}}</div>
            </div>
            <div v-if="details.responsibilityUser"
                 class="info-item">
              <div class="info-item-name">负责领导</div>
              <div class="info-item-value">{{details.responsibilityUserName}}</div>
            </div>
            <div v-if="details.responsibilityOrg"
                 class="info-item">
              <div class="info-item-name">责任部门</div>
              <div class="info-item-value">{{details.responsibilityOrgName}}</div>
            </div>
            <div v-if="details.assistingOrg"
                 class="info-item">
              <div class="info-item-name">协办部门</div>
              <div class="info-item-value">{{details.assistingOrgName}}</div>
            </div>
            <div style="clear: both;"></div>
          </div>
        </div>
      </li>
      <div class="item_name"
           v-if="details.otherMemberJoiners && details.otherMemberJoiners.length!=0">其他反映人（委员）
        <div class="item_name_content flex_box T-flex-flow-row-wrap">
          <div v-for="(item,index) in details.otherMemberJoiners"
               :style="$general.loadConfiguration(-2)"
               :key="index">{{item.userName+(index==details.otherMemberJoiners.length-1?'':'，')}}</div>
        </div>
      </div>
      <div class="item_name"
           v-if="details.otherUserJoiners && details.otherUserJoiners.length!=0">其他反映人（非委员）
        <div class="item_name_content flex_box T-flex-flow-row-wrap">
          <div v-for="(item,index) in details.otherUserJoiners"
               :style="$general.loadConfiguration(-2)"
               :key="index">{{item.userName}}</div>
        </div>
      </div>
      <!-- <div class="listen_btn"
           @click="footerBtnClick(footerBtns[1])">{{footerBtns[1].name}}</div> -->
      <!-- <div class="item_name">正文</div> -->
      <div class="n_details_content"
           @click="setImgBigger"
           :style="$general.loadConfiguration()"
           v-html="details.content"></div>
      <!--展示附件-->
      <template v-if="attachInfo.data.length != 0">
        <div class="add_warp attach_warp flex_box">
          <div class="attach_name">{{attachInfo.name}}:</div>
          <div class="">
            <van-swipe-cell v-for="(nItem,nIndex) in attachInfo.data"
                            :key="nIndex"
                            class="flex_box">
              <van-cell :border="false"
                        :class="nItem.state==2?'cache':'cache'"
                        @click="annexClick(nItem)"
                        :title="nItem.name"
                        :title-style="'font-size:14px;'"></van-cell>
            </van-swipe-cell>
          </div>
        </div>
      </template>
      <!-- 跟踪情况-->
      <div class="flow-list-title"
           v-if="trackList.length">跟踪情况</div>
      <div class="flow-list"
           v-for="item in trackList"
           :key="item.id">
        <div class="info-item2">
          <div class="info-item-name2">跟踪时间</div>
          <div class="info-item-value2">{{item.date}}</div>
        </div>
        <div class="info-item2">
          <div class="info-item-name2">提交人</div>
          <div class="info-item-value2">{{item.userName}}</div>
        </div>
        <div class="info-item2">
          <div class="info-item-name2">完成度</div>
          <div class="info-item-value2">{{item.completedProgress||0}}%</div>
        </div>
        <div class="info-item2"
             style="margin-bottom: 10px;">
          <div class="info-item-name2">跟踪情况</div>
          <div class="info-item-value2"
               v-html="item.content"></div>
        </div>
      </div>
      <!-- 处理进程-->
      <div class="process_box_name flex_box"
           v-if="details.processParams && details.processParams.length != 0">
        <div class="process_box_name_line"></div>
        <div>处理流程</div>
      </div>
      <div class="process_box">
        <div class="process_item_box flex_box"
             v-for="(item,index) in details.processParams"
             :key="index">
          <div class="process_item_content_title">{{item.statusName}}</div>
          <div class="process_item_img">
            <img v-if="(item.pointFlagNot || details.processParams.length -1 == index) && details.statusName != '完结' && details.statusName != '留存'"
                 :src="process_on" />
            <img v-else
                 :src="item.pointFlag?process_on:process" />
            <div class="line_shu"
                 v-if="index!=details.processParams.length-1"></div>
          </div>
          <div class="process_item_content">
            <div class="process_item_content_name">
              <div v-if="item.handleUserName">处理人：{{item.handleUserName}}</div>
              <div v-if="item.approveType">处理结果：{{item.approveType}}</div>
              <div v-if="item.approveContent">处理意见：<br />{{item.approveContent}}</div>
            </div>
          </div>
          <div class="process_item_right">
            <div class="process_item_right_time"
                 v-if="item.approveTime">{{dayjs(item.approveTime).format('YYYY-MM-DD')}}</div>
          </div>
        </div>
      </div>
      <!-- <div class="keyWorkDetailsBox">
        <div class="noticeTitle">{{details.titile}}</div>
        <div class="noticeInfo">
          <div>{{details.publishDate}}</div>
          <div>{{details.org}}</div>
        </div>
        <div class="noticeContent"
             @click="setImgBigger"
             v-html="details.content"></div>
      </div>
      <div class="readUserBox"
           v-if="details.attachments && details.attachments.length != 0">
        <div class="number">附件：</div>
        <div class="readUser"
             style="line-height:30px;">
          <div v-for="(item,index) in details.attachments"
               @click="annexClick(item)"
               style="text-decoration:underline;color:#08c"
               :key="index">{{item.originalName}}</div>
        </div>
      </div> -->
    </van-pull-refresh>
  </div>
</template>
<script>
import { NavBar, Sticky, ImagePreview, Tag } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'keyWorkDetails',
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Tag.name]: Tag,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '详情',
      id: route.query.id,
      currentProcessStatus: route.query.currentProcessStatus || '',
      details: {},
      trackList: [],
      process_no: require('../../assets/img/process_no.png'),
      process_on: require('../../assets/img/process_on.png'),
      process: require('../../assets/img/process.png'),
      backgroundcolor: ['#FFE7DC', '#DCEBFF', '#EBF0F6'],
      textcolor: ['#FE7530', '#3088FE', '#9CA6B3'],
      refreshing: false,
      show: false,
      attachInfo: { name: '附件', data: [] } // 附件对象
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getInfo()
    })
    const onRefresh = () => {
      getInfo()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }

    // 列表请求
    const getInfo = async () => {
      getTrackList()
      const res = await $api.keyWork.keyWorkInfo(data.id)
      var { data: details } = res
      details.title = details.title || ''// 标题
      details.org = '重点工作'// 部门
      details.dataTime = dayjs(details.reportTime).format('YYYY-MM-DD HH:mm:ss') || ''// 时间
      details.content = $general.dealWithCon(details.content || '')// 内容
      data.details = details
      data.attachInfo.data = []
      var attachmentList = details.attachmentList || []// 附件
      if (attachmentList.length !== 0) {
        for (var k = 0; k < attachmentList.length; k++) {
          var nItemName = attachmentList[k].fileName
          var nItemPath = attachmentList[k].filePath
          data.attachInfo.data.push({
            url: nItemPath,
            state: 0,
            schedule: -1,
            name: nItemName
          })
        }
      }
    }
    const getTrackList = async () => {
      const res = await $api.keyWork.keyWorkTrackingList({
        pageNo: 1,
        pageSize: 999,
        workId: data.id
      })
      data.trackList = res.data
    }
    const getColor = (type) => {
      var color = data.backgroundcolor[0]
      switch (type) {
        case '完结':
          color = data.backgroundcolor[0]
          break
        case '待接收':
          color = data.backgroundcolor[1]
          break
        case '领导审批':
          color = data.backgroundcolor[1]
          break
        case '处长主任审核':
          color = data.backgroundcolor[0]
          break
        case '拟稿':
          color = data.backgroundcolor[2]
          break
      }
      return color
    }
    const getTextColor = (type) => {
      var color = data.textcolor[0]
      switch (type) {
        case '完结':
          color = data.textcolor[0]
          break
        case '待接收':
          color = data.textcolor[1]
          break
        case '领导审批':
          color = data.textcolor[1]
          break
        case '处长主任审核':
          color = data.textcolor[0]
          break
        case '拟稿':
          color = data.textcolor[2]
          break
      }
      return color
    }
    const setImgBigger = (e) => {
      if (e.target.nodeName === 'IMG') {
        var taga = document.querySelectorAll('.n_details_content img') // 返回一个标签对象数组
        var img = []
        var nowIndex = 0
        taga.forEach((element, index) => {
          if (element.src === e.target.currentSrc) {
            nowIndex = index
          }
          img.push(element.src)
        })
        ImagePreview(img, nowIndex)
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), onRefresh, onClickLeft, setImgBigger, annexClick, $general, dayjs, getColor, getTextColor }
  }
}
</script>
<style lang="less">
.keyWorkDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  // background: #f8f8f8;
  .vue_newslist_li:active {
    background: rgba(0, 0, 0, 0);
  }
  .info-item-name {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: bold;
    margin-bottom: 8px;
    color: #333333;
  }

  .info-item-value {
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
    margin-bottom: 13px;
  }

  .info-item-name2 {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    margin-bottom: 4px;
    color: #333333;
  }

  .info-item-value2 {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #333333;
    margin-bottom: 8px;
  }
  .flow-list-title {
    font-size: 17px;
    font-family: PingFang SC;
    font-weight: bold;
    color: #333333;
    margin-bottom: 15px;
    padding-left: 5px;
    border-left: 6px solid #5a9adb;
    margin-left: 20px;
  }

  .flow-list {
    padding: 20px;
  }

  .item-reportTypeView {
    padding: 2px 8px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: #fe7530;
    background-color: #ffe3d6;
    border-radius: 2px;
    margin-right: 5px;
  }

  .item-stateView {
    padding: 2px 8px;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: bold;
    color: #3088fe;
    background-color: #d6e7ff;
    border-radius: 2px;
    text-align: center;
  }
  /*内容的样式 处理内容的样式*/
  .n_details_content {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
  .n_details_content * {
    font-size: inherit;
    font-family: inherit;
  }

  .list_item {
    margin-top: 15px;
  }
  .attach_name {
    font-size: 14px;
    width: 40px;
    font-weight: 400;
    color: #999999;
    opacity: 1;
    margin-top: 10px;
  }
  .add_warp {
    padding: 10px 10px;
    background: #fff;
  }
  .vue_newslist_li {
    padding: 10px 12px;
    width: 100%;
    position: relative;
    box-sizing: border-box;
  }
  .vue_newslist_more {
    color: #888;
    float: left;
    padding: 20px 0;
    line-height: 1.5;
  }
  .vue_newslist_more_right {
    float: right;
    padding: 20px 0;
    line-height: 1.5;
  }
  .vue_newslist_title {
    font-weight: bold;
    margin-bottom: 5px;
  }
  .vue_newslist_li:after {
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    width: 200%;
    height: 1px;
    background-color: #dedede;
    transform: scale(0.5);
    -webkit-transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    box-sizing: border-box;
  }
  .vue_newslist_more + .vue_newslist_more {
    margin-left: 10px;
  }

  .list_item_box {
    width: 100%;
    left: 0;
    right: 0;
    margin: 10px auto 0;
    background: #ffffff;
  }
  .list_title {
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    opacity: 1;
    padding: 10px;
  }
  .bottom_box {
    padding: 10px;
    color: #999999;
    width: 100%;
  }
  .bottom_left {
    width: 70%;
  }
  .list_name {
    margin-right: 10px;
  }
  .bottom_right {
    width: 30%;
  }
  .bottom_right .van-tag {
    float: right;
  }
  .list_item {
    width: 100%;
  }
  .item_name {
    margin-left: 10px;
    margin-top: 10px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: #333333;
    opacity: 1;
  }
  .item_name_content {
    font-weight: 400;
    color: #666666;
    opacity: 1;
    margin: 5px 0;
    word-break: normal;
  }
  .join_name_box {
    margin: 10px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    opacity: 1;
  }
  .listen_btn {
    margin-top: 10px;
    float: right;
    margin-right: 10px;
    width: 50px;
    height: 18px;
    background: #3088fe;
    opacity: 1;
    border-radius: 9px;
    font-size: 10px;
    font-weight: 400;
    line-height: 18px;
    color: #ffffff;
    opacity: 1;
    text-align: center;
  }

  .process_box_name {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    opacity: 1;
    height: 22px;
    line-height: 22px;
    margin-bottom: 20px;
  }
  .process_box_name_line {
    width: 3px;
    height: 22px;
    background: #3088fe;
    margin: 0 10px;
  }
  .process_btn {
    width: 113px;
    height: 18px;
    line-height: 18px;
    background: #ffffff;
    box-shadow: 0px 2px 8px rgba(24, 64, 118, 0.1);
    font-size: 10px;
    font-weight: 400;
    color: #8c96a2;
    text-align: center;
    left: 0;
    right: 0;
    margin: auto;
    position: absolute;
    top: -10px;
    opacity: 1;
  }
  .process_name {
    margin-left: 16px;
    margin-top: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    opacity: 1;
  }
  .process_box {
    position: relative;
    margin-top: 10px;
    max-height: 100%;
    overflow: auto;
  }
  .process_item_box {
    position: relative;
    width: 90%;
    left: 0;
    right: 0;
    margin: auto;
  }
  .process_item_img {
    width: 40px;
    text-align: center;
  }
  .process_item_img img {
    width: 14px;
    left: 0;
    right: 0;
    margin: auto;
  }
  .line_shu {
    width: 1px;
    background: #dcebff;
    height: 100%;
    left: 0;
    right: 0;
    margin: auto;
    overflow: hidden;
  }
  .process_item_content {
    width: 100%;
    background: #f8f9fa;
    opacity: 1;
    border-radius: 2px;
    padding: 10px 10px;
    margin-bottom: 20px;
  }
  .process_item_content_title {
    width: 80px;
    text-align: right;
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    opacity: 1;
    line-height: 20px;
  }
  .process_item_content_name {
    position: relative;
  }
  .process_item_content_name div {
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    opacity: 1;
    line-height: 20px;
  }
  .process_item_content_name div div {
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    opacity: 1;
    line-height: 20px;
  }
  .process_item_content_name .answerDate {
    position: absolute;
    right: 0;
    top: 0;
    color: #999999;
  }
  .process_item_right {
    position: absolute;
    right: 0;
    top: 0;
  }
  .process_item_right_type {
  }
  .process_item_right_type_tag {
    font-size: 10px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border: 1px solid #adbbcb;
    opacity: 1;
    color: #8c96a2;
    border-radius: 2px;
    padding: 1px 5px;
  }
  .process_item_right_time {
    white-space: nowrap;
    width: 82px;
    text-align: left;
    font-size: 12px;
    font-weight: 400;
    margin-top: 10px;
    color: #999999;
    opacity: 1;
  }
}
</style>

<template>
  <div :id="id">
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { debounce } from '../../../utils/debounce.js'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'line2',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: Nav<PERSON>ar,
    [Sticky.name]: <PERSON><PERSON>
  },
  props: {
    color: String,
    id: String,
    list: Array,
    status: String
  },
  setup (props) {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      viewportWidth: ''
    })
    var myChart = null
    onMounted(() => {
      var chartDom = document.getElementById(props.id)
      myChart = echarts.init(chartDom)
      // 监听窗口尺寸变化事件
      window.addEventListener('resize', debounce(() => {
        myChart.resize() // 调整图表大小
        data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
        setOptions()
      }, 500))
      data.viewportWidth = window.innerWidth || document.documentElement.clientWidth
      setOptions()
    })
    watch(props, (val) => {
      if (val.status && props.id === 'lines1') {
        setOptions()
      } else if (val.status && props.id === 'lines2') {
        setOptions()
      }
    }, { deep: true })
    const setOptions = () => {
      // console.log(parseInt(data.viewportWidth * 0.03))
      var options = {
        legend: { right: 7, top: -15, padding: [15, 0, 10, 0], data: ['人次', '人数'] },
        grid: {
          top: '10%',
          left: '4%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis', // 触发类型——坐标轴
          // 鼠标移入条目下面的背景
          axisPointer: {
            type: 'line',
            z: 0,
            lineStyle: {
              type: 'solid',
              color: 'rgba(225,225,225,.3)',
              width: 26
            }
          }
        },
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
          offset: 0,
          color: '#B0A8FF' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#A5C4FF' // 100% 处的颜色
        }], false),
        xAxis: {
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#B0BCC2',
              width: '0'
            }
          },
          type: 'category',
          data: ['市南区', '市北区', '黄岛区', '即墨区', '城阳区', '胶州市', '崂山区'],
          axisLabel: {
            interval: 0, // 坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            rotate: 38 // 调整数值改变倾斜的幅度（范围-90到90）
          },
          axisTick: {
            show: false
          },
          splitLine: { show: '' }
        },
        yAxis: {
          data: [],
          axisTick: {
            show: false
          },
          splitLine: { // 网格线
            show: false // 隐藏或显示
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#DCE4E8',
              width: '1'
            },
            show: true
          },
          type: 'value'
        },
        series: [
          {
            name: '人次',
            data: [150, 120, 133, 320, 256, 145, 98],
            type: 'bar',
            barWidth: '24px',
            barGap: '1%',
            itemStyle: { // 设置在柱状图上显示数值
              normal: {
                // 改变折线点的颜色
                // color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                //     offset: 0,
                //     color: '#B0A8FF' // 0% 处的颜色
                // }, {
                //     offset: 1,
                //     color: '#A5C4FF' // 100% 处的颜色
                // }], false),
                label: {
                  show: true, // 开启显示
                  position: 'top', // 在上方显示
                  textStyle: { // 数值样式
                    color: '#B0BCC2',
                    fontSize: 12
                  }
                }
              }
            }
          },
          {
            name: '人数',
            data: [200, 500, 325, 655, 365, 689, 300],
            type: 'line',
            barWidth: '9px',
            // symbol: 'none',
            showSymbol: true, // 是否默认展示圆点
            smooth: true,
            itemStyle: {
              normal: {
                color: '#82DD63' // 改变折线点的颜色
              }
            },
            lineStyle: {
              color: '#82DD63'
            }
          }
        ]
      }
      var options2 = {
        legend: { right: 7, top: -15, padding: [15, 0, 10, 0], data: ['人次', '人数'] },
        grid: {
          top: '10%',
          left: '4%',
          right: '1%',
          bottom: '1%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            z: 0,
            lineStyle: {
              type: 'solid',
              color: 'rgba(225,225,225,.3)',
              width: 18
            }
          }
        },
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
          offset: 1,
          color: '#3894FF' // 0% 处的颜色
        }, {
          offset: 0,
          color: '#f5f7fa' // 100% 处的颜色
        }], false),
        xAxis: {
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#B0BCC2',
              width: '0'
            }
          },
          type: 'category',
          data: ['市南区', '市北区', '黄岛区', '即墨区', '城阳区', '胶州市', '崂山区'],
          axisLabel: {
            interval: 0, // 坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            rotate: 38 // 调整数值改变倾斜的幅度（范围-90到90）
          },
          axisTick: {
            show: false
          },
          splitLine: { show: '' }
        },
        yAxis: {
          data: [],
          axisTick: {
            show: false
          },
          splitLine: { // 网格线
            show: false // 隐藏或显示
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#DCE4E8',
              width: '1'
            },
            show: true
          },
          type: 'value'
        },
        series: [
          {
            name: '人次',
            data: [150, 120, 133, 320, 256, 145, 98],
            type: 'bar',
            barWidth: '14px',
            barGap: '1%',
            itemStyle: { // 设置在柱状图上显示数值
              normal: {
                // 改变折线点的颜色
                // color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                //     offset: 0,
                //     color: '#B0A8FF' // 0% 处的颜色
                // }, {
                //     offset: 1,
                //     color: '#A5C4FF' // 100% 处的颜色
                // }], false),
                label: {
                  show: true, // 开启显示
                  position: 'top', // 在上方显示
                  textStyle: { // 数值样式
                    color: '#B0BCC2',
                    fontSize: 12
                  }
                }
              }
            }
          },
          {
            name: '人数',
            data: [200, 500, 325, 655, 365, 689, 300],
            type: 'line',
            barWidth: '9px',
            // symbol: 'none',
            showSymbol: true, // 是否默认展示圆点
            smooth: true,
            itemStyle: {
              normal: {
                color: '#82DD63' // 改变折线点的颜色
              }
            },
            lineStyle: {
              color: '#82DD63'
            }
          }
        ]
      }
      nextTick(() => {
        if (props.id === 'lines1') {
          options.xAxis.data = props.list.map(item => item.name)
          options.series[0].data = props.list.map(item => item.nums)
          options.series[1].data = props.list.map(item => item.num)
          myChart.setOption(options)
        } else if (props.id === 'lines2') {
          options2.xAxis.data = props.list.map(item => item.name)
          options2.series[0].data = props.list.map(item => item.times)
          options2.series[1].data = props.list.map(item => item.num)
          myChart.setOption(options2)
        }
      })
    }
    return { ...toRefs(data), general, confirm }
  }
}
</script>
<style lang="less" scoped>
#lines1 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
}

#lines2 {
  background: #fff;
  width: 100%;
  height: 180px;
  margin: 10px 0;
  box-sizing: border-box;
}
</style>

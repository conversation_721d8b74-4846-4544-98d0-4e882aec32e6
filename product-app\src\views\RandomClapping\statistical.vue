<template>
  <div class="statistical">
    <div class="header_box">
      <div class="header">
        <img src="../../assets/img/g_01.png"
             alt="icon"
             class="icon-image" />
        <p class="title">代表随手拍</p>
      </div>
      <div class="content">
        <p>代表共提交 <span class="highlight">{{ totalSubmissions }}</span> 件，其中已办结 <span
                class="highlight">{{ completedSubmissions }}</span> 件。</p>
      </div>
    </div>
    <div class="analysis_box">
      <div class="flex_box flex_align_center">
        <p class="line"></p>
        <div class="analysis_text">统计分析</div>
      </div>
      <EChartsComponent title="随手拍提交情况"
                        :data="chartData1"
                        :categories="categories1"
                        @timeframe-switch="handleTimeframeSwitch1"
                        :colors="colors1" />
      <EChartsComponent title="随手拍类别分析"
                        :data="chartData2"
                        :categories="categories2"
                        @timeframe-switch="handleTimeframeSwitch2"
                        :colors="colors2" />
      <EChartsDonutComponent title="各区市受理情况统计"
                             :data="chartData" />
    </div>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import EChartsComponent from './echarts/echarts1.vue'
import EChartsDonutComponent from './echarts/echarts2.vue'
export default {
  name: 'statistical',
  components: {
    EChartsComponent,
    EChartsDonutComponent
  },
  setup () {
    const router = useRouter()
    const $ifzx = inject('$ifzx')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      SYS_IF_ZX: $ifzx,
      appFontSize: $general.data.appFontSize,
      appTheme: $appTheme,
      user: JSON.parse(sessionStorage.getItem('user')) || JSON.parse(localStorage.getItem('user')),
      totalSubmissions: 0,
      completedSubmissions: 0,
      chartData1: {},
      categories1: [],
      colors1: [
        ['#2E86EE', '#ABF5FF'], // 已办理
        ['#F8C92B', '#FFF3CB'], // 待办理
        ['#00C74A', '#DDFFEA'] // 正在办理
      ],
      chartData2: {},
      categories2: [],
      colors2: [
        ['#3B82F6', '#93C5FD'], // 本月
        ['#EE732E', '#FFF4CC'] // 上月
      ],
      chartData: [] // 各区市受理情况统计
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      getCount()
    })

    const handleTimeframeSwitch1 = (timeframe) => {
      getSubCount(timeframe)
    }

    const handleTimeframeSwitch2 = (timeframe) => {
      getSubTypeCount(timeframe)
    }

    const getCount = async () => {
      const res = await $api.RandomClapping.getCount()
      data.totalSubmissions = res.data.total
      data.completedSubmissions = res.data.completedTotal
      getSubCount()
    }

    // 提交情况
    const getSubCount = async (type) => {
      const res = await $api.RandomClapping.getSubCount({
        dateType: type || '1'
      })
      const processedData = {
        已办理: [],
        待处理: [],
        正在办理: []
      }
      data.categories1 = res.data.map(b => {
        if (b.dateDays.length > 2) return b.dateDays
        else return b.dateDays + '月'
      })
      res.data.forEach(day => {
        day.count.forEach(item => {
          if (processedData[item.representativeName]) {
            processedData[item.representativeName].push(item.count)
          }
        })
      })
      data.chartData1 = processedData
      getSubTypeCount('1')
    }

    // 类别分析
    const getSubTypeCount = async (type) => {
      const res = await $api.RandomClapping.getSubTypeCount({
        dateType: type || '1'
      })
      const chartData2 = {
        本月: [],
        上月: []
      }
      const chartData21 = {
        本季: [],
        上季: []
      }
      const categories2 = []
      if (type === '1') {
        res.data.forEach(item => {
          categories2.push(item.name)
          chartData21.本季.push(item.currentQuarterMonthsTotal)
          chartData21.上季.push(item.previousQuarterMonthsTotal)
        })
        data.chartData2 = chartData21
        data.categories2 = categories2
      } else {
        res.data.forEach(item => {
          categories2.push(item.name)
          chartData2.本月.push(item.previousQuarterMonthsTotal)
          chartData2.上月.push(item.currentQuarterMonthsTotal)
        })
        data.chartData2 = chartData2
        data.categories2 = categories2
      }
      getSubAreaCount()
    }

    // 各区市受理情况统计
    const getSubAreaCount = async () => {
      const res = await $api.RandomClapping.getSubAreaCount()
      data.chartData = res.data.map(item => {
        return {
          name: item.areaName,
          value: item.count
        }
      })
    }

    return { ...toRefs(data), dayjs, router, $general, handleTimeframeSwitch1, handleTimeframeSwitch2 }
  }
}
</script>
<style lang="less" scoped>
.statistical {
  background-color: #f5f7fa;
  border-radius: 10px;
  padding: 15px;
  font-size: 16px;
  color: #333;
}

.header_box {
  background: #fff;
  padding: 10px 15px;
  border-radius: 10px;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .icon-image {
      width: 22px;
      height: 22px;
      margin-right: 5px;
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      margin: 0;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    font-size: 14px;

    .highlight {
      color: #2e86ee;
      font-weight: bold;
      font-size: 16px;
    }
  }
}

.analysis_box {
  margin-top: 15px;

  .line {
    width: 3px;
    height: 15px;
    background-color: #2e86ee;
  }

  .analysis_text {
    margin-left: 10px;
    color: #2e86ee;
    font-size: 18px;
  }
}
</style>

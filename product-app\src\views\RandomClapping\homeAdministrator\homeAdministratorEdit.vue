<template>
  <div class="homeAdministratorEdit">
    <div class="RandomClappingUpload-box">
      <van-cell-group inset>
        <van-field v-model="username"
                   name="代表姓名"
                   label="代表姓名"
                   required
                   input-align="right"
                   placeholder="代表姓名" />
        <van-field v-model="mobile"
                   name="代表电话"
                   required
                   label="代表电话"
                   input-align="right"
                   placeholder="代表电话" />
        <van-field v-model="messageDate"
                   name="反映时间"
                   label="反映时间"
                   disabled
                   input-align="right"
                   placeholder="反映时间" />
      </van-cell-group>
    </div>
    <div class="RandomClappingUpload-box">
      <van-cell-group inset>
        <van-field label="地址"
                   v-model="address"
                   placeholder="请上请选择问题发生地地址传地址"
                   required
                   readonly
                   input-align="right"
                   right-icon="location-o"
                   @click="router.push('/selectMap')" />
        <van-field v-model="selectname"
                   label="代表之家"
                   maxlength="20"
                   disabled
                   input-align="right"
                   placeholder="代表之家" />
      </van-cell-group>
    </div>
    <div class="RandomClappingUpload-box">
      <van-cell-group inset>
        <van-field v-model="title"
                   label="标题"
                   maxlength="20"
                   required
                   input-align="right"
                   placeholder="请输入不超过20字的标题" />
        <van-field v-model="result"
                   is-link
                   required
                   readonly
                   name="picker"
                   input-align="right"
                   label="问题类别"
                   placeholder="点击选择问题类别"
                   @click="showPicker = true" />
        <van-popup v-model:show="showPicker"
                   position="bottom">
          <van-picker :columns="typeLists"
                      @confirm="onConfirm"
                      @cancel="showPicker = false" />
        </van-popup>
        <van-field class="newContent"
                   v-model="content"
                   name="content"
                   label="问题描述"
                   required
                   rows="6"
                   maxlength="200"
                   type="textarea"
                   placeholder="'随手拍' 反映即时解决问题，请准确定位或准确描述问题发生位置，简明扼要描述问题" />
      </van-cell-group>
    </div>
    <div class="RandomClappingUpload-box">
      <div class="picUploader">
        <div class="picUploader_title">
          <span style="color: #f20a24;">*</span>上传图片(最多上传{{ Uploadmax }}张)
        </div>
        <div class="imgloager">
          <div class="img_box"
               v-for="(nItem, nIndex) in attachmentIds"
               :key="nIndex">
            <van-icon name="clear"
                      @click.stop="$general.delItemForKey(nIndex, attachmentIds)"
                      class="clear" />
            <van-image @click.stop="ImagePreview(setImg(nItem.url))"
                       width="2rem"
                       height="2rem"
                       fit="cover"
                       :src="setImg(nItem.url)" />
          </div>
          <!-- <van-uploader :preview-full-image="true"
                        ref="vanUploader"
                        accept="image/*"
                        capture="camera"
                        multiple
                        :disabled="attachmentIds.length == Uploadmax"
                        :after-read="imgUploader"
                        :max-count="Uploadmax">
          </van-uploader> -->
          <van-uploader :preview-full-image="true"
                        ref="vanUploader"
                        accept="image/*"
                        v-if="attachmentIds.length != Uploadmax"
                        :after-read="imgUploader"
                        :max-count="Uploadmax">
            <div class="photo">
              <van-icon name="add-o" />
            </div>
          </van-uploader>
        </div>
      </div>
    </div>
    <!-- <div class="RandomClappingUpload-submit"
         @click="Submit">提交</div> -->
    <div class="footer">
      <div class="RandomClappingUpload-submit"
           @click="Submit">提交</div>
      <div class="RandomClappingUpload-submits"
           @click="router.back()">取消</div>
    </div>
  </div>
  <!-- <van-popup v-model:show="mapVisible"
             :round="false"
             style="width: 100vw;height: 100vh;">
    <SelectMap @close="mapVisible = false"
               @confirm="mapConfirm"
               :dataList="selectTitleList" />
  </van-popup> -->
</template>
<script>
import { onMounted, reactive, toRefs, inject, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { Uploader, Image as VanImage, Toast } from 'vant'
import { imgZip } from '../imgZip.js'
// import SelectMap from '@/components/SelectMap/index.vue'
export default ({
  name: 'homeAdministratorEdit',
  props: {},
  components: {
    // SelectMap,
    [VanImage.name]: VanImage,
    [Uploader.name]: Uploader
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      module: route.query.module,
      id: route.query.id,
      title: '',
      content: '',
      address: '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      username: '',
      mobile: '',
      longitude: '',
      positionObj: {},
      dimensionality: '',
      township: '',
      Uploadmax: 3,
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      mapVisible: false,
      selectTitleList: [],
      selectname: '',
      selectHome: {
        name: '',
        id: ''
      },
      result: '',
      resultvalue: '',
      showPicker: false,
      typeLists: [
      ],
      areaTree: [],
      messageDate: '',
      standbyTwo: '',
      userId: ''
    })
    onMounted(() => {
      data.positionObj = computed(() => store.state.positionObj)
      selectTitle()
      getareaTree()
      getInfo()
      getpubkvs()
    })
    const getpubkvs = async () => {
      const res = await $api.general.pubkvs({
        types: 'photograph_message_type'
      })
      var { data: list } = res
      data.typeLists = list.photograph_message_type.map(item => {
        return {
          text: item.value,
          id: item.id
        }
      })
    }
    const getInfo = async () => {
      const { data: list } = await $api.RandomClapping.representativemessageInfo(data.id)
      data.selectHome = {
        name: list.standbyTwo,
        id: list.standbyOne
      }
      data.selectname = list.standbyTwo
      // data.selectHome.id = list.standbyOne
      data.address = list.address
      data.username = list.userName
      data.mobile = list.mobile
      data.messageDate = list.messageDate
      data.content = list.messageMessage
      data.title = list.title
      data.township = list.standbyFive
      data.userId = list.userId
      data.result = list.messageTypeName
      data.resultvalue = list.messageType
      data.state = list.state
      if (list.imageVo.length) {
        data.attachmentIds = list.imageVo.map(item => {
          return {
            url: item.fullUrl,
            uploadId: item.id
          }
        })
      }
      console.log(list)
    }
    const onConfirm = (e) => {
      data.result = e.text
      data.resultvalue = e.id
      data.showPicker = false
    }
    const setImg = (url) => {
      if (url && !window.location.origin.includes('localhost') && !window.location.origin.includes('http://**************')) {
        console.log()
        return window.location.origin + '/lzt' + url.split('lzt')[1]
      } else {
        return url
      }
    }
    const selectTitle = async () => {
      const { data: list } = await $api.RandomClapping.representativehomeSelectTitle()
      data.selectTitleList = list.map(item => {
        return {
          name: item.title,
          id: item.id,
          longitude: item.longitude,
          latitude: item.dimension,
          standbyFour: item.standbyFour
        }
      })
    }
    const getareaTree = async () => {
      const { data: list } = await $api.RandomClapping.areaTree()
      data.areaTree = list
      if (Object.keys(data.positionObj).length) {
        mapConfirm(data.positionObj)
      }
    }
    const imgUploader = async (file) => {
      imgZip(file, uploadFile)
    }
    const uploadFile = async (e) => {
      const item = { url: '', uploadUrl: '', name: '', uploadId: '', status: 'uploading', module: 'lzbl' }
      const formData = new FormData()
      formData.append('attachment', e)
      formData.append('module', 'lzbl')
      formData.append('siteId', JSON.parse(sessionStorage.getItem('areaId')))
      const ret = await $api.general.uploadFile(formData)
      if (ret) {
        var info = ret.data[0]
        item.status = 'done'
        item.url = info.filePath || ''
        item.name = info.fileName || ''
        item.uploadId = info.id || ''
      } else {
        item.status = 'failed'
        item.error = ret.errmsg || ''
      }
      data.attachmentIds.push(item)
    }
    // 地图选点
    const mapConfirm = (positionObj) => {
      data.address = positionObj.address
      data.longitude = positionObj.lon
      data.dimensionality = positionObj.lat
      data.township = findIdByName(data.areaTree, positionObj.township)
      data.adcode = positionObj.adcode
      console.log('地图选点', data.township)
      console.log('地图选点', data.adcode)
      data.mapVisible = false
      data.selectHome = data.selectTitleList.find(item => {
        return item.standbyFour == data.township // eslint-disable-line
      })
      data.selectname = data.selectHome.name || ''
      console.log(data.selectHome)
      console.log(data.selectTitleList)
    }
    // 递归函数
    function findIdByName (data, name) {
      console.log('findIdByName')
      console.log(data)
      console.log(name)
      for (var i = 0; i < data.length; i++) {
        if (data[i].name === name) {
          return data[i].id
        } else if (data[i].children.length > 0) {
          var foundId = findIdByName(data[i].children, name)
          if (foundId !== null) {
            return foundId
          }
        }
      }
      return null // 如果未找到匹配项，则返回 null
    }
    // 提交
    const Submit = async () => {
      console.log(data.selectHome)
      if (data.title === '') {
        Toast('请填写标题')
        return
      }
      if (data.content === '') {
        Toast('请填写内容')
        return
      }
      if (data.attachmentIds.length === 0) {
        Toast('请上传图片')
        return
      }
      if (data.resultvalue === '') {
        Toast('请选择分类')
        return
      }
      if (data.address === '') {
        Toast('请选择地址')
        return
      }
      var params = {
        id: data.id,
        title: data.title,
        messageMessage: data.content,
        address: data.address,
        userName: data.username,
        mobile: data.mobile,
        userId: data.userId,
        standbyOne: data.selectHome.id || '',
        standbyTwo: data.selectHome.name || '',
        standbyFive: data.township,
        messageType: data.resultvalue,
        // standbyFive: '370285001',
        areaId: data.adcode,
        messageDate: data.messageDate,
        state: data.state,
        image: data.attachmentIds.map(item => item.uploadId).join(',')
      }
      console.log(params, 'params')
      const { errcode } = await $api.RandomClapping.representativemessageEdit(params)
      if (errcode === 200) {
        Toast('留言成功')
        store.commit('setPositionObj', {})
        router.back()
      }
    }
    const ImagePreview = (url) => {
      router.push({ path: '/openImg', query: { url } })
    }
    return { ...toRefs(data), dayjs, route, router, onConfirm, setImg, $api, imgUploader, ImagePreview, $general, mapConfirm, Submit }
  }
})
</script>
<style lang='less'>
.homeAdministratorEdit {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;
  padding: 15px;

  .footer {
    width: 90%;
    height: 56px;
    position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .RandomClappingUpload-submit {
    width: 45%;
    height: 40px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }

  .RandomClappingUpload-submits {
    width: 45%;
    height: 40px;
    border: 1px solid #3088fe;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #3088fe;
    border-radius: 5px;
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../../assets/font/PingFang Medium_downcc.otf");
  }

  .RandomClappingUpload-box {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  // .RandomClappingUpload-submit {
  //   position: fixed;
  //   left: 50%;
  //   bottom: 2%;
  //   transform: translateX(-50%);
  //   width: 90%;
  //   height: 56px;
  //   background: #3088fe;
  //   box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
  //   font-size: 16px;
  //   font-family: PingFang SC;
  //   font-weight: 400;
  //   line-height: 56px;
  //   text-align: center;
  //   color: #ffffff;
  //   border-radius: 5px;
  // }
}
</style>

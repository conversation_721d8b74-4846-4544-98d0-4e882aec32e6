<template>
  <div class="girdModuleMoreList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead" :title="title" left-text="" left-arrow @click-left="onClickLeft" />
      <!-- <div>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
      </div> -->
    </van-sticky>
    <div class="grid_box">
      <div class="title_box">我的应用</div>
      <div style="color: #999999;font-size: 12px;">长按并拖动可调整应用位置与排序</div>
    </div>
    <!--顶上选中的栏目-->
    <van-grid v-if="refreshList" clickable :column-num="4">
      <!-- <draggable class="flex_placeholder flex_box T-flex-flow-row-wrap"
                 group="memu"
                 v-model="modeList"> -->
      <van-grid-item v-for="(item, index) in modeListShow" :key="index" :badge="+ ''" style="font-size: 14px;"
        @click.stop.prevent="itemClick(item, 'up')">
        <template v-slot:default>
          <van-badge :content="getPointNumber(item) != 0 ? getPointNumber(item) : ''">
            <div style="position: relative;">
              <img class="draggable_item" :src="item.url"
                style="object-fit: cover;width: 40px;height: 40px;-webkit-touch-callout: none;pointer-events:none" />
            </div>
            <div :style="'font-weight: 500;color: #333;font-size:12px;'" v-html="item.name"></div>
            <p v-if="item.pointNumber > 0" class="flex_box flex_align_center flex_justify_content text_one"
              :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
              :style="item.pointType == 'big' ? 'font-size:12px;width:20px;height:20px;' : 'width:10px;height:10px;'"
              v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></p>
          </van-badge>
        </template>
      </van-grid-item>
      <!-- </draggable> -->
    </van-grid>
    <div class="grid_box" style="margin-top: 10px;">
      <div class="title_box">其他应用</div>
    </div>
    <!--未选中的栏目-->
    <van-grid clickable :column-num="4">
      <!-- <draggable class="flex_placeholder flex_box T-flex-flow-row-wrap"
                 group="memu"
                 v-model="menuList"> -->
      <van-grid-item v-for="(item, index) in menuListShow" :key="index" style="font-size: 14px;"
        @click.stop.prevent="itemClick(item, 'down')">
        <template v-slot:default>
          <van-badge :content="getPointNumber(item) != 0 ? getPointNumber(item) : ''">
            <div style="position: relative;">
              <img class="draggable_item" :src="item.url"
                style="object-fit: cover;width: 40px;height: 40px;-webkit-touch-callout: none;pointer-events:none" />
            </div>
            <div :style="'font-weight: 500;color: #333;font-size:12px;'" v-html="item.name"></div>
            <p v-if="item.pointNumber > 0" class="flex_box flex_align_center flex_justify_content text_one"
              :class="item.pointType == 'big' ? 'footer_item_hot_big' : 'footer_item_hot'"
              :style="item.pointType == 'big' ? 'font-size:12px;width:20px;height:20px;' : 'width:10px;height:10px;'"
              v-html="item.pointType == 'big' ? (item.pointNumber > 99 ? '99+' : item.pointNumber) : ''"></p>
          </van-badge>
        </template>
      </van-grid-item>
      <!-- </draggable> -->
    </van-grid>
  </div>
</template>
<script>
// import { VueDraggableNext } from 'vue-draggable-next'
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Toast, Badge } from 'vant'
export default {
  name: 'noticeList',
  components: {
    // draggable: VueDraggableNext,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [Badge.name]: Badge,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      type: route.query.type || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: route.query.areaId,
      keyword: '',
      refreshList: true, // 刷新列表
      listData: [],
      modeList: [], // 已选中的模块
      modeListShow: [],
      menuList: [], // 未选中的模块
      menuListShow: []
    })
    if (data.title) {
      document.title = data.title
    }
    watch(() => data.modeList, (newName, oldName) => {
      showModeList()
      setMainSelect()
    })
    onMounted(() => {
      getConferenceAgentNumbe()
      var mainSelectData = localStorage.getItem(data.user.areaId + 'mainSelectData' + data.user.id) || ''
      if (mainSelectData) {
        data.modeList = mainSelectData.split('|,|')
      }
      getModuleList()
    })
    const getConferenceAgentNumbe = async () => {
      const res = await $api.general.getConferenceAgentNumbe()
      sessionStorage.setItem('conferenceAgentNumbe', JSON.stringify(res.data))
    }
    const getPointNumber = (item) => {
      var browseNotCount = JSON.parse(sessionStorage.getItem('browseNotCount'))
      var committeesayUnread = sessionStorage.getItem('committeesayUnread')
      var conferenceAgentNumbe = JSON.parse(sessionStorage.getItem('conferenceAgentNumbe')).sum
      var areaId = sessionStorage.getItem('areaId')
      if (item.infoUrl2 === 'newsMore?type=survey') { // 意见征集
        return browseNotCount[areaId].notCount4 || ''
      } else if (item.infoUrl2 === 'conferenceActivities') { // 会议活动
        return conferenceAgentNumbe || ''
      } else if (item.infoUrl2 === 'beingCountedList') { // 履职圈
        return committeesayUnread || ''
      } else {
        return ''
      }
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getModuleList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        getModuleList()
      }, 520)
    }
    // 列表请求
    const getModuleList = async () => {
      var res = await $api.general.appList({ parentId: sessionStorage.getItem('historyIndex'), areaId: sessionStorage.getItem('areaId') })
      if (res) {
        var list = res.data || []
        var dataLength = list ? list.length : 0
        if (!list || dataLength === 0) {
          return
        }
        list = list.find(item => item.name === '工作台').children || []
        console.log('list==>', list)
        dataLength = list ? list.length : 0
        data.listData = []
        data.menuList = []
        for (var i = 0; i < dataLength; i++) {
          var item = {}
          var itemData = list[i]
          // console.log(itemData)
          item.id = itemData.id + ''
          item.name = itemData.name
          item.sort = itemData.sort
          item.url = itemData.iconUrl
          item.infoUrl2 = itemData.infoUrl2
          item.type = itemData.type
          item.remarks = itemData.remarks
          item.pointType = 'big'
          item.key = ''
          item.pointNumber = 0
          item.needRecord = itemData.needRecord
          if (item.infoUrl2 && item.infoUrl2 !== '#') {
            data.listData.push(item)
          }
          // data.listData.push(item)
        }
        if (!data.modeList) {
          for (var j = 0; j < dataLength; j++) {
            if (j < 7) {
              data.modeList = []
              data.modeList.push(data[j].id + '')
            }
          }
        }
        showModeList()
        data.refreshList = false
        setTimeout(function () {
          data.refreshList = true
        }, 0)
      }
    }
    // 显示选择的数据
    const showModeList = () => {
      if (!data.listData.length) {
        return
      }
      data.modeListShow = []
      data.modeList.forEach(function (_eItem, _eIndex, _eArr) {
        var nItem = $general.getItemForKey(_eItem, data.listData, 'id')
        console.log('nItem==>', nItem)
        if (nItem) {
          data.modeListShow.push(nItem)
        }
      })
      data.menuList = []
      data.listData.forEach(function (_eItem, _eIndex, _eArr) {
        var result = !data.modeList.includes(_eItem.id)
        if (result) {
          data.menuList.push(_eItem.id)
        }
      })
      data.menuListShow = []
      data.menuList.forEach(function (_eItem, _eIndex, _eArr) {
        var nItem = $general.getItemForKey(_eItem, data.listData, 'id')
        if (nItem) {
          data.menuListShow.push(nItem)
        }
      })
      // that.$forceUpdate()
    }
    // 设置选择的模块
    const setMainSelect = async () => {
      localStorage.setItem(data.user.areaId + 'mainSelectData' + data.user.id, data.modeList.join('|,|'))
    }
    // 中间栏目点击
    const itemClick = (_item) => {
      switch (_item.name) {
        case '群众留言':
          window.location.href = 'http://123.206.212.39/qingdaord-meet-app/#/crowdReply?token={{token}}'
          break
        case '议案建议':
          var token = JSON.parse(sessionStorage.getItem('token'))
          if (_item.infoUrl2.indexOf('mobile') !== -1 || _item.infoUrl2.indexOf('mobile') !== -1) {
            window.location.href = _item.infoUrl2 + '?token=' + token
          } else {
            window.location.href = _item.infoUrl2
          }
          break
        case '意见征集':
          if (_item.infoUrl2) {
            var routerStr2 = _item.infoUrl2 + (_item.remarks === null ? '' : '?' + _item.remarks)
            var pageData2 = {}
            if (_item.infoUrl2.indexOf('?')) {
              var pageArr2 = routerStr2.split('?')
              var pageIndex2 = pageArr2.findIndex(item => item.includes('='))
              console.log(pageArr2)
              // var pageIndex2 = pageArr2.length - 1
              pageData2[pageArr2[pageIndex2].split('=')[0]] = pageArr2[pageIndex2].split('=')[1]
            }
            if (routerStr2.indexOf('http') === 0) {
              window.location.href = routerStr2
              return false
            }
            router.push({ path: routerStr2, query: pageData2 })
          } else {
            Toast('请配置好H5路由')
          }
          break
        case '圈子':
          if (_item.infoUrl2) {
            // console.log(infoUrl2)
            var routerStr1 = _item.infoUrl2 + (_item.remarks === null ? '' : '?' + _item.remarks)
            var pageData = {}
            if (_item.infoUrl2.indexOf('?')) {
              var pageArr = routerStr1.split('?')
              console.log(pageArr)
              var pageIndex = pageArr.findIndex(item => item.includes('='))
              // var pageIndex = pageArr.length - 1
              pageData[pageArr[pageIndex].split('=')[0]] = pageArr[pageIndex].split('=')[1]
            }
            if (routerStr1.indexOf('http') === 0) {
              //   console.log(routerStr1)
              window.location.href = routerStr1
              return false
            }
            // console.log(routerStr1.indexOf('http'))
            // console.log(routerStr1)
            router.push({ path: routerStr1, query: pageData })
          } else {
            Toast('请配置好H5路由')
          }
          break
        default:
          if (_item.infoUrl2) {
            // console.log(infoUrl2)
            var routerStr = _item.infoUrl2
            var pageDatas = {}
            if (routerStr.indexOf('http') === 0) {
              //   console.log(routerStr)
              window.location.href = routerStr
              return false
            }
            if (_item.infoUrl2.indexOf('?')) {
              console.log(_item.infoUrl2.indexOf('?'))
              var pageArrs = routerStr.split('?')
              console.log(pageArrs)
              var pageIndexs = pageArrs.findIndex(item => item.includes('='))
              if (pageIndexs !== -1) {
                pageDatas[pageArrs[pageIndexs].split('=')[0]] = pageArrs[pageIndexs].split('=')[1]
              }
            }
            // console.log(routerStr.indexOf('http'))
            router.push({ path: routerStr, query: pageDatas })
          } else {
            Toast('请配置好H5路由')
          }
          break
      }
    }
    const onClickLeft = () => history.back()
    return { ...toRefs(data), getPointNumber, itemClick, search, onClickLeft, onRefresh }
  }
}
document.οncοntextmenu = function (e) {
  e.preventDefault()
}
</script>

<style lang="less">
body {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.girdModuleMoreList {
  width: 100%;
  min-height: 100%;
  background: #f8f8f8;

  .grid_box {
    display: flex;
    padding: 14px 20px;
    justify-content: space-between;
    background-color: #fff;

    .title_box {
      font-weight: bold;
      color: #333333;
      font-size: 16px;
    }
  }
}

.van-grid {
  background-color: #fff;
}

::v-deep .van-grid-item__content {
  padding: 0 !important;
}
</style>

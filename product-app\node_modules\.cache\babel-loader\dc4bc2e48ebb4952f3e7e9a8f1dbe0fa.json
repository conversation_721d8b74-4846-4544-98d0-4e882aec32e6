{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue", "mtime": 1756438117302}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\babel.config.js", "mtime": 1754028950133}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["useRoute", "useRouter", "inject", "reactive", "toRefs", "onMounted", "LeaderDrivingBox", "Bar", "Pie", "RankingList", "Map", "Line", "Pie2", "Line2", "Radar", "MemoryBar", "Demo", "name", "components", "_Dialog", "Component", "_Overlay", "_ActionSheet", "_PasswordInput", "_NumberKeyboard", "_Icon", "_Tag", "_Image", "_Grid", "_GridItem", "_NavBar", "_Sticky", "_Circle", "setup", "route", "router", "ifzx", "appTheme", "general", "isShowHead", "$api", "data", "pageNot", "text", "pageNo", "pageSize", "safeAreaTop", "SYS_IF_ZX", "appFontSize", "relateType", "query", "title", "user", "JSON", "parse", "sessionStorage", "getItem", "areaId", "areas", "areaIdStatus", "years", "Date", "getFullYear", "showPopover", "actionsText", "active", "tabList", "value", "generalize", "num", "install", "representative", "key", "type", "keywordsList", "keywords", "mapList", "mapListShow", "rate", "cityYear", "dumplingYear", "show", "dynamicTab", "id", "dynamicId", "subactive", "groupActivity", "istrictEntry", "districtActivity", "representativeText", "satisfactionStatus", "sex", "birthday", "party", "representerElement", "representerTeam", "memberEducationData", "officeVos", "<PERSON><PERSON><PERSON>", "AdviceByToday", "AdviceByDomain", "currentCategoryData", "BySatisfaction", "SatisfactionBy", "SatisfactionByData", "ByRepresentative", "ByDelegation", "findWygzsTitleData", "findStudioCountByCityData", "findWygzsTitlesCountData", "findWygzsTitlesCountShow", "findHotspotKeywordsData", "findWygzsTitlesRankingData", "findWygzsStudioTitlesCountData", "dutynumList", "delegationScore", "dutynumCityList", "appToday", "todayLoginNum", "rorfNum", "riseOrFallNum", "todayLoginTimes", "rorfTime", "riseOrFallTimes", "appLoginActivation", "appInstall", "areaInstall", "showCount", "isExpanded", "isExpanded1", "isExpanded2", "memberCMemTeamInstallount", "appLoginActivationByNumTim", "appLoginActivationCity", "appLoginActivationByMemOff", "appLoginActivationByTeam", "appLoginByArea", "appLoginActivationByArea", "document", "areaList", "map", "item", "organization", "recommendation", "interfaceLocation", "PerformanceReport", "runningCondition", "getMemberCount", "memberEducation", "getOrganization", "memberChange", "getAdviceByToday", "getAdviceByDomain", "currentCategory", "getAdviceBySatisfaction", "getNumberByRepresentative", "getNumberByDelegation", "getMapList", "findWygzsTitleList", "findStudioCountByCity", "findWygzsTitlesCount", "findHotspotKeywords", "findWygzsTitlesRanking", "findWygzsStudioTitlesCount", "appTodayLogin", "appAllInstall", "res", "leaderDriving", "findStudioCountByDistrict", "memberType", "representative<PERSON><PERSON>", "y", "dumplingTab", "tabClick", "setItem", "onSelect", "dynamic", "satisfactionAll", "slice", "memberCount", "index", "amount", "itemStyle", "color", "proportion", "reverse", "regionName", "regionTotal", "totalNumber", "representative<PERSON><PERSON><PERSON>", "officeNumber", "vacantNum", "repairNum", "personCode", "result", "suggestGoLink", "mType", "url", "code", "filter", "ress", "getSatisfactionByCategory", "satisfaction", "satisfactionNumber", "percentage", "basicallySatisfied", "somewhatSatisfiedNumber", "somewhatSatisfied", "dissatisfaction", "unsatisfactoryNumber", "unsatisfactory", "suggest<PERSON><PERSON>", "suggestCode", "issueCount", "userCode", "delegation<PERSON>ame", "adviceTotal", "delegationCode", "parseFloat", "responseRate", "replace", "replyCount", "splice", "year", "dutyNumListVos", "score", "username", "userid", "delegationview", "<PERSON><PERSON><PERSON>", "errmsg", "a", "concat", "LOAD_MORE", "LOAD_ALL", "length", "Number", "t", "time", "activation", "totalInstall", "memberInstall", "officeInstall", "nums", "times", "numMem", "numOff", "activationOff", "activationMem", "console", "log", "MessagePage", "_item", "window", "location", "href", "massMessagesClick", "push", "loadMore", "NET_ERR", "LOAD_ING", "<PERSON><PERSON><PERSON>", "representativeAll1", "representativeAll2", "representative<PERSON><PERSON>", "confirm"], "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\leaderDriving\\leaderDriving.vue"], "sourcesContent": ["<template>\r\n  <div class=\"leaderDriving\">\r\n    <div class=\"leaderDriving_top\">\r\n    </div>\r\n    <div class=\"leaderDriving_tab\">\r\n      <div @click=\"tabClick(item)\"\r\n        :class=\"{ leaderDriving_tab_item: true, leaderDriving_tab_item_active: active == item.value }\"\r\n        v-for=\"item in tabList\" :key=\"item.value\">{{ item.name }}</div>\r\n    </div>\r\n    <div v-if=\"active == 1\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市概括\r\n      </div>\r\n      <leader-driving-box title=\"组织概括\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in generalize\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总用户量\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar1\" v-if=\"representativeVos.length\" :list=\"representativeVos\"></Bar>\r\n          <Bar :color=\"'rgba(255, 123, 49)'\" id=\"bar2\" v-if=\"officeVos.length\" :list=\"officeVos\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"市代表变动情况\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in representative\" :key=\"index\"\r\n              @click=\"representativeClick(item)\">\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                <span class=\"leaderDriving_generalize_item_title_span\">{{ item.title }}</span>\r\n                <span v-if=\"index == 0\">\r\n                  <el-icon style=\"color: #41ce81;\">\r\n                    <Bottom style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n                <span v-else>\r\n                  <el-icon style=\"color: #ff6da2;\">\r\n                    <Top style=\"width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;\" />\r\n                  </el-icon>\r\n                </span>\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_num\" :style=\"{ color: index == 0 ? '#41ce81' : '#ff6da2' }\">\r\n                {{ index == 0 ? '-' : '+' }}{{ item.num }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各代表团人数\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(255, 110, 110)'\" id=\"bar3\" v-if=\"representerTeam.length\" :list=\"representerTeam\"></Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表学历分析\">\r\n        <template v-slot:content>\r\n          <Radar id=\"radar1\" v-if=\"memberEducationData.length\" :list=\"memberEducationData\"></Radar>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表年龄分析\">\r\n        <template v-slot:content>\r\n          <Pie :id=\"'pie1'\" :list=\"birthday\" v-if=\"birthday.length\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表性别分析\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie\">\r\n            <div class=\"box_left\">\r\n              <Pie :id=\"'pie2'\" v-if=\"sex.length\" :list=\"sex\"></Pie>\r\n            </div>\r\n            <div class=\"box_right\" v-if=\"sex.length\">\r\n              <div class=\"top\">\r\n                <div><img :src=\"require('../../assets/img/man.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[0].name }}性{{ sex[0].value }}名</span>\r\n                <span style=\"color: #3894ff;\">{{ parseInt(sex[0].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <div><img :src=\"require('../../assets/img/woman.png')\" alt=\"\"></div>\r\n                <span style=\"color: #6D787E;\">{{ sex[1].name }}性{{ sex[1].value }}名</span>\r\n                <span style=\"color: #ff8197;\">{{ parseInt(sex[1].value / (sex[0].value + sex[1].value) * 100) }}%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表结构分析\">\r\n        <template v-slot:content>\r\n          <Bar :color=\"'rgba(60, 150, 255)'\" id=\"bar4\" v-if=\"representerElement.length\" :list=\"representerElement\">\r\n          </Bar>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 2\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              今日提交总额\r\n            </div>\r\n            <div class=\"suggest_num\">\r\n              <span style=\"font-weight: 700;\">{{ AdviceByToday }}</span>\r\n              件\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议总数\r\n          </div>\r\n          <div class=\"suggest_box\">\r\n            <div :class=\"{ suggest_meet: index == 0, suggest_flat: index == 1 }\" v-for=\"item, index in AdviceByDomain\"\r\n              :key=\"index\">\r\n              <div class=\"meet_num\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1')\">\r\n                <span>{{ item.adviceCount }}</span>\r\n                件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1020')\">\r\n                正在办理<span>{{ item.transacting }}</span>件\r\n              </div>\r\n              <div class=\"suggest_transaction\" @click=\"suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1100')\">\r\n                已办结<span>{{ item.transactAccomplish }}</span>件\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"suggest_title\">\r\n            类别占比\r\n          </div>\r\n          <Pie :id=\"'pie3'\" v-if=\"currentCategoryData.length\" :list=\"currentCategoryData\"></Pie>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in keywords\" :key=\"index\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"content_box\">\r\n            <div class=\"suggest_title\">\r\n              满意度\r\n            </div>\r\n            <div class=\"suggest_satisfaction\">\r\n              <div class=\"satisfaction_item\" v-for=\"item, index in ['满意', '基本满意', '不满意']\" :key=\"index\">\r\n                <span :style=\"{ 'background': index == 0 ? '#40cd80' : index == 1 ? '#ffd055' : '#ff6d6d' }\"></span>\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_title\">\r\n            <p>建议满意度</p>\r\n            <template v-if=\"BySatisfaction.length\">\r\n              <memory-bar v-for=\"item, index in BySatisfaction\" :key=\"index\" :item=\"item\"></memory-bar>\r\n            </template>\r\n          </div>\r\n          <div class=\"satisfaction_title\" style=\"border: 0;\">\r\n            <p>类别满意度</p>\r\n            <div class=\"satisfaction_item\" v-for=\"item, index in SatisfactionByData\" :key=\"index\">\r\n              <!-- <span>{{ item.name }}</span> -->\r\n              <memory-bar :item=\"item\"></memory-bar>\r\n            </div>\r\n          </div>\r\n          <div class=\"satisfaction_all\">\r\n            <p v-if=\"satisfactionStatus\" @click=\"satisfactionAll(false)\"><van-icon name=\"arrow-up\" />\r\n              收起</p>\r\n            <p v-if=\"!satisfactionStatus\" @click=\"satisfactionAll(true)\"><van-icon name=\"arrow-down\" />\r\n              点击展开查看更多</p>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByRepresentative\" :click=\"true\"\r\n            :title=\"['排行', '姓名', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团提交建议总排行榜\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"ByDelegation\" :click=\"true\"\r\n            :title=\"['排行', '代表团', '件数']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 3\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"message_box\">\r\n            <img :src=\"require('../../assets/img/ldjsc_message.png')\" alt=\"\">\r\n            <template v-if=\"findWygzsTitleData && findWygzsTitleData.length != 0\">\r\n              <div class=\"message\">\r\n                <div v-for=\"(item, index) in findWygzsTitleData\" :key=\"index\" @click=\"MessagePage(item)\">\r\n                  <div v-if=\"index < 2\" class=\"news_text_box_item\">{{ item.title }}</div>\r\n                </div>\r\n              </div>\r\n              <div style=\"color: #7e7d7d;padding: 0.1rem;position: absolute;right: 10px;top: 0;\"\r\n                v-if=\"findWygzsTitleData.length >= 2 && (areaId == '370215' || areaId == '370200')\"\r\n                @click=\"massMessagesClick\"> >\r\n              </div>\r\n            </template>\r\n            <!-- <div class=\"message\"\r\n                 v-if=\"findWygzsTitleData.length\">\r\n              <p v-for=\"item,index in findWygzsTitleData\"\r\n                 :key=\"index\"\r\n                 v-show=\"index < 2\"><span>{{ item.title }}</span><span v-if=\"index == 0\">></span></p>\r\n            </div> -->\r\n            <template v-else>\r\n              <div class=\"messageNull\">\r\n                暂无数据\r\n              </div>\r\n            </template>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            数据概括\r\n          </div>\r\n          <div class=\"interface_location_box\">\r\n            <div class=\"interface_location_left\">\r\n              <div class=\"interface_location_left_title\">\r\n                联络站总数量\r\n              </div>\r\n              <div class=\"interface_location_left_bot\">\r\n                <span>{{ findStudioCountByCityData.studioCount }}</span>个\r\n              </div>\r\n            </div>\r\n            <div class=\"interface_location_right\">\r\n              <Pie2 v-if=\"findWygzsTitlesCountShow\"\r\n                :datas=\"{ percentage: findWygzsTitlesCountData.responseRate, num: findWygzsTitlesCountData.num, text: '回复率', }\"\r\n                id=\"pie2\"></Pie2>\r\n            </div>\r\n          </div>\r\n          <div class=\"interface_location_box_bot\">\r\n            <div>\r\n              <p>总留言数</p>\r\n              <p>{{ (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) ?\r\n                (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>已回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.repliedCount ? findWygzsTitlesCountData.repliedCount : '暂无数据' }}</p>\r\n            </div>\r\n            <div>\r\n              <p>未回复数</p>\r\n              <p>{{ findWygzsTitlesCountData.noRreplyCount ? findWygzsTitlesCountData.noRreplyCount : '暂无数据' }}</p>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            联络站分布\r\n          </div>\r\n          <Map v-if=\"mapListShow\" :list=\"mapList\" id=\"maplist\"></Map>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            建议热词\r\n          </div>\r\n          <div class=\"hotWord\" v-for=\"item, index in findHotspotKeywordsData\" :key=\"index\" v-show=\"index < 5\">\r\n            <div class=\"hotWord_item\">\r\n              <div class=\"index\"\r\n                :style=\"{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }\">\r\n                {{ index + 1 }}</div>\r\n              {{ item }}\r\n            </div>\r\n            <div class=\"hotWord_right\"\r\n              :style=\"{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }\"\r\n              v-if=\"index + 1 < 4\">\r\n              热\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            区市联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsTitlesRankingData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区联络站活跃度\r\n          </div>\r\n          <ranking-list urlType=\"medal\" :dataList=\"findWygzsStudioTitlesCountData\"\r\n            :title=\"['排行', '联络站', '活跃度']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 4\">\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            市代表标兵\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear == years }\"\r\n              @click=\"representativeTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: cityYear != years }\"\r\n              @click=\"representativeTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"medal\" type=\"resumption\" :dataList=\"dutynumList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"router.push('/performanceFilesList')\">\r\n            查看更多\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            代表团排行\r\n            <van-icon name=\"question-o\" color=\"#d5d5d5\" size=\"24\" @click=\"show = true\" />\r\n          </div>\r\n          <div class=\"representative_tab\">\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear == years }\"\r\n              @click=\"dumplingTab(new Date().getFullYear())\">年度积分</div>\r\n            <div :class=\"{ representative_tab_item: true, representative_tab_active: dumplingYear != years }\"\r\n              @click=\"dumplingTab('')\">总积分</div>\r\n          </div>\r\n          <ranking-list urlType=\"trophy\" :dataList=\"delegationScore\" :title=\"['排行', '代表团', '得分']\"></ranking-list>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\">\r\n            各区市代表标兵\r\n            <div>\r\n              <van-popover v-model:show=\"showPopover\" placemen=\"bottom-end\" :actions=\"areas\" @select=\"onSelect\">\r\n                <template #reference>\r\n                  <p>{{ actionsText }} <van-icon name=\"play\" style=\"transform: rotate(90deg);\" /></p>\r\n                </template>\r\n              </van-popover>\r\n            </div>\r\n          </div>\r\n          <ranking-list type=\"resumption\" urlType=\"medal\" :dataList=\"dutynumCityList\"\r\n            :title=\"['排行', '姓名', '得分']\"></ranking-list>\r\n          <div class=\"notText\" style=\"font-size:14px;color: #ccc;\" v-html=\"pageNot.text\" @click=\"loadMore()\"></div>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 5\">\r\n      <div class=\"leaderDriving_title\">\r\n        全市\r\n      </div>\r\n      <leader-driving-box title=\"总安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"sex_pie1\">\r\n            <div class=\"box_left\">\r\n              <pie-2 v-if=\"appToday.num\" :datas=\"{ num: appToday.num, text: '总安装率' }\" id=\"pie1\"></pie-2>\r\n            </div>\r\n            <div class=\"box_right\">\r\n              <div class=\"top\">\r\n                <p>今日登录人数\r\n                  <span :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\">{{ appToday.todayLoginNum\r\n                  }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }\"> <van-icon name=\"down\"\r\n                    style=\"transform: rotate(-90deg)\" /> 较昨日{{ appToday.rorfNum === 1 ? '增加' : '下降' }}{{\r\n                      appToday.riseOrFallNum }}\r\n                </p>\r\n              </div>\r\n              <div class=\"bot\">\r\n                <p>今日登录人次\r\n                  <span :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\">{{\r\n                    appToday.todayLoginTimes }}</span>\r\n                </p>\r\n                <p :style=\"{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }\"><van-icon name=\"down\" />\r\n                  较昨日{{ appToday.rorfTime === 1 ? '增加' : '下降' }}{{ appToday.riseOrFallTimes }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"总活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: dynamicId == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '1')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line1\" :list=\"appLoginActivation\" v-if=\"appLoginActivation.length\" :status=\"dynamicId\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市本级\r\n      </div>\r\n      <leader-driving-box title=\"安装率\">\r\n        <template v-slot:content>\r\n          <div class=\"leaderDriving_generalize\">\r\n            <div class=\"leaderDriving_generalize_item\" v-for=\"item, index in install\" :key=\"index\">\r\n              <div class=\"leaderDriving_generalize_item_num\"\r\n                :style=\"{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }\">\r\n                {{ item.num }}\r\n              </div>\r\n              <div class=\"leaderDriving_generalize_item_title\">\r\n                {{ item.title }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团安装率排行\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded2 ? memberCMemTeamInstallount : memberCMemTeamInstallount.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll2\">\r\n            {{ isExpanded2 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"青岛市本级活跃度分析\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: subactive == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '2')\">{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            登录人数/人次\r\n          </div>\r\n          <line-2 id=\"lines1\" v-if=\"appLoginActivationByNumTim.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByNumTim\"></line-2>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            活跃度\r\n          </div>\r\n          <Line id=\"line2\" v-if=\"appLoginActivationCity.length\" :status=\"subactive\" :list=\"appLoginActivationCity\">\r\n          </Line>\r\n          <div class=\"suggest_title\" style=\"font-weight: 400;\">\r\n            机关、代表活跃度\r\n          </div>\r\n          <Line id=\"line3\" v-if=\"appLoginActivationByMemOff.length\" :status=\"subactive\"\r\n            :list=\"appLoginActivationByMemOff\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"代表团活跃度排行\" color=\"#e2eeff\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: groupActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '3')\"> 本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\"\r\n            :dataList=\"isExpanded1 ? appLoginActivationByTeam : appLoginActivationByTeam.slice(0, showCount)\"\r\n            :title=\"['排行', '代表团', '活跃度']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll1\">\r\n            {{ isExpanded1 ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <div class=\"leaderDriving_title\">\r\n        青岛市各区\r\n      </div>\r\n      <leader-driving-box title=\"总安装率排名\" color=\"#ffebcf\">\r\n        <template v-slot:content>\r\n          <ranking-list urlType=\"trophy\" color=\"#fdf6f2\"\r\n            :dataList=\"isExpanded ? areaInstall : areaInstall.slice(0, showCount)\"\r\n            :title=\"['排行', '区市', '安装率']\"></ranking-list>\r\n          <div class=\"representative_all\" @click=\"representativeAll\">\r\n            {{ isExpanded ? '收起' : '点击查看更多' }}\r\n          </div>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市登录情况\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: istrictEntry == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '4')\">本{{ item.name }}</div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <line-2 id=\"lines2\" v-if=\"appLoginByArea.length\" :status=\"istrictEntry\" :list=\"appLoginByArea\"></line-2>\r\n        </template>\r\n      </leader-driving-box>\r\n      <leader-driving-box title=\"各区市活跃度\">\r\n        <template v-slot:tab>\r\n          <div class=\"dynamic_tab\">\r\n            <div :class=\"{ dynamic_tab_item: true, dynamic_tab_item_active: districtActivity == item.id }\"\r\n              v-for=\"item in dynamicTab\" :key=\"item.id\" @click=\"dynamic(item.id, '5')\">\r\n              <div>本{{ item.name }}</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-slot:content>\r\n          <Line id=\"line4\" v-if=\"appLoginActivationByArea.length\" :status=\"districtActivity\"\r\n            :list=\"appLoginActivationByArea\"></Line>\r\n        </template>\r\n      </leader-driving-box>\r\n    </div>\r\n    <div v-if=\"active == 6\">\r\n      <iframe src=\"http://120.221.72.187:9003/cockpit/#/\" frameborder=\"0\"\r\n        style=\"width: 100%;height: 680px;z-index: 99999;-webkit-overflow-scrolling: touch; overflow: scroll;\"></iframe>\r\n    </div>\r\n    <demo></demo>\r\n    <van-popup close-icon=\"close\" round v-model:show=\"show\" closeable :style=\"{ height: '13%', width: '90%' }\">\r\n      <div class=\"popup_con\">\r\n        <div class=\"popup_con_title\">\r\n          提示\r\n        </div>\r\n        <div class=\"info\">代表团积分=代表团中代表之和/总人数</div>\r\n      </div>\r\n    </van-popup>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'\r\nimport LeaderDrivingBox from './components/leaderDrivingBox.vue'\r\nimport Bar from './components/bar.vue'\r\nimport Pie from './components/pie.vue'\r\nimport RankingList from './components/rankingList.vue'\r\nimport Map from './components/map.vue'\r\nimport Line from './components/line.vue'\r\nimport Pie2 from './components/pie2.vue'\r\nimport Line2 from './components/line2.vue'\r\nimport Radar from './components/radar.vue'\r\nimport MemoryBar from './components/memoryBar.vue'\r\nimport Demo from './components/demo.vue'\r\nexport default {\r\n  name: 'leaderDriving',\r\n  components: {\r\n    LeaderDrivingBox,\r\n    Bar,\r\n    Pie,\r\n    RankingList,\r\n    Map,\r\n    Line,\r\n    Pie2,\r\n    Line2,\r\n    Radar,\r\n    MemoryBar,\r\n    Demo,\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky,\r\n    [Circle.name]: Circle\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const ifzx = inject('$ifzx')\r\n    const appTheme = inject('$appTheme')\r\n    const general = inject('$general')\r\n    const isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      pageNot: { text: '' },\r\n      pageNo: 1,\r\n      pageSize: 5,\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: ifzx,\r\n      appFontSize: general.data.appFontSize,\r\n      appTheme: appTheme,\r\n      isShowHead: isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      areaId: JSON.parse(sessionStorage.getItem('areaId')),\r\n      areas: [],\r\n      areaIdStatus: '',\r\n      years: new Date().getFullYear(),\r\n      showPopover: false,\r\n      actionsText: '',\r\n      active: sessionStorage.getItem('leaderActive') || '1',\r\n      tabList: [{\r\n        name: '组织情况',\r\n        value: '1'\r\n      }, {\r\n        name: '建议情况',\r\n        value: '2'\r\n      }, {\r\n        name: '联络站',\r\n        value: '3'\r\n      }, {\r\n        name: '履职报表',\r\n        value: '4'\r\n      }, {\r\n        name: '运行情况',\r\n        value: '5'\r\n      }, {\r\n        name: '信访情况',\r\n        value: '6'\r\n      }],\r\n      generalize: [\r\n        {\r\n          num: '',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      install: [\r\n        {\r\n          num: '0',\r\n          title: '总人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '代表人数'\r\n        },\r\n        {\r\n          num: '0',\r\n          title: '机关人数'\r\n        }\r\n      ],\r\n      representative: [\r\n        {\r\n          num: 1,\r\n          title: '出缺代表',\r\n          key: '1',\r\n          type: 'hasVacant'\r\n        },\r\n        {\r\n          num: 1,\r\n          title: '新增代表',\r\n          key: '2',\r\n          type: ''\r\n        }\r\n      ],\r\n      keywordsList: ['教育', '产业链'],\r\n      keywords: ['教育', '产业链', '农业'],\r\n      mapList: [],\r\n      mapListShow: false,\r\n      rate: 50,\r\n      cityYear: new Date().getFullYear(),\r\n      dumplingYear: 2025,\r\n      show: false,\r\n      dynamicTab: [{\r\n        name: '日',\r\n        id: '1'\r\n      }, {\r\n        name: '月',\r\n        id: '2'\r\n      }],\r\n      dynamicId: '1',\r\n      subactive: '1',\r\n      groupActivity: '1',\r\n      istrictEntry: '1',\r\n      districtActivity: '1',\r\n      representativeText: '点击查看更多',\r\n      satisfactionStatus: false,\r\n      sex: [],\r\n      birthday: [],\r\n      party: [],\r\n      representerElement: [],\r\n      representerTeam: [],\r\n      memberEducationData: [],\r\n      officeVos: [],\r\n      representativeVos: [],\r\n      AdviceByToday: '0',\r\n      AdviceByDomain: [],\r\n      currentCategoryData: [],\r\n      BySatisfaction: [],\r\n      SatisfactionBy: [],\r\n      SatisfactionByData: [],\r\n      ByRepresentative: [],\r\n      ByDelegation: [],\r\n      findWygzsTitleData: [],\r\n      findStudioCountByCityData: {},\r\n      findWygzsTitlesCountData: {},\r\n      findWygzsTitlesCountShow: false,\r\n      findHotspotKeywordsData: {},\r\n      findWygzsTitlesRankingData: [],\r\n      findWygzsStudioTitlesCountData: [],\r\n      dutynumList: [],\r\n      delegationScore: [],\r\n      dutynumCityList: [],\r\n      appToday: {\r\n        todayLoginNum: '', // 今日登录人数\r\n        rorfNum: '', // 较昨日上升或下降\r\n        riseOrFallNum: '', // 上升或下降数量\r\n        todayLoginTimes: '', // 今日登陆人次\r\n        rorfTime: '', // 较昨日上升或下降\r\n        riseOrFallTimes: '', // 上升或下降数量\r\n        num: 0\r\n      },\r\n      appLoginActivation: [],\r\n      appInstall: [],\r\n      areaInstall: [],\r\n      showCount: 5, // 控制展示的数据数量，默认为5\r\n      isExpanded: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded1: false, // 控制是否展开全部数据，默认为false\r\n      isExpanded2: false, // 控制是否展开全部数据，默认为false\r\n      memberCMemTeamInstallount: [],\r\n      appLoginActivationByNumTim: [],\r\n      appLoginActivationCity: [],\r\n      appLoginActivationByMemOff: [],\r\n      appLoginActivationByTeam: [],\r\n      appLoginByArea: [],\r\n      appLoginActivationByArea: []\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      const areaList = JSON.parse(sessionStorage.getItem('areas'))\r\n      data.areas = areaList.map(item => {\r\n        return {\r\n          text: item.name,\r\n          id: item.id,\r\n          name: item.name\r\n        }\r\n      })\r\n      // data.areas.splice(0, 1)\r\n      data.actionsText = data.areas[0].name\r\n      data.areaIdStatus = data.areas[0].id\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    })\r\n    // 组织情况\r\n    const organization = () => {\r\n      getMemberCount()\r\n      memberEducation()\r\n      getOrganization()\r\n      memberChange()\r\n    }\r\n    // 建议情况\r\n    const recommendation = () => {\r\n      getAdviceByToday()\r\n      getAdviceByDomain()\r\n      currentCategory()\r\n      keywords()\r\n      getAdviceBySatisfaction()\r\n      getNumberByRepresentative()\r\n      getNumberByDelegation()\r\n    }\r\n    // 联络站\r\n    const interfaceLocation = () => {\r\n      getMapList()\r\n      findWygzsTitleList()\r\n      findStudioCountByCity()\r\n      findWygzsTitlesCount()\r\n      findHotspotKeywords()\r\n      findWygzsTitlesRanking()\r\n      findWygzsStudioTitlesCount()\r\n    }\r\n    // 履职报表\r\n    const PerformanceReport = () => {\r\n      dutynumList(2025)\r\n      delegationScore()\r\n      dutynumCityList()\r\n    }\r\n    // 运行情况\r\n    const runningCondition = () => {\r\n      appTodayLogin()\r\n      appAllInstall()\r\n      appLoginActivation()\r\n      appInstall()\r\n      memberCMemTeamInstallount()\r\n      appLoginActivationByNumTim()\r\n      appLoginActivationCity()\r\n      appLoginActivationByMemOff()\r\n      appLoginActivationByTeam()\r\n      areaInstall()\r\n      appLoginByArea()\r\n      appLoginActivationByArea()\r\n    }\r\n    const getMapList = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByDistrict({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.mapList = res.data\r\n      data.mapListShow = true\r\n    }\r\n    const representativeTab = (y) => {\r\n      data.cityYear = y\r\n      dutynumList(y)\r\n    }\r\n    const dumplingTab = (y) => {\r\n      data.dumplingYear = y\r\n      delegationScore(y)\r\n    }\r\n    const tabClick = (item) => {\r\n      sessionStorage.setItem('leaderActive', item.value)\r\n      data.active = sessionStorage.getItem('leaderActive')\r\n      if (data.active === '1') {\r\n        organization() // 组织情况\r\n      } else if (data.active === '2') {\r\n        recommendation() // 建议情况\r\n      } else if (data.active === '3') {\r\n        interfaceLocation() // 联络站\r\n      } else if (data.active === '4') {\r\n        PerformanceReport() // 履职报表\r\n      } else if (data.active === '5') {\r\n        runningCondition() // 运行情况\r\n      }\r\n    }\r\n    const onSelect = (item) => {\r\n      data.actionsText = item.text\r\n      data.areaIdStatus = item.id\r\n      dutynumCityList()\r\n    }\r\n    const dynamic = (id, type) => {\r\n      switch (type) {\r\n        case '1':\r\n          data.dynamicId = id\r\n          appLoginActivation(id)\r\n          break\r\n        case '2':\r\n          data.subactive = id\r\n          appLoginActivationByNumTim(id)\r\n          appLoginActivationCity(id)\r\n          appLoginActivationByMemOff(id)\r\n          break\r\n        case '3':\r\n          data.groupActivity = id\r\n          appLoginActivationByTeam(id)\r\n          break\r\n        case '4':\r\n          data.istrictEntry = id\r\n          appLoginByArea(id)\r\n          break\r\n        case '5':\r\n          data.districtActivity = id\r\n          appLoginActivationByArea(id)\r\n          break\r\n      }\r\n    }\r\n    const satisfactionAll = (type) => {\r\n      data.satisfactionStatus = type\r\n      if (data.satisfactionStatus) {\r\n        data.SatisfactionByData = data.SatisfactionBy\r\n      } else {\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getMemberCount = async () => {\r\n      var res = await $api.leaderDriving.memberCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      if (res.data) {\r\n        data.sex = res.data.sex.map((item, index) => {\r\n          return {\r\n            name: item.name,\r\n            value: item.amount,\r\n            key: item.key,\r\n            itemStyle: { color: index === 0 ? '#3da2ff' : '#ff738c' }\r\n          }\r\n        })\r\n        data.birthday = res.data.birthday.map(item => {\r\n          return {\r\n            key: item.key,\r\n            name: item.name,\r\n            value: item.amount\r\n          }\r\n        })\r\n        data.party = res.data.party.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        })\r\n        data.representerElement = res.data.representerElement.map(item => {\r\n          return {\r\n            value: item.amount,\r\n            key: item.key,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n        data.representerTeam = res.data.representerTeam.map(item => {\r\n          return {\r\n            key: item.key,\r\n            value: item.amount,\r\n            name: item.name\r\n          }\r\n        }).reverse()\r\n      }\r\n      // console.log(data.birthday)\r\n      // console.log('getMemberCount', res.data)\r\n    }\r\n    const memberEducation = async () => {\r\n      var res = await $api.leaderDriving.memberEducation({})\r\n      if (res.data) {\r\n        data.memberEducationData = res.data.map(item => {\r\n          return {\r\n            value: item.value,\r\n            name: item.name,\r\n            proportion: item.proportion\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const getOrganization = async () => {\r\n      var res = await $api.leaderDriving.getOrganization({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.officeVos = res.data.officeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.representativeVos = res.data.representativeVos.map(item => {\r\n        return {\r\n          name: item.regionName,\r\n          value: item.regionTotal\r\n        }\r\n      })\r\n      data.generalize[0].num = res.data.totalNumber\r\n      data.generalize[1].num = res.data.representativeNumber\r\n      data.generalize[2].num = res.data.officeNumber\r\n    }\r\n    const memberChange = async () => {\r\n      var res = await $api.leaderDriving.memberChange({})\r\n      data.representative[0].num = res.data.vacantNum\r\n      data.representative[1].num = res.data.repairNum\r\n    }\r\n    const getAdviceByToday = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByToday({ personCode: '' })\r\n      if (res.result) {\r\n        data.AdviceByToday = res.result\r\n      }\r\n    }\r\n    const getAdviceByDomain = async () => {\r\n      var res = await $api.leaderDriving.getAdviceByDomain({})\r\n      if (res.result) {\r\n        data.AdviceByDomain = res.result.reverse()\r\n      }\r\n    }\r\n    const suggestGoLink = (type, mType) => {\r\n      // if (mType) {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&manageType=${mType}&token={{token}}`\r\n      // } else {\r\n      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&token={{token}}`\r\n      // }\r\n    }\r\n    const currentCategory = async () => {\r\n      var res = await $api.leaderDriving.currentCategory({ type: '' })\r\n      if (res.result) {\r\n        data.currentCategoryData = res.result.map(item => {\r\n          return {\r\n            name: item.name,\r\n            proportion: item.proportion,\r\n            value: item.value,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_cate?type=${item.code}&token={{token}}`\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const keywords = async () => {\r\n      var res = await $api.leaderDriving.keywords({})\r\n      if (res) {\r\n        data.keywordsList = res.data.filter(item => item !== '青岛市')\r\n      }\r\n    }\r\n    const getAdviceBySatisfaction = async () => {\r\n      var res = await $api.leaderDriving.getAdviceBySatisfaction({ type: '' })\r\n      var ress = await $api.leaderDriving.getSatisfactionByCategory({ type: '' })\r\n      if (res) {\r\n        data.BySatisfaction = [res.result].map(item => {\r\n          return {\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionBy = ress.result.map(item => {\r\n          return {\r\n            name: item.suggestName,\r\n            satisfaction: {\r\n              num: item.satisfactionNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.satisfaction\r\n            },\r\n            basicallySatisfied: {\r\n              num: item.somewhatSatisfiedNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.somewhatSatisfied\r\n            },\r\n            dissatisfaction: {\r\n              num: item.unsatisfactoryNumber,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&type=${item.suggestCode}&token={{token}}`,\r\n              percentage: item.unsatisfactory\r\n            }\r\n          }\r\n        })\r\n        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)\r\n      }\r\n    }\r\n    const getNumberByRepresentative = async () => {\r\n      var res = await $api.leaderDriving.getNumberByRepresentative({ type: '' })\r\n      if (res) {\r\n        data.ByRepresentative = res.result.map(item => {\r\n          return {\r\n            num: item.issueCount,\r\n            name: item.name,\r\n            url: `http://120.221.72.187:9002/mobile/task/advice_mylist?personCode=${item.userCode}&token={{token}}`\r\n          }\r\n        }).slice(0, 5)\r\n      }\r\n    }\r\n    const getNumberByDelegation = async () => {\r\n      var res = await $api.leaderDriving.getNumberByDelegation({ type: '' })\r\n      if (res) {\r\n        data.ByDelegation = res.result.map(item => {\r\n          if (item.delegationName !== '解放军代表团') {\r\n            return {\r\n              num: item.adviceTotal,\r\n              name: item.delegationName,\r\n              url: `http://120.221.72.187:9002/mobile/task/advice_group?groupId=${item.delegationCode}&token={{token}}`\r\n            }\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const findWygzsTitleList = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })\r\n      data.findWygzsTitleData = res.data\r\n    }\r\n    const findStudioCountByCity = async () => {\r\n      var res = await $api.leaderDriving.findStudioCountByCity({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findStudioCountByCityData = res.data[0]\r\n    }\r\n    const findWygzsTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesCountData = res.data\r\n      data.findWygzsTitlesCountData.num = parseFloat(data.findWygzsTitlesCountData.responseRate.replace('%', ''))\r\n      data.findWygzsTitlesCountShow = true\r\n    }\r\n    const findHotspotKeywords = async () => {\r\n      var res = await $api.leaderDriving.findHotspotKeywords({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findHotspotKeywordsData = res.data.filter(item => item !== '测试')\r\n      // console.log('findHotspotKeywords', res.data)\r\n    }\r\n    const findWygzsTitlesRanking = async () => {\r\n      var res = await $api.leaderDriving.findWygzsTitlesRanking({ memberType: data.SYS_IF_ZX ? '1' : '3' })\r\n      data.findWygzsTitlesRankingData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      })\r\n    }\r\n    const findWygzsStudioTitlesCount = async () => {\r\n      var res = await $api.leaderDriving.findWygzsStudioTitlesCount({})\r\n      data.findWygzsStudioTitlesCountData = res.data.map(item => {\r\n        return {\r\n          num: item.replyCount,\r\n          name: item.name\r\n        }\r\n      }).splice(0, 10)\r\n    }\r\n    const dutynumList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: '1',\r\n        pageSize: '5',\r\n        year: y,\r\n        areaId: data.areaId\r\n      })\r\n      data.dutynumList = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username,\r\n          year: y,\r\n          userid: item.userid\r\n        }\r\n      }).splice(0, 5)\r\n    }\r\n    const delegationScore = async (y) => {\r\n      var res = await $api.leaderDriving.delegationScore({\r\n        pageNo: '1',\r\n        pageSize: '10',\r\n        year: data.years\r\n      })\r\n      data.delegationScore = res.data.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.delegationview\r\n        }\r\n      })\r\n    }\r\n    const dutynumCityList = async (y) => {\r\n      var res = await $api.leaderDriving.dutynumList({\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        year: new Date().getFullYear(),\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.pageNot.text = res && res.errcode !== 200 ? res.errmsg || res.data : ''\r\n      var a = res.data.dutyNumListVos.map(item => {\r\n        return {\r\n          num: item.score,\r\n          id: item.id,\r\n          name: item.username\r\n        }\r\n      })\r\n      data.dutynumCityList = data.dutynumCityList.concat(a)\r\n      var LOAD_MORE = '点击加载更多'\r\n      var LOAD_ALL = '已加载完'\r\n      data.pageNot.text = data.dutynumCityList.length === 0 ? '' : res.data.dutyNumListVos.length >= data.pageSize ? LOAD_MORE : LOAD_ALL\r\n    }\r\n    const appTodayLogin = async (y) => {\r\n      var res = await $api.leaderDriving.appTodayLogin({\r\n        areaId: data.areaId\r\n      })\r\n      data.appToday.todayLoginNum = Number(res.data.todayLoginNum) // 今日登录人数\r\n      data.appToday.rorfNum = Number(res.data.rorfNum) // 较昨日上升或下降\r\n      data.appToday.riseOrFallNum = Number(res.data.riseOrFallNum) // 上升或下降数量\r\n      data.appToday.todayLoginTimes = Number(res.data.todayLoginTimes) // 今日登陆人次\r\n      data.appToday.rorfTime = Number(res.data.rorfTime) // 较昨日上升或下降\r\n      data.appToday.riseOrFallTimes = Number(res.data.riseOrFallTimes) // 上升或下降数量\r\n    }\r\n    const appAllInstall = async (y) => {\r\n      var res = await $api.leaderDriving.appAllInstall({\r\n        areaId: data.areaIdStatus\r\n      })\r\n      data.appToday.num = Number(res.data.rate.replace('%', ''))\r\n    }\r\n    const appLoginActivation = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId == '370215' ? data.areaId : '' }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivation = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appInstall = async () => {\r\n      var res = await $api.leaderDriving.appInstall({ areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.install[0].num = res.data.totalInstall\r\n        data.install[1].num = res.data.memberInstall\r\n        data.install[2].num = res.data.officeInstall\r\n      }\r\n    }\r\n    const areaInstall = async () => {\r\n      var res = await $api.leaderDriving.areaInstall({ areaId: data.areaId })\r\n      if (res) {\r\n        data.areaInstall = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const memberCMemTeamInstallount = async () => {\r\n      var res = await $api.leaderDriving.memberCMemTeamInstallount({ areaId: data.areaId })\r\n      if (res) {\r\n        data.memberCMemTeamInstallount = res.data.map(item => {\r\n          return {\r\n            num: item.value,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByNumTim = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByNumTim({ type: t, areaId: data.areaId }) // eslint-disable-line\r\n      if (res) {\r\n        data.appLoginActivationByNumTim = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            nums: item.times\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationCity = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationCity = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.time,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByMemOff = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByMemOff({ type: t, areaId: data.areaId }) // eslint-disable-line \r\n      if (res) {\r\n        data.appLoginActivationByMemOff = res.data.map(item => {\r\n          return {\r\n            numMem: item.numMem,\r\n            numOff: item.numOff,\r\n            name: item.time,\r\n            activationOff: item.activationOff,\r\n            activationMem: item.activationMem\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginActivationByTeam = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByTeam({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByTeam = res.data.map(item => {\r\n          return {\r\n            num: item.activation,\r\n            name: item.name\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const appLoginByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            times: item.times\r\n          }\r\n        })\r\n        console.log('data.appLoginByArea===>', data.appLoginByArea)\r\n      }\r\n    }\r\n    const appLoginActivationByArea = async (t = '1') => {\r\n      var res = await $api.leaderDriving.appLoginActivationByArea({ type: t, areaId: data.areaId })\r\n      if (res) {\r\n        data.appLoginActivationByArea = res.data.map(item => {\r\n          return {\r\n            num: item.num,\r\n            name: item.name,\r\n            activation: item.activation\r\n          }\r\n        })\r\n      }\r\n    }\r\n    const MessagePage = async (_item) => {\r\n      window.location.href = `http://120.221.72.187:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`\r\n    }\r\n    const massMessagesClick = async () => {\r\n      router.push({ name: 'messageMorePage' })\r\n    }\r\n    const loadMore = async () => {\r\n      var LOAD_MORE = '点击加载更多'\r\n      var NET_ERR = '网络不小心断开了'\r\n      var LOAD_ING = '加载中，请稍候...'\r\n      if ((data.pageNot.text === LOAD_MORE || data.pageNot.text === NET_ERR) && data.pageNo !== 1) {\r\n        data.pageNot.text = LOAD_ING\r\n        data.pageNo++\r\n        dutynumCityList()\r\n      } else {\r\n        data.pageNo = data.pageNo + 1\r\n        dutynumCityList()\r\n      }\r\n    }\r\n    const representativeAll = () => {\r\n      data.isExpanded = !data.isExpanded\r\n    }\r\n    const representativeAll1 = () => {\r\n      data.isExpanded1 = !data.isExpanded1\r\n    }\r\n    const representativeAll2 = () => {\r\n      data.isExpanded2 = !data.isExpanded2\r\n    }\r\n    const representativeClick = (_item) => {\r\n      router.push({ name: 'peopleList', query: { key: _item.key, type: _item.type } })\r\n    }\r\n    return { ...toRefs(data), loadMore, MessagePage, massMessagesClick, representativeAll, representativeAll1, representativeAll2, suggestGoLink, general, confirm, tabClick, representativeTab, dumplingTab, onSelect, dynamic, router, satisfactionAll, representativeClick }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.leaderDriving {\r\n  background: #f8f8f8;\r\n  box-sizing: border-box;\r\n  padding: 15px 10px 10px 10px;\r\n  height: 100%;\r\n\r\n  .satisfaction_title {\r\n    width: 95%;\r\n    margin: 10px 10px 0 10px;\r\n    padding-bottom: 5px;\r\n    border-bottom: 1px solid #d8d8d8;\r\n  }\r\n\r\n  .satisfaction_item {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    >span {\r\n      font-size: 14px;\r\n      display: inline-block;\r\n      width: 25%;\r\n    }\r\n  }\r\n\r\n  .satisfaction_all {\r\n    text-align: center;\r\n    color: #3894ff;\r\n    margin: 15px 0;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .dynamic_tab {\r\n    width: 100%;\r\n    height: 100%;\r\n    text-align: center;\r\n    line-height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n\r\n    .dynamic_tab_item {\r\n      width: 50%;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .dynamic_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .sex_pie1 {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 50%;\r\n      height: 120px;\r\n      // display: flex;\r\n      // flex-direction: column;\r\n      // justify-content: space-around;\r\n      font-size: 16px;\r\n\r\n      .top {\r\n        display: flex;\r\n        // align-items: center;\r\n        flex-direction: column;\r\n        margin-bottom: 10px;\r\n        font-size: 16px;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        // align-items: center;\r\n        font-size: 16px;\r\n        flex-direction: column;\r\n\r\n        p:nth-child(2) {\r\n          font-size: 14px;\r\n          margin-top: 5px;\r\n        }\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .popup_con {\r\n    margin: 0px 10px;\r\n\r\n    .popup_con_title {\r\n      text-align: center;\r\n      font-size: 20px;\r\n      margin: 10px 0;\r\n      font-weight: 700;\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin: 10px 0;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .representative_all {\r\n    width: 100%;\r\n    text-align: center;\r\n    color: #a2a2a2;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .representative_tab {\r\n    width: 94%;\r\n    height: 30px;\r\n    display: flex;\r\n    border: 1px solid #3894ff;\r\n    margin: 0 10px;\r\n\r\n    .representative_tab_item {\r\n      flex: 1;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      color: #3894ff;\r\n      text-align: center;\r\n    }\r\n\r\n    .representative_tab_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .interface_location_box_bot {\r\n    width: 100%;\r\n    height: 80px;\r\n    background: #f8fbfe;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    text-align: center;\r\n\r\n    >div {\r\n      flex: 1;\r\n\r\n      >p {\r\n        margin: 10px 0;\r\n      }\r\n\r\n      p:nth-child(1) {\r\n        color: #8c9fb7;\r\n      }\r\n\r\n      p:nth-child(2) {\r\n        font-weight: 700;\r\n      }\r\n    }\r\n  }\r\n\r\n  .interface_location_box {\r\n    width: 100%;\r\n    height: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 10px;\r\n\r\n    .interface_location_left {\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      height: 100%;\r\n\r\n      .interface_location_left_title {\r\n        color: #747474;\r\n      }\r\n\r\n      .interface_location_left_bot {\r\n        >span {\r\n          font-weight: 700;\r\n          color: #3894ff;\r\n          font-size: 45px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .interface_location_right {\r\n      width: 37%;\r\n      height: 90px;\r\n      position: relative;\r\n      margin-right: 30px;\r\n\r\n      .text {\r\n        position: absolute;\r\n        top: 26px;\r\n        left: 22px;\r\n        text-align: center;\r\n\r\n        >p:nth-child(1) {\r\n          font-weight: 700;\r\n          font-size: 20px;\r\n        }\r\n\r\n        >p:nth-child(2) {\r\n          font-size: 12px;\r\n          color: #a2a2a2;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_satisfaction {\r\n    width: 65%;\r\n    margin: 0 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .satisfaction_item {\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: 14px;\r\n\r\n      >span {\r\n        width: 14px;\r\n        height: 14px;\r\n        display: inline-block;\r\n        margin: 0 5px;\r\n        border-radius: 7px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message_box {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    >img {\r\n      height: 50px;\r\n      width: 50px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n\r\n  .message {\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n    margin: 0 10px;\r\n\r\n    .news_text_box_item {\r\n      display: -webkit-box;\r\n      -webkit-box-orient: vertical;\r\n      -webkit-line-clamp: 1;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      margin: 2px 0;\r\n      font-size: 15px;\r\n    }\r\n\r\n    p:nth-child(1) {\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n\r\n  .messageNull {\r\n    text-align: center;\r\n    height: 100%;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: #aeaeae;\r\n  }\r\n\r\n  .content_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .hotWord {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    height: 35px;\r\n    padding: 5px 10px;\r\n    border-bottom: 1px solid #f0f0f0;\r\n\r\n    .hotWord_item {\r\n      display: flex;\r\n      width: 70%;\r\n      align-items: center;\r\n\r\n      .index {\r\n        margin: 0 10px 0 0;\r\n      }\r\n    }\r\n\r\n    .hotWord_right {\r\n      color: #fff;\r\n      // padding: 3px;\r\n      height: 24px;\r\n      width: 24px;\r\n      line-height: 24px;\r\n      border-radius: 3px;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .suggest_box {\r\n    display: flex;\r\n    color: #fff;\r\n\r\n    .suggest_transaction {\r\n      margin-left: 10px;\r\n      margin-bottom: 5px;\r\n\r\n      >span {\r\n        font-size: 22px;\r\n        margin: 0 5px;\r\n      }\r\n    }\r\n\r\n    .suggest_meet {\r\n      flex: 1;\r\n      margin: 0 5px;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg1.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .suggest_flat {\r\n      margin: 0 5px;\r\n      flex: 1;\r\n      height: 150px;\r\n      background: url(\"../../assets/img/ldjsc_sug_bg2.png\") no-repeat;\r\n      background-size: 100% 100%;\r\n\r\n      .meet_num {\r\n        margin-top: 40px;\r\n        margin-left: 80px;\r\n        margin-bottom: 20px;\r\n\r\n        >span {\r\n          font-size: 22px;\r\n          margin: 0 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .suggest_title {\r\n    height: 24px;\r\n    font-weight: 700;\r\n    font-size: 16px;\r\n    margin: 5px 10px 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    >div {\r\n      width: 29%;\r\n      color: #3894ff;\r\n    }\r\n  }\r\n\r\n  .suggest_num {\r\n    color: #3894ff;\r\n    margin-right: 10px;\r\n\r\n    >span {\r\n      font-size: 28px;\r\n    }\r\n  }\r\n\r\n  .sex_pie {\r\n    width: 100%;\r\n    height: 120px;\r\n    display: flex;\r\n\r\n    // align-items: center;\r\n    .box_left {\r\n      width: 40%;\r\n      height: 120px;\r\n    }\r\n\r\n    .box_right {\r\n      width: 60%;\r\n      height: 120px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-around;\r\n      font-size: 18px;\r\n\r\n      .top {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .bot {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          margin: 0 10px;\r\n        }\r\n\r\n        >div {\r\n          width: 25px;\r\n          height: 30px;\r\n          margin: 0 10px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .leaderDriving_top {\r\n    width: 100%;\r\n    height: 100px;\r\n    // margin: 15px 10px 0;\r\n    background: url(\"../../assets/img/ldjsc_head_bg.png\");\r\n    background-size: 100% 100%;\r\n  }\r\n\r\n  .leaderDriving_tab {\r\n    margin-top: 10px;\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_tab_item {\r\n      width: 60px;\r\n      height: 60px;\r\n      background: #fff;\r\n      border-radius: 30px;\r\n      text-align: center;\r\n      box-sizing: border-box;\r\n      padding: 10px;\r\n      box-shadow: 0px 5px 15px -3px rgba(138, 138, 138, 0.1);\r\n    }\r\n\r\n    .leaderDriving_tab_item_active {\r\n      background: #3894ff;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  .leaderDriving_title {\r\n    width: 100%;\r\n    height: 30px;\r\n    margin: 10px 0;\r\n    color: #3894ff;\r\n    padding-left: 10px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n    position: relative;\r\n  }\r\n\r\n  .leaderDriving_title::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    height: 18px;\r\n    width: 4px;\r\n    top: 4px;\r\n    left: 0px;\r\n    background: #3894ff;\r\n    border-radius: 1px;\r\n  }\r\n\r\n  .leaderDriving_generalize {\r\n    width: 100%;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .leaderDriving_generalize_item {\r\n      // width: 32%;\r\n      flex: 1;\r\n      height: 100%;\r\n      // padding-left: 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .leaderDriving_generalize_item_num {\r\n        font-size: 28px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title {\r\n        color: #8196af;\r\n        // display: flex;\r\n        font-size: 16px;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .leaderDriving_generalize_item_title_span {}\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAwiBA,SAASA,QAAQ,EAAEC,SAAQ,QAAS,YAAW;AAC/C,SAASC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAQ,QAAS,KAAI;AAExD,OAAOC,gBAAe,MAAO,mCAAkC;AAC/D,OAAOC,GAAE,MAAO,sBAAqB;AACrC,OAAOC,GAAE,MAAO,sBAAqB;AACrC,OAAOC,WAAU,MAAO,8BAA6B;AACrD,OAAOC,GAAE,MAAO,sBAAqB;AACrC,OAAOC,IAAG,MAAO,uBAAsB;AACvC,OAAOC,IAAG,MAAO,uBAAsB;AACvC,OAAOC,KAAI,MAAO,wBAAuB;AACzC,OAAOC,KAAI,MAAO,wBAAuB;AACzC,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,IAAG,MAAO,uBAAsB;AACvC,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVZ,gBAAgB;IAChBC,GAAG;IACHC,GAAG;IACHC,WAAW;IACXC,GAAG;IACHC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,KAAK;IACLC,SAAS;IACTC,IAAI;IACJ,CAACG,OAAA,CAAOC,SAAS,CAACH,IAAI,GAAGE,OAAA,CAAOC,SAAS;IACzC,CAACC,QAAA,CAAQJ,IAAI,GAAAI,QAAU;IACvB,CAACC,YAAA,CAAYL,IAAI,GAAAK,YAAc;IAC/B,CAACC,cAAA,CAAcN,IAAI,GAAAM,cAAgB;IACnC,CAACC,eAAA,CAAeP,IAAI,GAAAO,eAAiB;IACrC,CAACC,KAAA,CAAKR,IAAI,GAAAQ,KAAO;IACjB,CAACC,IAAA,CAAIT,IAAI,GAAAS,IAAM;IACf,CAACC,MAAA,CAASV,IAAI,GAAAU,MAAW;IACzB,CAACC,KAAA,CAAKX,IAAI,GAAAW,KAAO;IACjB,CAACC,SAAA,CAASZ,IAAI,GAAAY,SAAW;IACzB,CAACC,OAAA,CAAOb,IAAI,GAAAa,OAAS;IACrB,CAACC,OAAA,CAAOd,IAAI,GAAAc,OAAS;IACrB,CAACC,OAAA,CAAOf,IAAI,GAAAe;EACd,CAAC;EACDC,KAAIA,CAAA,EAAK;IACP,MAAMC,KAAI,GAAIlC,QAAQ,CAAC;IACvB,MAAMmC,MAAK,GAAIlC,SAAS,CAAC;IACzB,MAAMmC,IAAG,GAAIlC,MAAM,CAAC,OAAO;IAC3B,MAAMmC,QAAO,GAAInC,MAAM,CAAC,WAAW;IACnC,MAAMoC,OAAM,GAAIpC,MAAM,CAAC,UAAU;IACjC,MAAMqC,UAAS,GAAIrC,MAAM,CAAC,aAAa;IACvC,MAAMsC,IAAG,GAAItC,MAAM,CAAC,MAAM;IAC1B;IACA,MAAMuC,IAAG,GAAItC,QAAQ,CAAC;MACpBuC,OAAO,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC;MACrBC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAEX,IAAI;MACfY,WAAW,EAAEV,OAAO,CAACG,IAAI,CAACO,WAAW;MACrCX,QAAQ,EAAEA,QAAQ;MAClBE,UAAU,EAAEA,UAAU;MACtBU,UAAU,EAAEf,KAAK,CAACgB,KAAK,CAACD,UAAS,IAAK,EAAE;MACxCE,KAAK,EAAEjB,KAAK,CAACgB,KAAK,CAACC,KAAI,IAAK,EAAE;MAC9BC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;MAChDC,MAAM,EAAEJ,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC;MACpDE,KAAK,EAAE,EAAE;MACTC,YAAY,EAAE,EAAE;MAChBC,KAAK,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/BC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAEV,cAAc,CAACC,OAAO,CAAC,cAAc,KAAK,GAAG;MACrDU,OAAO,EAAE,CAAC;QACRjD,IAAI,EAAE,MAAM;QACZkD,KAAK,EAAE;MACT,CAAC,EAAE;QACDlD,IAAI,EAAE,MAAM;QACZkD,KAAK,EAAE;MACT,CAAC,EAAE;QACDlD,IAAI,EAAE,KAAK;QACXkD,KAAK,EAAE;MACT,CAAC,EAAE;QACDlD,IAAI,EAAE,MAAM;QACZkD,KAAK,EAAE;MACT,CAAC,EAAE;QACDlD,IAAI,EAAE,MAAM;QACZkD,KAAK,EAAE;MACT,CAAC,EAAE;QACDlD,IAAI,EAAE,MAAM;QACZkD,KAAK,EAAE;MACT,CAAC,CAAC;MACFC,UAAU,EAAE,CACV;QACEC,GAAG,EAAE,EAAE;QACPlB,KAAK,EAAE;MACT,CAAC,EACD;QACEkB,GAAG,EAAE,EAAE;QACPlB,KAAK,EAAE;MACT,CAAC,EACD;QACEkB,GAAG,EAAE,EAAE;QACPlB,KAAK,EAAE;MACT,EACD;MACDmB,OAAO,EAAE,CACP;QACED,GAAG,EAAE,GAAG;QACRlB,KAAK,EAAE;MACT,CAAC,EACD;QACEkB,GAAG,EAAE,GAAG;QACRlB,KAAK,EAAE;MACT,CAAC,EACD;QACEkB,GAAG,EAAE,GAAG;QACRlB,KAAK,EAAE;MACT,EACD;MACDoB,cAAc,EAAE,CACd;QACEF,GAAG,EAAE,CAAC;QACNlB,KAAK,EAAE,MAAM;QACbqB,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;MACR,CAAC,EACD;QACEJ,GAAG,EAAE,CAAC;QACNlB,KAAK,EAAE,MAAM;QACbqB,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE;MACR,EACD;MACDC,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;MAC3BC,QAAQ,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7BC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,KAAK;MAClBC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAClCkB,YAAY,EAAE,IAAI;MAClBC,IAAI,EAAE,KAAK;MACXC,UAAU,EAAE,CAAC;QACXjE,IAAI,EAAE,GAAG;QACTkE,EAAE,EAAE;MACN,CAAC,EAAE;QACDlE,IAAI,EAAE,GAAG;QACTkE,EAAE,EAAE;MACN,CAAC,CAAC;MACFC,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE,GAAG;MACdC,aAAa,EAAE,GAAG;MAClBC,YAAY,EAAE,GAAG;MACjBC,gBAAgB,EAAE,GAAG;MACrBC,kBAAkB,EAAE,QAAQ;MAC5BC,kBAAkB,EAAE,KAAK;MACzBC,GAAG,EAAE,EAAE;MACPC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,kBAAkB,EAAE,EAAE;MACtBC,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,iBAAiB,EAAE,EAAE;MACrBC,aAAa,EAAE,GAAG;MAClBC,cAAc,EAAE,EAAE;MAClBC,mBAAmB,EAAE,EAAE;MACvBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,kBAAkB,EAAE,EAAE;MACtBC,gBAAgB,EAAE,EAAE;MACpBC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,yBAAyB,EAAE,CAAC,CAAC;MAC7BC,wBAAwB,EAAE,CAAC,CAAC;MAC5BC,wBAAwB,EAAE,KAAK;MAC/BC,uBAAuB,EAAE,CAAC,CAAC;MAC3BC,0BAA0B,EAAE,EAAE;MAC9BC,8BAA8B,EAAE,EAAE;MAClCC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,eAAe,EAAE,EAAE;MACnBC,QAAQ,EAAE;QACRC,aAAa,EAAE,EAAE;QAAE;QACnBC,OAAO,EAAE,EAAE;QAAE;QACbC,aAAa,EAAE,EAAE;QAAE;QACnBC,eAAe,EAAE,EAAE;QAAE;QACrBC,QAAQ,EAAE,EAAE;QAAE;QACdC,eAAe,EAAE,EAAE;QAAE;QACrBtD,GAAG,EAAE;MACP,CAAC;MACDuD,kBAAkB,EAAE,EAAE;MACtBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,CAAC;MAAE;MACdC,UAAU,EAAE,KAAK;MAAE;MACnBC,WAAW,EAAE,KAAK;MAAE;MACpBC,WAAW,EAAE,KAAK;MAAE;MACpBC,yBAAyB,EAAE,EAAE;MAC7BC,0BAA0B,EAAE,EAAE;MAC9BC,sBAAsB,EAAE,EAAE;MAC1BC,0BAA0B,EAAE,EAAE;MAC9BC,wBAAwB,EAAE,EAAE;MAC5BC,cAAc,EAAE,EAAE;MAClBC,wBAAwB,EAAE;IAC5B,CAAC;IACDpI,SAAS,CAAC,MAAM;MACd,IAAIoC,IAAI,CAACU,KAAK,EAAE;QACduF,QAAQ,CAACvF,KAAI,GAAIV,IAAI,CAACU,KAAI;MAC5B;MACA,MAAMwF,QAAO,GAAItF,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3Df,IAAI,CAACiB,KAAI,GAAIiF,QAAQ,CAACC,GAAG,CAACC,IAAG,IAAK;QAChC,OAAO;UACLlG,IAAI,EAAEkG,IAAI,CAAC5H,IAAI;UACfkE,EAAE,EAAE0D,IAAI,CAAC1D,EAAE;UACXlE,IAAI,EAAE4H,IAAI,CAAC5H;QACb;MACF,CAAC;MACD;MACAwB,IAAI,CAACuB,WAAU,GAAIvB,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC,CAACzC,IAAG;MACpCwB,IAAI,CAACkB,YAAW,GAAIlB,IAAI,CAACiB,KAAK,CAAC,CAAC,CAAC,CAACyB,EAAC;MACnC,IAAI1C,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QACvB6E,YAAY,CAAC,GAAE;MACjB,OAAO,IAAIrG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9B8E,cAAc,CAAC,GAAE;MACnB,OAAO,IAAItG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9B+E,iBAAiB,CAAC,GAAE;MACtB,OAAO,IAAIvG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9BgF,iBAAiB,CAAC,GAAE;MACtB,OAAO,IAAIxG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9BiF,gBAAgB,CAAC,GAAE;MACrB;IACF,CAAC;IACD;IACA,MAAMJ,YAAW,GAAIA,CAAA,KAAM;MACzBK,cAAc,CAAC;MACfC,eAAe,CAAC;MAChBC,eAAe,CAAC;MAChBC,YAAY,CAAC;IACf;IACA;IACA,MAAMP,cAAa,GAAIA,CAAA,KAAM;MAC3BQ,gBAAgB,CAAC;MACjBC,iBAAiB,CAAC;MAClBC,eAAe,CAAC;MAChB9E,QAAQ,CAAC;MACT+E,uBAAuB,CAAC;MACxBC,yBAAyB,CAAC;MAC1BC,qBAAqB,CAAC;IACxB;IACA;IACA,MAAMZ,iBAAgB,GAAIA,CAAA,KAAM;MAC9Ba,UAAU,CAAC;MACXC,kBAAkB,CAAC;MACnBC,qBAAqB,CAAC;MACtBC,oBAAoB,CAAC;MACrBC,mBAAmB,CAAC;MACpBC,sBAAsB,CAAC;MACvBC,0BAA0B,CAAC;IAC7B;IACA;IACA,MAAMlB,iBAAgB,GAAIA,CAAA,KAAM;MAC9B/B,WAAW,CAAC,IAAI;MAChBC,eAAe,CAAC;MAChBC,eAAe,CAAC;IAClB;IACA;IACA,MAAM8B,gBAAe,GAAIA,CAAA,KAAM;MAC7BkB,aAAa,CAAC;MACdC,aAAa,CAAC;MACdzC,kBAAkB,CAAC;MACnBC,UAAU,CAAC;MACXM,yBAAyB,CAAC;MAC1BC,0BAA0B,CAAC;MAC3BC,sBAAsB,CAAC;MACvBC,0BAA0B,CAAC;MAC3BC,wBAAwB,CAAC;MACzBT,WAAW,CAAC;MACZU,cAAc,CAAC;MACfC,wBAAwB,CAAC;IAC3B;IACA,MAAMoB,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAIS,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACC,yBAAyB,CAAC;QAAEC,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MACvGN,IAAI,CAACmC,OAAM,GAAI0F,GAAG,CAAC7H,IAAG;MACtBA,IAAI,CAACoC,WAAU,GAAI,IAAG;IACxB;IACA,MAAM6F,iBAAgB,GAAKC,CAAC,IAAK;MAC/BlI,IAAI,CAACsC,QAAO,GAAI4F,CAAA;MAChBzD,WAAW,CAACyD,CAAC;IACf;IACA,MAAMC,WAAU,GAAKD,CAAC,IAAK;MACzBlI,IAAI,CAACuC,YAAW,GAAI2F,CAAA;MACpBxD,eAAe,CAACwD,CAAC;IACnB;IACA,MAAME,QAAO,GAAKhC,IAAI,IAAK;MACzBtF,cAAc,CAACuH,OAAO,CAAC,cAAc,EAAEjC,IAAI,CAAC1E,KAAK;MACjD1B,IAAI,CAACwB,MAAK,GAAIV,cAAc,CAACC,OAAO,CAAC,cAAc;MACnD,IAAIf,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QACvB6E,YAAY,CAAC,GAAE;MACjB,OAAO,IAAIrG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9B8E,cAAc,CAAC,GAAE;MACnB,OAAO,IAAItG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9B+E,iBAAiB,CAAC,GAAE;MACtB,OAAO,IAAIvG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9BgF,iBAAiB,CAAC,GAAE;MACtB,OAAO,IAAIxG,IAAI,CAACwB,MAAK,KAAM,GAAG,EAAE;QAC9BiF,gBAAgB,CAAC,GAAE;MACrB;IACF;IACA,MAAM6B,QAAO,GAAKlC,IAAI,IAAK;MACzBpG,IAAI,CAACuB,WAAU,GAAI6E,IAAI,CAAClG,IAAG;MAC3BF,IAAI,CAACkB,YAAW,GAAIkF,IAAI,CAAC1D,EAAC;MAC1BiC,eAAe,CAAC;IAClB;IACA,MAAM4D,OAAM,GAAIA,CAAC7F,EAAE,EAAEV,IAAI,KAAK;MAC5B,QAAQA,IAAI;QACV,KAAK,GAAG;UACNhC,IAAI,CAAC2C,SAAQ,GAAID,EAAC;UAClByC,kBAAkB,CAACzC,EAAE;UACrB;QACF,KAAK,GAAG;UACN1C,IAAI,CAAC4C,SAAQ,GAAIF,EAAC;UAClBiD,0BAA0B,CAACjD,EAAE;UAC7BkD,sBAAsB,CAAClD,EAAE;UACzBmD,0BAA0B,CAACnD,EAAE;UAC7B;QACF,KAAK,GAAG;UACN1C,IAAI,CAAC6C,aAAY,GAAIH,EAAC;UACtBoD,wBAAwB,CAACpD,EAAE;UAC3B;QACF,KAAK,GAAG;UACN1C,IAAI,CAAC8C,YAAW,GAAIJ,EAAC;UACrBqD,cAAc,CAACrD,EAAE;UACjB;QACF,KAAK,GAAG;UACN1C,IAAI,CAAC+C,gBAAe,GAAIL,EAAC;UACzBsD,wBAAwB,CAACtD,EAAE;UAC3B;MACJ;IACF;IACA,MAAM8F,eAAc,GAAKxG,IAAI,IAAK;MAChChC,IAAI,CAACiD,kBAAiB,GAAIjB,IAAG;MAC7B,IAAIhC,IAAI,CAACiD,kBAAkB,EAAE;QAC3BjD,IAAI,CAAC+D,kBAAiB,GAAI/D,IAAI,CAAC8D,cAAa;MAC9C,OAAO;QACL9D,IAAI,CAAC+D,kBAAiB,GAAI/D,IAAI,CAAC8D,cAAc,CAAC2E,KAAK,CAAC,CAAC,EAAE,CAAC;MAC1D;IACF;IACA,MAAM/B,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAImB,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACY,WAAW,CAAC;QAAEV,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MACzF,IAAIuH,GAAG,CAAC7H,IAAI,EAAE;QACZA,IAAI,CAACkD,GAAE,GAAI2E,GAAG,CAAC7H,IAAI,CAACkD,GAAG,CAACiD,GAAG,CAAC,CAACC,IAAI,EAAEuC,KAAK,KAAK;UAC3C,OAAO;YACLnK,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfkD,KAAK,EAAE0E,IAAI,CAACwC,MAAM;YAClB7G,GAAG,EAAEqE,IAAI,CAACrE,GAAG;YACb8G,SAAS,EAAE;cAAEC,KAAK,EAAEH,KAAI,KAAM,IAAI,SAAQ,GAAI;YAAU;UAC1D;QACF,CAAC;QACD3I,IAAI,CAACmD,QAAO,GAAI0E,GAAG,CAAC7H,IAAI,CAACmD,QAAQ,CAACgD,GAAG,CAACC,IAAG,IAAK;UAC5C,OAAO;YACLrE,GAAG,EAAEqE,IAAI,CAACrE,GAAG;YACbvD,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfkD,KAAK,EAAE0E,IAAI,CAACwC;UACd;QACF,CAAC;QACD5I,IAAI,CAACoD,KAAI,GAAIyE,GAAG,CAAC7H,IAAI,CAACoD,KAAK,CAAC+C,GAAG,CAACC,IAAG,IAAK;UACtC,OAAO;YACLrE,GAAG,EAAEqE,IAAI,CAACrE,GAAG;YACbL,KAAK,EAAE0E,IAAI,CAACwC,MAAM;YAClBpK,IAAI,EAAE4H,IAAI,CAAC5H;UACb;QACF,CAAC;QACDwB,IAAI,CAACqD,kBAAiB,GAAIwE,GAAG,CAAC7H,IAAI,CAACqD,kBAAkB,CAAC8C,GAAG,CAACC,IAAG,IAAK;UAChE,OAAO;YACL1E,KAAK,EAAE0E,IAAI,CAACwC,MAAM;YAClB7G,GAAG,EAAEqE,IAAI,CAACrE,GAAG;YACbvD,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfuK,UAAU,EAAE3C,IAAI,CAAC2C;UACnB;QACF,CAAC;QACD/I,IAAI,CAACsD,eAAc,GAAIuE,GAAG,CAAC7H,IAAI,CAACsD,eAAe,CAAC6C,GAAG,CAACC,IAAG,IAAK;UAC1D,OAAO;YACLrE,GAAG,EAAEqE,IAAI,CAACrE,GAAG;YACbL,KAAK,EAAE0E,IAAI,CAACwC,MAAM;YAClBpK,IAAI,EAAE4H,IAAI,CAAC5H;UACb;QACF,CAAC,CAAC,CAACwK,OAAO,CAAC;MACb;MACA;MACA;IACF;IACA,MAAMrC,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAIkB,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACnB,eAAe,CAAC,CAAC,CAAC;MACrD,IAAIkB,GAAG,CAAC7H,IAAI,EAAE;QACZA,IAAI,CAACuD,mBAAkB,GAAIsE,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UAC9C,OAAO;YACL1E,KAAK,EAAE0E,IAAI,CAAC1E,KAAK;YACjBlD,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfuK,UAAU,EAAE3C,IAAI,CAAC2C;UACnB;QACF,CAAC;MACH;IACF;IACA,MAAMnC,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAIiB,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAClB,eAAe,CAAC;QAAEoB,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MAC7FN,IAAI,CAACwD,SAAQ,GAAIqE,GAAG,CAAC7H,IAAI,CAACwD,SAAS,CAAC2C,GAAG,CAACC,IAAG,IAAK;QAC9C,OAAO;UACL5H,IAAI,EAAE4H,IAAI,CAAC6C,UAAU;UACrBvH,KAAK,EAAE0E,IAAI,CAAC8C;QACd;MACF,CAAC;MACDlJ,IAAI,CAACyD,iBAAgB,GAAIoE,GAAG,CAAC7H,IAAI,CAACyD,iBAAiB,CAAC0C,GAAG,CAACC,IAAG,IAAK;QAC9D,OAAO;UACL5H,IAAI,EAAE4H,IAAI,CAAC6C,UAAU;UACrBvH,KAAK,EAAE0E,IAAI,CAAC8C;QACd;MACF,CAAC;MACDlJ,IAAI,CAAC2B,UAAU,CAAC,CAAC,CAAC,CAACC,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACmJ,WAAU;MAC5CnJ,IAAI,CAAC2B,UAAU,CAAC,CAAC,CAAC,CAACC,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACoJ,oBAAmB;MACrDpJ,IAAI,CAAC2B,UAAU,CAAC,CAAC,CAAC,CAACC,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACqJ,YAAW;IAC/C;IACA,MAAMxC,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAIgB,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACjB,YAAY,CAAC,CAAC,CAAC;MAClD7G,IAAI,CAAC8B,cAAc,CAAC,CAAC,CAAC,CAACF,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACsJ,SAAQ;MAC9CtJ,IAAI,CAAC8B,cAAc,CAAC,CAAC,CAAC,CAACF,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACuJ,SAAQ;IAChD;IACA,MAAMzC,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAIe,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAChB,gBAAgB,CAAC;QAAE0C,UAAU,EAAE;MAAG,CAAC;MACtE,IAAI3B,GAAG,CAAC4B,MAAM,EAAE;QACdzJ,IAAI,CAAC0D,aAAY,GAAImE,GAAG,CAAC4B,MAAK;MAChC;IACF;IACA,MAAM1C,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAIc,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACf,iBAAiB,CAAC,CAAC,CAAC;MACvD,IAAIc,GAAG,CAAC4B,MAAM,EAAE;QACdzJ,IAAI,CAAC2D,cAAa,GAAIkE,GAAG,CAAC4B,MAAM,CAACT,OAAO,CAAC;MAC3C;IACF;IACA,MAAMU,aAAY,GAAIA,CAAC1H,IAAI,EAAE2H,KAAK,KAAK;MACrC;MACA;MACA;MACA;MACA;IAAA,CACF;IACA,MAAM3C,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAIa,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACd,eAAe,CAAC;QAAEhF,IAAI,EAAE;MAAG,CAAC;MAC/D,IAAI6F,GAAG,CAAC4B,MAAM,EAAE;QACdzJ,IAAI,CAAC4D,mBAAkB,GAAIiE,GAAG,CAAC4B,MAAM,CAACtD,GAAG,CAACC,IAAG,IAAK;UAChD,OAAO;YACL5H,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfuK,UAAU,EAAE3C,IAAI,CAAC2C,UAAU;YAC3BrH,KAAK,EAAE0E,IAAI,CAAC1E,KAAK;YACjBkI,GAAG,EAAE,2DAA2DxD,IAAI,CAACyD,IAAI;UAC3E;QACF,CAAC;MACH;IACF;IACA,MAAM3H,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAI2F,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAC5F,QAAQ,CAAC,CAAC,CAAC;MAC9C,IAAI2F,GAAG,EAAE;QACP7H,IAAI,CAACiC,YAAW,GAAI4F,GAAG,CAAC7H,IAAI,CAAC8J,MAAM,CAAC1D,IAAG,IAAKA,IAAG,KAAM,KAAK;MAC5D;IACF;IACA,MAAMa,uBAAsB,GAAI,MAAAA,CAAA,KAAY;MAC1C,IAAIY,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACb,uBAAuB,CAAC;QAAEjF,IAAI,EAAE;MAAG,CAAC;MACvE,IAAI+H,IAAG,GAAI,MAAMhK,IAAI,CAAC+H,aAAa,CAACkC,yBAAyB,CAAC;QAAEhI,IAAI,EAAE;MAAG,CAAC;MAC1E,IAAI6F,GAAG,EAAE;QACP7H,IAAI,CAAC6D,cAAa,GAAI,CAACgE,GAAG,CAAC4B,MAAM,CAAC,CAACtD,GAAG,CAACC,IAAG,IAAK;UAC7C,OAAO;YACL6D,YAAY,EAAE;cACZrI,GAAG,EAAEwE,IAAI,CAAC8D,kBAAkB;cAC5BN,GAAG,EAAE,iEAAiE,IAAI,kBAAkB;cAC5FO,UAAU,EAAE/D,IAAI,CAAC6D;YACnB,CAAC;YACDG,kBAAkB,EAAE;cAClBxI,GAAG,EAAEwE,IAAI,CAACiE,uBAAuB;cACjCT,GAAG,EAAE,iEAAiE,IAAI,kBAAkB;cAC5FO,UAAU,EAAE/D,IAAI,CAACkE;YACnB,CAAC;YACDC,eAAe,EAAE;cACf3I,GAAG,EAAEwE,IAAI,CAACoE,oBAAoB;cAC9BZ,GAAG,EAAE,iEAAiE,IAAI,kBAAkB;cAC5FO,UAAU,EAAE/D,IAAI,CAACqE;YACnB;UACF;QACF,CAAC;QACDzK,IAAI,CAAC8D,cAAa,GAAIiG,IAAI,CAACN,MAAM,CAACtD,GAAG,CAACC,IAAG,IAAK;UAC5C,OAAO;YACL5H,IAAI,EAAE4H,IAAI,CAACsE,WAAW;YACtBT,YAAY,EAAE;cACZrI,GAAG,EAAEwE,IAAI,CAAC8D,kBAAkB;cAC5BN,GAAG,EAAE,iEAAiE,IAAI,SAASxD,IAAI,CAACuE,WAAW,kBAAkB;cACrHR,UAAU,EAAE/D,IAAI,CAAC6D;YACnB,CAAC;YACDG,kBAAkB,EAAE;cAClBxI,GAAG,EAAEwE,IAAI,CAACiE,uBAAuB;cACjCT,GAAG,EAAE,iEAAiE,IAAI,SAASxD,IAAI,CAACuE,WAAW,kBAAkB;cACrHR,UAAU,EAAE/D,IAAI,CAACkE;YACnB,CAAC;YACDC,eAAe,EAAE;cACf3I,GAAG,EAAEwE,IAAI,CAACoE,oBAAoB;cAC9BZ,GAAG,EAAE,iEAAiE,IAAI,SAASxD,IAAI,CAACuE,WAAW,kBAAkB;cACrHR,UAAU,EAAE/D,IAAI,CAACqE;YACnB;UACF;QACF,CAAC;QACDzK,IAAI,CAAC+D,kBAAiB,GAAI/D,IAAI,CAAC8D,cAAc,CAAC2E,KAAK,CAAC,CAAC,EAAE,CAAC;MAC1D;IACF;IACA,MAAMvB,yBAAwB,GAAI,MAAAA,CAAA,KAAY;MAC5C,IAAIW,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACZ,yBAAyB,CAAC;QAAElF,IAAI,EAAE;MAAG,CAAC;MACzE,IAAI6F,GAAG,EAAE;QACP7H,IAAI,CAACgE,gBAAe,GAAI6D,GAAG,CAAC4B,MAAM,CAACtD,GAAG,CAACC,IAAG,IAAK;UAC7C,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACwE,UAAU;YACpBpM,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfoL,GAAG,EAAE,mEAAmExD,IAAI,CAACyE,QAAQ;UACvF;QACF,CAAC,CAAC,CAACpC,KAAK,CAAC,CAAC,EAAE,CAAC;MACf;IACF;IACA,MAAMtB,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAIU,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACX,qBAAqB,CAAC;QAAEnF,IAAI,EAAE;MAAG,CAAC;MACrE,IAAI6F,GAAG,EAAE;QACP7H,IAAI,CAACiE,YAAW,GAAI4D,GAAG,CAAC4B,MAAM,CAACtD,GAAG,CAACC,IAAG,IAAK;UACzC,IAAIA,IAAI,CAAC0E,cAAa,KAAM,QAAQ,EAAE;YACpC,OAAO;cACLlJ,GAAG,EAAEwE,IAAI,CAAC2E,WAAW;cACrBvM,IAAI,EAAE4H,IAAI,CAAC0E,cAAc;cACzBlB,GAAG,EAAE,+DAA+DxD,IAAI,CAAC4E,cAAc;YACzF;UACF;QACF,CAAC;MACH;IACF;IACA,MAAM3D,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAIQ,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACT,kBAAkB,CAAC;QAAElH,MAAM,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAM,CAAC;MACtFJ,IAAI,CAACkE,kBAAiB,GAAI2D,GAAG,CAAC7H,IAAG;IACnC;IACA,MAAMsH,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAIO,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACR,qBAAqB,CAAC;QAAEU,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MACnGN,IAAI,CAACmE,yBAAwB,GAAI0D,GAAG,CAAC7H,IAAI,CAAC,CAAC;IAC7C;IACA,MAAMuH,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAIM,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACP,oBAAoB,CAAC;QAAES,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MAClGN,IAAI,CAACoE,wBAAuB,GAAIyD,GAAG,CAAC7H,IAAG;MACvCA,IAAI,CAACoE,wBAAwB,CAACxC,GAAE,GAAIqJ,UAAU,CAACjL,IAAI,CAACoE,wBAAwB,CAAC8G,YAAY,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MAC1GnL,IAAI,CAACqE,wBAAuB,GAAI,IAAG;IACrC;IACA,MAAMmD,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAIK,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACN,mBAAmB,CAAC;QAAEQ,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MACjGN,IAAI,CAACsE,uBAAsB,GAAIuD,GAAG,CAAC7H,IAAI,CAAC8J,MAAM,CAAC1D,IAAG,IAAKA,IAAG,KAAM,IAAI;MACpE;IACF;IACA,MAAMqB,sBAAqB,GAAI,MAAAA,CAAA,KAAY;MACzC,IAAII,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACL,sBAAsB,CAAC;QAAEO,UAAU,EAAEhI,IAAI,CAACM,SAAQ,GAAI,GAAE,GAAI;MAAI,CAAC;MACpGN,IAAI,CAACuE,0BAAyB,GAAIsD,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;QACrD,OAAO;UACLxE,GAAG,EAAEwE,IAAI,CAACgF,UAAU;UACpB5M,IAAI,EAAE4H,IAAI,CAAC5H;QACb;MACF,CAAC;IACH;IACA,MAAMkJ,0BAAyB,GAAI,MAAAA,CAAA,KAAY;MAC7C,IAAIG,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACJ,0BAA0B,CAAC,CAAC,CAAC;MAChE1H,IAAI,CAACwE,8BAA6B,GAAIqD,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;QACzD,OAAO;UACLxE,GAAG,EAAEwE,IAAI,CAACgF,UAAU;UACpB5M,IAAI,EAAE4H,IAAI,CAAC5H;QACb;MACF,CAAC,CAAC,CAAC6M,MAAM,CAAC,CAAC,EAAE,EAAE;IACjB;IACA,MAAM5G,WAAU,GAAI,MAAOyD,CAAC,IAAK;MAC/B,IAAIL,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACrD,WAAW,CAAC;QAC7CtE,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,GAAG;QACbkL,IAAI,EAAEpD,CAAC;QACPlH,MAAM,EAAEhB,IAAI,CAACgB;MACf,CAAC;MACDhB,IAAI,CAACyE,WAAU,GAAIoD,GAAG,CAAC7H,IAAI,CAACuL,cAAc,CAACpF,GAAG,CAACC,IAAG,IAAK;QACrD,OAAO;UACLxE,GAAG,EAAEwE,IAAI,CAACoF,KAAK;UACf9I,EAAE,EAAE0D,IAAI,CAAC1D,EAAE;UACXlE,IAAI,EAAE4H,IAAI,CAACqF,QAAQ;UACnBH,IAAI,EAAEpD,CAAC;UACPwD,MAAM,EAAEtF,IAAI,CAACsF;QACf;MACF,CAAC,CAAC,CAACL,MAAM,CAAC,CAAC,EAAE,CAAC;IAChB;IACA,MAAM3G,eAAc,GAAI,MAAOwD,CAAC,IAAK;MACnC,IAAIL,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACpD,eAAe,CAAC;QACjDvE,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,IAAI;QACdkL,IAAI,EAAEtL,IAAI,CAACmB;MACb,CAAC;MACDnB,IAAI,CAAC0E,eAAc,GAAImD,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;QAC1C,OAAO;UACLxE,GAAG,EAAEwE,IAAI,CAACoF,KAAK;UACf9I,EAAE,EAAE0D,IAAI,CAAC1D,EAAE;UACXlE,IAAI,EAAE4H,IAAI,CAACuF;QACb;MACF,CAAC;IACH;IACA,MAAMhH,eAAc,GAAI,MAAOuD,CAAC,IAAK;MACnC,IAAIL,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACrD,WAAW,CAAC;QAC7CtE,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;QACvBkL,IAAI,EAAE,IAAIlK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC9BL,MAAM,EAAEhB,IAAI,CAACkB;MACf,CAAC;MACDlB,IAAI,CAACC,OAAO,CAACC,IAAG,GAAI2H,GAAE,IAAKA,GAAG,CAAC+D,OAAM,KAAM,GAAE,GAAI/D,GAAG,CAACgE,MAAK,IAAKhE,GAAG,CAAC7H,IAAG,GAAI,EAAC;MAC3E,IAAI8L,CAAA,GAAIjE,GAAG,CAAC7H,IAAI,CAACuL,cAAc,CAACpF,GAAG,CAACC,IAAG,IAAK;QAC1C,OAAO;UACLxE,GAAG,EAAEwE,IAAI,CAACoF,KAAK;UACf9I,EAAE,EAAE0D,IAAI,CAAC1D,EAAE;UACXlE,IAAI,EAAE4H,IAAI,CAACqF;QACb;MACF,CAAC;MACDzL,IAAI,CAAC2E,eAAc,GAAI3E,IAAI,CAAC2E,eAAe,CAACoH,MAAM,CAACD,CAAC;MACpD,IAAIE,SAAQ,GAAI,QAAO;MACvB,IAAIC,QAAO,GAAI,MAAK;MACpBjM,IAAI,CAACC,OAAO,CAACC,IAAG,GAAIF,IAAI,CAAC2E,eAAe,CAACuH,MAAK,KAAM,IAAI,EAAC,GAAIrE,GAAG,CAAC7H,IAAI,CAACuL,cAAc,CAACW,MAAK,IAAKlM,IAAI,CAACI,QAAO,GAAI4L,SAAQ,GAAIC,QAAO;IACpI;IACA,MAAMtE,aAAY,GAAI,MAAOO,CAAC,IAAK;MACjC,IAAIL,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACH,aAAa,CAAC;QAC/C3G,MAAM,EAAEhB,IAAI,CAACgB;MACf,CAAC;MACDhB,IAAI,CAAC4E,QAAQ,CAACC,aAAY,GAAIsH,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAAC6E,aAAa,GAAE;MAC7D7E,IAAI,CAAC4E,QAAQ,CAACE,OAAM,GAAIqH,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAAC8E,OAAO,GAAE;MACjD9E,IAAI,CAAC4E,QAAQ,CAACG,aAAY,GAAIoH,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAAC+E,aAAa,GAAE;MAC7D/E,IAAI,CAAC4E,QAAQ,CAACI,eAAc,GAAImH,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAACgF,eAAe,GAAE;MACjEhF,IAAI,CAAC4E,QAAQ,CAACK,QAAO,GAAIkH,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAACiF,QAAQ,GAAE;MACnDjF,IAAI,CAAC4E,QAAQ,CAACM,eAAc,GAAIiH,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAACkF,eAAe,GAAE;IACnE;IACA,MAAM0C,aAAY,GAAI,MAAOM,CAAC,IAAK;MACjC,IAAIL,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACF,aAAa,CAAC;QAC/C5G,MAAM,EAAEhB,IAAI,CAACkB;MACf,CAAC;MACDlB,IAAI,CAAC4E,QAAQ,CAAChD,GAAE,GAAIuK,MAAM,CAACtE,GAAG,CAAC7H,IAAI,CAACqC,IAAI,CAAC8I,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC3D;IACA,MAAMhG,kBAAiB,GAAI,MAAAA,CAAOiH,CAAA,GAAI,GAAG,KAAK;MAC5C,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAC3C,kBAAkB,CAAC;QAAEnD,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB,MAAK,IAAK,QAAO,GAAIhB,IAAI,CAACgB,MAAK,GAAI;MAAG,CAAC,GAAE;MACvH,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAACmF,kBAAiB,GAAI0C,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UAC7C,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACxE,GAAG;YACbpD,IAAI,EAAE4H,IAAI,CAACiG,IAAI;YACfC,UAAU,EAAElG,IAAI,CAACkG;UACnB;QACF,CAAC;MACH;IACF;IACA,MAAMlH,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAIyC,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAC1C,UAAU,CAAC;QAAEpE,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC,GAAE;MACvE,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACD,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACuM,YAAW;QAC1CvM,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACD,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACwM,aAAY;QAC3CxM,IAAI,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAACD,GAAE,GAAIiG,GAAG,CAAC7H,IAAI,CAACyM,aAAY;MAC7C;IACF;IACA,MAAMpH,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAIwC,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACzC,WAAW,CAAC;QAAErE,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC;MACtE,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAACqF,WAAU,GAAIwC,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACtC,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAAC1E,KAAK;YACflD,IAAI,EAAE4H,IAAI,CAAC5H;UACb;QACF,CAAC;MACH;IACF;IACA,MAAMkH,yBAAwB,GAAI,MAAAA,CAAA,KAAY;MAC5C,IAAImC,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACpC,yBAAyB,CAAC;QAAE1E,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC;MACpF,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC0F,yBAAwB,GAAImC,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACpD,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAAC1E,KAAK;YACflD,IAAI,EAAE4H,IAAI,CAAC5H;UACb;QACF,CAAC;MACH;IACF;IACA,MAAMmH,0BAAyB,GAAI,MAAAA,CAAOyG,CAAA,GAAI,GAAG,KAAK;MACpD,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACnC,0BAA0B,CAAC;QAAE3D,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC,GAAE;MAChG,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC2F,0BAAyB,GAAIkC,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACrD,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACxE,GAAG;YACbpD,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfkO,IAAI,EAAEtG,IAAI,CAACuG;UACb;QACF,CAAC;MACH;IACF;IACA,MAAM/G,sBAAqB,GAAI,MAAAA,CAAOwG,CAAA,GAAI,GAAG,KAAK;MAChD,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAC3C,kBAAkB,CAAC;QAAEnD,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC,GAAE;MACxF,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC4F,sBAAqB,GAAIiC,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACjD,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACxE,GAAG;YACbpD,IAAI,EAAE4H,IAAI,CAACiG,IAAI;YACfC,UAAU,EAAElG,IAAI,CAACkG;UACnB;QACF,CAAC;MACH;IACF;IACA,MAAMzG,0BAAyB,GAAI,MAAAA,CAAOuG,CAAA,GAAI,GAAG,KAAK;MACpD,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAACjC,0BAA0B,CAAC;QAAE7D,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC,GAAE;MAChG,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC6F,0BAAyB,GAAIgC,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACrD,OAAO;YACLwG,MAAM,EAAExG,IAAI,CAACwG,MAAM;YACnBC,MAAM,EAAEzG,IAAI,CAACyG,MAAM;YACnBrO,IAAI,EAAE4H,IAAI,CAACiG,IAAI;YACfS,aAAa,EAAE1G,IAAI,CAAC0G,aAAa;YACjCC,aAAa,EAAE3G,IAAI,CAAC2G;UACtB;QACF,CAAC;MACH;IACF;IACA,MAAMjH,wBAAuB,GAAI,MAAAA,CAAOsG,CAAA,GAAI,GAAG,KAAK;MAClD,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAChC,wBAAwB,CAAC;QAAE9D,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC;MAC5F,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC8F,wBAAuB,GAAI+B,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACnD,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACkG,UAAU;YACpB9N,IAAI,EAAE4H,IAAI,CAAC5H;UACb;QACF,CAAC;MACH;IACF;IACA,MAAMuH,cAAa,GAAI,MAAAA,CAAOqG,CAAA,GAAI,GAAG,KAAK;MACxC,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAC/B,cAAc,CAAC;QAAE/D,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC;MAClF,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAAC+F,cAAa,GAAI8B,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACzC,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACxE,GAAG;YACbpD,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACfmO,KAAK,EAAEvG,IAAI,CAACuG;UACd;QACF,CAAC;QACDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEjN,IAAI,CAAC+F,cAAc;MAC5D;IACF;IACA,MAAMC,wBAAuB,GAAI,MAAAA,CAAOoG,CAAA,GAAI,GAAG,KAAK;MAClD,IAAIvE,GAAE,GAAI,MAAM9H,IAAI,CAAC+H,aAAa,CAAC9B,wBAAwB,CAAC;QAAEhE,IAAI,EAAEoK,CAAC;QAAEpL,MAAM,EAAEhB,IAAI,CAACgB;MAAO,CAAC;MAC5F,IAAI6G,GAAG,EAAE;QACP7H,IAAI,CAACgG,wBAAuB,GAAI6B,GAAG,CAAC7H,IAAI,CAACmG,GAAG,CAACC,IAAG,IAAK;UACnD,OAAO;YACLxE,GAAG,EAAEwE,IAAI,CAACxE,GAAG;YACbpD,IAAI,EAAE4H,IAAI,CAAC5H,IAAI;YACf8N,UAAU,EAAElG,IAAI,CAACkG;UACnB;QACF,CAAC;MACH;IACF;IACA,MAAMY,WAAU,GAAI,MAAOC,KAAK,IAAK;MACnCC,MAAM,CAACC,QAAQ,CAACC,IAAG,GAAI,iEAAiEH,KAAK,CAACzK,EAAE,aAAY;IAC9G;IACA,MAAM6K,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC7N,MAAM,CAAC8N,IAAI,CAAC;QAAEhP,IAAI,EAAE;MAAkB,CAAC;IACzC;IACA,MAAMiP,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAIzB,SAAQ,GAAI,QAAO;MACvB,IAAI0B,OAAM,GAAI,UAAS;MACvB,IAAIC,QAAO,GAAI,YAAW;MAC1B,IAAI,CAAC3N,IAAI,CAACC,OAAO,CAACC,IAAG,KAAM8L,SAAQ,IAAKhM,IAAI,CAACC,OAAO,CAACC,IAAG,KAAMwN,OAAO,KAAK1N,IAAI,CAACG,MAAK,KAAM,CAAC,EAAE;QAC3FH,IAAI,CAACC,OAAO,CAACC,IAAG,GAAIyN,QAAO;QAC3B3N,IAAI,CAACG,MAAM,EAAC;QACZwE,eAAe,CAAC;MAClB,OAAO;QACL3E,IAAI,CAACG,MAAK,GAAIH,IAAI,CAACG,MAAK,GAAI;QAC5BwE,eAAe,CAAC;MAClB;IACF;IACA,MAAMiJ,iBAAgB,GAAIA,CAAA,KAAM;MAC9B5N,IAAI,CAACuF,UAAS,GAAI,CAACvF,IAAI,CAACuF,UAAS;IACnC;IACA,MAAMsI,kBAAiB,GAAIA,CAAA,KAAM;MAC/B7N,IAAI,CAACwF,WAAU,GAAI,CAACxF,IAAI,CAACwF,WAAU;IACrC;IACA,MAAMsI,kBAAiB,GAAIA,CAAA,KAAM;MAC/B9N,IAAI,CAACyF,WAAU,GAAI,CAACzF,IAAI,CAACyF,WAAU;IACrC;IACA,MAAMsI,mBAAkB,GAAKZ,KAAK,IAAK;MACrCzN,MAAM,CAAC8N,IAAI,CAAC;QAAEhP,IAAI,EAAE,YAAY;QAAEiC,KAAK,EAAE;UAAEsB,GAAG,EAAEoL,KAAK,CAACpL,GAAG;UAAEC,IAAI,EAAEmL,KAAK,CAACnL;QAAK;MAAE,CAAC;IACjF;IACA,OAAO;MAAE,GAAGrE,MAAM,CAACqC,IAAI,CAAC;MAAEyN,QAAQ;MAAEP,WAAW;MAAEK,iBAAiB;MAAEK,iBAAiB;MAAEC,kBAAkB;MAAEC,kBAAkB;MAAEpE,aAAa;MAAE7J,OAAO;MAAEmO,OAAO;MAAE5F,QAAQ;MAAEH,iBAAiB;MAAEE,WAAW;MAAEG,QAAQ;MAAEC,OAAO;MAAE7I,MAAM;MAAE8I,eAAe;MAAEuF;IAAoB;EAC5Q;AACF", "ignoreList": []}]}
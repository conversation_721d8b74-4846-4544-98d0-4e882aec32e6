<template>
  <div class="commentList">
    <div class="homeAdministratorList-switch">
      <div class="seach-box">
        <van-checkbox v-model="Allchecked"
                      @click="AllcheckedFn">全选</van-checkbox>
        <div class="dels"
             @click="dels">
          <van-icon name="delete-o"
                    class="dels_icon"
                    color="#3799fe" /> 删除
        </div>
      </div>
    </div>
    <!-- 列表区 -->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="30"
                @load="onLoad">
        <div class="homeAdministratorListBox">
          <ul>
            <van-checkbox-group v-model="checkedList"
                                style="all: initial;"
                                direction="horizontal">
              <van-swipe-cell v-for="item in dataList"
                              :key="item.id">
                <template #left>
                  <div claass="left_che">
                    <van-checkbox :name="item.id"
                                  @click.stop></van-checkbox>
                  </div>
                </template>
                <li class="commentBox-li">
                  <div class="commentBox-li-btm">
                    <div class="commentBox-li-btm-left">
                      <img :src="item.userImage || defImg"
                           alt="">
                    </div>
                    <div class="commentBox-li-btm-right">
                      <div class="commentBox-li-btm-right-top">
                        <div class="commentBox-li-btm-right-top-name">{{ item.userName }}</div>
                      </div>
                      <div class="commentBox-li-btm-right-btm">
                        <div class="commentBox-li-btm-right-btm-time">{{ item.messageDate }}</div>
                        <div class="like-and-leave-message">
                          <div class="edit"
                               @click.stop="edit(item)">
                            <van-icon name="records" /> 编辑
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="commentBox-content"
                       v-html="item.messageMessage"></div>
                </li>
                <template #right>
                  <div class="homeAdministratorListBox_right">
                    <van-icon name="delete-o"
                              @click.stop="del(item)"
                              class="icons"
                              color="#389eff" />
                  </div>
                  <!-- <van-button square
                            type="primary"
                            text="收藏" /> -->
                </template>
              </van-swipe-cell>
            </van-checkbox-group>
          </ul>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Image as VanImage, ImagePreview, SwipeCell, Toast, Dialog, Checkbox, CheckboxGroup } from 'vant'
export default ({
  name: 'commentList',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    [Checkbox.name]: Checkbox,
    [CheckboxGroup.name]: CheckboxGroup,
    [SwipeCell.name]: SwipeCell
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      module: route.query.module,
      Allchecked: false,
      id: route.query.id,
      defImg: require('../../../assets/img/icon_def_head_img.png'),
      switchData: [
        {
          id: 1,
          name: '最新发布'
        },
        {
          id: 2,
          name: '最新回复'
        }
      ],
      stateList: {
        1: '待办理',
        2: '已转办',
        3: '已办结',
        4: '退回',
        5: '二次交办',
        6: '已评价',
        11: '正在办理'
      },
      switchAction: 1,
      keyword: '',
      loading: false,
      finished: false,
      user: JSON.parse(sessionStorage.getItem('user')) || '',
      areaId: sessionStorage.getItem('areaId') || '',
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      userName: '',
      roleList: [],
      adminShow: false,
      checkedList: []
    })
    onMounted(() => {
      onRefresh()
    })
    const getList = async () => {
      const { data: List, total } = await $api.RandomClapping.representativecommentList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        messageId: data.id
      })
      data.dataList = data.dataList.concat(List)
      // data.dataList = yiguanzhuLists
      data.total = total
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const switchClick = (id) => {
      data.switchAction = id
      onRefresh()
      // console.log(data.switchAction)
    }

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 打开详情
    const openDetails = (item) => {
      router.push({ name: 'homeAdministratorDetails', query: { id: item.id } })
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? 'representativelike/addLikeApp' : 'representativelike/cancelLike'
      var params = {
        messageId: _id,
        userId: data.user.id,
        areaId: data.areaId
      }
      await $api.general.fabulous({ url, params })
      // onRefresh()
    }
    const downLike = (item) => {
      console.log(item)
      item.isLike = !item.isLike
      if (item.isLike) {
        item.likeCount++
        fabulousInfo(item.isLike, item.id)
      } else {
        item.likeCount--
        fabulousInfo(item.isLike, item.id)
      }
    }
    const uploadClick = () => {
      router.push({ name: 'RandomClappingUpload' })
    }
    const del = (item) => {
      Dialog.confirm({
        message:
          '确定要删除该条评论吗？',
        confirmButtonColor: '#389eff'
      })
        .then(async () => {
          const res = await $api.RandomClapping.representativecommentDels({
            ids: item.id
          })
          if (res.errcode === 200) {
            Toast('删除成功')
            onRefresh()
          }
        })
        .catch(() => {
        })
    }
    const edit = (item) => {
      router.push({ path: '/commentEdit', query: { id: item.id } })
    }
    const AllcheckedFn = () => {
      if (data.Allchecked) {
        data.checkedList = data.dataList.map(item => item.id)
      } else {
        data.checkedList = []
      }
    }
    const dels = (item) => {
      if (data.checkedList.length === 0) {
        Toast('请先选择')
        return
      }
      Dialog.confirm({
        message:
          '确定要删除所选评论吗？',
        confirmButtonColor: '#389eff'
      })
        .then(async () => {
          const res = await $api.RandomClapping.representativecommentDels({
            ids: data.checkedList.join(',')
          })
          if (res.errcode === 200) {
            Toast('删除成功')
            onRefresh()
            data.Allchecked = false
          }
        })
        .catch(() => {
        })
    }
    return { ...toRefs(data), dayjs, dels, route, router, $api, del, AllcheckedFn, edit, switchClick, onRefresh, uploadClick, openDetails, onLoad, downLike, ImagePreview }
  }
})
</script>
<style lang='less'>
.commentList {
  width: 100%;
  background: #f0f6fb;
  overflow: hidden;

  .van-swipe-cell__left {
    padding: 0 0 0 10px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .van-button {}

  .imageBox {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .details-image {
      margin-right: 10px;
    }
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../../assets/font/PingFang Medium_downcc.otf");
  }

  .homeAdministratorList-top {
    width: 100%;
    height: 235px;
    background: url("../../../assets/img/RandomClapping/001.png") no-repeat;
    background-size: 100% 100%;
    position: relative;

    >p {
      position: absolute;
      top: 30px;
      left: 50%;
      transform: translate(-50%, 0);
      color: #fff;
      font-size: 20px;
    }

    .homeAdministratorList-upload {
      position: absolute;
      top: 50%;
      left: 20px;
      width: 139px;
      height: 43px;
      background: #2c87c7;
      border-radius: 22px;
      // opacity: 0.22;
      text-align: center;
      line-height: 43px;

      span {
        font-size: 23px;
        font-weight: 600;
        font-family: PingFangSC-Semibold;
        color: #0d0d0d;
        // text-shadow: 0px 2px 4px rgba(20, 60, 141, 0.4);
        background: linear-gradient(0deg, #abfafb 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .homeAdministratorList-switch {
    background: #fff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 0px;
  }

  .switch-box {
    display: flex;
    align-items: center;

    .switch {
      width: 98px;
      height: 34px;
      text-align: center;
      line-height: 34px;
      background: #ffffff;
      box-shadow: 0px 0px 4px 0px rgba(180, 181, 182, 0.4);
      border-radius: 7px;
      color: #666;
      margin-right: 10px;
    }
  }

  .switch-action {
    width: 98px;
    height: 34px;
    background: #3894ff !important;
    box-shadow: 0px 4px 5px 0px rgba(56, 148, 255, 0.4) !important;
    border-radius: 7px;
    text-align: center;
    line-height: 34px;
    font-size: 17px;
    font-weight: 600;
    color: #fff !important;
  }

  .seach-box {
    width: 350px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .dels {
      color: #3799fe;
      display: flex;
      align-items: center;
      justify-content: center;

      .dels_icon {
        font-size: 20px;
      }
    }

    .van-search {
      padding: 0px !important;

      .van-search__content {
        border-radius: 17px;
      }

      .van-field__control {
        font-size: 14px;
      }

      .van-field__left-icon {
        margin-right: 2px !important;
      }
    }
  }

  .homeAdministratorList-my {
    position: fixed;
    left: 50%;
    bottom: 5%;
    transform: translateX(-50%);
    width: 56px;
    height: 56px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    border-radius: 50%;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 56px;
    text-align: center;
    color: #ffffff;
  }

  .homeAdministratorListBox {
    background: #fff;

    .commentBox-li {
      padding: 12px;
      border-bottom: 1px solid #e8e8e8;

      .commentBox-li-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .commentBox-title {
          width: 70%;
          font-size: 17px;
          color: #333333;
          font-weight: 700;
          font-family: PingFangSC-Medium;
        }

        .commentBox-state {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 21px;
          background: linear-gradient(0deg, #3894ff, #38c3ff);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }

        .commentBox-state2 {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 21px;
          background: linear-gradient(0deg, #f6a531, #fddf2f);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }
      }

      .commentBox-li-btm {
        display: flex;
        align-items: center;
        margin-top: 8px;
        height: 60px;

        .commentBox-li-btm-left {
          img {
            width: 45px;
            height: 55px;
            border-radius: 5px;
            overflow: hidden;
            margin: 0 5px;
          }
        }

        .commentBox-li-btm-right {
          margin-left: 3px;
          width: 100%;
          height: 100%;

          .commentBox-li-btm-right-top {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            margin-top: 5px;

            .commentBox-li-btm-right-top-name {
              font-size: 16px;
              color: #7e7e7e;
              margin-right: 10px;
            }

            .commentBox-li-btm-right-top-position {
              font-size: 13px;
              color: #3291ff;
              background: #e9f3ff;
              padding: 3px 6px;
              border-radius: 2px;
            }
          }

          .commentBox-li-btm-right-btm {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .commentBox-li-btm-right-btm-time {
              font-size: 14px;
              color: #7e7e7e;
            }

            .like-and-leave-message {
              display: flex;
              align-items: center;

              .edit {
                border: 1px solid #429dfb;
                padding: 2px 4px;
                border-radius: 20px;
                color: #429dfb;
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: center;

                .van-icon {
                  font-size: 20px;
                  margin-right: 5px;
                }
              }

              .leave-message {
                margin-left: 10px;
                font-size: 14px;
                display: flex;
                align-items: center;
                color: #7e7e7e;

                .van-icon {
                  font-size: 16px;
                  margin-top: 2px;
                }
              }
            }
          }
        }
      }

      .commentBox-content {
        font-weight: 500;
        font-size: 17px;
        color: #333333;
        line-height: 28px;
        font-family: PingFangSC-Medium;
        padding: 10px 0px;
      }

      .commentBox-reply {
        width: 100%;
        padding: 8px 12px;
        background: #f4f6f8;
        border-radius: 4px;

        .commentBox-reply-title {
          font-weight: 600;
          font-size: 15px;
          color: #000000;
          font-family: PingFangSC-Medium;
          margin-bottom: 5px;

          span {
            font-weight: 500;
            font-size: 15px;
            color: #666;
          }
        }

        .commentBox-reply-content {
          font-weight: 500;
          font-size: 17px;
          color: #333333;
          line-height: 28px;
          font-family: PingFangSC-Medium;
        }
      }
    }

    .homeAdministratorListBox_right {
      width: 80px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icons {
        font-size: 30px;
      }
    }

    .homeAdministratorListBox-li {
      padding: 12px;
      border-bottom: 1px solid #e8e8e8;

      .homeAdministratorListBox-li-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .homeAdministratorListBox-title {
          width: 70%;
          font-size: 17px;
          color: #333333;
          font-weight: 700;
          font-family: PingFangSC-Medium;
        }

        .homeAdministratorListBox-state {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 21px;
          background: linear-gradient(0deg, #3894ff, #38c3ff);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }

        .homeAdministratorListBox-state2 {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 21px;
          background: linear-gradient(0deg, #f6a531, #fddf2f);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }
      }

      .homeAdministratorListBox-li-btm {
        display: flex;
        align-items: center;
        margin-top: 8px;
        height: 60px;

        .homeAdministratorListBox-li-btm-left {
          img {
            width: 45px;
            height: 55px;
            border-radius: 5px;
            overflow: hidden;
            margin: 0 5px;
          }
        }

        .homeAdministratorListBox-li-btm-right {
          margin-left: 3px;
          width: 100%;
          height: 100%;

          .homeAdministratorListBox-li-btm-right-top {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            margin-top: 5px;

            .homeAdministratorListBox-li-btm-right-top-name {
              font-size: 16px;
              color: #7e7e7e;
              margin-right: 10px;
            }

            .homeAdministratorListBox-li-btm-right-top-position {
              font-size: 13px;
              color: #3291ff;
              background: #e9f3ff;
              padding: 3px 6px;
              border-radius: 2px;
            }
          }

          .homeAdministratorListBox-li-btm-right-btm {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .homeAdministratorListBox-li-btm-right-btm-time {
              font-size: 14px;
              color: #7e7e7e;
            }

            .like-and-leave-message {
              display: flex;
              align-items: center;

              .like {
                font-size: 14px;
                display: flex;
                align-items: center;
                color: #7e7e7e;

                .van-icon {
                  font-size: 16px;
                }
              }

              .leave-message {
                margin-left: 10px;
                font-size: 14px;
                display: flex;
                align-items: center;
                color: #7e7e7e;

                .van-icon {
                  font-size: 16px;
                  margin-top: 2px;
                }
              }
            }
          }
        }
      }

      .homeAdministratorListBox-reply {
        width: 100%;
        padding: 8px 12px;
        background: #f4f6f8;
        border-radius: 4px;

        .homeAdministratorListBox-reply-title {
          font-weight: 600;
          font-size: 15px;
          color: #000000;
          font-family: PingFangSC-Medium;
          margin-bottom: 5px;

          span {
            font-weight: 500;
            font-size: 15px;
            color: #666;
          }
        }

        .homeAdministratorListBox-reply-content {
          font-weight: 500;
          font-size: 17px;
          color: #333333;
          line-height: 28px;
          font-family: PingFangSC-Medium;
        }
      }
    }
  }
}
</style>

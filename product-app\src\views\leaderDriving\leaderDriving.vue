<template>
  <div class="leaderDriving">
    <div class="leaderDriving_top">
    </div>
    <div class="leaderDriving_tab">
      <div @click="tabClick(item)"
        :class="{ leaderDriving_tab_item: true, leaderDriving_tab_item_active: active == item.value }"
        v-for="item in tabList" :key="item.value">{{ item.name }}</div>
    </div>
    <div v-if="active == 1">
      <div class="leaderDriving_title">
        全市概括
      </div>
      <leader-driving-box title="组织概括">
        <template v-slot:content>
          <div class="leaderDriving_generalize">
            <div class="leaderDriving_generalize_item" v-for="item, index in generalize" :key="index">
              <div class="leaderDriving_generalize_item_num"
                :style="{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }">
                {{ item.num }}
              </div>
              <div class="leaderDriving_generalize_item_title">
                {{ item.title }}
              </div>
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="总用户量">
        <template v-slot:content>
          <Bar :color="'rgba(60, 150, 255)'" id="bar1" v-if="representativeVos.length" :list="representativeVos"></Bar>
          <Bar :color="'rgba(255, 123, 49)'" id="bar2" v-if="officeVos.length" :list="officeVos"></Bar>
        </template>
      </leader-driving-box>
      <div class="leaderDriving_title">
        青岛市本级
      </div>
      <leader-driving-box title="市代表变动情况">
        <template v-slot:content>
          <div class="leaderDriving_generalize">
            <div class="leaderDriving_generalize_item" v-for="item, index in representative" :key="index">
              <div class="leaderDriving_generalize_item_title">
                <span class="leaderDriving_generalize_item_title_span">{{ item.title }}</span>
                <span v-if="index == 0">
                  <el-icon style="color: #41ce81;">
                    <Bottom style="width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;" />
                  </el-icon>
                </span>
                <span v-else>
                  <el-icon style="color: #ff6da2;">
                    <Top style="width: 0.48rem;height: 0.48rem;margin-bottom: -0.1rem;" />
                  </el-icon>
                </span>
              </div>
              <div class="leaderDriving_generalize_item_num" :style="{ color: index == 0 ? '#41ce81' : '#ff6da2' }">
                {{ index == 0 ? '-' : '+' }}{{ item.num }}
              </div>
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="各代表团人数">
        <template v-slot:content>
          <Bar :color="'rgba(255, 110, 110)'" id="bar3" v-if="representerTeam.length" :list="representerTeam"></Bar>
        </template>
      </leader-driving-box>
      <leader-driving-box title="代表学历分析">
        <template v-slot:content>
          <Radar id="radar1" v-if="memberEducationData.length" :list="memberEducationData"></Radar>
        </template>
      </leader-driving-box>
      <leader-driving-box title="代表年龄分析">
        <template v-slot:content>
          <Pie :id="'pie1'" :list="birthday" v-if="birthday.length"></Pie>
        </template>
      </leader-driving-box>
      <leader-driving-box title="代表性别分析">
        <template v-slot:content>
          <div class="sex_pie">
            <div class="box_left">
              <Pie :id="'pie2'" v-if="sex.length" :list="sex"></Pie>
            </div>
            <div class="box_right" v-if="sex.length">
              <div class="top">
                <div><img :src="require('../../assets/img/man.png')" alt=""></div>
                <!-- {{ sex[0].name }}性{{ sex[0].value }}名 -->
                <span style="color: #3894ff;">{{ parseInt(sex[0].value / (sex[0].value + sex[1].value) * 100) }}%</span>
              </div>
              <div class="bot">
                <div><img :src="require('../../assets/img/woman.png')" alt=""></div>
                <!-- {{ sex[1].name }}性{{ sex[1].value }}名 -->
                <span style="color: #ff8197;">{{ parseInt(sex[1].value / (sex[0].value + sex[1].value) * 100) }}%</span>
              </div>
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="代表结构分析">
        <template v-slot:content>
          <Bar :color="'rgba(60, 150, 255)'" id="bar4" v-if="representerElement.length" :list="representerElement">
          </Bar>
        </template>
      </leader-driving-box>
    </div>
    <div v-if="active == 2">
      <leader-driving-box>
        <template v-slot:content>
          <div class="content_box">
            <div class="suggest_title">
              今日提交总额
            </div>
            <div class="suggest_num">
              <span style="font-weight: 700;">{{ AdviceByToday }}</span>
              件
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            建议总数
          </div>
          <div class="suggest_box">
            <div :class="{ suggest_meet: index == 0, suggest_flat: index == 1 }" v-for="item, index in AdviceByDomain"
              :key="index">
              <div class="meet_num" @click="suggestGoLink(item.suggestionFlag == '平' ? '2' : '1')">
                <span>{{ item.adviceCount }}</span>
                件
              </div>
              <div class="suggest_transaction" @click="suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1020')">
                正在办理<span>{{ item.transacting }}</span>件
              </div>
              <div class="suggest_transaction" @click="suggestGoLink(item.suggestionFlag == '平' ? '2' : '1', '1100')">
                已办结<span>{{ item.transactAccomplish }}</span>件
              </div>
            </div>
          </div>
          <div class="suggest_title">
            类别占比
          </div>
          <Pie :id="'pie3'" v-if="currentCategoryData.length" :list="currentCategoryData"></Pie>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            建议热词
          </div>
          <div class="hotWord" v-for="item, index in keywords" :key="index">
            <div class="hotWord_item">
              <div class="index"
                :style="{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }">
                {{ index + 1 }}</div>
              {{ item }}
            </div>
            <div class="hotWord_right"
              :style="{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }"
              v-if="index + 1 < 4">
              热
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="content_box">
            <div class="suggest_title">
              满意度
            </div>
            <div class="suggest_satisfaction">
              <div class="satisfaction_item" v-for="item, index in ['满意', '基本满意', '不满意']" :key="index">
                <span :style="{ 'background': index == 0 ? '#40cd80' : index == 1 ? '#ffd055' : '#ff6d6d' }"></span>
                {{ item }}
              </div>
            </div>
          </div>
          <div class="satisfaction_title">
            <p>建议满意度</p>
            <template v-if="BySatisfaction.length">
              <memory-bar v-for="item, index in BySatisfaction" :key="index" :item="item"></memory-bar>
            </template>
          </div>
          <div class="satisfaction_title" style="border: 0;">
            <p>类别满意度</p>
            <div class="satisfaction_item" v-for="item, index in SatisfactionByData" :key="index">
              <!-- <span>{{ item.name }}</span> -->
              <memory-bar :item="item"></memory-bar>
            </div>
          </div>
          <div class="satisfaction_all">
            <p v-if="satisfactionStatus" @click="satisfactionAll(false)"><van-icon name="arrow-up" />
              收起</p>
            <p v-if="!satisfactionStatus" @click="satisfactionAll(true)"><van-icon name="arrow-down" />
              点击展开查看更多</p>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            代表提交建议总排行榜
          </div>
          <ranking-list urlType="medal" :dataList="ByRepresentative" :click="true"
            :title="['排行', '姓名', '件数']"></ranking-list>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            代表团提交建议总排行榜
          </div>
          <ranking-list urlType="medal" :dataList="ByDelegation" :click="true"
            :title="['排行', '代表团', '件数']"></ranking-list>
        </template>
      </leader-driving-box>
    </div>
    <div v-if="active == 3">
      <leader-driving-box>
        <template v-slot:content>
          <div class="message_box">
            <img :src="require('../../assets/img/ldjsc_message.png')" alt="">
            <template v-if="findWygzsTitleData && findWygzsTitleData.length != 0">
              <div class="message">
                <div v-for="(item, index) in findWygzsTitleData" :key="index" @click="MessagePage(item)">
                  <div v-if="index < 2" class="news_text_box_item">{{ item.title }}</div>
                </div>
              </div>
              <div style="color: #7e7d7d;padding: 0.1rem;position: absolute;right: 10px;top: 0;"
                v-if="findWygzsTitleData.length >= 2 && (areaId == '370215' || areaId == '370200')"
                @click="massMessagesClick"> >
              </div>
            </template>
            <!-- <div class="message"
                 v-if="findWygzsTitleData.length">
              <p v-for="item,index in findWygzsTitleData"
                 :key="index"
                 v-show="index < 2"><span>{{ item.title }}</span><span v-if="index == 0">></span></p>
            </div> -->
            <template v-else>
              <div class="messageNull">
                暂无数据
              </div>
            </template>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            数据概括
          </div>
          <div class="interface_location_box">
            <div class="interface_location_left">
              <div class="interface_location_left_title">
                联络站总数量
              </div>
              <div class="interface_location_left_bot">
                <span>{{ findStudioCountByCityData.studioCount }}</span>个
              </div>
            </div>
            <div class="interface_location_right">
              <Pie2 v-if="findWygzsTitlesCountShow"
                :datas="{ percentage: findWygzsTitlesCountData.responseRate, num: findWygzsTitlesCountData.num, text: '回复率', }"
                id="pie2"></Pie2>
            </div>
          </div>
          <div class="interface_location_box_bot">
            <div>
              <p>总留言数</p>
              <p>{{ (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) ?
                (findWygzsTitlesCountData.repliedCount + findWygzsTitlesCountData.noRreplyCount) : '暂无数据' }}</p>
            </div>
            <div>
              <p>已回复数</p>
              <p>{{ findWygzsTitlesCountData.repliedCount ? findWygzsTitlesCountData.repliedCount : '暂无数据' }}</p>
            </div>
            <div>
              <p>未回复数</p>
              <p>{{ findWygzsTitlesCountData.noRreplyCount ? findWygzsTitlesCountData.noRreplyCount : '暂无数据' }}</p>
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            联络站分布
          </div>
          <Map v-if="mapListShow" :list="mapList" id="maplist"></Map>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            建议热词
          </div>
          <div class="hotWord" v-for="item, index in findHotspotKeywordsData" :key="index" v-show="index < 5">
            <div class="hotWord_item">
              <div class="index"
                :style="{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '' }">
                {{ index + 1 }}</div>
              {{ item }}
            </div>
            <div class="hotWord_right"
              :style="{ 'background': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : '#ffcf55' }"
              v-if="index + 1 < 4">
              热
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            区市联络站活跃度
          </div>
          <ranking-list urlType="medal" :dataList="findWygzsTitlesRankingData"
            :title="['排行', '联络站', '活跃度']"></ranking-list>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            各区联络站活跃度
          </div>
          <ranking-list urlType="medal" :dataList="findWygzsStudioTitlesCountData"
            :title="['排行', '联络站', '活跃度']"></ranking-list>
        </template>
      </leader-driving-box>
    </div>
    <div v-if="active == 4">
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            市代表标兵
          </div>
          <div class="representative_tab">
            <div :class="{ representative_tab_item: true, representative_tab_active: cityYear == years }"
              @click="representativeTab(new Date().getFullYear())">年度积分</div>
            <div :class="{ representative_tab_item: true, representative_tab_active: cityYear != years }"
              @click="representativeTab('')">总积分</div>
          </div>
          <ranking-list urlType="medal" type="resumption" :dataList="dutynumList"
            :title="['排行', '姓名', '得分']"></ranking-list>
          <div class="representative_all" @click="router.push('/performanceFilesList')">
            查看更多
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            代表团排行
            <van-icon name="question-o" color="#d5d5d5" size="24" @click="show = true" />
          </div>
          <div class="representative_tab">
            <div :class="{ representative_tab_item: true, representative_tab_active: dumplingYear == years }"
              @click="dumplingTab(new Date().getFullYear())">年度积分</div>
            <div :class="{ representative_tab_item: true, representative_tab_active: dumplingYear != years }"
              @click="dumplingTab('')">总积分</div>
          </div>
          <ranking-list urlType="trophy" :dataList="delegationScore" :title="['排行', '代表团', '得分']"></ranking-list>
        </template>
      </leader-driving-box>
      <leader-driving-box>
        <template v-slot:content>
          <div class="suggest_title">
            各区市代表标兵
            <div>
              <van-popover v-model:show="showPopover" placemen="bottom-end" :actions="areas" @select="onSelect">
                <template #reference>
                  <p>{{ actionsText }} <van-icon name="play" style="transform: rotate(90deg);" /></p>
                </template>
              </van-popover>
            </div>
          </div>
          <ranking-list type="resumption" urlType="medal" :dataList="dutynumCityList"
            :title="['排行', '姓名', '得分']"></ranking-list>
          <div class="notText" style="font-size:14px;color: #ccc;" v-html="pageNot.text" @click="loadMore()"></div>
        </template>
      </leader-driving-box>
    </div>
    <div v-if="active == 5">
      <div class="leaderDriving_title">
        全市
      </div>
      <leader-driving-box title="总安装率">
        <template v-slot:content>
          <div class="sex_pie1">
            <div class="box_left">
              <pie-2 v-if="appToday.num" :datas="{ num: appToday.num, text: '总安装率' }" id="pie1"></pie-2>
            </div>
            <div class="box_right">
              <div class="top">
                <p>今日登录人数
                  <span :style="{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }">{{ appToday.todayLoginNum
                  }}</span>
                </p>
                <p :style="{ 'color': appToday.rorfNum === 1 ? '#ff6d6d' : '#40cd80' }"> <van-icon name="down"
                    style="transform: rotate(-90deg)" /> 较昨日{{ appToday.rorfNum === 1 ? '增加' : '下降' }}{{
                      appToday.riseOrFallNum }}
                </p>
              </div>
              <div class="bot">
                <p>今日登录人次
                  <span :style="{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }">{{
                    appToday.todayLoginTimes }}</span>
                </p>
                <p :style="{ 'color': appToday.rorfTime === 1 ? '#ff6d6d' : '#40cd80' }"><van-icon name="down" />
                  较昨日{{ appToday.rorfTime === 1 ? '增加' : '下降' }}{{ appToday.riseOrFallTimes }}</p>
              </div>
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="总活跃度">
        <template v-slot:tab>
          <div class="dynamic_tab">
            <div :class="{ dynamic_tab_item: true, dynamic_tab_item_active: dynamicId == item.id }"
              v-for="item in dynamicTab" :key="item.id" @click="dynamic(item.id, '1')">{{ item.name }}</div>
          </div>
        </template>
        <template v-slot:content>
          <Line id="line1" :list="appLoginActivation" v-if="appLoginActivation.length" :status="dynamicId"></Line>
        </template>
      </leader-driving-box>
      <div class="leaderDriving_title">
        青岛市本级
      </div>
      <leader-driving-box title="安装率">
        <template v-slot:content>
          <div class="leaderDriving_generalize">
            <div class="leaderDriving_generalize_item" v-for="item, index in install" :key="index">
              <div class="leaderDriving_generalize_item_num"
                :style="{ color: index == 0 ? '#3894ff' : index == 1 ? '#4adb47' : '#ff6da2' }">
                {{ item.num }}
              </div>
              <div class="leaderDriving_generalize_item_title">
                {{ item.title }}
              </div>
            </div>
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="代表团安装率排行" color="#ffebcf">
        <template v-slot:content>
          <ranking-list urlType="trophy" color="#fdf6f2"
            :dataList="isExpanded2 ? memberCMemTeamInstallount : memberCMemTeamInstallount.slice(0, showCount)"
            :title="['排行', '代表团', '安装率']"></ranking-list>
          <div class="representative_all" @click="representativeAll2">
            {{ isExpanded2 ? '收起' : '点击查看更多' }}
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="青岛市本级活跃度分析">
        <template v-slot:tab>
          <div class="dynamic_tab">
            <div :class="{ dynamic_tab_item: true, dynamic_tab_item_active: subactive == item.id }"
              v-for="item in dynamicTab" :key="item.id" @click="dynamic(item.id, '2')">{{ item.name }}</div>
          </div>
        </template>
        <template v-slot:content>
          <div class="suggest_title" style="font-weight: 400;">
            登录人数/人次
          </div>
          <line-2 id="lines1" v-if="appLoginActivationByNumTim.length" :status="subactive"
            :list="appLoginActivationByNumTim"></line-2>
          <div class="suggest_title" style="font-weight: 400;">
            活跃度
          </div>
          <Line id="line2" v-if="appLoginActivationCity.length" :status="subactive" :list="appLoginActivationCity">
          </Line>
          <div class="suggest_title" style="font-weight: 400;">
            机关、代表活跃度
          </div>
          <Line id="line3" v-if="appLoginActivationByMemOff.length" :status="subactive"
            :list="appLoginActivationByMemOff"></Line>
        </template>
      </leader-driving-box>
      <leader-driving-box title="代表团活跃度排行" color="#e2eeff">
        <template v-slot:tab>
          <div class="dynamic_tab">
            <div :class="{ dynamic_tab_item: true, dynamic_tab_item_active: groupActivity == item.id }"
              v-for="item in dynamicTab" :key="item.id" @click="dynamic(item.id, '3')"> 本{{ item.name }}</div>
          </div>
        </template>
        <template v-slot:content>
          <ranking-list urlType="trophy"
            :dataList="isExpanded1 ? appLoginActivationByTeam : appLoginActivationByTeam.slice(0, showCount)"
            :title="['排行', '代表团', '活跃度']"></ranking-list>
          <div class="representative_all" @click="representativeAll1">
            {{ isExpanded1 ? '收起' : '点击查看更多' }}
          </div>
        </template>
      </leader-driving-box>
      <div class="leaderDriving_title">
        青岛市各区
      </div>
      <leader-driving-box title="总安装率排名" color="#ffebcf">
        <template v-slot:content>
          <ranking-list urlType="trophy" color="#fdf6f2"
            :dataList="isExpanded ? areaInstall : areaInstall.slice(0, showCount)"
            :title="['排行', '区市', '安装率']"></ranking-list>
          <div class="representative_all" @click="representativeAll">
            {{ isExpanded ? '收起' : '点击查看更多' }}
          </div>
        </template>
      </leader-driving-box>
      <leader-driving-box title="各区市登录情况">
        <template v-slot:tab>
          <div class="dynamic_tab">
            <div :class="{ dynamic_tab_item: true, dynamic_tab_item_active: istrictEntry == item.id }"
              v-for="item in dynamicTab" :key="item.id" @click="dynamic(item.id, '4')">本{{ item.name }}</div>
          </div>
        </template>
        <template v-slot:content>
          <line-2 id="lines2" v-if="appLoginByArea.length" :status="istrictEntry" :list="appLoginByArea"></line-2>
        </template>
      </leader-driving-box>
      <leader-driving-box title="各区市活跃度">
        <template v-slot:tab>
          <div class="dynamic_tab">
            <div :class="{ dynamic_tab_item: true, dynamic_tab_item_active: districtActivity == item.id }"
              v-for="item in dynamicTab" :key="item.id" @click="dynamic(item.id, '5')">
              <div>本{{ item.name }}</div>
            </div>
          </div>
        </template>
        <template v-slot:content>
          <Line id="line4" v-if="appLoginActivationByArea.length" :status="districtActivity"
            :list="appLoginActivationByArea"></Line>
        </template>
      </leader-driving-box>
    </div>
    <div v-if="active == 6">
      <iframe src="http://120.221.72.187:9003/cockpit/#/" frameborder="0"
        style="width: 100%;height: 680px;z-index: 99999;-webkit-overflow-scrolling: touch; overflow: scroll;"></iframe>
    </div>
    <demo></demo>
    <van-popup close-icon="close" round v-model:show="show" closeable :style="{ height: '13%', width: '90%' }">
      <div class="popup_con">
        <div class="popup_con_title">
          提示
        </div>
        <div class="info">代表团积分=代表团中代表之和/总人数</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Circle } from 'vant'
import LeaderDrivingBox from './components/leaderDrivingBox.vue'
import Bar from './components/bar.vue'
import Pie from './components/pie.vue'
import RankingList from './components/rankingList.vue'
import Map from './components/map.vue'
import Line from './components/line.vue'
import Pie2 from './components/pie2.vue'
import Line2 from './components/line2.vue'
import Radar from './components/radar.vue'
import MemoryBar from './components/memoryBar.vue'
import Demo from './components/demo.vue'
export default {
  name: 'leaderDriving',
  components: {
    LeaderDrivingBox,
    Bar,
    Pie,
    RankingList,
    Map,
    Line,
    Pie2,
    Line2,
    Radar,
    MemoryBar,
    Demo,
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Circle.name]: Circle
  },
  setup () {
    const route = useRoute()
    const router = useRouter()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      pageNot: { text: '' },
      pageNo: 1,
      pageSize: 5,
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: JSON.parse(sessionStorage.getItem('areaId')),
      areas: [],
      areaIdStatus: '',
      years: new Date().getFullYear(),
      showPopover: false,
      actionsText: '',
      active: sessionStorage.getItem('leaderActive') || '1',
      tabList: [{
        name: '组织情况',
        value: '1'
      }, {
        name: '建议情况',
        value: '2'
      }, {
        name: '联络站',
        value: '3'
      }, {
        name: '履职报表',
        value: '4'
      }, {
        name: '运行情况',
        value: '5'
      }, {
        name: '信访情况',
        value: '6'
      }],
      generalize: [
        {
          num: '',
          title: '总人数'
        },
        {
          num: '',
          title: '代表人数'
        },
        {
          num: '',
          title: '机关人数'
        }
      ],
      install: [
        {
          num: '0',
          title: '总人数'
        },
        {
          num: '0',
          title: '代表人数'
        },
        {
          num: '0',
          title: '机关人数'
        }
      ],
      representative: [
        {
          num: 1,
          title: '总人数'
        },
        {
          num: 1,
          title: '代表人数'
        }
      ],
      keywordsList: ['教育', '产业链'],
      keywords: ['教育', '产业链', '农业'],
      mapList: [],
      mapListShow: false,
      rate: 50,
      cityYear: new Date().getFullYear(),
      dumplingYear: 2025,
      show: false,
      dynamicTab: [{
        name: '日',
        id: '1'
      }, {
        name: '月',
        id: '2'
      }],
      dynamicId: '1',
      subactive: '1',
      groupActivity: '1',
      istrictEntry: '1',
      districtActivity: '1',
      representativeText: '点击查看更多',
      satisfactionStatus: false,
      sex: [],
      birthday: [],
      party: [],
      representerElement: [],
      representerTeam: [],
      memberEducationData: [],
      officeVos: [],
      representativeVos: [],
      AdviceByToday: '0',
      AdviceByDomain: [],
      currentCategoryData: [],
      BySatisfaction: [],
      SatisfactionBy: [],
      SatisfactionByData: [],
      ByRepresentative: [],
      ByDelegation: [],
      findWygzsTitleData: [],
      findStudioCountByCityData: {},
      findWygzsTitlesCountData: {},
      findWygzsTitlesCountShow: false,
      findHotspotKeywordsData: {},
      findWygzsTitlesRankingData: [],
      findWygzsStudioTitlesCountData: [],
      dutynumList: [],
      delegationScore: [],
      dutynumCityList: [],
      appToday: {
        todayLoginNum: '', // 今日登录人数
        rorfNum: '', // 较昨日上升或下降
        riseOrFallNum: '', // 上升或下降数量
        todayLoginTimes: '', // 今日登陆人次
        rorfTime: '', // 较昨日上升或下降
        riseOrFallTimes: '', // 上升或下降数量
        num: 0
      },
      appLoginActivation: [],
      appInstall: [],
      areaInstall: [],
      showCount: 5, // 控制展示的数据数量，默认为5
      isExpanded: false, // 控制是否展开全部数据，默认为false
      isExpanded1: false, // 控制是否展开全部数据，默认为false
      isExpanded2: false, // 控制是否展开全部数据，默认为false
      memberCMemTeamInstallount: [],
      appLoginActivationByNumTim: [],
      appLoginActivationCity: [],
      appLoginActivationByMemOff: [],
      appLoginActivationByTeam: [],
      appLoginByArea: [],
      appLoginActivationByArea: []
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      const areaList = JSON.parse(sessionStorage.getItem('areas'))
      data.areas = areaList.map(item => {
        return {
          text: item.name,
          id: item.id,
          name: item.name
        }
      })
      // data.areas.splice(0, 1)
      data.actionsText = data.areas[0].name
      data.areaIdStatus = data.areas[0].id
      if (data.active === '1') {
        organization() // 组织情况
      } else if (data.active === '2') {
        recommendation() // 建议情况
      } else if (data.active === '3') {
        interfaceLocation() // 联络站
      } else if (data.active === '4') {
        PerformanceReport() // 履职报表
      } else if (data.active === '5') {
        runningCondition() // 运行情况
      }
    })
    // 组织情况
    const organization = () => {
      getMemberCount()
      memberEducation()
      getOrganization()
      memberChange()
    }
    // 建议情况
    const recommendation = () => {
      getAdviceByToday()
      getAdviceByDomain()
      currentCategory()
      keywords()
      getAdviceBySatisfaction()
      getNumberByRepresentative()
      getNumberByDelegation()
    }
    // 联络站
    const interfaceLocation = () => {
      getMapList()
      findWygzsTitleList()
      findStudioCountByCity()
      findWygzsTitlesCount()
      findHotspotKeywords()
      findWygzsTitlesRanking()
      findWygzsStudioTitlesCount()
    }
    // 履职报表
    const PerformanceReport = () => {
      dutynumList(2025)
      delegationScore()
      dutynumCityList()
    }
    // 运行情况
    const runningCondition = () => {
      appTodayLogin()
      appAllInstall()
      appLoginActivation()
      appInstall()
      memberCMemTeamInstallount()
      appLoginActivationByNumTim()
      appLoginActivationCity()
      appLoginActivationByMemOff()
      appLoginActivationByTeam()
      areaInstall()
      appLoginByArea()
      appLoginActivationByArea()
    }
    const getMapList = async () => {
      var res = await $api.leaderDriving.findStudioCountByDistrict({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      data.mapList = res.data
      data.mapListShow = true
    }
    const representativeTab = (y) => {
      data.cityYear = y
      dutynumList(y)
    }
    const dumplingTab = (y) => {
      data.dumplingYear = y
      delegationScore(y)
    }
    const tabClick = (item) => {
      sessionStorage.setItem('leaderActive', item.value)
      data.active = sessionStorage.getItem('leaderActive')
      if (data.active === '1') {
        organization() // 组织情况
      } else if (data.active === '2') {
        recommendation() // 建议情况
      } else if (data.active === '3') {
        interfaceLocation() // 联络站
      } else if (data.active === '4') {
        PerformanceReport() // 履职报表
      } else if (data.active === '5') {
        runningCondition() // 运行情况
      }
    }
    const onSelect = (item) => {
      data.actionsText = item.text
      data.areaIdStatus = item.id
      dutynumCityList()
    }
    const dynamic = (id, type) => {
      switch (type) {
        case '1':
          data.dynamicId = id
          appLoginActivation(id)
          break
        case '2':
          data.subactive = id
          appLoginActivationByNumTim(id)
          appLoginActivationCity(id)
          appLoginActivationByMemOff(id)
          break
        case '3':
          data.groupActivity = id
          appLoginActivationByTeam(id)
          break
        case '4':
          data.istrictEntry = id
          appLoginByArea(id)
          break
        case '5':
          data.districtActivity = id
          appLoginActivationByArea(id)
          break
      }
    }
    const satisfactionAll = (type) => {
      data.satisfactionStatus = type
      if (data.satisfactionStatus) {
        data.SatisfactionByData = data.SatisfactionBy
      } else {
        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)
      }
    }
    const getMemberCount = async () => {
      var res = await $api.leaderDriving.memberCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      if (res.data) {
        data.sex = res.data.sex.map((item, index) => {
          return {
            name: item.name,
            value: item.amount,
            key: item.key,
            itemStyle: { color: index === 0 ? '#3da2ff' : '#ff738c' }
          }
        })
        data.birthday = res.data.birthday.map(item => {
          return {
            key: item.key,
            name: item.name,
            value: item.amount
          }
        })
        data.party = res.data.party.map(item => {
          return {
            key: item.key,
            value: item.amount,
            name: item.name
          }
        })
        data.representerElement = res.data.representerElement.map(item => {
          return {
            value: item.amount,
            key: item.key,
            name: item.name,
            proportion: item.proportion
          }
        })
        data.representerTeam = res.data.representerTeam.map(item => {
          return {
            key: item.key,
            value: item.amount,
            name: item.name
          }
        }).reverse()
      }
      // console.log(data.birthday)
      // console.log('getMemberCount', res.data)
    }
    const memberEducation = async () => {
      var res = await $api.leaderDriving.memberEducation({})
      if (res.data) {
        data.memberEducationData = res.data.map(item => {
          return {
            value: item.value,
            name: item.name,
            proportion: item.proportion
          }
        })
      }
    }
    const getOrganization = async () => {
      var res = await $api.leaderDriving.getOrganization({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      data.officeVos = res.data.officeVos.map(item => {
        return {
          name: item.regionName,
          value: item.regionTotal
        }
      })
      data.representativeVos = res.data.representativeVos.map(item => {
        return {
          name: item.regionName,
          value: item.regionTotal
        }
      })
      data.generalize[0].num = res.data.totalNumber
      data.generalize[1].num = res.data.representativeNumber
      data.generalize[2].num = res.data.officeNumber
    }
    const memberChange = async () => {
      var res = await $api.leaderDriving.memberChange({})
      data.representative[0].num = res.data.vacantNum
      data.representative[1].num = res.data.repairNum
    }
    const getAdviceByToday = async () => {
      var res = await $api.leaderDriving.getAdviceByToday({ personCode: '' })
      if (res.result) {
        data.AdviceByToday = res.result
      }
    }
    const getAdviceByDomain = async () => {
      var res = await $api.leaderDriving.getAdviceByDomain({})
      if (res.result) {
        data.AdviceByDomain = res.result.reverse()
      }
    }
    const suggestGoLink = (type, mType) => {
      // if (mType) {
      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&manageType=${mType}&token={{token}}`
      // } else {
      //   window.location.href = `http://120.221.72.187:9002/mobile/task/sessionList?type=${type}&token={{token}}`
      // }
    }
    const currentCategory = async () => {
      var res = await $api.leaderDriving.currentCategory({ type: '' })
      if (res.result) {
        data.currentCategoryData = res.result.map(item => {
          return {
            name: item.name,
            proportion: item.proportion,
            value: item.value,
            url: `http://120.221.72.187:9002/mobile/task/advice_cate?type=${item.code}&token={{token}}`
          }
        })
      }
    }
    const keywords = async () => {
      var res = await $api.leaderDriving.keywords({})
      if (res) {
        data.keywordsList = res.data.filter(item => item !== '青岛市')
      }
    }
    const getAdviceBySatisfaction = async () => {
      var res = await $api.leaderDriving.getAdviceBySatisfaction({ type: '' })
      var ress = await $api.leaderDriving.getSatisfactionByCategory({ type: '' })
      if (res) {
        data.BySatisfaction = [res.result].map(item => {
          return {
            satisfaction: {
              num: item.satisfactionNumber,
              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&token={{token}}`,
              percentage: item.satisfaction
            },
            basicallySatisfied: {
              num: item.somewhatSatisfiedNumber,
              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&token={{token}}`,
              percentage: item.somewhatSatisfied
            },
            dissatisfaction: {
              num: item.unsatisfactoryNumber,
              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&token={{token}}`,
              percentage: item.unsatisfactory
            }
          }
        })
        data.SatisfactionBy = ress.result.map(item => {
          return {
            name: item.suggestName,
            satisfaction: {
              num: item.satisfactionNumber,
              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1000}&type=${item.suggestCode}&token={{token}}`,
              percentage: item.satisfaction
            },
            basicallySatisfied: {
              num: item.somewhatSatisfiedNumber,
              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1001}&type=${item.suggestCode}&token={{token}}`,
              percentage: item.somewhatSatisfied
            },
            dissatisfaction: {
              num: item.unsatisfactoryNumber,
              url: `http://120.221.72.187:9002/mobile/task/advice_handling?status=${1002}&type=${item.suggestCode}&token={{token}}`,
              percentage: item.unsatisfactory
            }
          }
        })
        data.SatisfactionByData = data.SatisfactionBy.slice(0, 3)
      }
    }
    const getNumberByRepresentative = async () => {
      var res = await $api.leaderDriving.getNumberByRepresentative({ type: '' })
      if (res) {
        data.ByRepresentative = res.result.map(item => {
          return {
            num: item.issueCount,
            name: item.name,
            url: `http://120.221.72.187:9002/mobile/task/advice_mylist?personCode=${item.userCode}&token={{token}}`
          }
        }).slice(0, 5)
      }
    }
    const getNumberByDelegation = async () => {
      var res = await $api.leaderDriving.getNumberByDelegation({ type: '' })
      if (res) {
        data.ByDelegation = res.result.map(item => {
          if (item.delegationName !== '解放军代表团') {
            return {
              num: item.adviceTotal,
              name: item.delegationName,
              url: `http://120.221.72.187:9002/mobile/task/advice_group?groupId=${item.delegationCode}&token={{token}}`
            }
          }
        })
      }
    }
    const findWygzsTitleList = async () => {
      var res = await $api.leaderDriving.findWygzsTitleList({ pageNo: '1', pageSize: '100' })
      data.findWygzsTitleData = res.data
    }
    const findStudioCountByCity = async () => {
      var res = await $api.leaderDriving.findStudioCountByCity({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      data.findStudioCountByCityData = res.data[0]
    }
    const findWygzsTitlesCount = async () => {
      var res = await $api.leaderDriving.findWygzsTitlesCount({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      data.findWygzsTitlesCountData = res.data
      data.findWygzsTitlesCountData.num = parseFloat(data.findWygzsTitlesCountData.responseRate.replace('%', ''))
      data.findWygzsTitlesCountShow = true
    }
    const findHotspotKeywords = async () => {
      var res = await $api.leaderDriving.findHotspotKeywords({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      data.findHotspotKeywordsData = res.data.filter(item => item !== '测试')
      // console.log('findHotspotKeywords', res.data)
    }
    const findWygzsTitlesRanking = async () => {
      var res = await $api.leaderDriving.findWygzsTitlesRanking({ memberType: data.SYS_IF_ZX ? '1' : '3' })
      data.findWygzsTitlesRankingData = res.data.map(item => {
        return {
          num: item.replyCount,
          name: item.name
        }
      })
    }
    const findWygzsStudioTitlesCount = async () => {
      var res = await $api.leaderDriving.findWygzsStudioTitlesCount({})
      data.findWygzsStudioTitlesCountData = res.data.map(item => {
        return {
          num: item.replyCount,
          name: item.name
        }
      }).splice(0, 10)
    }
    const dutynumList = async (y) => {
      var res = await $api.leaderDriving.dutynumList({
        pageNo: '1',
        pageSize: '5',
        year: y,
        areaId: data.areaId
      })
      data.dutynumList = res.data.dutyNumListVos.map(item => {
        return {
          num: item.score,
          id: item.id,
          name: item.username,
          year: y,
          userid: item.userid
        }
      }).splice(0, 5)
    }
    const delegationScore = async (y) => {
      var res = await $api.leaderDriving.delegationScore({
        pageNo: '1',
        pageSize: '10',
        year: data.years
      })
      data.delegationScore = res.data.map(item => {
        return {
          num: item.score,
          id: item.id,
          name: item.delegationview
        }
      })
    }
    const dutynumCityList = async (y) => {
      var res = await $api.leaderDriving.dutynumList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        year: new Date().getFullYear(),
        areaId: data.areaIdStatus
      })
      data.pageNot.text = res && res.errcode !== 200 ? res.errmsg || res.data : ''
      var a = res.data.dutyNumListVos.map(item => {
        return {
          num: item.score,
          id: item.id,
          name: item.username
        }
      })
      data.dutynumCityList = data.dutynumCityList.concat(a)
      var LOAD_MORE = '点击加载更多'
      var LOAD_ALL = '已加载完'
      data.pageNot.text = data.dutynumCityList.length === 0 ? '' : res.data.dutyNumListVos.length >= data.pageSize ? LOAD_MORE : LOAD_ALL
    }
    const appTodayLogin = async (y) => {
      var res = await $api.leaderDriving.appTodayLogin({
        areaId: data.areaId
      })
      data.appToday.todayLoginNum = Number(res.data.todayLoginNum) // 今日登录人数
      data.appToday.rorfNum = Number(res.data.rorfNum) // 较昨日上升或下降
      data.appToday.riseOrFallNum = Number(res.data.riseOrFallNum) // 上升或下降数量
      data.appToday.todayLoginTimes = Number(res.data.todayLoginTimes) // 今日登陆人次
      data.appToday.rorfTime = Number(res.data.rorfTime) // 较昨日上升或下降
      data.appToday.riseOrFallTimes = Number(res.data.riseOrFallTimes) // 上升或下降数量
    }
    const appAllInstall = async (y) => {
      var res = await $api.leaderDriving.appAllInstall({
        areaId: data.areaIdStatus
      })
      data.appToday.num = Number(res.data.rate.replace('%', ''))
    }
    const appLoginActivation = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId == '370215' ? data.areaId : '' }) // eslint-disable-line
      if (res) {
        data.appLoginActivation = res.data.map(item => {
          return {
            num: item.num,
            name: item.time,
            activation: item.activation
          }
        })
      }
    }
    const appInstall = async () => {
      var res = await $api.leaderDriving.appInstall({ areaId: data.areaId }) // eslint-disable-line
      if (res) {
        data.install[0].num = res.data.totalInstall
        data.install[1].num = res.data.memberInstall
        data.install[2].num = res.data.officeInstall
      }
    }
    const areaInstall = async () => {
      var res = await $api.leaderDriving.areaInstall({ areaId: data.areaId })
      if (res) {
        data.areaInstall = res.data.map(item => {
          return {
            num: item.value,
            name: item.name
          }
        })
      }
    }
    const memberCMemTeamInstallount = async () => {
      var res = await $api.leaderDriving.memberCMemTeamInstallount({ areaId: data.areaId })
      if (res) {
        data.memberCMemTeamInstallount = res.data.map(item => {
          return {
            num: item.value,
            name: item.name
          }
        })
      }
    }
    const appLoginActivationByNumTim = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginActivationByNumTim({ type: t, areaId: data.areaId }) // eslint-disable-line
      if (res) {
        data.appLoginActivationByNumTim = res.data.map(item => {
          return {
            num: item.num,
            name: item.name,
            nums: item.times
          }
        })
      }
    }
    const appLoginActivationCity = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginActivation({ type: t, areaId: data.areaId }) // eslint-disable-line 
      if (res) {
        data.appLoginActivationCity = res.data.map(item => {
          return {
            num: item.num,
            name: item.time,
            activation: item.activation
          }
        })
      }
    }
    const appLoginActivationByMemOff = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginActivationByMemOff({ type: t, areaId: data.areaId }) // eslint-disable-line 
      if (res) {
        data.appLoginActivationByMemOff = res.data.map(item => {
          return {
            numMem: item.numMem,
            numOff: item.numOff,
            name: item.time,
            activationOff: item.activationOff,
            activationMem: item.activationMem
          }
        })
      }
    }
    const appLoginActivationByTeam = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginActivationByTeam({ type: t, areaId: data.areaId })
      if (res) {
        data.appLoginActivationByTeam = res.data.map(item => {
          return {
            num: item.activation,
            name: item.name
          }
        })
      }
    }
    const appLoginByArea = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginByArea({ type: t, areaId: data.areaId })
      if (res) {
        data.appLoginByArea = res.data.map(item => {
          return {
            num: item.num,
            name: item.name,
            times: item.times
          }
        })
        console.log('data.appLoginByArea===>', data.appLoginByArea)
      }
    }
    const appLoginActivationByArea = async (t = '1') => {
      var res = await $api.leaderDriving.appLoginActivationByArea({ type: t, areaId: data.areaId })
      if (res) {
        data.appLoginActivationByArea = res.data.map(item => {
          return {
            num: item.num,
            name: item.name,
            activation: item.activation
          }
        })
      }
    }
    const MessagePage = async (_item) => {
      window.location.href = `http://120.221.72.187:81/zht-meeting-app/#/messageDetails?&id=${_item.id}&isApp=true`
    }
    const massMessagesClick = async () => {
      router.push({ name: 'messageMorePage' })
    }
    const loadMore = async () => {
      var LOAD_MORE = '点击加载更多'
      var NET_ERR = '网络不小心断开了'
      var LOAD_ING = '加载中，请稍候...'
      if ((data.pageNot.text === LOAD_MORE || data.pageNot.text === NET_ERR) && data.pageNo !== 1) {
        data.pageNot.text = LOAD_ING
        data.pageNo++
        dutynumCityList()
      } else {
        data.pageNo = data.pageNo + 1
        dutynumCityList()
      }
    }
    const representativeAll = () => {
      data.isExpanded = !data.isExpanded
    }
    const representativeAll1 = () => {
      data.isExpanded1 = !data.isExpanded1
    }
    const representativeAll2 = () => {
      data.isExpanded2 = !data.isExpanded2
    }

    return { ...toRefs(data), loadMore, MessagePage, massMessagesClick, representativeAll, representativeAll1, representativeAll2, suggestGoLink, general, confirm, tabClick, representativeTab, dumplingTab, onSelect, dynamic, router, satisfactionAll }
  }
}
</script>
<style lang="less" scoped>
.leaderDriving {
  background: #f8f8f8;
  box-sizing: border-box;
  padding: 15px 10px 10px 10px;
  height: 100%;

  .satisfaction_title {
    width: 95%;
    margin: 10px 10px 0 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #d8d8d8;
  }

  .satisfaction_item {
    display: flex;
    align-items: center;

    >span {
      font-size: 14px;
      display: inline-block;
      width: 25%;
    }
  }

  .satisfaction_all {
    text-align: center;
    color: #3894ff;
    margin: 15px 0;
    font-size: 14px;
  }

  .dynamic_tab {
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 30px;
    display: flex;
    border: 1px solid #3894ff;

    .dynamic_tab_item {
      width: 50%;
      font-weight: 400;
    }

    .dynamic_tab_item_active {
      background: #3894ff;
      color: #fff;
    }
  }

  .sex_pie1 {
    width: 100%;
    height: 120px;
    display: flex;
    justify-content: space-between;

    // align-items: center;
    .box_left {
      width: 40%;
      height: 120px;
    }

    .box_right {
      width: 50%;
      height: 120px;
      // display: flex;
      // flex-direction: column;
      // justify-content: space-around;
      font-size: 16px;

      .top {
        display: flex;
        // align-items: center;
        flex-direction: column;
        margin-bottom: 10px;
        font-size: 16px;

        p:nth-child(2) {
          font-size: 14px;
          margin-top: 5px;
        }

        span {
          margin: 0 10px;
        }
      }

      .bot {
        display: flex;
        // align-items: center;
        font-size: 16px;
        flex-direction: column;

        p:nth-child(2) {
          font-size: 14px;
          margin-top: 5px;
        }

        span {
          margin: 0 10px;
        }
      }
    }
  }

  .popup_con {
    margin: 0px 10px;

    .popup_con_title {
      text-align: center;
      font-size: 20px;
      margin: 10px 0;
      font-weight: 700;
    }

    .info {
      font-size: 14px;
      margin: 10px 0;
      text-align: center;
    }
  }

  .representative_all {
    width: 100%;
    text-align: center;
    color: #a2a2a2;
    margin-bottom: 10px;
  }

  .representative_tab {
    width: 94%;
    height: 30px;
    display: flex;
    border: 1px solid #3894ff;
    margin: 0 10px;

    .representative_tab_item {
      flex: 1;
      height: 30px;
      line-height: 30px;
      color: #3894ff;
      text-align: center;
    }

    .representative_tab_active {
      background: #3894ff;
      color: #fff;
    }
  }

  .interface_location_box_bot {
    width: 100%;
    height: 80px;
    background: #f8fbfe;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;

    >div {
      flex: 1;

      >p {
        margin: 10px 0;
      }

      p:nth-child(1) {
        color: #8c9fb7;
      }

      p:nth-child(2) {
        font-weight: 700;
      }
    }
  }

  .interface_location_box {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;

    .interface_location_left {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 100%;

      .interface_location_left_title {
        color: #747474;
      }

      .interface_location_left_bot {
        >span {
          font-weight: 700;
          color: #3894ff;
          font-size: 45px;
        }
      }
    }

    .interface_location_right {
      width: 37%;
      height: 90px;
      position: relative;
      margin-right: 30px;

      .text {
        position: absolute;
        top: 26px;
        left: 22px;
        text-align: center;

        >p:nth-child(1) {
          font-weight: 700;
          font-size: 20px;
        }

        >p:nth-child(2) {
          font-size: 12px;
          color: #a2a2a2;
        }
      }
    }
  }

  .suggest_satisfaction {
    width: 65%;
    margin: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .satisfaction_item {
      display: flex;
      align-items: center;
      font-size: 14px;

      >span {
        width: 14px;
        height: 14px;
        display: inline-block;
        margin: 0 5px;
        border-radius: 7px;
      }
    }
  }

  .message_box {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
    padding: 10px;
    position: relative;

    >img {
      height: 50px;
      width: 50px;
      margin-right: 10px;
    }
  }

  .message {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0 10px;

    .news_text_box_item {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 2px 0;
      font-size: 15px;
    }

    p:nth-child(1) {
      display: flex;
      justify-content: space-between;
    }
  }

  .messageNull {
    text-align: center;
    height: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #aeaeae;
  }

  .content_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .hotWord {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 35px;
    padding: 5px 10px;
    border-bottom: 1px solid #f0f0f0;

    .hotWord_item {
      display: flex;
      width: 70%;
      align-items: center;

      .index {
        margin: 0 10px 0 0;
      }
    }

    .hotWord_right {
      color: #fff;
      // padding: 3px;
      height: 24px;
      width: 24px;
      line-height: 24px;
      border-radius: 3px;
      font-size: 14px;
      text-align: center;
    }
  }

  .suggest_box {
    display: flex;
    color: #fff;

    .suggest_transaction {
      margin-left: 10px;
      margin-bottom: 5px;

      >span {
        font-size: 22px;
        margin: 0 5px;
      }
    }

    .suggest_meet {
      flex: 1;
      margin: 0 5px;
      height: 150px;
      background: url("../../assets/img/ldjsc_sug_bg1.png") no-repeat;
      background-size: 100% 100%;

      .meet_num {
        margin-top: 40px;
        margin-left: 80px;
        margin-bottom: 20px;

        >span {
          font-size: 22px;
          margin: 0 5px;
        }
      }
    }

    .suggest_flat {
      margin: 0 5px;
      flex: 1;
      height: 150px;
      background: url("../../assets/img/ldjsc_sug_bg2.png") no-repeat;
      background-size: 100% 100%;

      .meet_num {
        margin-top: 40px;
        margin-left: 80px;
        margin-bottom: 20px;

        >span {
          font-size: 22px;
          margin: 0 5px;
        }
      }
    }
  }

  .suggest_title {
    height: 24px;
    font-weight: 700;
    font-size: 16px;
    margin: 5px 10px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    >div {
      width: 29%;
      color: #3894ff;
    }
  }

  .suggest_num {
    color: #3894ff;
    margin-right: 10px;

    >span {
      font-size: 28px;
    }
  }

  .sex_pie {
    width: 100%;
    height: 120px;
    display: flex;

    // align-items: center;
    .box_left {
      width: 40%;
      height: 120px;
    }

    .box_right {
      width: 60%;
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-size: 18px;

      .top {
        display: flex;
        align-items: center;

        span {
          margin: 0 10px;
        }

        >div {
          width: 25px;
          height: 30px;
          margin: 0 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .bot {
        display: flex;
        align-items: center;

        span {
          margin: 0 10px;
        }

        >div {
          width: 25px;
          height: 30px;
          margin: 0 10px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .leaderDriving_top {
    width: 100%;
    height: 100px;
    // margin: 15px 10px 0;
    background: url("../../assets/img/ldjsc_head_bg.png");
    background-size: 100% 100%;
  }

  .leaderDriving_tab {
    margin-top: 10px;
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .leaderDriving_tab_item {
      width: 60px;
      height: 60px;
      background: #fff;
      border-radius: 30px;
      text-align: center;
      box-sizing: border-box;
      padding: 10px;
      box-shadow: 0px 5px 15px -3px rgba(138, 138, 138, 0.1);
    }

    .leaderDriving_tab_item_active {
      background: #3894ff;
      color: #fff;
    }
  }

  .leaderDriving_title {
    width: 100%;
    height: 30px;
    margin: 10px 0;
    color: #3894ff;
    padding-left: 10px;
    font-size: 20px;
    font-weight: 600;
    position: relative;
  }

  .leaderDriving_title::before {
    content: "";
    position: absolute;
    height: 18px;
    width: 4px;
    top: 4px;
    left: 0px;
    background: #3894ff;
    border-radius: 1px;
  }

  .leaderDriving_generalize {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .leaderDriving_generalize_item {
      // width: 32%;
      flex: 1;
      height: 100%;
      // padding-left: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .leaderDriving_generalize_item_num {
        font-size: 28px;
      }

      .leaderDriving_generalize_item_title {
        color: #8196af;
        // display: flex;
        font-size: 16px;
        line-height: 20px;
      }

      .leaderDriving_generalize_item_title_span {}
    }
  }
}
</style>

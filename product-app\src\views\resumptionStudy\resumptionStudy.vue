<template>
  <div class="resumptionStudy">
    <div class="resumptionStudy_top">
      <img :src="require('../../assets/img/huiText2.png')"
           alt=""
           class="resumptionStudy_top_left">
      <img :src="require('../../assets/img/BookImg.png')"
           alt=""
           class="resumptionStudy_top_right">
      <div class="resumptionStudy_top_search"
           @click="router.push('/searchBook')">
        <van-icon name="search" />
        <input type="text"
               placeholder="请输入搜索内容">
      </div>
    </div>
    <div class="resumptionStudy_column">
      <div class="resumptionStudy_column_list"
           v-for="item in columnList"
           :key="item.id"
           @click="clickToView('columnList',item.id)">
        <p class="text">{{item.name}}</p>
        <p>点击查看</p>
      </div>
      <div class="resumptionStudy_column_list"
           @click="clickToView('bookDeskHome')">
        <p class="text">我的书架</p>
        <p>点击查看</p>
      </div>
    </div>
    <!-- 电子书库 -->
    <div class="resumptionStudy_stack">
      <div class="resumptionStudy_stack_top">
        <div class="resumptionStudy_stack_top_title">
          电子书库
        </div>
        <div class="resumptionStudy_stack_top_All"
             @click="router.push('/searchBook')">
          更多 >
        </div>
      </div>
      <div class="resumptionStudy_stack_con">
        <div class="resumptionStudy_stack_con_li"
             v-for="item in BookList"
             :key="item.id"
             @click="bookDetail(item.id)">
          <img :src="item.coverImgUrl"
               alt=""
               class="resumptionStudy_stack_con_img">
          <div class="resumptionStudy_stack_con_li_title">{{ item.bookName }}</div>
          <div class="resumptionStudy_stack_con_li_name">{{ item.authorName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
export default ({
  name: 'schedule',
  props: {},
  components: {},
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const data = reactive({
      module: route.query.module,
      columnList: [], // 栏目列表
      BookList: [] // 书籍列表
    })
    onMounted(() => {
      getColumn()
      getBookList()
    })
    // 获取栏目列表
    const getColumn = async () => {
      const res = await $api.resumptionStudy.getColumnList({
        pageNo: 1,
        pageSize: 10,
        module: data.module || 3,
        isPushApp: 1
      })
      // console.log(res)
      data.columnList = res.data
    }
    // 点击栏目跳更多
    const clickToView = (type, id) => {
      if (type === 'bookDeskHome') {
        router.push({ name: type })
        return false
      }
      router.push({ name: 'resumptionStudyColumnList', query: { type, id } })
    }
    // 获取书籍
    const getBookList = async () => {
      const res = await $api.bookAcademy.getBookList({ pageNo: 1, pageSize: 6, isIssue: 1 })
      // console.log(res)
      data.BookList = res.data
    }
    // 图书详情 /bookDetail
    const bookDetail = (id) => {
      router.push({ name: 'bookDetail', query: { id } })
    }
    return { ...toRefs(data), dayjs, route, router, $api, clickToView, bookDetail }
  }
})
</script>
<style lang='less' scoped>
.resumptionStudy {
  width: 100%;
  background: #f0f6fb;
  overflow: hidden;
  .resumptionStudy_stack {
    width: 88%;
    margin: 0 auto;
    padding: 10px 10px 80px 10px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 10px;
    .resumptionStudy_stack_top {
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      .resumptionStudy_stack_top_title {
        font-style: 18px;
        font-weight: 700;
      }
      .resumptionStudy_stack_top_All {
        font-size: 12px;
        font-weight: 700;
        color: #adadad;
      }
    }
    .resumptionStudy_stack_con {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      .resumptionStudy_stack_con_li {
        width: 32%;
        height: 180px;
        > img {
          width: 100%;
          height: 75%;
        }
        .resumptionStudy_stack_con_li_title {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .resumptionStudy_stack_con_li_name {
          width: 100%;
          font-size: 14px;
          color: #adadad;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .resumptionStudy_column {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-around;
    padding: 20px;
    box-sizing: border-box;
    .resumptionStudy_column_list {
      width: 48%;
      height: 100px;
      margin: 5px 0;
      border-radius: 10px;
      background: rebeccapurple;
      text-align: center;
      background: url(../../assets/img/columnImg.png);
      background-size: 100% 100%;
      .text {
        margin-top: 25px;
        font-size: 17px;
        font-weight: bold;
        color: #4099ff;
      }
      > p {
        font-size: 14px;
        color: #4099ff;
      }
    }
  }
  .resumptionStudy_top {
    width: 100%;
    height: 180px;
    padding: 20px;
    box-sizing: border-box;
    .resumptionStudy_top_left {
      width: 180px;
      height: 110px;
    }
    .resumptionStudy_top_right {
      width: 80;
      height: 100px;
      margin-left: 10px;
    }
    .resumptionStudy_top_search {
      width: 100%;
      margin: 0 auto;
      background: #fff;
      height: 40px;
      display: flex;
      align-items: center;
      border-radius: 10px;
      padding: 0 10px;
    }
  }
}
</style>

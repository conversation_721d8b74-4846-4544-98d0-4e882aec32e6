<template>
  <div class="seachTurnToHold">
    <van-nav-bar title="地区切换"
                 class="seachTurnToHold_top"
                 left-text=""
                 right-text="确定"
                 left-arrow
                 @click-left="navclickleft"
                 @click-right="Submit" />
    <div class="top_title">
      <div class="top_title_left">区市</div>
      <div class="top_title_right">街道</div>
    </div>
    <van-tree-select v-model:active-id="activeId"
                     @click-nav="navClick"
                     @click-item="itemClick"
                     :max="1"
                     height="700"
                     v-model:main-active-index="activeIndex"
                     :items="areaTree" />
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ImagePreview, Uploader, Image as VanImage, TreeSelect, NavBar } from 'vant'
export default ({
  name: 'seachTurnToHold',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    [TreeSelect.name]: TreeSelect,
    [Uploader.name]: Uploader,
    [NavBar.name]: NavBar
  },
  setup () {
    const router = useRouter()
    const store = useStore()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const data = reactive({
      module: route.query.module,
      id: route.query.id,
      status: route.query.status,
      activeId: '',
      activeIndex: '0',
      title: '',
      content: '',
      address: '',
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      areaIds: '370200',
      username: '',
      mobile: '',
      longitude: '',
      dimensionality: '',
      township: '',
      Uploadmax: 3,
      UploadData: [],
      attachmentIds: [],
      fileList: [],
      file: null,
      image: null,
      images: [],
      mapVisible: false,
      selectTitleList: [],
      selectHome: {},
      messageDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      standbyTwo: '',
      areaTree: [
        // { text: '青岛市', id: '370200', children: [] },
      ],
      areaTrees: [],
      seachArea: {},
      navindex: '0',
      navItem: {},
      areaList: []
    })
    onMounted(() => {
      console.log(data.user, 'user')
      data.username = data.user.userName
      data.mobile = data.user.mobile
      data.seachArea = JSON.parse(sessionStorage.getItem('seachArea'))
      getInfo()
    })
    const getInfo = async () => {
      const { data: { cityNames, districtNames } } = await $api.general.nologin({ codes: 'cityNames,districtNames' })
      data.cityNames = cityNames
      data.districtNames = districtNames
      data.areaList = [
        { text: '青岛市' + cityNames, id: '370200', children: [] },
        { text: '市南区' + districtNames, id: '370202', children: [] },
        { text: '市北区' + districtNames, id: '370203', children: [] },
        { text: '李沧区' + districtNames, id: '370213', children: [] },
        { text: '崂山区' + districtNames, id: '370212', children: [] },
        { text: '黄岛区' + districtNames, id: '370211', children: [] },
        { text: '城阳区' + districtNames, id: '370214', children: [] },
        { text: '即墨区' + districtNames, id: '370215', children: [] },
        { text: '胶州市' + districtNames, id: '370281', children: [] },
        { text: '平度市' + districtNames, id: '370283', children: [] },
        { text: '莱西市' + districtNames, id: '370285', children: [] }
      ]
      var arr = [
        { text: '青岛市', id: '370200', children: [] },
        { text: '市南区', id: '370202', children: [] },
        { text: '市北区', id: '370203', children: [] },
        { text: '李沧区', id: '370213', children: [] },
        { text: '崂山区', id: '370212', children: [] },
        { text: '黄岛区', id: '370211', children: [] },
        { text: '城阳区', id: '370214', children: [] },
        { text: '即墨区', id: '370215', children: [] },
        { text: '胶州市', id: '370281', children: [] },
        { text: '平度市', id: '370283', children: [] },
        { text: '莱西市', id: '370285', children: [] }
      ]
      data.areaTree = arr
      data.activeIndex = data.seachArea.activeIndex ? data.seachArea.activeIndex + '' : '0'
      selectTitle()
      setTimeout(() => {
        if (Object.keys(data.seachArea).length) {
          data.activeId = data.seachArea.id || ''
          data.navItem = data.areaTree[data.activeIndex].children.find(item => item.id === data.activeId)
        }
      }, 500)
    }
    const selectTitle = async (e) => {
      const { data: list } = await $api.RandomClapping.representativehomeSelectTitle({ areaId: data.areaTree[data.activeIndex].id })
      data.selectTitleList = list.map(item => {
        return {
          text: item.title,
          id: item.id,
          longitude: item.longitude,
          latitude: item.dimension,
          standbyFour: item.standbyFour
        }
      })
      data.areaTree[data.activeIndex].children = data.areaTree[data.activeIndex].id === '370200' ? [...data.areaList.filter(item => item.id === data.areaTree[data.activeIndex].id)] : [
        ...data.areaList.filter(item => item.id === data.areaTree[data.activeIndex].id),
        ...data.selectTitleList
      ]
    }
    // 提交
    const Submit = () => {
      sessionStorage.setItem('seachArea', JSON.stringify({
        areaId: data.areaTree[data.activeIndex].id,
        text: data.areaTree[data.activeIndex].text,
        activeIndex: data.activeIndex,
        ...data.navItem
      }))
      store.commit('eliminate', true)
      router.back()
    }
    const navclickleft = () => {
      // sessionStorage.setItem('seachArea', {})
      router.back()
    }
    const navClick = (e) => {
      data.activeIndex = e
      data.activeId = ''
      data.navItem = {}
      selectTitle()
    }
    const itemClick = (e) => {
      data.navItem = e
      // if (data.activeId) {
      //   data.activeId = ''
      // } else {
      //   data.navItem = e
      // }
    }
    return { ...toRefs(data), dayjs, route, router, $api, navClick, navclickleft, itemClick, ImagePreview, $general, Submit }
  }
})
</script>
<style lang='less'>
:root {
  --van-sidebar-selected-border-color: #006ef1;
  --van-tree-select-item-active-color: #006ef1;
}

.seachTurnToHold {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;

  .seachTurnToHold_top {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
  }

  .top_title {
    width: 100%;
    margin-top: 45px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    background: #fff;
    border-bottom: 1px solid #f4f6f8;

    .top_title_left {
      width: 120px;
    }

    .top_title_right {
      flex: 1;
    }

    >div {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .van-tree-select__item {
    line-height: 4.3 !important;
  }

  // padding: 15px;
  .footer {
    width: 90%;
    height: 56px;
    position: fixed;
    left: 50%;
    bottom: 2%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .RandomClappingUpload-submit {
    width: 45%;
    height: 40px;
    background: #3088fe;
    box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #ffffff;
    border-radius: 5px;
  }

  .RandomClappingUpload-submits {
    width: 45%;
    height: 40px;
    border: 1px solid #3088fe;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 40px;
    text-align: center;
    color: #3088fe;
    border-radius: 5px;
  }

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../../assets/font/PingFang Medium_downcc.otf");
  }

  .RandomClappingUpload-box {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 10px;

    .van-cell-group {
      margin: 0px;
    }

    .newContent {
      flex-wrap: wrap;

      .van-cell__title {
        width: 100%;
        margin-bottom: 6px;
      }

      .van-field__body {
        background-color: #f4f6f8;
        padding: 6px 12px;
        border-radius: 10px;
      }
    }

    .picUploader {
      background: #fff;
      overflow: hidden;
      margin-top: 10px;
      padding: 10px;

      .picUploader_title {
        margin-top: 10px;
        margin-bottom: 10px;
      }
    }

    .imgloager {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        margin-right: 10px;
        position: relative;

        .clear {
          position: absolute;
          top: 0;
          right: 0;
          font-size: 16px;
          z-index: 999;
        }
      }

      .photo {
        width: 2.13333rem;
        height: 2.13333rem;
        margin: 0 0.21333rem 0.21333rem 0;
        border-radius: 0.10667rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #dcdee0;
        font-size: 24px;
      }
    }
  }

  // .RandomClappingUpload-submit {
  //   position: fixed;
  //   left: 50%;
  //   bottom: 2%;
  //   transform: translateX(-50%);
  //   width: 90%;
  //   height: 56px;
  //   background: #3088fe;
  //   box-shadow: 0px 4px 12px rgba(24, 64, 118, 0.15);
  //   font-size: 16px;
  //   font-family: PingFang SC;
  //   font-weight: 400;
  //   line-height: 56px;
  //   text-align: center;
  //   color: #ffffff;
  //   border-radius: 5px;
  // }
}
</style>

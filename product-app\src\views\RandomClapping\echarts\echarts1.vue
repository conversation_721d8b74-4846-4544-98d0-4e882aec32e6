<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="chart-title">
        <div style="margin-right: 6px;">
          <p style="width: 4px;height: 8px;background: #7EDBFB;"></p>
          <p style="width: 4px;height: 8px;background: #2E86EE;"></p>
        </div>
        <span>{{title}}</span>
      </div>
      <div class="chart-controls">
        <div class="button-group">
          <div :class="['button', isSeasonSelected ? 'selected' : '']"
               @click="switchToSeason('1')">按季</div>
          <div :class="['button', !isSeasonSelected ? 'selected' : '']"
               @click="switchToMonth('0')">按月</div>
        </div>
      </div>
    </div>
    <!-- <div style="width: 100vw;"> -->
    <v-chart :option="chartOptions"
             autoresize
             class="chart"></v-chart>
    <!-- </div> -->
  </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { use } from 'echarts/core'
import VChart from 'vue-echarts'
import { BarChart } from 'echarts/charts'
import { TooltipComponent, GridComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([BarChart, TooltipComponent, GridComponent, LegendComponent, TitleComponent, CanvasRenderer])

export default defineComponent({
  name: 'EChartsComponent',
  components: {
    VChart
  },
  props: {
    title: {
      type: String,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    categories: {
      type: Array,
      required: true
    },
    colors: {
      type: Array,
      required: true
    }
  },
  setup (props, { emit }) {
    const isSeasonSelected = ref(true)
    const chartOptions = ref({})
    const chartReady = ref(false)

    const updateChartOptions = () => {
      if (Object.keys(props.data).length > 0 && props.categories.length > 0) {
        chartOptions.value = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: (params) => {
              const total = params.reduce((sum, item) => sum + item.value, 0)
              let tooltipText = `总计：${total}件<br/>`
              params.forEach(item => {
                tooltipText += `${item.marker} ${item.seriesName}：${item.value}件<br/>`
              })
              return tooltipText
            }
          },
          legend: {
            data: Object.keys(props.data),
            top: '5%',
            left: 'left',
            textStyle: {
              color: '#000'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '2%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: props.categories,
            axisLine: {
              lineStyle: {
                color: '#ccc'
              }
            },
            axisLabel: {
              color: '#666',
              rotate: 45, // 斜着显示
              formatter: function (value) {
                return value.length > 2 ? value : value
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#ccc',
                width: 1 // 细线
              }
            },
            axisLabel: {
              color: '#666'
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
                width: 1
              }
            }
          },
          series: Object.keys(props.data).map((key, index) => ({
            name: key,
            type: 'bar',
            data: props.data[key],
            barWidth: 16,
            label: {
              show: true,
              position: 'top',
              color: props.colors[index][0],
              fontSize: 10,
              formatter: '{c}'
            },
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: props.colors[index][0] },
                  { offset: 1, color: props.colors[index][1] }
                ]
              }
            }
          }))
        }
        chartReady.value = true
      }
    }
    watch(() => props.data, () => {
      updateChartOptions()
    })
    watch(() => props.categories, () => {
      updateChartOptions()
    })

    const switchToSeason = (timeframe) => {
      isSeasonSelected.value = true
      emit('timeframe-switch', timeframe)
    }

    const switchToMonth = (timeframe) => {
      isSeasonSelected.value = false
      emit('timeframe-switch', timeframe)
    }
    return {
      chartOptions,
      switchToSeason,
      switchToMonth,
      isSeasonSelected
    }
  }
})
</script>

<style scoped>
.chart-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px 12px;
  font-size: 16px;
  color: #333;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-top: 15px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  display: flex;
  align-items: center;
}
.chart-title span {
  font-weight: 600;
  font-size: 16px;
  color: #061a31;
}

.icon-image {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.chart-controls {
  display: flex;
  gap: 10px;
}

.button-group {
  display: flex;
}

.button {
  padding: 5px 15px;
  border: 1px solid #ccc;
  /* border-radius: 5px; */
  cursor: pointer;
  background-color: #f5f5f5;
  color: #333;
  font-size: 14px;
}
.button.selected {
  background-color: #1989fa;
  color: #fff;
  border-color: #1989fa;
}
.chart {
  width: 100%;
  height: 220px;
}
</style>

const electronicreadingHome = () => import('@/views/electronicreading/electronicreadingHome')
const electronicreadingItemDetails = () => import('@/views/electronicreading/electronicreadingItemDetails')
const electronicreadingItemDetails2 = () => import('@/views/electronicreading/electronicreadingItemDetails2')
const meetingNoticeList = () => import('@/views/electronicreading/meetingNoticeList')

const electronicreading = [{
  path: '/electronicreadingHome',
  name: 'electronicreadingHome',
  component: electronicreadingHome,
  meta: {
    title: '电子阅文首页',
    keepAlive: true
  }
},
{
  path: '/electronicreadingItemDetails',
  name: 'electronicreadingItemDetails',
  component: electronicreadingItemDetails,
  meta: {
    title: '会议详情页面',
    keepAlive: true
  }
},
{
  path: '/electronicreadingItemDetails2',
  name: 'electronicreadingItemDetails2',
  component: electronicreadingItemDetails2,
  meta: {
    title: '会议详情常委会详情页面',
    keepAlive: true
  }
},
{
  path: '/fiveFile',
  name: 'fiveFile',
  component: () => import('@/views/electronicreading/fiveFile.vue'),
  meta: {
    title: '附件', // 会议附件(外)
    keepAlive: false
  }
},
{
  path: '/fiveFile2',
  name: 'fiveFile2',
  component: () => import('@/views/electronicreading/fiveFile2.vue'),
  meta: {
    title: '附件', // 会议附件(内)
    keepAlive: false
  }
},
{
  path: '/imgFiles',
  name: 'imgFiles',
  component: () => import('@/views/electronicreading/imgFiles.vue'),
  meta: {
    title: '图片附件', // 会议附件的图片附件
    keepAlive: false
  }
},
{
  path: '/meetingNoticeList',
  name: 'meetingNoticeList',
  component: meetingNoticeList,
  meta: {
    title: '会议通知列表', // 会议附件的图片附件
    keepAlive: false
  }
}]
export default electronicreading

<template>
  <div class="rankingList">
    <template v-if="dataList.length">
      <div class="rankingList_title" v-if="title">
        <div class="rankingList_title_item" v-for="item, index in title" :key="index">
          {{ item }}
        </div>
      </div>
      <div class="rankingList_content" v-for="item, index in dataList" :key="index"
        :style="`background: ${index % 2 == 0 ? color : '#fff'};`" @click="goLink(item)">
        <div class="rankingList_content_item">
          <template v-if="urlType == 'medal' && index < 3">
            <img :src="medalUrl[index]" alt="">
          </template>
          <template v-else-if="urlType == 'trophy' && index < 3">
            <img :src="trophyUrl[index]" alt="">
          </template>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div class="rankingList_content_item">
          <img class="headImg" :src="item.img" alt="" v-if="item.img">
          {{ item.name }}
        </div>
        <div class="rankingList_content_item">
          <span
            :style="{ 'color': index == 0 ? '#fb2f2f' : index == 1 ? '#ff833d' : index == 2 ? '#ffcf55' : '#3e97ff' }">{{
              item.num
            }}</span>
          <!-- <span v-else style="">暂无数据</span> -->
        </div>
      </div>
    </template>
    <template v-else>
      <div class="not_box">
        暂无数据
      </div>
    </template>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'rankingList',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: {
    title: Array,
    dataList: Array,
    urlType: String,
    color: {
      type: String,
      default: '#f1f7ff'
    },
    click: {
      type: Boolean,
      default: false
    }
  },
  setup (props) {
    const route = useRoute()
    // const router = useRouter()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      medalUrl: [
        require('../../../assets/img/ld_pm_one.png'),
        require('../../../assets/img/ld_pm_two.png'),
        require('../../../assets/img/ld_pm_there.png')
      ],
      trophyUrl: [
        require('../../../assets/img/ld_pmdele_one.png'),
        require('../../../assets/img/ld_pmdele_two.png'),
        require('../../../assets/img/ld_pmdele_there.png')
      ]
    })
    onMounted(() => {
    })
    const goLink = (item) => {
      // if (props.click) {
      //   window.location.href = item.url
      // } else {
      //   router.push({ name: 'resumption', query: { id: item.userid, uid: item.id, title: '履职详情', year: item.year } })
      // }
    }
    return { ...toRefs(data), general, goLink }
  }
}
</script>
<style lang="less" scoped>
.rankingList {
  width: 100%;
  padding: 3px 10px 10px 10px;
  box-sizing: border-box;

  .not_box {
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    text-align: center;
    color: #9d9d9d;
  }

  .rankingList_content:nth-child(even) {
    background: #f1f7ff;
  }

  .rankingList_content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // height: 50px;
    padding: 10px 10px;

    .rankingList_content_item {
      width: 80px;
      font-size: 16px;
      display: flex;
      align-items: center;

      .headImg {
        width: 26px;
        height: 26px;
        border-radius: 11px;
      }
    }

    .rankingList_content_item:nth-child(1) {
      width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;

      >img {
        width: 26px;
        height: 28px;
      }
    }

    .rankingList_content_item:nth-child(2) {
      width: 150px;
      white-space: normal;
      /* 允许自动换行 */
      word-wrap: break-word;
      /* 允许单词内换行 */
    }

    .rankingList_content_item:nth-child(3) {
      width: 50px;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .rankingList_title {
    display: flex;
    margin: 10px 0;
    align-items: center;
    justify-content: space-between;

    .rankingList_title_item {
      width: 80px;
      font-size: 16px;
      color: #9d9d9d;
    }

    .rankingList_title_item:nth-child(2) {
      width: 180px;
    }

    .rankingList_title_item:nth-child(3) {
      width: 50px;
      font-size: 16px;
    }
  }
}
</style>

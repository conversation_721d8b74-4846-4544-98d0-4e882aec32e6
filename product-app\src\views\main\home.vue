<template>
  <div class="home">
    <!-- <router-view></router-view> -->
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAlive">
        <component :is="Component" />
      </keep-alive>
    </router-view>
    <!-- <iframe v-else :src="iframeUrl" frameborder="0" width="100%" height="100%" sandbox="allow-same-origin"></iframe> -->
    <van-tabbar v-model="active"
                :active-color="appTheme"
                @change="onChange">
      <van-tabbar-item v-for="item in tabbarList"
                       :key="item.id"
                       :icon="active == item.id ? item.selectIconUrl : item.iconUrl"
                       :to="item.infoUrl"
                       :badge="getallNumber(item) ? getallNumber(item) > 99 ? '99+' : getallNumber(item) : ''"
                       :name="item.id">{{
                        item.name }}</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import { computed, defineComponent, reactive, toRefs, inject, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
export default defineComponent({
  name: 'home',
  setup () {
    const store = useStore()
    const $api = inject('$api')
    const router = useRouter()
    const $isShowHead = inject('$isShowHead')
    const state = reactive({
      appTheme: sessionStorage.getItem('appTheme'),
      isShowHead: $isShowHead,
      active: '',
      areaId: sessionStorage.getItem('areaId'),
      keepAlive: ['module101', 'module'],
      iframeUrl: '',
      tabbarList: computed(() => store.getters.tabbarList),
      homeCount: 0,
      allNumber: computed(() => store.state.allNumber)
    })

    watch(() => state.active, val => {
      if (val !== '490686998156148736') {
        var historyIndex = sessionStorage.getItem('historyIndex')
      }
      if (!val && historyIndex) {
        state.active = historyIndex
      } else if (val) {
        if (val !== '490686998156148736') {
          sessionStorage.setItem('historyIndex', val)
        }
      }
    }, { immediate: true })

    const init = () => {
      const historyIndex = sessionStorage.getItem('historyIndex') || ''
      if (historyIndex) return
      const homeItem = state.tabbarList.find(({ name }) => name === '工作台')
      state.active = homeItem.id
      var myParam = { title: homeItem.name }
      if (homeItem.remarks) {
        const a = homeItem.remarks.split('&')
        a.forEach(element => {
          myParam[element.split('=')[0]] = element.split('=')[1]
        })
      }
      router.push({ path: homeItem.infoUrl, query: myParam })
      // router.replace({ path: homeItem.infoUrl })
    }
    const appList = () => store.dispatch('getAppList')
    onMounted(() => {
      init()
      getConferenceAgentNumbe()
      browseNotCount()
    })
    const browseNotCount = async () => {
      const res = await $api.general.browseNotCount({
        areaIds: state.areaId
      })
      sessionStorage.setItem('browseNotCount', JSON.stringify(res.data))
      appList()
    }
    const getConferenceAgentNumbe = async () => {
      const res = await $api.general.getConferenceAgentNumbe()
      sessionStorage.setItem('conferenceAgentNumbe', JSON.stringify(res.data))
    }
    const getallNumber = (item) => {
      if (item.infoUrl2 === 'module') {
        return state.allNumber
      } else {
        return ''
      }
    }
    const onChange = (index) => {
      var url = state.tabbarList.find(item => item.id === index)
      state.iframeUrl = ''
      if (url.infoUrl.indexOf('http') === 0) {
        // window.location.href = url.infoUrl
        window.location.replace(url.infoUrl2)
        // state.iframeUrl = url.infoUrl
        // console.log(url.infoUrl)
      }
      // sessionStorage.setItem('historyIndex', index)
    }
    return {
      ...toRefs(state), onChange, getallNumber
    }
  }
})
</script>

<style lang="less" scoped>
.home {
  width: 100%;

  .van-tabbar {
    height: 60px;
  }

  .van-tabbar ::v-deep .van-tabbar-item__text {
    font-size: 12px !important;
    line-height: 12px;
  }

  .van-tabbar ::v-deep .van-tabbar-item__icon {
    font-size: 20px !important;
  }

  .van-tabbar ::v-deep .van-tabbar-item__icon {
    font-size: 36px !important;
  }

  // .van-tabbar
  //   ::v-deep
  //   .van-tabbar-item:nth-child(2)
  //   .van-tabbar-item__icon
  //   .van-icon
  //   .van-icon__image {
  //   height: 32px !important;
  // }
}
</style>

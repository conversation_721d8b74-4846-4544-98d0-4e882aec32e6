{"remainingRequest": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\peopleInformation\\peopleList.vue?vue&type=template&id=276b6b99&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\peopleInformation\\peopleList.vue", "mtime": 1756437821491}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\babel.config.js", "mtime": 1754028950133}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "action", "style", "_createElementBlock", "_hoisted_1", "_ctx", "isShowHead", "_createBlock", "_component_van_nav_bar", "title", "fixed", "placeholder", "onClickLeft", "$setup", "_createElementVNode", "_normalizeStyle", "$general", "loadConfiguration", "showSearch", "id", "_hoisted_2", "onClick", "_cache", "$event", "search", "_createVNode", "_component_van_icon", "size", "data", "appFontSize", "color", "name", "_hoisted_3", "seachPlaceholder", "maxlength", "type", "ref", "onKeyup", "_with<PERSON><PERSON><PERSON>", "seachText", "_component_van_dropdown_menu", "appTheme", "_component_van_dropdown_item", "_Fragment", "_renderList", "filters", "item", "index", "show", "_component_van_cell", "_withCtx", "_createCommentVNode", "value", "options", "key", "_component_van_switch", "_hoisted_5", "_component_van_button", "block", "onReset", "onConfirm", "_component_van_pull_refresh", "refreshing", "onRefresh", "_component_van_list", "loading", "finished", "offset", "onLoad", "_hoisted_6", "dataList", "_component_van_swipe_cell", "_hoisted_7", "_normalizeClass", "sex", "loadConfigurationSize", "src", "require", "age", "_hoisted_9", "_toDisplayString", "clickable", "openDetails", "_hoisted_10", "url", "_hoisted_12", "team", "des"], "sources": ["D:\\zy\\xm\\h5\\qdrd_h5\\product-app\\src\\views\\peopleInformation\\peopleList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"peopleList\">\r\n    <van-nav-bar v-if=\"isShowHead\" :title=\"title\" fixed placeholder safe-area-inset-top left-text=\"\" left-arrow\r\n      @click-left=\"onClickLeft\" />\r\n    <div :style=\"$general.loadConfiguration(1)\">\r\n      <div v-if=\"showSearch\" id=\"search\" class=\"search_box\" :style=\"$general.loadConfiguration()\">\r\n        <div class=\"search_warp flex_box\">\r\n          <div @click=\"search();\" class=\"search_btn flex_box flex_align_center flex_justify_content\">\r\n            <van-icon :size=\"$general.data.appFontSize + 'px'\" :color=\"'#666'\" :name=\"'search'\"></van-icon>\r\n          </div>\r\n          <form class=\"flex_placeholder flex_box flex_align_center search_input\" action=\"javascript:return true;\">\r\n            <input id=\"searchInput\" class=\"flex_placeholder\" :style=\"$general.loadConfiguration(-1)\"\r\n              :placeholder=\"seachPlaceholder\" maxlength=\"100\" type=\"text\" ref=\"btnSearch\" @keyup.enter=\"search()\"\r\n              v-model=\"seachText\" />\r\n            <div v-if=\"seachText\" @click=\"seachText = ''; search();\"\r\n              class=\"search_btn flex_box flex_align_center flex_justify_content\">\r\n              <van-icon :size=\"$general.data.appFontSize + 'px'\" :color=\"'#999'\" :name=\"'clear'\"></van-icon>\r\n            </div>\r\n          </form>\r\n          <van-dropdown-menu class=\"search-dropdown-menu flex_box flex_align_center\" :active-color=\"appTheme\"\r\n            :style=\"$general.loadConfiguration(-3)\">\r\n            <van-dropdown-item title=\"筛选\" ref=\"filter\">\r\n              <template v-for=\"(item, index) in filters\" :key=\"index\">\r\n                <van-cell v-if=\"item.show\" :title=\"item.title\" :style=\"$general.loadConfiguration()\">\r\n                  <template v-slot:right-icon>\r\n                    <!--选择-->\r\n                    <van-dropdown-menu v-if=\"item.type == 'select'\" :active-color=\"appTheme\">\r\n                      <van-dropdown-item v-model=\"item.value\" get-container=\"#search\"\r\n                        :options=\"item.data\"></van-dropdown-item>\r\n                    </van-dropdown-menu>\r\n                    <!--开关-->\r\n                    <van-switch v-else-if=\"item.type == 'switch'\" :active-color=\"appTheme\" v-model=\"item.value\"\r\n                      :size=\"$general.data.appFontSize + 8 + 'px'\"></van-switch>\r\n                    <!--其它只展示文字-->\r\n                    <div v-else :style=\"$general.loadConfiguration()\">{{ item.value }}</div>\r\n                  </template>\r\n                </van-cell>\r\n              </template>\r\n              <div class=\"flex_box\">\r\n                <van-button block @click=\"onReset\">重置</van-button>\r\n                <van-button block :color=\"appTheme\" @click=\"onConfirm\">确认</van-button>\r\n              </div>\r\n            </van-dropdown-item>\r\n          </van-dropdown-menu>\r\n        </div>\r\n      </div>\r\n      <van-pull-refresh v-model=\"refreshing\" @refresh=\"onRefresh\">\r\n        <van-list v-model:loading=\"loading\" :finished=\"finished\" finished-text=\"没有更多了\" offset=\"52\" @load=\"onLoad\">\r\n          <!--数据列表-->\r\n          <ul class=\"vue_newslist_box\">\r\n            <van-swipe-cell v-for=\"(item, index) in dataList\" :key=\"index\" class=\"van-hairline--bottom\">\r\n              <div class=\"top_box\">\r\n                <div class=\"flex_box flex_align_center\" :class=\"item.sex == 1 ? 'man' : 'woman'\"\r\n                  :style=\"$general.loadConfiguration(-3)\">\r\n                  <img :style=\"$general.loadConfigurationSize(-1, 'h')\"\r\n                    :src=\"item.sex == 1 ? require('../../assets/img/man.png') : require('../../assets/img/woman.png')\" />\r\n                  <div v-if=\"item.age\" style=\"min-width: 2.3em;margin-left: 5px;\" class=\"inherit flex_placeholder\">\r\n                    {{ item.age + (item.age ? '岁' : '') }}</div>\r\n                </div>\r\n              </div>\r\n              <van-cell clickable class=\"vue_newslist_item \" @click=\"openDetails(item)\">\r\n                <div class=\"flex_box\">\r\n                  <img class=\"vue_newslist_img\" v-if=\"item.url\" :src=\"item.url\" />\r\n                  <div class=\"flex_placeholder vue_newslist_warp\">\r\n                    <div class=\"vue_newslist_title text_two\" :style=\"$general.loadConfiguration(0)\">\r\n                      {{ item.name }}\r\n                    </div>\r\n                    <div class=\"vue_newslist_title text_two team\" :style=\"$general.loadConfiguration(-2)\">\r\n                      {{ item.team }}\r\n                    </div>\r\n                    <div class=\"vue_newslist_title text_two des\" :style=\"$general.loadConfiguration(-2)\">\r\n                      {{ item.des }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </van-cell>\r\n            </van-swipe-cell>\r\n          </ul>\r\n        </van-list>\r\n      </van-pull-refresh>\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n<script>\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { inject, reactive, toRefs, onMounted, watch } from 'vue'\r\nimport { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay, SwipeCell } from 'vant'\r\nexport default {\r\n  name: 'peopleList',\r\n  components: {\r\n    [Dialog.Component.name]: Dialog.Component,\r\n    [SwipeCell.name]: SwipeCell,\r\n    [Overlay.name]: Overlay,\r\n    [ActionSheet.name]: ActionSheet,\r\n    [PasswordInput.name]: PasswordInput,\r\n    [NumberKeyboard.name]: NumberKeyboard,\r\n    [Icon.name]: Icon,\r\n    [Tag.name]: Tag,\r\n    [VanImage.name]: VanImage,\r\n    [Grid.name]: Grid,\r\n    [GridItem.name]: GridItem,\r\n    [NavBar.name]: NavBar,\r\n    [Sticky.name]: Sticky\r\n  },\r\n  setup () {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const $ifzx = inject('$ifzx')\r\n    const $appTheme = inject('$appTheme')\r\n    const $general = inject('$general')\r\n    const $isShowHead = inject('$isShowHead')\r\n    const $api = inject('$api')\r\n    // const dayjs = require('dayjs')\r\n    const data = reactive({\r\n      safeAreaTop: 0,\r\n      SYS_IF_ZX: $ifzx,\r\n      appTheme: $appTheme,\r\n      isShowHead: $isShowHead,\r\n      relateType: route.query.relateType || '',\r\n      title: route.query.title || '',\r\n      user: JSON.parse(sessionStorage.getItem('user')),\r\n      seachPlaceholder: '搜索',\r\n      keyword: '',\r\n      seachText: '',\r\n      showSkeleton: true,\r\n      loading: false,\r\n      finished: false,\r\n      refreshing: false,\r\n      pageNo: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      dataList: [],\r\n      // show是否显示 type定义的类型 key唯一的字段 title提示文字 defaultValue默认值重置使用\r\n      filter: null,\r\n      filters: [\r\n        { show: $ifzx, type: 'select', key: 'committee', title: '请选择所属专委会', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: $ifzx, type: 'select', key: 'deleId', title: '请选择界别', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: !$ifzx, type: 'select', key: 'representerElement', title: '请选择所属结构', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: !$ifzx, type: 'select', key: 'representerTeam', title: '请选择代表团', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: true, type: 'select', key: 'party', title: '请选择党派', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: true, type: 'select', key: 'nation', title: '请选择民族', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        {\r\n          show: true,\r\n          type: 'select',\r\n          key: 'birthday',\r\n          title: '请选择年龄',\r\n          value: '',\r\n          defaultValue: '',\r\n          data: [{ text: '所有', value: '' }, { text: '0岁-9岁', value: '0' }, { text: '10岁-19岁', value: '10' }, { text: '20岁-29岁', value: '20' }, { text: '30岁-39岁', value: '30' }, { text: '40岁-49岁', value: '40' }, { text: '50岁-59岁', value: '50' }, { text: '60岁-69岁', value: '60' }, { text: '70岁-79岁', value: '70' }, { text: '80岁以上', value: '80' }, { text: '其他', value: '999' }]\r\n        },\r\n        { show: true, type: 'select', key: 'sex', title: '请选择性别', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] },\r\n        { show: true, type: 'select', key: 'hasVacant', title: '请选择是否出缺', value: '', defaultValue: '', data: [{ text: '所有', value: '' }] }\r\n      ], // 筛选集合\r\n      switchs: { value: 'all', data: [{ label: '所有', value: 'all' }] },\r\n      showSearch: true\r\n\r\n    })\r\n    onMounted(() => {\r\n      if (data.title) {\r\n        document.title = data.title\r\n      }\r\n      var key = route.query.key\r\n      var type = route.query.type\r\n      console.log(key + '===' + type)\r\n      if (key) {\r\n        if (type === 'representerElement') {\r\n          $general.getItemForKey('representerElement', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'representerTeam') {\r\n          $general.getItemForKey('representerTeam', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'deleId') {\r\n          $general.getItemForKey('deleId', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'party') {\r\n          $general.getItemForKey('party', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'nation') {\r\n          $general.getItemForKey('nation', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'sex') {\r\n          $general.getItemForKey('sex', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'birthday') {\r\n          $general.getItemForKey('birthday', data.filters, 'key').value = key\r\n        }\r\n        if (type === 'hasVacant') {\r\n          $general.getItemForKey('hasVacant', data.filters, 'key').value = key\r\n        }\r\n      }\r\n      getFiter()\r\n      setTimeout(() => {\r\n        onRefresh()\r\n      }, 100)\r\n    })\r\n    const getFiter = async () => {\r\n      var param = {\r\n        types: 'representer_element,representer_team,vacant_type,party_type,nation_type,sex,yes_no'\r\n      }\r\n      if (data.SYS_IF_ZX) {\r\n        param.types = 'committee_type,vacant_type,dele_type,party_type,nation_type,sex,yes_no'\r\n      }\r\n      const res = await $api.general.pubkvs(param)\r\n      if (res) {\r\n        var datas = res.data\r\n        var committee = datas.committee_type || []\r\n        for (var i in committee) {\r\n          var item = {}\r\n          item.text = committee[i].value\r\n          item.value = committee[i].id\r\n          $general.getItemForKey('committee', data.filters, 'key').data.push(item)\r\n        }\r\n        var deleId = datas.dele_type || []\r\n        deleId.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('deleId', data.filters, 'key').data.push(item)\r\n        })\r\n        var representerElement = datas.representer_element || []\r\n        representerElement.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('representerElement', data.filters, 'key').data.push(item)\r\n        })\r\n        var representerTeam = datas.representer_team || []\r\n        representerTeam.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('representerTeam', data.filters, 'key').data.push(item)\r\n        })\r\n        var hasVacant = data.vacant_type || []\r\n        hasVacant.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('party', data.filters, 'key').data.push(item)\r\n        })\r\n        var party = datas.party_type || []\r\n        party.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('party', data.filters, 'key').data.push(item)\r\n        })\r\n        var nation = datas.nation_type || []\r\n        nation.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('nation', data.filters, 'key').data.push(item)\r\n        })\r\n        var sex = datas.sex || []\r\n        sex.forEach(element => {\r\n          var item = {}\r\n          item.text = element.value\r\n          item.value = element.id\r\n          $general.getItemForKey('sex', data.filters, 'key').data.push(item)\r\n        })\r\n      }\r\n    }\r\n    const getInfo = async () => {\r\n      const param = {\r\n        pageNo: data.pageNo,\r\n        pageSize: data.pageSize,\r\n        keyword: data.seachText,\r\n        memberType: (data.SYS_IF_ZX ? 1 : 3),\r\n        startBirthday: '',\r\n        endBirthday: '',\r\n        isUsing: '1'\r\n      }\r\n      for (var i = 0; i < data.filters.length; i++) {\r\n        const filtersItem = data.filters[i]\r\n        if (filtersItem.key === 'birthday') {\r\n          if (filtersItem.value) {\r\n            var birthday = $general.getItemForKey('birthday', data.filters, 'key').data\r\n            var nItem = $general.getItemForKey(filtersItem.value, birthday, 'value')\r\n            console.log(nItem.value)\r\n            console.log(nItem.text)\r\n            if (nItem.text === '其他') {\r\n              param.startBirthday = 999\r\n              param.endBirthday = 1000\r\n            } else if (nItem.text.indexOf('-') >= 0) {\r\n              param.startBirthday = nItem.text.split('-')[0].split('岁')[0]\r\n              param.endBirthday = nItem.text.split('-')[1].split('岁')[0]\r\n            } else if (nItem.text.indexOf('以上') >= 0) {\r\n              param.startBirthday = nItem.text.split('岁以上')[0]\r\n              param.endBirthday = 1000\r\n            } else {\r\n              param.startBirthday = nItem.text.split('岁')[0]\r\n              param.endBirthday = 1000\r\n            }\r\n          }\r\n        } else {\r\n          param[filtersItem.key] = filtersItem.value\r\n        }\r\n      }\r\n      const res = await $api.peopleInformation.getMemberList(param)\r\n      var { data: list, total } = res\r\n      const newData = []\r\n      list.forEach(item => {\r\n        const _eItem = item\r\n        item.name = _eItem.userName || ''// 姓名\r\n        item.url = _eItem.headImg || ''// 头像\r\n        item.team = _eItem.representerTeam || _eItem.deleId || ''// 代表团 界别\r\n        item.des = _eItem.position || ''// 职务\r\n        item.sex = _eItem.sex === '女' ? 0 : 1// 性别\r\n        item.age = _eItem.age || ''// 年龄\r\n        item.relateType = 'representer'// 类型\r\n        newData.push(item)\r\n      })\r\n      data.dataList = data.dataList.concat(newData)\r\n      data.showSkeleton = false\r\n      data.loading = false\r\n      data.refreshing = false\r\n      // 数据全部加载完成\r\n      if (data.dataList.length >= total) {\r\n        data.finished = true\r\n      }\r\n    }\r\n    watch(() => data.dataList, (newName, oldName) => {\r\n\r\n    })\r\n\r\n    // 筛选重置事件\r\n    const onReset = () => {\r\n      for (var i = 0; i < data.filters.length; i++) {\r\n        data.filters[i].value = data.filters[i].defaultValue\r\n      }\r\n    }\r\n    // 筛选确定事件\r\n    const onConfirm = () => {\r\n      data.filter.toggle()\r\n      onRefresh()\r\n    }\r\n\r\n    const onRefresh = () => {\r\n      data.pageNo = 1\r\n      data.dataList = []\r\n      data.showSkeleton = true\r\n      data.loading = true\r\n      data.finished = false\r\n      getInfo()\r\n    }\r\n    const onLoad = () => {\r\n      data.pageNo = data.pageNo + 1\r\n      getInfo()\r\n    }\r\n    const openDetails = (_item) => {\r\n      router.push({ name: 'personData', query: { id: _item.id } })\r\n    }\r\n\r\n    const onClickLeft = () => history.back()\r\n\r\n    return { ...toRefs(data), onClickLeft, onRefresh, onLoad, $general, confirm, openDetails, onReset, onConfirm }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.peopleList {\r\n  background: #f8f8f8;\r\n\r\n  .a_box_warp {\r\n    background: #ffffff;\r\n    box-shadow: 0px 3px 10px rgba(34, 85, 172, 0.12);\r\n    opacity: 1;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .search_box {\r\n    background: #ffffff;\r\n  }\r\n\r\n  .a_search_box {\r\n    padding: 14px 15px 0 15px;\r\n  }\r\n\r\n  .a_search_select_box {\r\n    padding: 2px 0 2px 10px;\r\n  }\r\n\r\n  .a_search_select_text {\r\n    color: #222222;\r\n    font-weight: 500;\r\n    line-height: 1.46;\r\n  }\r\n\r\n  .a_search_box form {\r\n    padding: 0 13px;\r\n  }\r\n\r\n  .a_search_btn_box {\r\n    padding: 9px 16px;\r\n  }\r\n\r\n  .a_search_select_text_icon {\r\n    position: relative;\r\n    margin-left: 6px;\r\n    width: 13px;\r\n  }\r\n\r\n  .a_search_select_text_icon::after {\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -5px;\r\n    border: 3px solid;\r\n    border-color: transparent transparent #222 #222;\r\n    -webkit-transform: rotate(-45deg);\r\n    transform: rotate(-45deg);\r\n    opacity: 0.8;\r\n    content: \"\";\r\n  }\r\n\r\n  .a_select_btn_box {\r\n    background: #666666;\r\n    margin-left: 5px;\r\n    font-weight: 500;\r\n    line-height: 1.5;\r\n    color: #ffffff;\r\n    padding: 7px 11px;\r\n    border-radius: 2px;\r\n  }\r\n\r\n  #app .vue_newslist_item {\r\n    padding: 15px 15px;\r\n  }\r\n\r\n  #app .vue_newslist_box .van-cell {\r\n    background-color: rgba(0, 0, 0, 0) !important;\r\n  }\r\n\r\n  .vue_newslist_img {\r\n    width: 55px;\r\n    min-height: 0;\r\n    height: 74px;\r\n    border-radius: 2px;\r\n    margin-right: 10px;\r\n    background-position: center;\r\n  }\r\n\r\n  .van-hairline--bottom {\r\n    width: calc(100% - 32px);\r\n    left: 0;\r\n    right: 0;\r\n    margin: auto;\r\n    margin-top: 10px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 3px 20px rgba(34, 85, 172, 0.12);\r\n    opacity: 1;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .vue_newslist_title {\r\n    font-weight: 600;\r\n    color: #222222;\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .vue_newslist_warp {\r\n    padding-bottom: 0;\r\n    position: relative;\r\n  }\r\n\r\n  .team {\r\n    font-weight: 500;\r\n    color: #222222;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  .des {\r\n    font-weight: 500;\r\n    color: #222222;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  .top_box {\r\n    position: absolute;\r\n    right: 15px;\r\n    top: 15px;\r\n    color: #6499f0;\r\n    opacity: 1;\r\n    z-index: 99;\r\n  }\r\n\r\n  .woman {\r\n    color: #f06981;\r\n  }\r\n\r\n  .van-swipe-cell__wrapper {\r\n    position: relative;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAKZA,KAAK,EAAC;AAAsB;;EAIzBA,KAAK,EAAC,0DAA0D;EAACC,MAAM,EAAC;;;;EA4BrED,KAAK,EAAC;AAAU;;EAWrBA,KAAK,EAAC;AAAkB;;EAEnBA,KAAK,EAAC;AAAS;;;;EAKKE,KAA0C,EAA1C;IAAA;IAAA;EAAA,CAA0C;EAACF,KAAK,EAAC;;;EAKnEA,KAAK,EAAC;AAAU;;;EAEdA,KAAK,EAAC;AAAoC;;;;;;;;;;;;uBA9D/DG,mBAAA,CAgFM,OAhFNC,UAgFM,GA/EeC,IAAA,CAAAC,UAAU,I,cAA7BC,YAAA,CAC8BC,sBAAA;;IADEC,KAAK,EAAEJ,IAAA,CAAAI,KAAK;IAAEC,KAAK,EAAL,EAAK;IAACC,WAAW,EAAX,EAAW;IAAC,qBAAmB,EAAnB,EAAmB;IAAC,WAAS,EAAC,EAAE;IAAC,YAAU,EAAV,EAAU;IACxGC,WAAU,EAAEC,MAAA,CAAAD;0FACfE,mBAAA,CA4EM;IA5EAZ,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;MAC1BZ,IAAA,CAAAa,UAAU,I,cAArBf,mBAAA,CAwCM;;IAxCiBgB,EAAE,EAAC,QAAQ;IAACnB,KAAK,EAAC,YAAY;IAAEE,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;MACtFH,mBAAA,CAsCM,OAtCNM,UAsCM,GArCJN,mBAAA,CAEM;IAFAO,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA;MAAElB,IAAA,CAAAmB,MAAM;IAAA;IAAKxB,KAAK,EAAC;MAC5ByB,YAAA,CAA+FC,mBAAA;IAApFC,IAAI,EAAEd,MAAA,CAAAG,QAAQ,CAACY,IAAI,CAACC,WAAW;IAAUC,KAAK,EAAE,MAAM;IAAGC,IAAI,EAAE;uCAE5EjB,mBAAA,CAQO,QARPkB,UAQO,G,gBAPLlB,mBAAA,CAEwB;IAFjBK,EAAE,EAAC,aAAa;IAACnB,KAAK,EAAC,kBAAkB;IAAEE,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;IAChFN,WAAW,EAAEN,IAAA,CAAA4B,gBAAgB;IAAEC,SAAS,EAAC,KAAK;IAACC,IAAI,EAAC,MAAM;IAACC,GAAG,EAAC,WAAW;IAAEC,OAAK,EAAAf,MAAA,QAAAA,MAAA,MAAAgB,SAAA,CAAAf,MAAA,IAAQlB,IAAA,CAAAmB,MAAM;+DACvFnB,IAAA,CAAAkC,SAAS,GAAAhB,MAAA;8EAATlB,IAAA,CAAAkC,SAAS,E,GACTlC,IAAA,CAAAkC,SAAS,I,cAApBpC,mBAAA,CAGM;;IAHiBkB,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA;MAAElB,IAAA,CAAAkC,SAAS;MAAOlC,IAAA,CAAAmB,MAAM;IAAA;IAClDxB,KAAK,EAAC;MACNyB,YAAA,CAA8FC,mBAAA;IAAnFC,IAAI,EAAEd,MAAA,CAAAG,QAAQ,CAACY,IAAI,CAACC,WAAW;IAAUC,KAAK,EAAE,MAAM;IAAGC,IAAI,EAAE;8EAG9EN,YAAA,CAwBoBe,4BAAA;IAxBDxC,KAAK,EAAC,iDAAiD;IAAE,cAAY,EAAEK,IAAA,CAAAoC,QAAQ;IAC/FvC,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;;sBAClC,MAqBoB,CArBpBQ,YAAA,CAqBoBiB,4BAAA;MArBDjC,KAAK,EAAC,IAAI;MAAC2B,GAAG,EAAC;;wBACtB,MAAgC,E,kBAA1CjC,mBAAA,CAeWwC,SAAA,QAAAC,WAAA,CAfuBvC,IAAA,CAAAwC,OAAO,GAAvBC,IAAI,EAAEC,KAAK;;eAAoBA;QAAK,IACpCD,IAAI,CAACE,IAAI,I,cAAzBzC,YAAA,CAaW0C,mBAAA;;UAbiBxC,KAAK,EAAEqC,IAAI,CAACrC,KAAK;UAAGP,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;;UAC9D,YAAU,EAAAiC,QAAA,CACzB,MAAS,CAATC,mBAAA,MAAS,EACgBL,IAAI,CAACX,IAAI,gB,cAAlC5B,YAAA,CAGoBiC,4BAAA;;YAH6B,cAAY,EAAEnC,IAAA,CAAAoC;;8BAC7D,MAC2C,CAD3ChB,YAAA,CAC2CiB,4BAAA;0BADfI,IAAI,CAACM,KAAK;+CAAVN,IAAI,CAACM,KAAK,GAAA7B,MAAA;cAAE,eAAa,EAAC,SAAS;cAC5D8B,OAAO,EAAEP,IAAI,CAAClB;;;mEAGIkB,IAAI,CAACX,IAAI,gB,cAAhChC,mBAAA,CAC4DwC,SAAA;YAAAW,GAAA;UAAA,IAF5DH,mBAAA,MAAS,EACT1B,YAAA,CAC4D8B,qBAAA;YADb,cAAY,EAAElD,IAAA,CAAAoC,QAAQ;wBAAWK,IAAI,CAACM,KAAK;6CAAVN,IAAI,CAACM,KAAK,GAAA7B,MAAA;YACvFI,IAAI,EAAEd,MAAA,CAAAG,QAAQ,CAACY,IAAI,CAACC,WAAW;oKAElC1B,mBAAA,CAAwEwC,SAAA;YAAAW,GAAA;UAAA,IADxEH,mBAAA,WAAc,EACdrC,mBAAA,CAAwE;YAA3DZ,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;8BAAO6B,IAAI,CAACM,KAAK,wB;;;sCAIrEtC,mBAAA,CAGM,OAHN0C,UAGM,GAFJ/B,YAAA,CAAkDgC,qBAAA;QAAtCC,KAAK,EAAL,EAAK;QAAErC,OAAK,EAAER,MAAA,CAAA8C;;0BAAS,MAAErC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;;;sCACrCG,YAAA,CAAsEgC,qBAAA;QAA1DC,KAAK,EAAL,EAAK;QAAE5B,KAAK,EAAEzB,IAAA,CAAAoC,QAAQ;QAAGpB,OAAK,EAAER,MAAA,CAAA+C;;0BAAW,MAAEtC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,mB;;;;;;;wGAMnEG,YAAA,CAiCmBoC,2BAAA;gBAjCQxD,IAAA,CAAAyD,UAAU;+DAAVzD,IAAA,CAAAyD,UAAU,GAAAvC,MAAA;IAAGwC,SAAO,EAAElD,MAAA,CAAAkD;;sBAC/C,MA+BW,CA/BXtC,YAAA,CA+BWuC,mBAAA;MA/BOC,OAAO,EAAE5D,IAAA,CAAA4D,OAAO;8DAAP5D,IAAA,CAAA4D,OAAO,GAAA1C,MAAA;MAAG2C,QAAQ,EAAE7D,IAAA,CAAA6D,QAAQ;MAAE,eAAa,EAAC,OAAO;MAACC,MAAM,EAAC,IAAI;MAAEC,MAAI,EAAEvD,MAAA,CAAAuD;;wBAChG,MAAW,CAAXjB,mBAAA,QAAW,EACXrC,mBAAA,CA4BK,MA5BLuD,UA4BK,I,kBA3BHlE,mBAAA,CA0BiBwC,SAAA,QAAAC,WAAA,CA1BuBvC,IAAA,CAAAiE,QAAQ,GAAxBxB,IAAI,EAAEC,KAAK;6BAAnCxC,YAAA,CA0BiBgE,yBAAA;UA1BkCjB,GAAG,EAAEP,KAAK;UAAE/C,KAAK,EAAC;;4BACnE,MAQM,CARNc,mBAAA,CAQM,OARN0D,UAQM,GAPJ1D,mBAAA,CAMM;YANDd,KAAK,EAAAyE,eAAA,EAAC,4BAA4B,EAAS3B,IAAI,CAAC4B,GAAG;YACrDxE,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;cAClCH,mBAAA,CACuG;YADjGZ,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAAC2D,qBAAqB;YACxCC,GAAG,EAAE9B,IAAI,CAAC4B,GAAG,QAAQG,OAAO,+BAA+BA,OAAO;uDAC1D/B,IAAI,CAACgC,GAAG,I,cAAnB3E,mBAAA,CAC8C,OAD9C4E,UAC8C,EAAAC,gBAAA,CAAzClC,IAAI,CAACgC,GAAG,IAAIhC,IAAI,CAACgC,GAAG,gC,6DAG7BrD,YAAA,CAeWwB,mBAAA;YAfDgC,SAAS,EAAT,EAAS;YAACjF,KAAK,EAAC,mBAAoB;YAAEqB,OAAK,EAAAE,MAAA,IAAEV,MAAA,CAAAqE,WAAW,CAACpC,IAAI;;8BACrE,MAaM,CAbNhC,mBAAA,CAaM,OAbNqE,WAaM,GAZgCrC,IAAI,CAACsC,GAAG,I,cAA5CjF,mBAAA,CAAgE;;cAA3DH,KAAK,EAAC,kBAAkB;cAAkB4E,GAAG,EAAE9B,IAAI,CAACsC;uFACzDtE,mBAAA,CAUM,OAVNuE,WAUM,GATJvE,mBAAA,CAEM;cAFDd,KAAK,EAAC,6BAA6B;cAAEE,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;gCACtE6B,IAAI,CAACf,IAAI,yBAEdjB,mBAAA,CAEM;cAFDd,KAAK,EAAC,kCAAkC;cAAEE,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;gCAC3E6B,IAAI,CAACwC,IAAI,yBAEdxE,mBAAA,CAEM;cAFDd,KAAK,EAAC,iCAAiC;cAAEE,KAAK,EAAAa,eAAA,CAAEF,MAAA,CAAAG,QAAQ,CAACC,iBAAiB;gCAC1E6B,IAAI,CAACyC,GAAG,wB", "ignoreList": []}]}
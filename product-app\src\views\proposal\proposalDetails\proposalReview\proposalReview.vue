<template>
  <div class="mainModuleReview">
    <van-form @submit="onSubmit">
      <van-cell-group inset>
        <van-field v-model="form.contentMainTypeName"
                   is-link
                   readonly
                   name="picker"
                   label="提案大类"
                   placeholder="点击选择提案大类"
                   @click="bigTypeShow = true" />
        <van-field v-model="form.contentDetailTypeName"
                   is-link
                   readonly
                   name="picker"
                   label="提案小类"
                   placeholder="点击选择提案小类"
                   @click="smallTypeShow = true" />
        <van-field name="radio"
                   label-width="4.8em"
                   label="单选框">
          <template #input>
            <van-radio-group v-model="form.acceptResult"
                             direction="horizontal">
              <van-radio v-for="item in acceptResultData"
                         :key="item.value"
                         :name="item.value"
                         icon-size="16">{{item.label}}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field v-model="form.content"
                   name="content"
                   label="审查意见"
                   rows="3"
                   label-width="4.8em"
                   type="textarea"
                   placeholder="请输入审查意见" />
        <van-field is-link
                   label-width="4.8em"
                   name="unit"
                   label="主办单位"
                   @click="unitShow = !unitShow">
          <template #input>
            <div class="newDivBox">
              <div class="newDiv"
                   v-for="item in unitData"
                   :key="item.id">{{item.value}}</div>
            </div>
          </template>
        </van-field>
        <van-field is-link
                   label-width="4.8em"
                   name="unit"
                   label="协办单位"
                   @click="unitShow = !unitShow">
          <template #input>
            <div class="newDivBox">
              <div class="newDiv"
                   v-for="item in unitData"
                   :key="item.id">{{item.value}}</div>
            </div>
          </template>
        </van-field>
        <van-field is-link
                   label-width="4.8em"
                   name="unit"
                   label="分办单位"
                   @click="unitShow = !unitShow">
          <template #input>
            <div class="newDivBox">
              <div class="newDiv"
                   v-for="item in unitData"
                   :key="item.id">{{item.value}}</div>
            </div>
          </template>
        </van-field>
      </van-cell-group>
      <div class="newButton">
        <van-button type="primary"
                    @click="logo = false"
                    native-type="submit">存为草稿</van-button>
        <van-button type="primary"
                    @click="logo = true"
                    native-type="submit">提交</van-button>
      </div>
    </van-form>
    <van-popup v-model:show="unitShow"
               position="bottom"
               :style="{height:'80%'}">
      <chooseHandleUnit ref="unit"
                        :data="defaultUnit"></chooseHandleUnit>
    </van-popup>
    <van-popup v-model:show="bigTypeShow"
               position="bottom">
      <van-picker :columns="contentMainType"
                  title="提案大类"
                  @confirm="bigConfirm"
                  @cancel="bigTypeShow = false" />
    </van-popup>
    <van-popup v-model:show="smallTypeShow"
               position="bottom">
      <van-picker :columns="contentDetailType"
                  title="提案小类"
                  @confirm="smallConfirm"
                  @cancel="smallTypeShow = false" />
    </van-popup>
  </div>
</template>
<script>
import { Toast } from 'vant'
import { useRouter } from 'vue-router'
import { inject, onMounted, reactive, ref, toRefs, watch } from 'vue'
import chooseHandleUnit from '../../components/chooseHandleUnit'
export default {
  name: 'proposalReview',
  components: {
    chooseHandleUnit
  },
  props: ['id'],
  setup (props) {
    const router = useRouter()
    const $api = inject('$api')
    const data = reactive({
      user: JSON.parse(sessionStorage.getItem('user')),
      logo: true,
      form: {
        contentMainType: '',
        contentDetailType: '',
        contentMainTypeName: '',
        contentDetailTypeName: '',
        content: '',
        acceptResult: ''
      },
      rules: {
        title: [{ required: true, message: '请输入提案标题' }]
      },
      unitData: [],
      unitShow: false,
      defaultUnit: [],
      bigTypeShow: false,
      smallTypeShow: false,
      typeData: [],
      contentMainType: [],
      contentDetailType: [],
      acceptResultData: []
    })
    onMounted(() => {
      chooseList()
      auditDetail()
    })
    const unit = ref(null)
    watch(() => data.unitShow, () => {
      if (!data.unitShow) {
        data.unitData = unit.value.selectedUnit
      }
    })
    const auditDetail = async () => {
      const res = await $api.proposal.auditDetail({
        id: props.id
      })
      var { data: detail } = res
      data.acceptResultData = detail.operationValues
    }
    const chooseList = async (ids) => {
      const res = await $api.proposal.chooseList({
        pageNo: 1,
        pageSize: 0,
        inList: 1,
        submissionTopicQuery: 1
      })
      var { data: typeData } = res
      data.typeData = typeData
      typeData.forEach(item => {
        data.contentMainType.push({ text: item.name, id: item.id })
      })
    }
    const onSubmit = () => {
      if (data.logo) {
        generalAdd()
      } else {
        generalAdd(1)
      }
    }
    const generalAdd = async (type) => {
      const res = await $api.general.general('/proposal/add', {})
      var { errcode, errmsg } = res
      if (errcode === 200) {
        Toast.success(errmsg)
        router.go(-1)
      }
    }
    const bigConfirm = (value) => {
      if (value.id !== data.form.contentMainType) {
        data.form.contentMainType = value.id
        data.form.contentMainTypeName = value.text
        data.form.contentDetailType = ''
        data.form.contentDetailTypeName = ''
        data.contentDetailType = []
        data.typeData.forEach(item => {
          if (value.id === item.id) {
            item.children.forEach(items => {
              data.contentDetailType.push({ text: items.name, id: items.id })
            })
          }
        })
      }
      data.bigTypeShow = false
    }
    const smallConfirm = (value) => {
      data.form.contentDetailType = value.id
      data.form.contentDetailTypeName = value.text
      data.smallTypeShow = false
    }
    return { ...toRefs(data), unit, onSubmit, bigConfirm, smallConfirm }
  }
}
</script>
<style lang="less">
</style>

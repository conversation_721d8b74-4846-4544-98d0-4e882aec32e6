<template>
  <div class="meetingNoticeDetail">
    <template v-if="firstAjax && title">
      <!-- v-if="!msgMore.is?index<msgMore.num:true" -->
      <div v-for="(item, index) in msgs"
           :key="index"
           class="msgs_item flex_box van-hairline--bottom">
        <div :style="$general.loadConfiguration(-1)"
             class="msgs_hint"
             v-html="item.hint"></div>
        <div :style="$general.loadConfiguration(-1)"
             class="flex_placeholder msgs_value"
             :class="item.link ? 'text_one2' : ''"
             v-html="item.value"></div>
        <div v-if="item.link"
             @click="openLink(item)"
             :style="$general.loadConfiguration(-1) + 'color:' + appTheme"
             class="msgs_link">{{ item.linkName }}</div>
      </div>
      <!-- 会议通知 -->
      <div class="n_details_content"
           :style="$general.loadConfiguration(-1) + 'border-top:10px solid #F4F4F4;'">
        <div class="inherit"
             style="font-weight: bold;margin-top: 10px;margin-left: 15px;">会议议程:</div>
        <div class="inherit"
             style="color: #333333;line-height: 2.2;margin-left: 15px;"
             v-html="content"></div>
      </div>

    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item, index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>

    <!--返回顶部-->
    <ul v-if="footerBtnsShow"
        class="footer_btn_box"
        :style="'bottom:' + (safeAreaBottom + 30) + 'px;'">
      <transition name="van-slide-right">
        <div v-if="footerMore.show">
          <template v-for="(item, index) in footerBtns"
                    :key="index">
            <!-- <div v-if="scrollTop >= 100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="(($general.appFontSize + 25) * 0.01) + 'rem'"
                        name="upgrade"></van-icon>
            </div> -->
            <div v-if="item.type == 'btn'"
                 class="van-button-box"
                 :style="$general.loadConfiguration(-3)">
              <van-button loading-type="spinner"
                          :loading-size="(($general.appFontSize) * 0.01) + 'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color ? item.color : appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)"
                          :icon="item.icon">{{ item.name }}</van-button>
            </div>
          </template>
        </div>
      </transition>
      <div v-if="title && footerBtns.length != 0"
           @click="footerMore.show = !footerMore.show;"
           :style="$general.loadConfigurationSize(15) + 'border-radius:50%;background:' + appTheme"
           class="footer_item flex_box flex_align_center flex_justify_content">
        <van-icon :size="(($general.appFontSize + 2) * 0.01) + 'rem'"
                  color="#FFF"
                  :name="footerMore.show ? 'arrow-down' : 'ellipsis'"></van-icon>
      </div>
    </ul>
    <!-- 参会完成核对个人信息弹框 -->
    <!-- <van-dialog v-model:show="showCheck"
                title="参会成功,请您核对个人信息"
                show-cancel-button
                confirmButtonColor="#3088fe"
                @confirm="Clickdialog">
      <van-field v-model="form.name"
                 label="姓名"
                 disabled
                 placeholder="请输入分类名字" />
      <van-field v-model="form.mobile"
                 label="手机号"
                 disabled
                 placeholder="请输入手机号" />
      <van-field v-model="form.position"
                 label="单位及职务"
                 placeholder="请输入单位及职务" />
    </van-dialog> -->
    <van-action-sheet v-model:show="showSignIn"
                      :description="description"
                      :actions="actions"
                      @select="onSelect"
                      cancel-text="取消" />
    <van-overlay :show="dialogShow"
                 @click="showSignIn = false">
      <van-dialog v-model:show="dialogShow"
                  :title="dialogTitle"
                  :width="'288px'"
                  :overlay="false"
                  @confirm="confirm"
                  show-cancel-button>
        <div class="inherit"
             style="padding: 20px 0 20px 0;">
          <!-- 密码输入框 -->
          <van-password-input :value="value"
                              :mask="false"
                              :length="signlength"
                              :focused="showKeyboard"
                              @focus="showKeyboard = true" />
        </div>

      </van-dialog>
      <!-- 数字键盘 -->
      <van-number-keyboard v-model="value"
                           :show="showKeyboard"
                           :z-index="'99'"
                           @blur="showKeyboard = false" />
    </van-overlay>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, watch } from 'vue'
import { Dialog, Toast, ActionSheet, PasswordInput, NumberKeyboard, Overlay, Skeleton } from 'vant'
export default {
  name: 'meetingDetailFile',
  components: {
    [Skeleton.name]: Skeleton,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const $general = inject('$general')
    const appTheme = inject('$appTheme')
    const isShowHead = inject('$isShowHead')
    const route = useRoute()
    const data = reactive({
      safeAreaBottom: 0,
      appTheme: appTheme,
      isShowHead: isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      recordId: route.query.id || '',
      showSkeleton: false,
      firstAjax: true,
      msgs: [
        { hint: '会议名称:', value: '政协第十二届第十五次常委会议-开 幕式' },
        { hint: '会议:', value: '视察视察视察视察视察', link: 'firstAjax && title', linkName: '实景' },
        { hint: '组织部门:', value: '市城管执法局' },
        { hint: '报名截止:', value: '2021-12-10 16:00' },
        { hint: '签到时间:', value: '2021-12-10 16:00<br />至2021-12-10 16:00' },
        { hint: '会议时间:', value: '2021-12-10 16:00<br />至2021-12-10 16:00' },
        { hint: '会议地点:', value: '青岛王朝大酒店' }
      ],
      msgMore: { is: false, num: 4 }, // is是否展开 num多少条收起
      title: '',
      content: '1.审议通过本次常委会会议议程<br />2.传达中共十九届五中全会精神<br />3.传达全国政协十三届常委会第十四次会议精神<br />4.传达10月30日省级党员领导干部会议精神<br />5.省发展改革委负责同志作学习贯彻中共十九届五中全会精神辅导报告', // 正文内容
      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部  btn为按钮
      footerMore: { show: true }, // 展开附加按钮
      // showCheck: false, // 参会完弹框
      form: { // 参会完弹框表单
        name: JSON.parse(sessionStorage.getItem('user')).userName,
        mobile: JSON.parse(sessionStorage.getItem('user')).mobile,
        position: JSON.parse(sessionStorage.getItem('user')).position
      },
      showSignIn: false, // 签到弹框
      actions: [{ name: '签到口令', color: '#3088fe' }, { name: '二维码', color: '#3088fe' }], // 签到选择二维码还是口令
      description: '签到方式',
      dialogShow: false,
      dialogTitle: '',
      showKeyboard: true,
      value: '',
      signlength: 4,
      attachInfo: { name: '附件', data: [] }// 附件对象
    })

    onMounted(() => {
      onRefresh()
    })
    watch(() => data.value, (newVal) => {
      console.log(newVal)
      if (newVal.length > data.signlength) {
        Toast(`签到口令为${data.signlength}位`)
        data.value = data.value.slice(0, 4)
      }
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getDetails()
    }
    const getDetails = async () => {
      var res = []
      res = await $api.conferenceActivitiesFile.conferencenfo(data.id) // 详情
      var { data: list } = res
      // _________________________________________
      data.showSkeleton = false
      data.firstAjax = true
      var info = list || {}
      data.title = info.name || ''// 标题
      window.time = info.startTime
      data.content = info.content || ''
      data.id = info.id || ''
      data.msgs = []
      data.msgs.push({ hint: '会议名称:', value: data.title })
      var assembly = info.bigType === '10010'// 是否大会
      if (!assembly) {
        data.msgs.push({ hint: '会议类型:', value: info.bigTypeName || info.bigType || '' })
        data.msgs.push({ hint: '报名截止:', value: info.signUpEndTime ? dayjs(info.signUpEndTime).format('YYYY-MM-DD HH:mm') : '' })
      }
      data.msgs.push({ hint: '签到时间:', value: (info.signInStartTime ? dayjs(info.signInStartTime).format('YYYY-MM-DD HH:mm') : '') + '<br/>至' + (info.signInEndTime ? dayjs(info.signInEndTime).format('YYYY-MM-DD HH:mm') : '') })
      data.msgs.push({ hint: '会议时间:', value: (info.startTime ? dayjs(info.startTime).format('YYYY-MM-DD HH:mm') : '') + '<br/>至' + (info.endTime ? dayjs(info.endTime).format('YYYY-MM-DD HH:mm') : '') })
      var place = { hint: '会议地点:', value: info.isConnectors === 1 ? info.conferenceRoomName || '' : info.place || '', link: info.isConnectors === 1 ? info.imgUrl || '' : '', linkName: '实景', jumpType: 'link' }
      var conferenceRoomLayoutDetailVo = info.conferenceRoomLayoutDetailVo || {}// 会议排座
      if (conferenceRoomLayoutDetailVo.id && info.isRelease) { // 说明有排座
        window.conferenceRoomId = conferenceRoomLayoutDetailVo.conferenceRoomId
        window.seatingArrangement = conferenceRoomLayoutDetailVo.seatingArrangement
        place.jumpType = 'arrange'
        place.linkName = window.seatingArrangement === '1' ? (conferenceRoomLayoutDetailVo.columnnum + '座') : ((conferenceRoomLayoutDetailVo.type === '1' ? '主席区' : '出席区') + conferenceRoomLayoutDetailVo.row + '排' + conferenceRoomLayoutDetailVo.columnnum + '座')
        place.link = place.linkName
      }
      data.msgs.push(place)
      // var conferenceTransactionDetailVo = info.conferenceTransactionDetailVo || {}// 事务安排
      // if (conferenceTransactionDetailVo.residence) data.msgs.push({ hint: '住地:', value: conferenceTransactionDetailVo.residence || '' })
      // if (conferenceTransactionDetailVo.dining) data.msgs.push({ hint: '用餐地址:', value: conferenceTransactionDetailVo.dining || '' })
      // if (conferenceTransactionDetailVo.car) data.msgs.push({ hint: '用车信息:', value: conferenceTransactionDetailVo.car || '' })
      // if (conferenceTransactionDetailVo.seat) data.msgs.push({ hint: '会议座位:', value: conferenceTransactionDetailVo.seat || '' })
      var collection = info.collection// 是否补录 补录的数据不需要报名签到和请假
      var signUpItem = { name: '报名', type: 'btn', click: 'signUp', icon: 'edit', color: '', loading: false, disabled: false }
      var signInItem = { name: '签到', type: 'btn', click: 'signIn', icon: 'sign', color: '', loading: false, disabled: false }
      var leaveItem = { name: '请假', type: 'btn', click: 'leave', icon: 'tosend', color: '', loading: false, disabled: false }
      var materaItem = { name: '资料', type: 'btn', click: 'matera', icon: 'newspaper-o', color: '', loading: false, disabled: false }
      var leaveApprovalItem = { name: '请假审核', type: 'btn', click: 'meetingleaveApproval', icon: 'newspaper-o', color: '', loading: false, disabled: false }
      var signUpBtn = $general.getItemForKey(signUpItem.click, data.footerBtns, 'click')// 报名
      var signInBtn = $general.getItemForKey(signInItem.click, data.footerBtns, 'click')// 签到
      var leaveBtn = $general.getItemForKey(leaveItem.click, data.footerBtns, 'click')// 请假
      var materaItemBtn = $general.getItemForKey(materaItem.click, data.footerBtns, 'click')// 资料
      var leaveApprovalItemBtn = $general.getItemForKey(leaveApprovalItem.click, data.footerBtns, 'click')// 请假审核
      $general.delItemForKey(signUpBtn, data.footerBtns, 'click')
      $general.delItemForKey(signInBtn, data.footerBtns, 'click')
      $general.delItemForKey(leaveBtn, data.footerBtns, 'click')
      $general.delItemForKey(materaItemBtn, data.footerBtns, 'click')
      $general.delItemForKey(leaveApprovalItemBtn, data.footerBtns, 'click')
      // var userId = JSON.parse(sessionStorage.getItem('user')).id
      if (!collection) { // 不是补录 就有操作
        var signUp = info.signUp// 是否可以报名
        var signUpList = info.signUpList || []// 所有报名人
        var signUpIn = $general.getItemForKey(data.user.id, signUpList, 'userId')// 当前人是否已报名
        if (signUp || signUpIn) {
          if (signUpIn) {
            signUpItem.name = '已报名'
            signUpItem.color = '#ccc'
            signUpItem.disabled = true
          }
          data.footerBtns.push(signUpItem)
        }
        var signIn = info.signIn// 是否可以签到
        var signInList = info.signInList || []// 所有签到人
        var signInIn = $general.getItemForKey(data.user.id, signInList, 'userId')// 当前人是否已签到
        if (signIn || signInIn) {
          if (signInIn) {
            signInItem.name = '已签到'
            signInItem.color = '#ccc'
            signInItem.disabled = true
          }
          data.footerBtns.push(signInItem)
        }
        var leave = info.leave // 是否可以请假
        var leaveState = info.leaveState || {}
        if (leave) {
          // status 0审核中 1通过
          var leaveName = [{ name: '请假审核中' }, { name: '请假已通过' }, { name: '请假不通过' }]
          leaveItem.name = $general.isParameters(leaveState.state) ? leaveName[Number(leaveState.state)].name : '请假'
          leaveItem.leaveId = leaveState.id || ''
          data.footerBtns.push(leaveItem)
        }
      }
      var hasShowFile = false
      var conferenceMaterialFormPos = info.conferenceMaterialFormPos || []
      conferenceMaterialFormPos.forEach(function (_eItem, _eIndex, _eArr) {
        if (_eItem.isRelease === 1) {
          hasShowFile = true
        }
      })
      if (hasShowFile) { // 是否有资料 并且没有隐藏
        data.footerBtns.push(materaItem)
      }
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }
    // 底部按钮事件
    const footerBtnClick = (_item) => {
      console.error(JSON.stringify(_item))
      switch (_item.click) {
        case 'signUp':// 报名
          _item.loading = true
          _item.loadingText = '报名'
          $api.conferenceActivitiesFile.conferenceAddMySignUp({
            conferenceIds: data.id
          }).then(res => {
            _item.loading = false
            if (!res) {
              Toast('失败,请重试!')
            } else {
              var code = res.errcode || 0
              if (code === 200) {
                Toast('报名成功')
                // data.showCheck = true
                getDetails()
              } else {
                Toast('失败,请重试!')
              }
            }
          })
          break
        case 'signIn':// 签到
          data.showSignIn = true
          break
        case 'leave':// 请假
          router.push({ name: 'meetingLeave', query: { title: '请假', id: data.id, paramType: 'addLeave', leaveId: _item.leaveId } })
          break
        case 'matera':// 会议资料
          router.push({
            name: 'fileList', query: { relateType: _item.click, id: data.recordId, sysType: 'meeting', title: _item.name }
          })
          break
        case 'meetingleaveApproval':// 请假审核
          router.push({
            name: 'meetingleaveApproval', query: { relateType: _item.click, id: data.recordId, sysType: 'meeting', title: _item.name }
          })
          break
      }
    }
    // 弹框核对信息
    const Clickdialog = async (_item) => {
      console.log('点击确定了')
      const res = await $api.conferenceActivitiesFile.conferenceUpdateUserInfo({
        conferenceId: data.id,
        userId: data.user.id,
        position: data.form.position
      })
      if (res.errcode === 200) {
        Toast('核对完成')
      }
    }
    const onSelect = async (item) => {
      data.showSignIn = false
      console.log('item===>', item)
      switch (item.name) {
        case '签到口令':
          data.dialogTitle = '会议签到码'
          data.value = ''
          data.dialogShow = true
          data.showKeyboard = true
          break
        case '二维码':
          router.push('scan')
          break
      }
    }
    const confirm = async () => {
      console.log(data.value)
      if (!data.value || data.value.length !== 4) {
        Toast('签到口令不正确')
        return
      }
      const res = await $api.conferenceActivitiesFile.conferenceAddMySignIn({
        conferenceId: data.id,
        signInType: 'signInCommand',
        command: data.value
      })
      var code = res.errcode || 0
      if (code === 200) {
        Toast('签到成功')
        onRefresh()
      } else {
        Toast(res.errmsg || res.data)
      }
    }
    return { ...toRefs(data), dayjs, $general, appTheme, isShowHead, footerBtnClick, Clickdialog, onSelect, confirm, annexClick }
  }
}
</script>

<style lang="less" >
.meetingNoticeDetail {
  width: 100%;
  height: 100vh;
  background: #fff;

  .msgs_item {
    padding: 16px 14px;
  }

  .msgs_hint {
    margin-right: 20px;
    font-weight: 500;
    color: #333333;
    line-height: 1.5;
    min-width: 4.35em;
  }

  .msgs_value {
    font-weight: bold;
    color: #333333;
    line-height: 1.5;
  }

  .msgs_link {
    padding: 0 0;
    position: relative;
  }

  .msgs_link:after {
    position: absolute;
    bottom: -0.01rem;
    content: " ";
    left: 0;
    right: 0;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: inherit;
  }

  .footer_btn_box {
    z-index: 1;
  }

  .van-field {
    margin: 15px 10px;
  }
}
</style>

<template>
  <div class="n_details_survey">
    <!-- 卡片栏目 -->
    <div class="card">
      <div class="dimback"
           v-if="surveyDetails.attachmentList && surveyDetails.attachmentList.length != 0">
        <van-image fit="cover"
                   :src="surveyDetails.attachmentList[0].filePath"
                   style="width: 100%;height: 100%;"></van-image>
      </div>
      <!-- <div class="card_box"
           :style="'top:'+(safeAreaTop+45)+'px'">
        <div v-if="surveyDetails.title"
             class="card_title"> {{surveyDetails.title}}
        </div>
      </div> -->
      <div class="card_box"
           :style="'top:' + (safeAreaTop + 45) + 'px'">
        <div v-if="surveyDetails.title"
             class="card_title"><span>#</span> {{ surveyDetails.title }}
          <span>#</span>
        </div>
      </div>
    </div>
    <!-- 内容栏目 -->
    <div class="content">
      <div class="content_box">
        <div class="flex_box flex_align_center">
          <div class="n_details_survey_content">
            <div class="n_details_survey_top">
              <div class="n_details_survey_top_time">{{ dayjs(surveyDetails.starTime).format('YYYY-MM-D') }}</div>
              <div class="n_details_survey_top_text"> {{ surveyDetails.moduleView }}</div>
            </div>
            <div class="n_details_survey_con"
                 v-html="surveyDetails.content"></div>
            <template v-if="attachInfo.data.length != 0">
              <div class="general_attach"
                   style="background-color: #fff;">
                <div v-for="(item, index) in attachInfo.data"
                     :key="index"
                     class="general_attach_item flex_box flex_align_center click"
                     @click="download(item, false)">
                  <img class="general_attach_icon"
                       :style="general.loadConfigurationSize([5, 7])"
                       src="../../assets/img/pdf.png" />
                  <div class="flex_placeholder flex_box flex_align_center">
                    <div class="general_attach_name text_one2"
                         style="font-size: 14px;display: -webkit-box;">
                      {{ item.name }}
                    </div>
                    <div class="general_attach_size"
                         style="font-size: 12px;">{{ general.getFileSize(item.size) }}</div>
                  </div>
                  <div v-if="item.state != 2"
                       class="general_attach_state flex_box flex_align_center flex_justify_content"
                       :style="general.loadConfigurationSize([7, 7])">
                    <van-icon v-if="item.state == 0"
                              class-prefix="iconfont"
                              color="#ccc"
                              :size="((appFontSize + 3) * 0.01) + 'rem'"
                              name="xiazai"></van-icon>
                    <van-circle v-else-if="item.state == 1"
                                :size="((appFontSize + 3) * 0.01) + 'rem'"
                                v-model="item.schedule"
                                :rate="item.schedule"
                                stroke-width="150"></van-circle>
                    <van-icon @click.stop="Toast('缓存异常，请点击标题重试');"
                              v-else-if="item.state == 3"
                              color="#ccc"
                              :size="((appFontSize + 3) * 0.01) + 'rem'"
                              name="warning-o"></van-icon>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- <div class="n_details_content details_content"
           style="background:#fff;padding-top:0rem;"
           v-html="content"></div> -->
    </div>
    <!--  :style="`background: url(${surveyDetails.attachmentList&&surveyDetails.attachmentList.length!=0?surveyDetails.attachmentList[0].filePath:''});`" -->
    <!-- <div class="n_details_survey_bg">
      <div class="dimback"
           v-if="surveyDetails.attachmentList&&surveyDetails.attachmentList.length!==0">
        <van-image width="100%"
                   height="100%"
                   fit="cover"
                   :src="surveyDetails.attachmentList[0].filePath"></van-image>
      </div>
      <div class="n_details_survey_title"><span style="color: #fff44b;">#</span> {{ surveyDetails.title}} <span style="color: #fff44b;">#</span></div>
      <div class="n_details_survey_bgooo">
        <div class="n_details_survey_content">
          <div class="n_details_survey_top">
            <div class="n_details_survey_top_time">{{ dayjs(surveyDetails.starTime).format('YYYY-MM-D') }}</div>
            <div class="n_details_survey_top_text"> {{ surveyDetails.moduleView }}</div>
          </div>
          <div class="n_details_survey_con"
               v-html="surveyDetails.content"></div>
        </div>
      </div>
    </div> -->

    <div class="survey_comment">
      <div class="survey_comment_title">
        <div class="survey_comment_bg"></div>
        <div class="survey_comment_fw">
          请您建言
        </div>
      </div>
      <div class="survey_comment_list"
           v-for="item in surveyComment"
           :key="item.id"
           @click="skipDetails('surveyComment', item)">
        <div class="survey_comment_list_top">
          <div class="survey_comment_list_top_img">
            <img :src="item.headImg"
                 alt="">
          </div>
          <div class="survey_comment_list_top_cen">
            <div class="survey_comment_list_top_name">{{ item.name }}</div>
            <div class="survey_comment_list_top_time">
              <div class="survey_comment_list_top_time_left">{{ item.createDate }}</div>
              <div class="survey_comment_list_top_time_right">
                <div :class="{ survey_comment_list_top_time_right_like: true }"
                     @click.stop="downLike(item)">
                  <van-icon name="good-job-o"
                            :class="{ bg: item.fabulousNumber > 0 }" />
                  {{ item.fabulousNumber }}
                </div>
                <div class="survey_comment_list_top_time_right_commentNumber">
                  <van-icon name="comment-o"
                            @click.stop="commtentClick(item)" />
                  {{ item.commentNumber }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="survey_comment_list_but">
          <div class="survey_comment_list_but_title">
            {{ item.title }}
          </div>
          <div class="survey_comment_list_but_detail">
            {{ item.detail }}
          </div>
        </div>
      </div>
    </div>
    <!-- <surveyComment ref="surveyComments"
                   @openInputBoxEvent="openInputBoxEvent"
                   :id="id"
    @fabulousInfo="fabulousInfo"></surveyComment> -->

    <surveyInput ref="inputBox"
                 :inputData="inputData"
                 :surveyDetailsTitle="surveyDetails.title"
                 :endTime="surveyDetails.endTime"
                 @addCommentEvent="addCommentEvent"
                 :pageType="pageType"
                 :type="type"
                 :id="id" />
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Image as VanImage } from 'vant'
// import surveyComment from './components/surveyComment.vue'
import surveyInput from './components/surveyInput.vue'
export default ({
  name: 'schedule',
  props: {},
  components: {
    [VanImage.name]: VanImage,
    // surveyComment
    surveyInput
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const general = inject('$general')
    const dayjs = require('dayjs')
    const data = reactive({
      id: route.query.id,
      pageType: route.query.type || '',
      type: 25,
      surveyDetails: {}, // 意见征集详情数据
      surveyComment: [], // 意见征集评论数据
      surveyComments: null,
      attachInfo: { name: '附件', data: [] }, // 附件对象
      inputData: {
        input_placeholder: '请您建言...', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: false, // 显示评论功能
        showLike: false, // 显示点赞功能
        showAttach: false// 显示添加附件(图片)
      },
      inputBox: null
    })
    onMounted(() => {
      getData()
    })
    const getData = async () => {
      const resData = await $api.news.getAurveyDetails({ id: data.id })
      data.surveyDetails = resData.data
      data.attachInfo.data = []
      var attachmentList = resData.data.attachmentList || []
      // eslint-disable-next-line eqeqeq
      if (attachmentList.length != 0) {
        for (var k = 0; k < attachmentList.length; k++) {
          var fileId = attachmentList[k].id
          var nItemPath = attachmentList[k].filePath
          var fileName = attachmentList[k].fileName
          var fileType = attachmentList[k].fileType
          if (attachmentList[k].moduleType === 'file') {
            data.attachInfo.data.push({
              url: nItemPath,
              state: 0,
              schedule: -1,
              name: fileName,
              fileId: fileId,
              iconInfo: general.getFileTypeAttr(fileType)
            })
          }
        }
      }
      const res = await $api.news.getAurveyComment({ surveyId: data.id, pageNo: '1', pageSize: '199', auditSituation: '' })
      data.surveyComment = res.data
    }
    const commtentClick = async (item) => {
      console.log('点击评论了')
      console.log('item==>', item)
      data.inputBox.changeType(2, item, false)
    }
    const download = (item) => {
      if (item.iconInfo.type === 'pdf') {
        if (window.location.origin === 'http://59.224.134.155') {
          window.open('http://59.224.134.155/pdf/web/viewer.html?file=' + item.url)
        } else {
          window.open('http://www.cszysoft.com:9090/pdf/web/viewer.html?file=' + item.url)
        }
      } else {
        var param = {
          id: item.id,
          url: item.url,
          name: item.name
        }
        router.push({ name: 'superFile', query: param })
      }
    }
    // 点击点赞
    const downLike = (_item) => {
      _item.isFabulous = !_item.isFabulous
      if (_item.isFabulous) {
        _item.fabulousNumber++
      } else {
        _item.fabulousNumber--
      }
      fabulousInfo(_item.isFabulous, _item.id)
    }
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '60'
      }
      await $api.general.fabulous({ url, params })
      getData()
    }
    const skipDetails = (type, { name, detail, createDate, id, title }) => {
      router.push({ name: 'surveyComentInfo', query: { id, type, userName: name, content: detail, createDate, title } })
    }
    const addCommentEvent = (value) => {
      getData()
    }
    return { ...toRefs(data), download, dayjs, route, router, $api, general, addCommentEvent, skipDetails, downLike, fabulousInfo, commtentClick }
  }
})
</script>
<style lang='less' scoped>
.n_details_survey {
  width: 100%;

  .card {
    position: absolute;
    width: 100%;
    height: 5rem;
    box-sizing: border-box;
    background: #a8a8a8;

    .dimback .van-image {
      opacity: 0.6;
      filter: alpha(opacity=60);
      filter: blur(1px);
      -webkit-filter: blur(1px);
    }

    .dimback {
      background: #686868;
      width: 100%;
      height: 100%;
    }

    .card_box {
      position: absolute;
      left: 0.16rem;
      top: 0.74rem;
      padding-right: 0.16rem;
      z-index: 999;

      // display: flex;
      // justify-content: center;
      // align-items: center;
      // position: relative;
      // top: -4.2rem;
      span {
        color: #fff44b;
      }

      .card_title {
        margin-bottom: 0.12rem;
        font-weight: bold;
        color: #fff;
        line-height: 1.5;
        font-size: 18px;
      }
    }
  }

  .n_details_survey_attachmentList {
    font-size: 14px;

    .n_details_survey_attachmentList_title {
      font-size: 14px;
      margin: 20px 0 5px 0;
    }

    .n_details_survey_attachmentList_item {
      font-size: 14px;
      margin: 5px 0;
      background-color: #e4e4e4;
      border-radius: 5px;
      padding: 5px 10px;
    }
  }

  .content {
    position: relative;
    padding-top: 4.8rem;
    margin-bottom: 0.1rem;

    .content_box {
      width: 100%;
      background-color: #fff;
      border-radius: 14px 14px 0px 0px;
      z-index: 888;
      padding: 15px 14px;

      .n_details_survey_content {
        width: 100%;
        background: #fff;
        border-radius: 20px 20px 0 0;
        // position: absolute;
        // top: 50px;
        // padding: 10px;
        box-sizing: border-box;

        .n_details_survey_top {
          width: 100%;
          height: 30px;
          color: #a8a8a8;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .n_details_survey_top_time {
            font-size: 14px;
          }

          .n_details_survey_top_text {
            font-size: 14px;
          }
        }
      }
    }

    .details_content {
      font-size: 17px;
      text-indent: 2em;
      line-height: 1.5;
      padding: 0 10px 15px 10px;
    }
  }

  .n_details_survey_bg {
    width: 100%;

    // height: 350px;
    // background: #000;
    // background-size: 100% 100% !important;
    // overflow: hidden;
    .dimback .van-image {
      opacity: 0.6;
      filter: alpha(opacity=60);
      filter: blur(1px);
      -webkit-filter: blur(1px);
    }

    .dimback {
      background: #000;
      width: 100%;
      height: 100%;
    }

    .n_details_survey_bgooo {
      position: relative;
      padding-top: 2rem;
      margin-bottom: 0.1rem;
      // height: 100%;
      // background: #0000005b;
    }

    .n_details_survey_title {
      width: 100%;
      height: 30px;
      font-size: 20px;
      font-weight: 700;
      color: #fff;
      margin: 0px 10px 0;
    }
  }

  .survey_comment {
    width: 100%;
    background: #fff;
    margin-bottom: 80px;

    .survey_comment_title {
      width: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;
    }

    .survey_comment_fw {
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-weight: 700;
    }

    .survey_comment_bg {
      width: 5px;
      height: 16px;
      background: #3894ff;
      margin: 0 10px;
    }

    .survey_comment_list {
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      border-bottom: 1px solid #e8e8e8;

      .survey_comment_list_but {
        box-sizing: border-box;
        width: 100%;

        .survey_comment_list_but_title {
          font-weight: 700;
          margin: 10px 0;
          font-size: 18px;
        }

        .survey_comment_list_but_detail {
          font-size: 14px;
        }
      }

      .survey_comment_list_top {
        display: flex;
        align-items: center;
        width: 100%;
        height: 50px;

        .survey_comment_list_top_img {
          width: 40px;
          height: 90%;
          border-radius: 3px;
          overflow: hidden;
          margin: 0 5px;

          >img {
            width: 100%;
            height: 100%;
          }
        }

        .survey_comment_list_top_cen {
          height: 100%;
          width: calc(100% - 30px);
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .survey_comment_list_top_name {}

          .survey_comment_list_top_time {
            width: 100%;
            color: #c2c2c2;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .survey_comment_list_top_time_left {
              font-size: 14px;
            }

            .survey_comment_list_top_time_right {
              width: 20%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin: 0 10px;

              .survey_comment_list_top_time_right_like {
                font-size: 14px;

                .bg {
                  color: #3894ff;
                }
              }

              .survey_comment_list_top_time_right_commentNumber {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>

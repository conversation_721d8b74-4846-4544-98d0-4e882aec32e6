// 建议撰写
const suggestNew = () => import('@/views/suggest/suggestNew')
// 所有建议
const suggestList = () => import('@/views/suggest/suggestList')
// 我的建议
const mySuggestList = () => import('@/views/suggest/mySuggestList')
// 建议详情
const suggestDetails = () => import('@/views/suggest/suggestDetails/suggestDetails')
// 沟通情况
const memberCommunication = () => import('@/views/suggest/memberCommunication')
// 答复意见详情
const suggestReply = () => import('@/views/suggest/suggestReply')
// 满意度详情
const suggestSatisfactionDetails = () => import('@/views/suggest/suggestSatisfactionDetails')
const suggestSatisfaction = () => import('@/views/suggest/suggestSatisfaction') // 满意度测评
const handlingPlan = () => import('@/views/suggest/handlingPlan')
const quantificat = () => import('@/views/suggest/quantificat')
const JimoEvaluate = () => import('@/views/suggest/JimoEvaluate')
const LichangEvaluate = () => import('@/views/suggest/LichangEvaluate')
const suggest = [
  {
    path: '/suggestNew',
    name: 'suggestNew',
    component: suggestNew,
    meta: {
      title: '建议撰写',
      keepAlive: true
    }
  },
  {
    path: '/suggestList',
    name: 'suggestList',
    component: suggestList,
    meta: {
      title: '所有建议',
      keepAlive: true
    }
  },
  {
    path: '/mySuggestList',
    name: 'mySuggestList',
    component: mySuggestList,
    meta: {
      title: '我的建议',
      keepAlive: true
    }
  },
  {
    path: '/suggestDetails',
    name: 'suggestDetails',
    component: suggestDetails,
    meta: {
      title: '建议详情',
      keepAlive: false
    }
  },
  {
    path: '/memberCommunication',
    name: 'memberCommunication',
    component: memberCommunication,
    meta: {
      title: '沟通情况',
      keepAlive: false
    }
  },
  {
    path: '/suggestReply',
    name: 'suggestReply',
    component: suggestReply,
    meta: {
      title: '答复意见详情',
      keepAlive: false
    }
  },
  {
    path: '/suggestSatisfactionDetails',
    name: 'suggestSatisfactionDetails',
    component: suggestSatisfactionDetails,
    meta: {
      title: '满意度测评详情',
      keepAlive: false
    }
  },
  {
    path: '/handlingPlan',
    name: 'handlingPlan',
    component: handlingPlan,
    meta: {
      title: '满意度测评详情',
      keepAlive: false
    }
  },
  {
    path: '/quantificat',
    name: 'quantificat',
    component: quantificat,
    meta: {
      title: '详情',
      keepAlive: false
    }
  },
  {
    path: '/JimoEvaluate',
    name: 'JimoEvaluate',
    component: JimoEvaluate,
    meta: {
      title: '满意度测评详情',
      keepAlive: false
    }
  },
  {
    path: '/suggestSatisfaction',
    name: 'suggestSatisfaction',
    component: suggestSatisfaction,
    meta: {
      title: '满意度测评详情',
      keepAlive: false
    }
  },
  {
    path: '/LichangEvaluate',
    name: 'LichangEvaluate',
    component: LichangEvaluate,
    meta: {
      title: '满意度测评详情',
      keepAlive: false
    }
  }
]
export default suggest

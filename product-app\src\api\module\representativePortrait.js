import { HTTP } from '../http.js'
class representativePortrait extends HTTP {
  // 获取二维码
  memberPortraitQrCode (params) {
    return this.request({ url: '/memberPortrait/qrCode', data: params })
  }

  // 获取代表画像
  memberPortraitDutyData (params) {
    return this.request({ url: '/memberPortrait/dutyData', data: params })
  }

  // 代表的年度报告
  getPersonSubmitInfo (params) {
    return this.request({ url: 'http://120.221.72.187:9002/yw_npc_api/npc/advice/getPersonSubmitInfo', data: params, method: 'post', header: { token: 'B02005501A67E46EE6D84A882E273FE5' } })
  }

  // 时光轴
  memberPortraitDutyTime (params) {
    return this.request({ url: '/memberPortrait/dutyTime', data: params })
  }
}
export {
  representativePortrait
}

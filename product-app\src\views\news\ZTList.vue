<template>
  <div class="ZTList">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <!-- <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" /> -->
      </div>
    </van-sticky>
    <div class="ZTList_img">
      <img :src="url" alt="">
    </div>
        <!--数据列表-->
        <div class="ZTList_list" >
          <div class="ZT_list_type3" v-if="item.type == 3">
            <div class="ZT_list_type3_item" v-for="it in item.specialsubjectNews" :key="it.id" @click="Skip(it, item.type)" >
              <div class="ZT_list_type3_item_title">
                <p class="ZT_list_type3_item_text">{{ it.title }}</p>
                <p class="ZT_list_type3_item_time">{{ it.createDate?dayjs(it.createDate).format('YYYY-MM-DD') : '' }}</p>
              </div>
              <div class="ZT_list_type3_item_img">
                <img :src="it.imgObj.fullUrl" alt="" >
              </div>
            </div>
          </div>
          <div class="ZT_list_item" v-else-if="item.type == 1">
            <div class="ZT_list_item_con" v-for="it in item.specialsubjectNews" :key="it.id">
              <div class="ZT_list_item_con_box">
                <img :src="it.imgObj.fullUrl" alt="" >
                <div class="ZT_list_item_con_title">
                  {{ it.title }}
                </div>
                <div class="ZT_list_item_con_time">
                  {{ it.createDate?dayjs(it.createDate).format('YYYY-MM-DD') : '' }}
                </div>
                <div class="ZT_list_item_con_shade" @click="Skip(it)">
                  <van-icon name="play-circle" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div v-for="item in dataList"
             :key="item.id"
             @click="details(item)">
          <div class="item-box">
            <div class="item-img"
                 :style="'background-image:url('+item.themeImg+')'"></div>
            <img :src="item.coverImg"
                 style="width:100%;"
                 alt=""
                 srcset="">
            <div class="item-title">{{item.title}}</div>
          </div>
        </div> -->
        <!--加载中提示 首次为骨架屏-->
        <div v-if="showSkeleton"
             class="notText">
          <van-skeleton v-for="(item,index) in 3"
                        :key="index"
                        title
                        :row="3"></van-skeleton>
        </div>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Image as VanImage } from 'vant'
export default {
  name: 'ZTList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      id: route.query.id || '',
      title: route.query.title || '',
      url: route.query.url || '',
      subjectId: route.query.subjectId || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      carouselList: [],
      dataList: [],
      switchs: {
        value: '',
        data: []
      }
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getList()
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      getList()
    })
    const search = () => {
      getList()
    }
    const onRefresh = () => {
      getList()
    }
    const onLoad = () => {
      getList()
    }
    // 列表请求
    const getList = async () => {
      var res = await $api.news.getSpecialsubjectRelateinfoListAll({
        pageNo: 1,
        pageSize: 50,
        title: '',
        subjectId: data.subjectId,
        columnId: data.id,
        isAppShow: 1,
        auditingFlag: 1,
        isPublish: 1
      })
      console.log(res)
      data.dataList = res.data
      console.log('数据', data.dataList)
      // 数据全部加载完成
      // if (data.dataList.length >= total) {
      // }
    }
    // 跳转视频
    const Skip = (item, type) => {
      if (type === 3 || type === '3') { // 列表类型
        router.push({ path: '/newsDetails', query: { type: 'listType', id: item.id } })
      }
      if (item.externalLinks != null) {
        window.location.href = item.externalLinks
      }
    }
    const onClickLeft = () => history.back()
    const details = (row) => {
      console.log(row)
      router.push({ name: 'newsZTListList', query: { id: row.id, title: row.title } })
    }
    return { ...toRefs(data), search, Skip, onClickLeft, onRefresh, onLoad, details, dayjs }
  }
}
</script>

<style lang="less" scoped>
.ZTList {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .item-box {
    width: calc(100% - 20px;);
    margin-left: 10px;
    margin-bottom: 20px;
  }
  .ZTList_img {
    width: 100%;
    height: 200px;
    >img {
      width: 100%;
      height: 100%;
    }
  }
  .ZTList_list {
    box-sizing: border-box;
    width: 100%;
    .ZTList_list_item {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: left;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 20px;
      .ZTList_list_item_con {
        height: 230px;
        width: 45%;
        position: relative;
        .ZTList_list_item_con_box {
          width: 100%;
          height: 100%;
          border-radius: 20px;
          overflow: hidden;
          .ZTList_list_item_con_title {
            margin: 0 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .ZTList_list_item_con_time {
            font-size: 14px;
            color: #666;
            text-align: right;
            margin-top: 20px;
          }
          >img {
            width: 100%;
            height: 130px;
          }
          .ZTList_list_item_con_shade {
            width: 100%;
            height: 130px;
            background: #0000003a;
            color: #fff;
            font-size: 40px;
            text-align: center;
            line-height: 130px;
            position: absolute;
            top: 0;
            left: 0;
            border-radius: 20px 20px 0 0 ;
          }
        }
      }
    }
    .ZTList_list_title {
      width: 92%;
      height: 30px;
      display: flex;
      align-items: center;
      position: relative;
      padding: 0 10px;
      box-sizing: border-box;
      justify-content: space-between;
      margin: 20px;
      .ZTList_list_title_p {
        z-index: 99;
        font-size: 20px;
        margin: 0 0px 0 20px;
        box-sizing: border-box;
      }
      .ZTList_list_title_more {
        font-size: 14px;
        color: #666;
      }
      >img{
        width: 50px;
        height: 20px;
        position: absolute;
        top: 5px;
        left: 10px;
      }
    }
  }
  .item-img {
    width: 100%;
    height: 170px;
    border-radius: 5px;
    background: url() no-repeat;
    background-size: 100%;
    background-position: center;
  }
  .item-title {
    font-size: 16px;
    margin: 10px 0;
    text-align: justify;
  }
}
</style>

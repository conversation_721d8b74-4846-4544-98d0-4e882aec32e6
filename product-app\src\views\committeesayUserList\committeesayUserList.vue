<template>
  <div class="noticeList">
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                @load="onLoad"
                :immediate-check="false">
        <van-cell v-for="item in dataList"
                  :key="item.id">
          <!-- 履职圈列表 -->
          <div class="representativeCircle_box_li"
               @click="skipDetails('resumption', item.id, null, item.isFollow, item.publishBy, item.isFabulous, item.fabulousCount)">
            <div class="representativeCircle_box_top">
              <img :src="item.headImg"
                   alt=""
                   class="representativeCircle_box_top_headImg">
              <div class="representativeCircle_box_name">
                <p class="representativeCircle_box_names">{{ item.publishName }}</p>
                <p class="representativeCircle_box_congressStr"
                   v-if="item.congressStr.length > 0"> {{ item.congressStr.join(' | ') + '人大代表' }}
                  {{ item.representerTeam === '' ? '' : '(' + item.representerTeam + ')' }}</p>
                <div :class="{ 'attention': true, 'attentionDel': item.isFollow == 1 }"
                     @click.stop="attentionEdit(item.publishBy, item.isFollow)"
                     v-if="item.publishBy != user.id">
                  {{ item.isFollow == 1 ? '已关注' : '+ 关注' }}
                </div>
                <div class="representativeCircle_box_del"
                     v-else>
                  <van-icon name="delete-o"
                            @click.stop="committeesayDel(item.id)" />
                </div>
              </div>
            </div>
            <div class="representativeCircle_box_center">
              <div class="representativeCircle_box_center_content"
                   v-html="item.content">
              </div>
              <div class="representativeCircle_box_center_attachmentList">
                <van-image position="contain"
                           width="2.5rem"
                           height="2rem"
                           fit="cover"
                           v-for="it in item.attachmentList"
                           :key="it.id"
                           :src="it.filePath"
                           @click.stop="previewCalback(item.attachmentList)" />
              </div>
            </div>
            <div class="representativeCircle_box_buttom">
              <div class="representativeCircle_box_buttom_time">{{ item.publishDate }}</div>
              <div class="representativeCircle_box_buttom_cont">
                <div class="representativeCircle_box_buttom_comment"
                     @click.stop="replyClick(item)">
                  <img :src="require('../../assets/img/icon_comments.png')"
                       alt="">
                  <span>{{ item.commentCount }}</span>
                </div>
                <div class="representativeCircle_box_buttom_like"
                     @click.stop="downLike(item)">
                  <img :src="require(!item.isFabulous ? '../../assets/img/icon_likes.png' : '../../assets/img/icon_likes_on.png')"
                       alt="">
                  <span>{{ item.fabulousCount }}</span>
                </div>
              </div>
            </div>
            <div class="likeComment_box">
              <div class="like_box"
                   :style="$general.loadConfiguration(-4)"
                   v-if="item.fabulousList != null && item.fabulousList.length">
                <van-icon name="like-o"
                          v-if="item.fabulousList != null && item.fabulousList.length" />
                <span :style="$general.loadConfiguration(-4) + 'margin-bottom: 2px;' + 'line-height: 0.2rem;'"
                      @click.stop="openUserList(it.id)"
                      v-for="(it, ind) in item.fabulousList"
                      :key="ind">
                  {{ ind > 0 ? ',' : '' }} {{ it.userName }} </span>
              </div>
              <div class="comment_box"
                   @click.stop=""
                   v-if="item.children.length != 0">
                <div v-for="(items, indexs) in item.children"
                     :key="indexs">
                  <p style="display: flex;align-items: center;">
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                          @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType }}:
                    </span>
                    <span :style="$general.loadConfiguration(-4) + 'flex:1;'"
                          @click.stop="replyClick(item, items, indexs)"> {{ items.content }} </span>
                  </p>
                  <p v-for="(ite, inde) in items.children"
                     :key="inde"
                     :style="$general.loadConfiguration(-4)">
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                          @click.stop="openUserList(ite.createBy)">{{ ite.userName }}</span> 回复
                    <span :style="$general.loadConfiguration(-4) + 'color: #6e7fa3;'"
                          @click.stop="openUserList(items.createBy)">{{ items.userName ? items.userName : items.userType }}:
                    </span>
                    <span :style="$general.loadConfiguration(-4)"
                          @click.stop="replyClick(item, items, inde)"> {{ ite.content }} </span>
                  </p>
                </div>
              </div>
              <div class="reply_box"
                   @click.stop=""
                   v-show="item.inputObj.replyShow">
                <div class="reply_box_item">
                  <input type="text"
                         v-model="item.commentObj.content"
                         :style="$general.loadConfiguration(-4)"
                         class="reply_box_inp"
                         :placeholder="item.inputObj.replyName">
                  <button :class="item.commentObj.content != '' ? 'reply_box_but' : 'reply_box_buts'"
                          :style="$general.loadConfiguration(-4)"
                          @click="transmitClick(item.commentObj, item.inputObj)">发送</button>
                </div>
              </div>
            </div>
          </div>
        </van-cell>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Image as VanImage, ImagePreview, Dialog, Toast, Grid } from 'vant'
export default {
  name: 'beingCountedList',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Grid.name]: Grid,
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const $general = inject('$general')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      uId: route.query.uId || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    watch(() => data.uId, (newName, oldName) => {
      console.log('newName==>', data.uId)
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      userList()
    })
    onMounted(() => {
      userList()
    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      userList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      userList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      userList()
    }
    // 列表请求
    const userList = async () => {
      const res = await $api.general.representativeCircle({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        publishBy: data.uId
      })
      var { data: list, total } = res
      list.forEach(item => {
        // 点赞人名称
        item.likes = item.fabulous || ''
        // 评论集合
        item.commentObj = {
          content: '',
          createBy: data.user.id,
          commentPid: '',
          keyId: item.id,
          attach: '',
          extend: '1',
          type: '25',
          areaId: '370200'
        }
        item.inputObj = {
          replyShow: false,
          replyName: '发送评论'
        }
        if (item.fabulousUser != null) {
          item.fabulousList = item.fabulousUser.map(items => {
            return {
              userName: items.userName,
              id: items.id
            }
          })
        } else {
          item.fabulousList = []
        }
      })
      console.log('list==>>', list)
      data.dataList = list
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    // 点击点赞
    const downLike = (_item) => {
      _item.isFabulous = !_item.isFabulous
      if (_item.isFabulous) {
        _item.fabulousCount++
        _item.likes = _item.likes + (_item.likes ? ',' : '') + data.user.userName
        _item.fabulousList.push({ id: data.user.id, userName: data.user.userName })
      } else {
        _item.fabulousCount--
        _item.fabulousList = _item.fabulousList.filter(item => item.id !== data.user.id)
      }
      fabulousInfo(_item.isFabulous, _item.id)
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '25'
      }
      await $api.general.fabulous({ url, params })
    }
    // 预览图片
    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    // 关注或取关
    const attentionEdit = async (followId, state) => {
      var type = ''
      if (state === 1) {
        type = 'del'
      } else {
        type = 'add'
      }
      await $api.news.attention({ params: { followId, type: 25 }, type })
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      userList()
    }
    const committeesayDel = (id) => {
      Dialog.confirm({
        title: '温馨提示',
        message: '确定删除吗'
      })
        .then(async () => {
          var { errcode } = await $api.news.committeesayDels({ ids: id })
          if (errcode === 200) {
            data.pageNo = 1
            data.dataList = []
            data.loading = true
            data.finished = false
            userList()
          }
          // on confirm
        })
        .catch(() => {
          // on cancel
        })
    }
    // 回复
    const replyClick = (_item, _items) => {
      _item.inputObj.replyShow = true
      _item.inputObj.replyName = '发送评论'
      if (_items) {
        _item.commentObj.commentPid = _items.id
        _item.inputObj.replyName = _items.userName ? '回复' + _items.userName : '回复'
      }
    }
    // 发送
    const transmitClick = async (_item, _items) => {
      if (_item.content === '') {
        return false
      }
      _items.replyShow = false
      var url = 'comment/save'
      var params = _item
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        userList()
      } else {
        Toast('请求失败。')
      }
    }
    // 打开用户列表
    const openUserList = (_item) => {
      router.push({ path: '/committeesayUserList', query: { uId: _item } })
      setTimeout(() => {
        window.location.reload()
      }, 800)
    }
    const skipDetails = (type, _id, url, isFollow, publishBy, isFabulous, fabulousCount) => {
      router.push({ name: 'newsDetails', query: { id: _id, type, isFollow, publishBy, ifIike: isFabulous, fabulousCount: fabulousCount } })
    }
    const details = (row) => {
      router.push({ name: 'noticeDetails', query: { id: row.id } })
    }
    return { ...toRefs(data), $general, openUserList, skipDetails, search, onRefresh, onLoad, details, downLike, previewCalback, attentionEdit, committeesayDel, replyClick, transmitClick }
  }
}
</script>
<style lang="less">
.representativeCircle_box_li {
  width: 100%;
  padding-bottom: 5px;

  .representativeCircle_box_top {
    width: 100%;
    height: 35px;
    margin: 5px 0;
    display: flex;
    align-items: center;
    position: relative;

    .representativeCircle_box_del {
      text-align: center;
      position: absolute;
      top: 0px;
      right: 10px;
      width: 66px;
      height: 65%;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .attention {
      text-align: center;
      position: absolute;
      top: -4px;
      right: 10px;
      width: 66px;
      height: 65%;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 40px;
      color: #3894ff;
      border: 1px solid #3894ff;
    }

    .attentionDel {
      color: #666;
      border: 1px solid #666;
    }

    .representativeCircle_box_top_headImg {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin: 5px;
    }

    .representativeCircle_box_name {
      font-size: 16px;
      margin-top: 5px;

      .representativeCircle_box_congressStr {
        font-size: 14px;
        color: #4c4c4c;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        overflow: hidden;
      }
    }
  }

  .representativeCircle_box_center {
    box-sizing: border-box;

    .representativeCircle_box_center_content {
      padding-left: 13px;
      margin: 5px 0;
    }

    .representativeCircle_box_center_attachmentList {
      width: 95%;
      margin: auto;
      display: flex;
      flex-wrap: wrap;

      // justify-content: space-between;
      .van-image {
        margin: 5px;
      }
    }
  }
}

.representativeCircle_box_buttom {
  width: 100%;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .representativeCircle_box_buttom_time {
    width: 70%;
    font-size: 14px;
    color: #a8a8a8;
  }

  .representativeCircle_box_buttom_cont {
    width: 25% !important;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .representativeCircle_box_buttom_comment {
      display: flex;
      align-items: center;
      justify-content: space-between;

      >img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }

    .representativeCircle_box_buttom_like {
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      line-height: 100%;

      >img {
        width: 16px;
        height: 16px;
        margin-right: 5px;
      }
    }
  }
}

.likeComment_box {
  background: #f7f7f7;
  margin: 0 0 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;

  .comment_box {
    margin: 0 5px 0px;
  }

  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }

  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;

    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;

      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }

      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }

      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}</style>

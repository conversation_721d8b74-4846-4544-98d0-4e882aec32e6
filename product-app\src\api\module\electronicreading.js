import { HTTP } from '../http.js'
class electronicreading extends HTTP {
  // 通过会议类型查询不同的文件
  getConferenceFile (params) {
    return this.request({ url: '/conferencematerial/getConferenceFile', data: params })
  }

  // 通过会议类型查询会议列表
  getHistoricalConference (params) {
    return this.request({ url: '/conference/getHistoricalConference', data: params })
  }

  // 会议通知列表
  conferencenoticeList (params) {
    return this.request({ url: '/conferenceNotice/getAppList', data: params })
  }

  // 会议通知详情
  conferenceNoticeInfo (params) {
    return this.request({ url: `/conferenceNotice/info/${params}` })
  }

  // 会议议程列表
  meetingScheduleList (params) {
    return this.request({ url: '/conferenceschedule/list', data: params })
  }

  // 会议日程
  conferencescheduleInfo (params) {
    return this.request({ url: `/conferenceschedule/info/${params}` })
  }

  // 会议文件列表
  conferencefileList (params) {
    return this.request({ url: '/conferencefile/list', data: params })
  }
}
export {
  electronicreading
}

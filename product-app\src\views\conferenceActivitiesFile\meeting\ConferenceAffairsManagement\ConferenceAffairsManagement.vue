<template>
  <div class="ConferenceAffairsManagement">
    <van-dialog v-if="qrcode.show"
                v-model:show="qrcode.show"
                title="签到二维码"
                :style="general.loadConfiguration()"
                show-cancel-button
                :confirm-button-color="appTheme"
                confirm-button-text="保存到相册"
                @confirm="btnSave()">
      <div class="qrcode_img">
        <van-image fit="cover"
                   id="saveImg"
                   :src="qrcode.src"></van-image>
      </div>
    </van-dialog>
    <!--子会筛选-->
    <van-pull-refresh v-model="refreshing"
                      style="min-height: 80vh;"
                      success-text="刷新成功"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <van-dropdown-menu v-if="allMeeting"
                           class="van-hairline--bottom"
                           :active-color="appTheme"
                           :style="general.loadConfiguration(-1)">
          <van-dropdown-item v-model="allMeetingChild.value"
                             @change="allMeetingChange"
                             :options="allMeetingChild.data"></van-dropdown-item>
        </van-dropdown-menu>
        <div class="manage_btn_box flex_box T-flex-flow-row-wrap">
          <van-cell v-for="(item,index) in listData"
                    :key="index"
                    clickable
                    class=""
                    @click="openDetails(item)">
            <div class="flex_box flex_align_center">
              <van-icon :size="((appFontSize+2)*0.02)+'rem'"
                        color="#777"
                        :name="item.icon"></van-icon>
              <div :style="general.loadConfiguration(-1)+'color: #333;margin-left:0.1rem;flex-shrink:0;'">{{item.name}}
              </div>
              <div class="flex_placeholder"></div>
              <div v-if="item.value"
                   :style="general.loadConfiguration(-2)+'color: #666;margin-right:0.1rem;'">
                {{item.value}}</div>
              <van-icon v-if="item.click"
                        :size="((appFontSize+2)*0.02)+'rem'"
                        color="#aaa"
                        name="arrow">
              </van-icon>
            </div>
          </van-cell>
        </div>
        <!--接收统计图表-->
        <div style="padding: 30px 0;background-color: #fff;">
          <div class="module_box">
            <div id="pieBox"
                 style="height: 200px;margin-left: 10px;"></div>
          </div>
          <div class="module_box">
            <div id="barBox"
                 div
                 style="height: 260px;"></div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
    <!--不为一级页面时 适配底部条-->
    <!-- <footer v-if="pageType=='page'"
            :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer> -->
  </div>

</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
import * as echarts from 'echarts'
export default {
  name: 'ConferenceAffairsManagement',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    const $api = inject('$api')
    const data = reactive({
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      myParam: JSON.parse(route.query.myParam) || {},
      loading: false,
      finished: false,
      refreshing: false,
      listData: [
        { name: '考勤管理', icon: 'points', click: 'attendance' },
        { name: '资料管理', icon: 'notes-o', click: 'matera' },
        { name: '参会人员信息', icon: 'user-o', click: 'Personnel' },
        { name: '签到二维码', icon: 'qr', click: 'signQR' },
        { key: 'signInCommand', name: '签到口令', icon: 'orders-o', value: '2345' }
      ], // 列表数据
      qrcode: { show: false, src: '' }, // 展示二维码
      signUpStatisticsHas: true,
      // 出勤率统计
      option1: {
        title: { text: '出勤率统计', textStyle: { fontSize: 16, color: '#333' }, padding: [13, 14] },
        // tooltip: {trigger: 'item',formatter: "{b}: {c} ({d}%)"},// {b}:数据名； {c}：数据值； {d}：百分比，可以自定义显示内容，
        legend: {
          top: '20%',
          orient: 'vertical',
          left: '3%',
          data: []
        },
        color: ['#FFC649', '#8E49FF', '#0271E3', '#F8B287', '#51E8CD', '#E34C4C', '#F9EA3E'],
        series: [{
          type: 'pie',
          radius: ['35%', '55%'],
          center: ['70%', '50%'],
          avoidLabelOverlap: false,
          label: {
            normal: { show: false, position: 'center', formatter: '{b}\n{c} ({d}%)' },
            emphasis: { show: true, textStyle: { fontSize: '13', color: '#6670AB' } } // 文字至于中间时，这里需为true
          },
          data: []
        }]
      },
      attendanceStatisticsHas: true,
      // 考勤数据统计
      option2: {
        title: { text: '考勤数据统计', textStyle: { fontSize: 16, color: '#333' }, padding: [13, 14] },
        color: ['#007BFF'], // 线条颜色
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: { show: true, textStyle: { color: '#666666', fontSize: 11 } },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          axisLabel: { show: true, textStyle: { color: '#666666', fontSize: 11 } },
          axisTick: { show: false }
        },
        series: [{
          type: 'bar',
          name: '人数',
          label: { normal: { show: true, position: 'top' } },
          barWidth: 24, // 柱图宽度
          data: []
        }]
      },
      allMeeting: false, // 是否全会管理
      allMeetingChild: { value: '1', data: [{ text: '子会', value: '1' }] },
      meetLeavePercentage: [],
      conferenceAllList: []
    })
    onMounted(() => {
      if (data.title) {
        document.title = data.title
      }
      data.allMeeting = data.myParam.allMeeting
      data.allMeetingChild.value = ''
      data.allMeetingChild.data = []
      if (data.allMeeting) {
        data.myParam.allMeetingChild.forEach(function (_eItem, _eIndex, _eArr) {
          data.allMeetingChild.data.push({ text: _eItem.title, value: _eItem.id, signInCommand: _eItem.signInCommand })
        })
        if (!data.allMeetingChild.value && data.allMeetingChild.data.length !== 0) {
          data.allMeetingChild.value = data.allMeetingChild.data[0].value
          general.getItemForKey('signInCommand', data.listData).value = data.allMeetingChild.data[0].signInCommand
        }
      } else {
        general.getItemForKey('signInCommand', data.listData).value = data.myParam.signInCommand
      }
      AttendanceStatistics()
      AttendanceDataStatistics()
    })
    const onRefresh = () => {
      data.pageNo = 1
      data.loading = true
      data.finished = false
      data.option2.xAxis.data = []
      data.option2.series[0].data = []
      data.option1.legend.data = []
      data.option1.series[0].data = []
      AttendanceStatistics()
      AttendanceDataStatistics()
    }
    const onLoad = () => {
    }
    const onClickLeft = () => history.back()
    // 出勤率统计
    const AttendanceStatistics = async () => {
      var res = []
      var datas = {
        conferenceId: data.myParam.meetId || ''
      }
      res = await $api.conferenceActivitiesFile.conferenceGetAttendanceNum(datas)
      var { data: list } = res
      list.forEach(function (_eItem, _eIndex, _eArr) {
        data.option1.legend.data.push(_eItem.name || '')
        data.option1.series[0].data.push({ value: _eItem.count, name: _eItem.name || '' })
      })
      echarts.init(document.getElementById('pieBox')).setOption(data.option1)
      data.loading = false
      data.refreshing = false
      data.finished = true
    }
    // 考勤数据统计
    const AttendanceDataStatistics = async () => {
      var res = []
      var datas = {
        conferenceId: data.myParam.meetId || ''
      }
      res = await $api.conferenceActivitiesFile.conferenceGetSignUpNum(datas)
      var { data: list } = res
      list.forEach(function (_eItem, _eIndex, _eArr) {
        data.option2.xAxis.data.push(_eItem.name || '')
        data.option2.series[0].data.push({ value: _eItem.count || '' })
      })
      echarts.init(document.getElementById('barBox')).setOption(data.option2)
      data.loading = false
      data.refreshing = false
      data.finished = true
    }
    // 全会切换子会
    const allMeetingChange = async (_value) => {
      general.getItemForKey('signInCommand', data.listData).value = general.getItemForKey(_value, data.allMeetingChild.data, 'value').signInCommand
      onRefresh()
    }
    // 打开详细
    const openDetails = async (_item) => {
      console.log('_item===>', _item)
      switch (_item.click) {
        case 'signQR':
          // var qrCode = sessionStorage.getItem('qrCode')
          // var rongCloudIdPrefix = sessionStorage.getItem('rongCloudIdPrefix')
          // data.qrcode.src = qrCode + rongCloudIdPrefix + '%7CmeetingSignIn%7C' + data.myParam.meetId + '&logo=' + logo
          var logo = 'https://www.apicloud.com/icon/85/ad/85adda1be97cef3ea98fcf91211bb10c.png'
          data.qrcode.src = 'http://www.cszysoft.com:9090/' + 'utils/qr?text=QingDaoRDTest' + '%7CmeetingSignIn%7C' + data.myParam.meetId + '&logo=' + logo
          console.log('data.qrcode.src===', JSON.stringify(data.qrcode.src))
          data.qrcode.show = true
          break
        case 'matera':// 资料管理
          router.push({
            name: 'meetingfileList', query: { relateType: _item.click, id: data.myParam.meetId, sysType: 'meeting', title: _item.name, management: true }
          })
          break
        case 'attendance':// 考勤管理
          router.push({
            name: 'AttendanceManagement', query: { relateType: _item.click, id: data.myParam.meetId, sysType: 'meeting', title: _item.name, management: true }
          })
          // $o.openDetails({ relateType: _item.click, id: 'a' + (data.allMeetingChild.value || data.pageParam.meetId) }, { sysType: 'meeting', title: _item.name, management: true });
          break
        case 'Personnel': // 参与人员信息
          router.push({ name: 'meetingPersonnel', query: { id: data.myParam.meetId } })
      }
    }
    // 保存图片到相册
    const btnSave = async (_item) => {
      var img = document.getElementById('saveImg')
      console.log('Url====>', img)
      var Url = data.qrcode.src // 图片路径，也可以传值进来
      // var triggerEvent = 'touchstart' // 指定下载方式
      var blob = new Blob([''], { type: 'application/octet-stream' }) // 二进制大型对象blob
      var url = URL.createObjectURL(blob) // 创建一个字符串路径空位
      var a = document.createElement('a') // 创建一个 a 标签
      a.href = Url // 把路径赋到a标签的href上
      a.download = Url.replace(/(.*\/)*([^.]+.*)/ig, '$2').split('?')[0]
      var e = new MouseEvent('click', (true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null))
      a.dispatchEvent(e)
      URL.revokeObjectURL(url)
    }
    return { ...toRefs(data), onClickLeft, onLoad, general, confirm, AttendanceDataStatistics, AttendanceStatistics, allMeetingChange, onRefresh, openDetails, btnSave }
  }
}
</script>
<style lang="less" scoped>
.ConferenceAffairsManagement {
  background: #f9f9f9;

  .manage_btn_box {
    border-bottom: 10px solid #f4f4f4;
    background: #fff;
  }

  .van-cell {
    padding: 16px 16px;
  }

  .qrcode_img {
    width: 200px;
    height: 200px;
    margin: auto;
    padding: 10px;
  }

  .item_warp {
    padding: 14px 14px 0 14px;
    background: #fff;
  }

  .van-dropdown-menu__bar {
    background: #ffffff;
    padding: 14px;
  }
  .van-dropdown-menu {
    height: 40px !important;
    background: #fff;
  }

  .van-dropdown-menu .van-dropdown-menu__title {
    width: 100%;
  }

  .van-dropdown-menu__title::after {
    right: 4px;
    border: 4px solid;
    border-color: transparent transparent #323232 #323232;
    margin-top: -8px;
  }

  .van-dropdown-menu__title--active::after {
    margin-top: -4px;
  }

  .van-dropdown-menu .van-ellipsis {
    padding-right: 10px;
  }
}
</style>

import { HTTP } from '../http.js'
class RandomClapping extends HTTP {
  representativemessageList (params) {
    return this.request({ url: 'representativemessage/getAppList', data: params })
  }

  representativemessageGetAppListInfo (params) {
    return this.request({ url: 'representativemessage/getAppListInfo', data: params })
  }

  representativeflowGetFlowsApp (params) {
    return this.request({ url: 'representativeflow/getFlowsApp', data: params })
  }

  representativehomeSelectTitle (params) {
    return this.request({ url: 'representativehome/selectTitle', data: params })
  }

  representativemessageAdd (params) {
    return this.request({ url: 'representativemessage/add', data: params })
  }

  representativemessageEdit (params) {
    return this.request({ url: 'representativemessage/edit', data: params })
  }

  representativemessageInfo (params) {
    return this.request({ url: 'representativemessage/info/' + params })
  }

  replyEvaluate (params) {
    return this.request({ url: '/representativereply/replyEvaluate', data: params })
  }

  representativecommentAdd (params) {
    return this.request({ url: '/representativecomment/add', data: params })
  }

  areaTree (params) {
    return this.request({ url: '/area/tree', data: params })
  }

  representativemessageDels (params) {
    return this.request({ url: '/representativemessage/dels', data: params })
  }

  representativecommentList (params) {
    return this.request({ url: '/representativecomment/list', data: params })
  }

  representativecommentDels (params) {
    return this.request({ url: '/representativecomment/dels', data: params })
  }

  representativecommentEdit (params) {
    return this.request({ url: '/representativecomment/edit', data: params })
  }

  representativecommentInfo (params) {
    return this.request({ url: '/representativecomment/info/' + params })
  }

  representativereplyAdd (params) {
    return this.request({ url: '/representativereply/add', data: params })
  }

  representativemessageTransfer (params) {
    return this.request({ url: '/representativemessage/transfer', data: params })
  }

  representativemessageLists (params) {
    return this.request({ url: '/representativemessage/list', data: params })
  }

  representativemessageEscalation (params) {
    return this.request({ url: '/representativemessage/escalation', data: params })
  }

  representativemessageEscalationListPc (params) {
    return this.request({ url: '/representativemessage/escalationListPc', data: params })
  }

  representativereplyReplyEvaluateContent (params) {
    return this.request({ url: '/representativereply/replyEvaluateContent', data: params })
  }

  representativemessageAssigned (params) {
    return this.request({ url: '/representativemessage/assigned', data: params })
  }

  handleProcessing (params) {
    return this.request({ url: '/representativemessage/handleProcessing', data: params })
  }

  getCount (params) {
    return this.request({ url: '/representativeDS/getCount', data: params })
  }

  getSubCount (params) {
    return this.request({ url: '/representativeDS/getSubCount', data: params })
  }

  getSubTypeCount (params) {
    return this.request({ url: '/representativeDS/getSubTypeCount', data: params })
  }

  getSubAreaCount (params) {
    return this.request({ url: '/representativeDS/getSubAreaCount', data: params })
  }
}
export {
  RandomClapping
}

<template>
  <div class="quantificat">
    <ul class="">
      <li>
        <div class="evaluate_item_box van-hairline--bottom"
             :style="$general.loadConfiguration(1)">
          <van-form validate-first
                    @submit="submission">
            <div>
              <div class="evaluate_item van-hairline--bottom flex_align_center">
                <div :style="$general.loadConfiguration()"
                     class="evaluate_item_name">1.沟通情况（0-20分）（沟通情况要求为办理前、办理中、办理后均需与代表见面的方式，缺一项减5分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg">
                  <van-field type="number"
                             v-model="ruleForm.communication"
                             :disabled="disabled"
                             @input="totalchange"
                             placeholder="请输入0-20之间的整数"
                             :rules="[{ pattern: /^(\b(?:0|1?\d|20)\b)$/, message: '请输入0-20之间的整数' }]" />
                </div>
              </div>
              <div class="evaluate_item van-hairline--bottom flex_align_center">
                <div :style="$general.loadConfiguration()"
                     class="evaluate_item_name">2.落实效果（0-50分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="evaluate_item_names">（1）落实措施针对性、可行性、可操作性（0-20分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg evaluate_item_names">
                  <van-field type="number"
                             v-model="ruleForm.practicable1"
                             :disabled="disabled"
                             @input="totalchange"
                             placeholder="请输入0-20之间的整数"
                             :rules="[{ pattern: /^(\b(?:0|1?\d|20)\b)$/, message: '请输入0-20之间的整数' }]" />
                </div>
                <div :style="$general.loadConfiguration(-2)"
                     class="evaluate_item_names">（2）落实效果（0—30分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg evaluate_item_names">
                  <van-field type="number"
                             v-model="ruleForm.practicable2"
                             :disabled="disabled"
                             @input="totalchange"
                             placeholder="请输入0-30之间的整数"
                             :rules="[{ pattern: /^(\b(?:0|1?\d|2\d|30)\b)$/, message: '请输入0-30之间的整数' }]" />
                </div>
              </div>
              <div class="evaluate_item van-hairline--bottom flex_align_center">
                <div :style="$general.loadConfiguration()"
                     class="evaluate_item_name">3.面复情况（0-20分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="evaluate_item_names">（1）面复领导（5分）（参加面复领导为办理单位主要领导或分管领导的可得5分，其他人员不得分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg evaluate_item_names">
                  <van-field type="number"
                             v-model="ruleForm.fullRecovery1"
                             :disabled="disabled"
                             @input="totalchange"
                             placeholder="请输入0-5之间的整数"
                             :rules="[{ pattern: /^(\b[0-5]\b)$/, message: '请输入0-5之间的整数' }]" />
                </div>
                <div :style="$general.loadConfiguration(-2)"
                     class="evaluate_item_names">（2）面复态度（5分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg evaluate_item_names">
                  <van-field type="number"
                             v-model="ruleForm.fullRecovery2"
                             :disabled="disabled"
                             @input="totalchange"
                             placeholder="请输入0-5之间的整数"
                             :rules="[{ pattern: /^(\b[0-5]\b)$/, message: '请输入0-5之间的整数' }]" />
                </div>
                <div :style="$general.loadConfiguration(-2)"
                     class="evaluate_item_names">（3）面复人员业务能力和政策水平（10分）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg evaluate_item_names">
                  <van-field type="number"
                             v-model="ruleForm.fullRecovery3"
                             :disabled="disabled"
                             @input="totalchange"
                             placeholder="请输入0-10之间的整数"
                             :rules="[{ pattern: /^(\b(?:[0-9]|10)\b)$/, message: '请输入0-10之间的整数' }]" />
                </div>
              </div>
              <div class="evaluate_item van-hairline--bottom flex_align_center">
                <div :style="$general.loadConfiguration()"
                     class="evaluate_item_name">4.跟踪反馈和续办续复情况（0-10分）（如无跟踪反馈和续办续复要求，打10分即可）</div>
                <div :style="$general.loadConfiguration(-2)"
                     class="field_bg">
                  <van-field type="number"
                             :disabled="disabled"
                             v-model="ruleForm.tailAfter"
                             placeholder="请输入0-10之间的整数"
                             @input="totalchange"
                             :rules="[{ pattern: /^(\b(?:[0-9]|10)\b)$/, message: '请输入0-10之间的整数' }]" />
                </div>
              </div>
              <div class="evaluate_item van-hairline--bottom flex_align_center">
                <div :style="$general.loadConfiguration(2)"
                     class="evaluate_item_name">总分: <span :style="$general.loadConfiguration(2)"
                        class="total">{{ total >= 100 ? 100 : total == 0 ? '' : total }}</span></div>
              </div>
            </div>
            <div class="flex_align_center_button"
                 v-if="!disabled">
              <van-button round
                          block
                          type="primary"
                          style="background: #1989fa;border: 0;"
                          native-type="submit"
                          :loading="ifLoading"
                          loading-text="提交中...">
                提交
              </van-button>
              <van-button style="background: #00000000;border: 1px solid #A0A0A0;"
                          @click="router.go(-1)">取消</van-button>
            </div>
          </van-form>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
/* eslint-disable */
import { Toast } from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, ref, toRefs, onMounted } from 'vue'
import selectUser from './components/selectUser'
import chooseHandleUnit from './components/chooseHandleUnit'
import subjectTerm from './components/subjectTerm'
export default {
  name: 'quantificat',
  components: {
    selectUser,
    chooseHandleUnit,
    subjectTerm
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const data = reactive({
      appTheme: '#3088fe',
      user: JSON.parse(sessionStorage.getItem('user')),
      logo: true,
      areaId: sessionStorage.getItem('areaId') || '',
      ifDisabled: false,// 是否禁用提交
      ifLoading: false,// 是否正在提交
      siteId: route.query.id,
      actionid: '',
      editId: '',
      hasSubmit: false,
      actions: [],
      scoreData: { '很满意': '很满意', '满意': '满意', '基本满意': '基本满意', '不满意': '不满意' },
      evaluateresulttype: {},
      proposalevaluateattitude: {},
      ruleForm: {
        communication: null,
        practicable1: null,
        practicable2: null,
        fullRecovery1: null,
        fullRecovery2: null,
        fullRecovery3: null,
        tailAfter: null
      },
      contactPhone: "",
      contactLetter: "",
      contactFace: "",
      contactNone: "",
      attitudeScore: "",
      efficiencyScore: "",
      resultScore: "",
      overallScore: "",
      advice: "",
      groupName: "",
      groupName1: "",
      groupName2: "",
      total: null,
      dataId: '',
      disabled: route.query.disabled || false,
      groupNames: route.query.groupName,
      groupType: route.query.groupType,
      details: {}
    })
    const user = ref(null)
    const unit = ref(null)
    const type = ref(null)
    const onSubmit = () => {
      if (data.logo) {
        generalAdd()
      } else {
        generalAdd(1)
      }
    }
    onMounted(() => {
      getpubkvs()
      getDatas(data)
    })
    const totalchange = () => {
      data.total = Number(data.ruleForm.communication) + Number(data.ruleForm.practicable1) + Number(data.ruleForm.practicable2)
        + Number(data.ruleForm.fullRecovery1) + Number(data.ruleForm.fullRecovery2) + Number(data.ruleForm.fullRecovery3) + Number(data.ruleForm.tailAfter)
    }
    const submit = async () => {
    }
    const submission = async () => {
      if (Number(data.total) > 100) {
        T.toast('请输入正确的分值')
        return
      }
      var postData = {
        id: data.dataId, // 建议id
        dataId: route.query.recordId, // 列表id
        communicationSituation: data.ruleForm.communication, // 沟通情况
        implementationMeasure: data.ruleForm.practicable1, // 落实措施
        implementationEffect: data.ruleForm.practicable2, // 落实效果
        faceLeader: data.ruleForm.fullRecovery1, // 面复领导
        faceAttitude: data.ruleForm.fullRecovery2, // 面复态度
        facePeople: data.ruleForm.fullRecovery3, // 面复人员
        trackCondition: data.ruleForm.tailAfter, // 跟踪情况
        groupName: data.details.directGroup, // 承办单位
        total: data.total // 总分
      }
      data.ifLoading = true;
      const res = await $api.general.general('/flowquantitativeevaluation/save', postData)
      data.ifLoading = false;
      var { errcode, errmsg } = res
      if (errcode === 200) {
        Toast.success(errmsg)
        router.go(-1)
      }
    }
    const getDatas = async () => {
      console.log(data)
      data.hasSubmit = false
      var postParam = {
        "dataId": route.query.recordId,
      };
      const ret = await $api.general.general('/suggest/flowQuantitativeEvaluateDetail', postParam)
      if (!ret) {//是网络错误
      } else {
        var datas = ret.data || {};
        data.details = datas
        var flowQuantitativeEvaluation = datas.flowQuantitativeEvaluation || '';
        if (flowQuantitativeEvaluation) {
          data.ruleForm.communication = flowQuantitativeEvaluation.communicationSituation // 沟通情况
          data.ruleForm.practicable1 = flowQuantitativeEvaluation.implementationMeasure // 落实措施
          data.ruleForm.practicable2 = flowQuantitativeEvaluation.implementationEffect // 落实效果
          data.ruleForm.fullRecovery1 = flowQuantitativeEvaluation.faceLeader // 面复领导
          data.ruleForm.fullRecovery2 = flowQuantitativeEvaluation.faceAttitude // 面复态度
          data.ruleForm.fullRecovery3 = flowQuantitativeEvaluation.facePeople // 面复人员
          data.ruleForm.tailAfter = flowQuantitativeEvaluation.trackCondition // 跟踪情况
          data.dataId = flowQuantitativeEvaluation.id
          data.total = flowQuantitativeEvaluation.total
        }
      }
    }
    const getpubkvs = async () => {
    }
    const generalAdd = async (type) => {
    }
    return { ...toRefs(data), user, unit, type, onSubmit, $general, submission, totalchange, router, submit }
  }
}
/* eslint-disable */
</script>
<style lang="less">
.quantificat {
  width: 100%;
  min-height: 100%;
  background-color: #fff;

  .van-cell-group {
    background-color: rgba(0, 0, 0, 0);
  }

  .list_item {
    margin-bottom: 0.14rem;
    font-size: inherit;
    font-family: inherit;
  }

  .footer {
    background: #FFF;
    padding: 0.12rem 0.1rem;
    box-sizing: border-box;
    bottom: 0;
    width: 100%;
    left: 0;
    right: 0;
    margin: auto;
  }

  #app .footer .van-button {
    padding: 0.13rem 0.21rem;
    width: 1.32rem;
    height: 0.33rem;
    line-height: 0.33rem;
    background: #3088FE;
    opacity: 1;
    border-radius: 0.04rem;
  }

  li {
    background: #FFF;
    margin-bottom: 0.1rem;
  }

  .evaluate_hint_box {
    width: 100%;
    height: 0.43rem;
    line-height: 0.48rem;
    color: #333;
    box-sizing: border-box;
  }

  .evaluate_item_box {
    width: calc(100% - 0.3rem);
    left: 0;
    right: 0;
    margin: auto;
    box-sizing: border-box;
  }

  .evaluate_item {
    width: 100%;
  }

  .evaluate_item,
  .evaluate_item_name {
    margin: 10px 0;
    padding-top: 0.05rem;
  }

  .van-field {
    height: 100%;
  }

  .evaluate_item_radio_box .van-radio {
    width: 100%;
  }

  #app .evaluate_item_radio_box .van-radio__label {
    margin-left: 0.01rem;
  }

  .evaluate_required {
    position: relative;
  }

  .evaluate_required:after {
    content: "*";
    color: red;
    position: absolute;
    top: 0;
    left: -0.09rem;
  }

  .evaluate_table_box {
    padding: 0.11rem;
    box-sizing: border-box;
  }

  .evaluate_table {
    border-radius: 0.05rem;
  }

  .evaluate_table_item {
    width: 100%;
    font-size: inherit;
    font-family: inherit;
  }

  .evaluate_table_average,
  .evaluate_table_total,
  .evaluate_table_satisfaction {
    width: 100%;
    text-align: center;
    font-size: inherit;
    font-family: inherit;
    background: #EEEEEE;
    line-height: 0.3rem;
  }

  .evaluate_table_average {
    border-top-left-radius: 0.05rem;
    border-top: 1px solid #A0A0A0;
    border-left: 1px solid #A0A0A0;
    border-bottom: 1px solid #A0A0A0;
  }

  .evaluate_table_total {
    border-top: 1px solid #A0A0A0;
    border-left: 1px solid #A0A0A0;
    border-bottom: 1px solid #A0A0A0;
  }

  .evaluate_table_satisfaction {
    border-top: 1px solid #A0A0A0;
    border-left: 1px solid #A0A0A0;
    border-right: 1px solid #A0A0A0;
    border-bottom: 1px solid #A0A0A0;
    border-top-right-radius: 0.05rem;
  }

  .evaluate_table_average1,
  .evaluate_table_total1,
  .evaluate_table_satisfaction1 {
    width: 100%;
    text-align: center;
    font-size: inherit;
    font-family: inherit;
    background: #FFF;
    line-height: 0.3rem;
  }

  .evaluate_table_average1 {
    border-bottom-left-radius: 0.05rem;
    border-left: 1px solid #BFBFBF;
    border-bottom: 1px solid #BFBFBF;
  }

  .evaluate_table_total1 {
    border-left: 1px solid #BFBFBF;
    border-bottom: 1px solid #BFBFBF;
  }

  .evaluate_table_satisfaction1 {
    border-left: 1px solid #BFBFBF;
    border-right: 1px solid #BFBFBF;
    border-bottom: 1px solid #BFBFBF;
    border-bottom-right-radius: 0.05rem;
  }

  [class*=van-hairline]::after {
    border: 0 solid #fff;
  }

  .field_bg {
    width: 95%;
    left: 0;
    right: 0;
    margin: auto;
    padding: 0.1rem 0;
    background: #fff;
    border: 1px solid #000;
    border-radius: 5px;
  }

  .field_bg_text {
    font-size: inherit;
    font-family: inherit;
    font-weight: 400;
    color: #666666;
    opacity: 1;
  }

  #app .van-cell {
    background: #fff;
    border-radius: 0.02rem;
    padding: 0 0.11rem;
    height: 40px;
    border: 1px solid #666;
  }

  #app .van-field {
    height: 40px;
  }

  .custom-button {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    font-size: 14px;
    background: #3088FE;
    color: #fff;
    text-align: center;
    line-height: 25px;
  }

  .flex_align_center_textarea {
    width: 100%;
    background: #fafafa;
    border-radius: 10px;
    padding: 5px;
  }

  .flex_align_center_text {
    width: 100%;
    background: #fff;
    border-radius: 5px;
    height: 40px;
    border: 1px solid #6e6e6e;
    padding: 3px;
  }

  .flex_align_center_button {
    width: 100%;
    height: 80px;
    display: flex;
    padding: 20px 0;
    align-items: center;
    justify-content: space-around;
  }

  .flex_align_center_button>button {
    width: 42%;
    height: 100%;
    border-radius: 10px;
  }

  .evaluate_item_names {
    margin: 10px 15px;
  }

  .total {
    width: 60px;
    text-align: center;
    border-bottom: 1px solid #666666;
    display: inline-block;
  }
}
</style>

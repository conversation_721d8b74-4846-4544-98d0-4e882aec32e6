const superFile = () => import('@/components/superFile/superFile')
const modules = () => import('@/views/module/module.vue')
const moduleMorePage = () => import('@/views/module/moduleMorePage.vue')
const scan = () => import('@/views/module/scan.vue')
const newsDetails = () => import('@/views/news/newsDetails.vue')
const modulesNew = () => import('@/views/module/moduleNew.vue') // 宁政通
const modulesMy = () => import('@/views/module/moduleMy.vue') // 我的宁夏
const myUser = () => import('@/views/myUser/myUser.vue')
const personalDataView = () => import('@/views/myUser/personalDataView.vue') // 个人资料页面
const openWorkPermit = () => import('@/views/myUser/openWorkPermit.vue') // 电子工作证
const favorite = () => import('@/views/myUser/favorite.vue') // 资料收藏
const informationModification = () => import('@/views/myUser/informationModification.vue') // 信息变更
const add = () => import('@/views/add/add.vue')
const leaderDriving = () => import('@/views/leaderDriving/leaderDriving.vue')
const messageMorePage = () => import('@/views/leaderDriving/message/messageMorePage.vue')
const resumptionList = () => import('@/views/resumption/resumptionList.vue')
const resumptionDetails = () => import('@/views/resumption/resumptionDetails.vue')
const announcementList = () => import('@/views/announcement/announcementList.vue')
const announcementDetails = () => import('@/views/announcement/announcementDetails.vue')
const politicalList = () => import('@/views/political/politicalList.vue')
const politicalDetails = () => import('@/views/political/politicalDetails.vue')
const twoInstitutesList = () => import('@/views/twoInstitutes/twoInstitutesList.vue')
const twoInstitutesDetails = () => import('@/views/twoInstitutes/twoInstitutesDetails.vue')
const userFeedback = () => import('@/views/userFeedback/userFeedback.vue')
const userFeedbackAdd = () => import('@/views/userFeedback/userFeedbackAdd.vue')
const news = [{
  path: '/superFile',
  name: 'superFile',
  component: superFile,
  meta: {
    title: '文件',
    keepAlive: true
  }
}, {
  path: '/module',
  name: 'module',
  component: modules,
  meta: {
    title: '模块',
    keepAlive: true
  }
}, {
  path: '/moduleMorePage',
  name: 'moduleMorePage',
  component: moduleMorePage,
  meta: {
    title: '更多模块页面',
    keepAlive: true
  }
}, {
  path: '/scan',
  name: 'scan',
  component: scan,
  meta: {
    title: '扫一扫',
    keepAlive: true
  }
}, {
  path: '/moduleNew',
  name: 'moduleNew',
  component: modulesNew,
  meta: {
    title: '人大工作',
    keepAlive: true
  }
}, {
  path: '/moduleMy',
  name: 'moduleMy',
  component: modulesMy,
  meta: {
    title: '代表履职',
    keepAlive: true
  }
}, {
  path: '/myUser',
  name: 'myUser',
  component: myUser,
  meta: {
    title: '我的',
    keepAlive: true
  }
}, {
  path: '/personalDataView',
  name: 'personalDataView',
  component: personalDataView,
  meta: {
    title: '个人资料',
    keepAlive: true
  }
}, {
  path: '/openWorkPermit',
  name: 'openWorkPermit',
  component: openWorkPermit,
  meta: {
    title: '电子工作证',
    keepAlive: true
  }
}, {
  path: '/favorite',
  name: 'favorite',
  component: favorite,
  meta: {
    title: '资料收藏',
    keepAlive: true
  }
}, {
  path: '/informationModification',
  name: 'informationModification',
  component: informationModification,
  meta: {
    title: '信息变更',
    keepAlive: true
  }
}, {
  path: '/add',
  name: 'add',
  component: add,
  meta: {
    title: '新增',
    keepAlive: true
  }
}, {
  path: '/newsDetails',
  name: 'newsDetails',
  component: newsDetails,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/leaderDriving',
  name: 'leaderDriving',
  component: leaderDriving,
  meta: {
    title: '详情',
    keepAlive: true
  }
}, {
  path: '/messageMorePage',
  name: 'messageMorePage',
  component: messageMorePage,
  meta: {
    title: '群众留言更多',
    keepAlive: true
  }
}, {
  path: '/resumptionList',
  name: 'resumptionList',
  component: resumptionList, // resumption 履职圈
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/resumptionDetails',
  name: 'resumptionDetails',
  component: resumptionDetails,
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/announcementList',
  name: 'announcementList',
  component: announcementList, // 公告
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/announcementDetails',
  name: 'announcementDetails',
  component: announcementDetails,
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/politicalList',
  name: 'politicalList',
  component: politicalList, // political 政情快递
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/politicalDetails',
  name: 'politicalDetails',
  component: politicalDetails,
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/twoInstitutesList',
  name: 'twoInstitutesList',
  component: twoInstitutesList, // twoInstitutes 两院咨询
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/twoInstitutesDetails',
  name: 'twoInstitutesDetails',
  component: twoInstitutesDetails,
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/userFeedback',
  name: 'userFeedback',
  component: userFeedback,
  meta: {
    title: '',
    keepAlive: true
  }
}, {
  path: '/userFeedbackAdd',
  name: 'userFeedbackAdd',
  component: userFeedbackAdd,
  meta: {
    title: '',
    keepAlive: true
  }
}]
export default news

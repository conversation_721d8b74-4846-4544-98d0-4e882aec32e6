<template>
  <div class="subjectTerm">
    <div class="subjectTermBox"
         v-for="item in typeData"
         :key="item.id">
      <div class="subjectTermName">{{item.name}}</div>
      <div class="subjectTermItem"
           :class="{subjectTermItemA:checked.includes(type.id)}"
           v-for="type in item.children"
           @click="change(type)"
           :key="type.id">{{type.name}}</div>
    </div>
  </div>
</template>
<script>
import { inject, onMounted, reactive, toRefs } from 'vue'
export default {
  name: 'subjectTerm',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const $api = inject('$api')
    const data = reactive({
      keyword: '',
      activeNames: [],
      typeData: [],
      filterType: [],
      checked: [],
      selectedType: []
    })
    onMounted(() => {
      obtain()
      chooseList()
    })
    const obtain = () => {
      var arr = []
      props.data.forEach(item => {
        arr.push(item.id)
      })
      data.checked = arr
      data.selectedType = props.data
    }
    const chooseList = async (ids) => {
      const res = await $api.proposal.chooseList({
        pageNo: 1,
        pageSize: 0,
        inList: 1,
        submissionTopicQuery: 1
      })
      var { data: typeData } = res
      data.typeData = typeData
      data.filterType = typeData
    }
    const onSearch = () => {
      data.filterType = filterTree(data.typeData, shopfilterNode) || []
    }
    const filterTree = (nodes, predicate) => {
      if (!nodes || !nodes.length) return void 0  // eslint-disable-line
      const children = []
      for (let node of nodes) {
        node = Object.assign({}, node)
        const sub = filterTree(node.idValueVoList, predicate)
        if ((sub && sub.length) || predicate(node)) {
          sub && (node.idValueVoList = sub)
          children.push(node)
        }
      }
      return children.length ? children : void 0  // eslint-disable-line
    }
    const shopfilterNode = (item) => {
      if (!item.value) return false
      return item.value.includes(data.keyword)
    }
    const change = (row) => {
      if (data.checked.includes(row.id)) {
        data.checked = data.checked.filter(tab => tab !== row.id)
        data.selectedType = data.selectedType.filter(tab => tab.id !== row.id)
      } else {
        data.checked.push(row.id)
        data.selectedType.push(row)
      }
    }
    return { ...toRefs(data), onSearch, change }
  }

}
</script>
<style lang="less">
.subjectTerm {
  width: 100%;
  padding: 16px;
  .subjectTermName {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 20px;
    color: #333333;
    padding: 9px 0;
  }
  .subjectTermItem {
    display: inline-block;
    padding: 0 9px;
    height: 21px;
    background: #f6f6f6;
    border-radius: 2px;
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 21px;
    color: #666666;
    margin-right: 12px;
    margin-bottom: 12px;
  }
  .subjectTermItemA {
    color: #3088fe;
    background-color: #dcebff;
  }
}
</style>

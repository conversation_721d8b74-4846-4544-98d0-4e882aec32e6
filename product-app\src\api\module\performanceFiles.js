import {
  HTTP
} from '../http.js'
class performanceFiles extends HTTP {
  general (url, params) {
    return this.request({
      url: url,
      data: params
    })
  }

  // 获取履职列表
  getGenerateDuty (params) {
    return this.request({
      url: '/dutynum/list',
      data: params
    })
  }

  // 获取履职信息
  getAppDutyNums (params) {
    return this.request({
      url: '/dutyconfig/getAppDutyNums',
      data: params
    })
  }

  // 获取履职足迹
  // getAppDutyDetail (params) {
  //   return this.request({
  //     url: '/dutyconfig/getAppDutyDetail',
  //     data: params
  //   })
  // }

  // 获取履职足迹
  getAppDutyDetail (params) {
    return this.request({
      url: '/duty/getAppDutyDetail',
      data: params
    })
  }

  // 新增履职补录
  addLzbl (params) {
    return this.request({
      url: '/activityfill/appAdd',
      data: params
    })
  }

  // 编辑履职补录
  editLzbl (params) {
    return this.request({
      url: '/activityfill/edit',
      data: params
    })
  }
}
export {
  performanceFiles
}

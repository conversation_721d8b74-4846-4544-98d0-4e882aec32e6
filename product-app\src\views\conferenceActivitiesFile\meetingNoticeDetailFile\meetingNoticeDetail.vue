<template>
  <div class="meetingNoticeDetail">
    <template v-if="firstAjax && title">
      <!-- v-if="!msgMore.is?index<msgMore.num:true" -->
      <div v-for="(item,index) in msgs"
           :key="index"
           class="msgs_item flex_box van-hairline--bottom">
        <div class="msgs_hint"
             v-html="item.hint"></div>
        <div class="flex_placeholder msgs_value"
             :class="item.link?'text_one2':''"
             v-html="item.value"></div>
        <div v-if="item.link"
             @click="openLink(item)"
             :style="general.loadConfiguration(-1)+'color:'+appTheme"
             class="msgs_link">{{item.linkName}}</div>
      </div>

      <div class="n_details_content"
           :style="general.loadConfiguration(-1)+'border-top:0.1rem solid #F4F4F4;'">
        <div class="inherit"
             style="font-weight: bold;">会议议程:</div>
        <div class="inherit"
             style="color: #333333;line-height: 2.2;"
             v-html="content"></div>
      </div>
    </template>
    <!--加载中提示 首次为骨架屏-->
    <div v-if="showSkeleton"
         class="notText">
      <van-skeleton v-for="(item,index) in 3"
                    :key="index"
                    title
                    :row="3"></van-skeleton>
    </div>

    <!--返回顶部-->
    <ul v-if="footerBtnsShow"
        class="footer_btn_box"
        :style="'bottom:'+(safeAreaBottom+30)+'px;'">
      <transition name="van-slide-right">
        <div v-if="footerMore.show">
          <template v-for="(item,index) in footerBtns"
                    :key="index">
            <div v-if="scrollTop>=100 && item.type == 'top'"
                 @click="backTop()"
                 class="back_top">
              <van-icon :size="((general.appFontSize+25)*0.01)+'rem'"
                        name="upgrade"></van-icon>
            </div>
            <div v-if="item.type == 'btn'"
                 class="van-button-box"
                 :style="general.loadConfiguration(-3)">
              <van-button loading-type="spinner"
                          :loading-size="((general.appFontSize)*0.01)+'rem'"
                          :loading="item.loading"
                          :loading-text="item.loadingText"
                          :color="item.color?item.color:appTheme"
                          :disabled="item.disabled"
                          @click="footerBtnClick(item)"
                          :icon="item.icon">{{item.name}}</van-button>
            </div>
          </template>
        </div>
      </transition>
      <div v-if="title && footerBtns.length != 0"
           @click="footerMore.show = !footerMore.show;"
           :style="general.loadConfigurationSize(15)+'border-radius:50%;background:'+appTheme"
           class="footer_item flex_box flex_align_center flex_justify_content">
        <van-icon :size="((general.appFontSize+2)*0.01)+'rem'"
                  color="#FFF"
                  :name="footerMore.show?'arrow-down':'ellipsis'"></van-icon>
      </div>
    </ul>
    <!--不为一级页面时 适配底部条-->
    <footer v-if="pageType=='page'"
            :style="{paddingBottom:(safeAreaBottom)+'px'}"></footer>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs } from 'vue'
import { Dialog } from 'vant'
export default {
  name: 'meetingNoticeDetail',
  components: {
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const dayjs = require('dayjs')
    const $api = inject('$api')
    const general = inject('$general')
    const appTheme = inject('$appTheme')
    const isShowHead = inject('$isShowHead')
    const route = useRoute()
    const data = reactive({
      appTheme: appTheme,
      isShowHead: isShowHead,
      user: JSON.parse(sessionStorage.getItem('user')),
      id: route.query.id,
      recordId: route.query.id || '',
      total: 0,
      detailsData: {

      },
      showSkeleton: false,
      firstAjax: true,
      pageNot: {
        type: '',
        text: ''
      },
      msgs: [
        { hint: '会议名称:', value: '政协第十二届第十五次常委会议-开 幕式' },
        { hint: '会议:', value: '视察视察视察视察视察', link: 'firstAjax && title', linkName: '实景' },
        { hint: '组织部门:', value: '市城管执法局' },
        { hint: '报名截止:', value: '2021-12-10 16:00' },
        { hint: '签到时间:', value: '2021-12-10 16:00<br />至2021-12-10 16:00' },
        { hint: '会议时间:', value: '2021-12-10 16:00<br />至2021-12-10 16:00' },
        { hint: '会议地点:', value: '青岛王朝大酒店' }
      ],
      msgMore: { is: false, num: 4 }, // is是否展开 num多少条收起
      title: '',
      content: '1.审议通过本次常委会会议议程<br />2.传达中共十九届五中全会精神<br />3.传达全国政协十三届常委会第十四次会议精神<br />4.传达10月30日省级党员领导干部会议精神<br />5.省发展改革委负责同志作学习贯彻中共十九届五中全会精神辅导报告', // 正文内容
      contentImgs: [], // 正文中图片集合

      footerBtnsShow: true, // 按钮是否隐藏
      footerBtns: [], // 底部按钮集合 top为返回顶部  btn为按钮
      footerMore: { show: true }, // 展开附加按钮
      floatModule: { state: '', id: '' } // 播报的状态 播报的id  0没有任何状态 1播放中 2已暂停 3已结束
    })

    onMounted(() => {
      getDetails()
    })

    const getDetails = async () => {
      var res = []
      res = await $api.activity.conferencenfo(data.id) // 详情
      var { data: list, errcode } = res
      // _________________________________________
      data.showSkeleton = false
      data.firstAjax = true
      // var code = errcode || ''
      data.pageNot.type = list ? (errcode === 200 ? 0 : 1) : 1 // 类型
      data.pageNot.text = list && errcode !== 200 ? errcode : ''//
      var info = list || {}
      data.title = info.name || ''// 标题
      window.time = info.startTime
      data.content = info.content || ''
      data.id = info.id || ''
      data.msgs = []
      data.msgs.push({ hint: '会议名称:', value: data.title })
      var assembly = info.bigType === '10010'// 是否大会
      if (!assembly) {
        data.msgs.push({ hint: '会议类型:', value: info.bigTypeName || info.bigType || '' })
        data.msgs.push({ hint: '报名截止:', value: info.signUpEndTime ? dayjs(info.signUpEndTime).format('YYYY-MM-DD HH:mm') : '' })
      }
      data.msgs.push({ hint: '签到时间:', value: (info.signInStartTime ? dayjs(info.signInStartTime).format('YYYY-MM-DD HH:mm') : '') + '<br/>至' + (info.signInEndTime ? dayjs(info.signInEndTime).format('YYYY-MM-DD HH:mm') : '') })
      data.msgs.push({ hint: '会议时间:', value: (info.startTime ? dayjs(info.startTime).format('YYYY-MM-DD HH:mm') : '') + '<br/>至' + (info.endTime ? dayjs(info.endTime).format('YYYY-MM-DD HH:mm') : '') })
      var place = { hint: '会议地点:', value: info.isConnectors === 1 ? info.conferenceRoomName || '' : info.place || '', link: info.isConnectors === 1 ? info.imgUrl || '' : '', linkName: '实景', jumpType: 'link' }
      var conferenceRoomLayoutDetailVo = info.conferenceRoomLayoutDetailVo || {}// 会议排座
      if (conferenceRoomLayoutDetailVo.id && info.isRelease) { // 说明有排座
        window.conferenceRoomId = conferenceRoomLayoutDetailVo.conferenceRoomId
        window.seatingArrangement = conferenceRoomLayoutDetailVo.seatingArrangement
        place.jumpType = 'arrange'
        place.linkName = window.seatingArrangement === '1' ? (conferenceRoomLayoutDetailVo.columnnum + '座') : ((conferenceRoomLayoutDetailVo.type === '1' ? '主席区' : '出席区') + conferenceRoomLayoutDetailVo.row + '排' + conferenceRoomLayoutDetailVo.columnnum + '座')
        place.link = place.linkName
      }
      data.msgs.push(place)
      var conferenceTransactionDetailVo = info.conferenceTransactionDetailVo || {}// 事务安排
      if (conferenceTransactionDetailVo.residence) data.msgs.push({ hint: '住地:', value: conferenceTransactionDetailVo.residence || '' })
      if (conferenceTransactionDetailVo.dining) data.msgs.push({ hint: '用餐地址:', value: conferenceTransactionDetailVo.dining || '' })
      if (conferenceTransactionDetailVo.car) data.msgs.push({ hint: '用车信息:', value: conferenceTransactionDetailVo.car || '' })
      if (conferenceTransactionDetailVo.seat) data.msgs.push({ hint: '会议座位:', value: conferenceTransactionDetailVo.seat || '' })
      var collection = info.collection// 是否补录 补录的数据不需要报名签到和请假
      var signUpItem = { name: '报名', type: 'btn', click: 'signUp', icon: 'edit', color: '', loading: false, disabled: false }
      var signInItem = { name: '签到', type: 'btn', click: 'signIn', icon: 'sign', color: '', loading: false, disabled: false }
      var leaveItem = { name: '请假', type: 'btn', click: 'leave', icon: 'tosend', color: '', loading: false, disabled: false }
      var materaItem = { name: '资料', type: 'btn', click: 'matera', icon: 'newspaper-o', color: '', loading: false, disabled: false }
      var signUpBtn = general.getItemForKey(signUpItem.click, data.footerBtns, 'click')// 报名
      var signInBtn = general.getItemForKey(signInItem.click, data.footerBtns, 'click')// 签到
      var leaveBtn = general.getItemForKey(leaveItem.click, data.footerBtns, 'click')// 请假
      var materaItemBtn = general.getItemForKey(materaItem.click, data.footerBtns, 'click')// 资料
      general.delItemForKey(signUpBtn, data.footerBtns, 'click')
      general.delItemForKey(signInBtn, data.footerBtns, 'click')
      general.delItemForKey(leaveBtn, data.footerBtns, 'click')
      general.delItemForKey(materaItemBtn, data.footerBtns, 'click')
      if (!collection) { // 不是补录 就有操作
        var signUp = info.signUp// 是否可以报名
        var signUpList = info.signUpList || []// 所有报名人
        var signUpIn = general.getItemForKey(data.user.id, signUpList, 'data.user.id')// 当前人是否已报名
        if (signUp || signUpIn) {
          if (signUpIn) {
            signUpItem.name = '已报名'
            signUpItem.color = '#ccc'
            signUpItem.disabled = true
          }
          data.footerBtns.push(signUpItem)
        }
        var signIn = info.signIn// 是否可以签到
        var signInList = info.signInList || []// 所有签到人
        var signInIn = general.getItemForKey(data.user.id, signInList, 'data.user.id')// 当前人是否已签到
        if (signIn || signInIn) {
          if (signInIn) {
            signInItem.name = '已签到'
            signInItem.color = '#ccc'
            signInItem.disabled = true
          }
          data.footerBtns.push(signInItem)
        }
        var leave = info.leave// 是否可以请假
        var leaveState = info.leaveState || {}
        if (leave) {
          // status 0审核中 1通过
          var leaveName = [{ name: '请假审核中' }, { name: '请假已通过' }, { name: '请假不通过' }]
          leaveItem.name = general.isParameters(leaveState.state) ? leaveName[Number(leaveState.state)].name : '请假'
          leaveItem.leaveId = leaveState.id || ''
          data.footerBtns.push(leaveItem)
        }
      }
      var hasShowFile = false;
      (info.conferenceMaterialFormPos || []).forEach(function (_eItem, _eIndex, _eArr) {
        if (_eItem.isRelease === 1) {
          hasShowFile = true
        }
      })
      if (hasShowFile) { // 是否有资料 并且没有隐藏
        data.footerBtns.push(materaItem)
      }
    }

    // 底部按钮事件
    const footerBtnClick = (_item) => {
      console.error(JSON.stringify(_item))
      switch (_item.click) {
        case 'signUp':// 报名
          _item.loading = true
          _item.loadingText = '参会'
          // var url = zyUrl.getAppUrl() + "conference/addMySignUp?";
          // T.ajax({ u: url }, 'addMySignUp', function (ret, err) {
          //   _item.loading = false;
          //   if (!ret) {
          //     T.toast(T.NET_ERR);
          //   } else {
          //     var code = ret.errcode || 0;
          //     if (code == "200") {
          //       T.toast("报名成功");
          //       T.refreshHeaderLoading();
          //     } else {
          //       T.toast(ret.errmsg || ret.data);
          //     }
          //   }
          // }, '报名', "post", { values: { "conferenceIds": recordId } });
          break
        case 'signIn':// 签到
          // T.nActionSheet({
          //   onlyName: "up_actionSheet_signIn",
          //   title: '签到方式',
          //   cancelTitle: '取消',
          //   buttons: ['二维码', '签到口令']
          // }, function (ret, err) {
          //   if (ret) {
          //     if (ret.buttonIndex == 1) {
          //       if (!T.confirmPer('camera')) { return; }
          //       var myParam = {};
          //       myParam.pageOnly = T.getNum();//表示页面唯一参数 必传
          //       myParam.okCallback = "signInScan" + myParam.pageOnly;
          //       T.addEventListener(myParam.okCallback, function (ret, err) {
          //         T.refreshHeaderLoading();
          //         api.removeEventListener({ name: myParam.okCallback });
          //       });
          //       $o.openDetails({ relateType: "scan" }, myParam);
          //     }
          //     if (ret.buttonIndex == 2) {
          //       T.nPrompt({
          //         title: "会议签到码",
          //         type: "signInCode"
          //       }, function (ret, err) {
          //         if (ret.buttonIndex == 1) {
          //           that.mBeforeClose(ret.text);
          //         }
          //       });
          //     }
          //   }
          // });
          break
        case 'leave':// 请假
          // $o.openDetails({ relateType: "add" }, { title: _item.name, id: that.id, leaveId: _item.leaveId, callback: T.frameName(), paramType: "addLeave", sysType: "meeting" });
          break
        case 'matera':// 会议资料
          router.push({
            name: 'fileList', query: { relateType: _item.click, id: data.recordId, sysType: 'meeting', title: _item.name }
          })
          break
      }
    }

    return { ...toRefs(data), dayjs, general, appTheme, isShowHead, footerBtnClick }
  }
}
</script>

<style lang="less" >
.meetingNoticeDetail {
  width: 100%;
  height: 100vh;
  .msgs_item {
    padding: 0.16rem 0.14rem;
  }

  .msgs_hint {
    margin-right: 0.2rem;
    font-weight: 500;
    color: #333333;
    line-height: 1.5;
    min-width: 4.35em;
  }

  .msgs_value {
    font-weight: bold;
    color: #333333;
    line-height: 1.5;
  }

  .msgs_link {
    padding: 0 0;
    position: relative;
  }

  .msgs_link:after {
    position: absolute;
    bottom: -0.01rem;
    content: " ";
    left: 0;
    right: 0;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: inherit;
  }
}
</style>

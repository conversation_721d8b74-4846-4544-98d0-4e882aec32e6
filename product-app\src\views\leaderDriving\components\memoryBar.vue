<template>
  <div class="memory-bar" :style="{ width: item.dissatisfaction.num > 3 ? '96%' : '100%' }">
    <!-- {{ item.dissatisfaction.num }} -->
    <template v-if="item.satisfaction.percentage == '0%'">
      <div class="memory-section" :style="{ width: '33%', background: '#40cd80' }"
        @click="goSatisfaction(item.satisfaction)">
        <div class="memory-section-text">
          {{ item.satisfaction.percentage }}
        </div>
      </div>
      <div class="memory-section" @click="goSatisfaction(item.basicallySatisfied)"
        :style="{ width: '33%', left: '33%', background: '#ffd055' }">
        <div class="memory-section-text" style="margin-right: 10px;">
          {{ item.basicallySatisfied.percentage }}
        </div>
      </div>
      <div class="memory-section" @click="goSatisfaction(item.dissatisfaction)"
        :style="{ width: '33%', left: '66%', background: '#ff6d6d' }">
        <div class="memory-section-text" style="margin-left: 30px;">
          {{ item.dissatisfaction.percentage }}
        </div>
      </div>
    </template>
    <template v-else>
      <div class="memory-section" :style="{ width: item.satisfaction.percentage, background: '#40cd80' }"
        @click="goSatisfaction(item.satisfaction)">
        <div class="memory-section-text">
          {{ item.satisfaction.percentage }}
        </div>
      </div>
      <div class="memory-section" @click="goSatisfaction(item.basicallySatisfied)"
        :style="{ width: item.basicallySatisfied.percentage, left: item.satisfaction.percentage, background: '#ffd055', 'border-radius': item.dissatisfaction.num == 0 ? '0 0.2rem 0.2rem 0' : '0px' }">
        <div class="memory-section-text" style="margin-right: 25px;">
          {{ item.basicallySatisfied.percentage }}
        </div>
      </div>
      <div class="memory-section" @click="goSatisfaction(item.dissatisfaction)" v-if="item.dissatisfaction.num != 0"
        :style="{ width: item.dissatisfaction.percentage, left: (Number(item.basicallySatisfied.percentage.replace('%', ' ')) + Number(item.satisfaction.percentage.replace('%', ' '))) + '%', background: '#ff6d6d' }">
        <div class="memory-section-text" v-if="item.dissatisfaction.num > 10" style="margin-left: 2px;">
          {{ item.dissatisfaction.percentage }}
        </div>
      </div>
    </template>

  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted } from 'vue'
import { NavBar, Sticky, Grid, GridItem, Image as VanImage, Tag, Icon, Dialog, ActionSheet, PasswordInput, NumberKeyboard, Overlay } from 'vant'
export default {
  name: 'memoryBar',
  components: {
    [Dialog.Component.name]: Dialog.Component,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [PasswordInput.name]: PasswordInput,
    [NumberKeyboard.name]: NumberKeyboard,
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [VanImage.name]: VanImage,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  props: ['title', 'item'],
  setup () {
    const route = useRoute()
    const ifzx = inject('$ifzx')
    const appTheme = inject('$appTheme')
    const general = inject('$general')
    const isShowHead = inject('$isShowHead')
    // const $api = inject('$api')
    // const dayjs = require('dayjs')
    const data = reactive({
      safeAreaTop: 0,
      SYS_IF_ZX: ifzx,
      appFontSize: general.data.appFontSize,
      appTheme: appTheme,
      isShowHead: isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user'))
    })
    onMounted(() => {
    })
    const goSatisfaction = (item) => {
      // window.location.href = item.url
    }
    return { ...toRefs(data), general, goSatisfaction }
  }
}
</script>
<style lang="less" scoped>
.memory-bar {
  width: 90%;
  height: 15px;
  background-color: #f0f0f0;
  border-radius: 10px;
  // overflow: hidden;
  position: relative;
  margin: 12px 5px;
}

.memory-section {
  height: 100%;
  border-radius: 8px;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;

  .memory-section-text {
    position: relative;
    bottom: -15px;
    font-size: 12px;
    transform: scale(0.8);
    color: #9a9a9a;
    z-index: 99;
  }

  .memory-section-text:nth-child(2) {
    left: -10px;
  }

  .memory-section-text:nth-child(3) {
    left: -5px;
  }
}

.memory-section:nth-child(1) {
  border-radius: 8px 0 0 8px;
}

.memory-section:nth-child(2) {
  border-radius: 0;
}

.memory-section:nth-child(3) {
  border-radius: 0 8px 8px 0;
}
</style>

<template>
  <div class="RandomClappingMy">
    <!-- 列表区 -->
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <div class="RandomClappingMyBox">
          <ul>
            <li class="RandomClappingMyBox-li"
                v-for="item in dataList"
                :key="item.id"
                @click="openDetails(item)">
              <div class="RandomClappingMyBox-top">
                <div class="RandomClappingMyBox-title">{{ item.title }}</div>
                <div :class="item.state == 1 ? 'RandomClappingMyBox-state2' : 'RandomClappingMyBox-state'">
                  {{ stateList[item.state] }}
                </div>
              </div>
              <div class="RandomClappingMyBox-time">{{ item.messageDate }}</div>
            </li>
          </ul>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { onMounted, reactive, toRefs, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import { ImagePreview, Uploader, Image as VanImage } from 'vant'
// import SelectMap from '@/components/SelectMap/index.vue'
export default ({
  name: 'RandomClappingMy',
  props: {},
  // components: {
  //   SelectMap,
  //   [VanImage.name]: VanImage,
  //   [Uploader.name]: Uploader
  // },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    // const $general = inject('$general')
    const data = reactive({
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      total: 0,
      pageSize: 10,
      user: JSON.parse(sessionStorage.getItem('user')),
      areaId: sessionStorage.getItem('areaId'),
      dataList: [],
      stateList: {
        1: '待办理',
        2: '已转办',
        3: '已办结',
        4: '退回',
        5: '二次交办',
        6: '已评价',
        11: '正在办理'
      }
    })
    onMounted(() => {
      onRefresh()
    })

    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.refreshing = false
      data.loading = true
      data.finished = false
      getList()
    }
    const getList = async () => {
      const { data: List, total } = await $api.RandomClapping.representativemessageList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        keyword: data.keyword,
        userId: data.user.id,
        areaId: data.areaId,
        selectStatus: 1
      })
      data.dataList = data.dataList.concat(List)
      // data.dataList = yiguanzhuLists
      data.total = total
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 打开详情
    const openDetails = (item) => {
      router.push({ name: 'RandomClappingDetails', query: { id: item.id } })
    }
    return { ...toRefs(data), dayjs, route, router, $api, onRefresh, onLoad, openDetails }
  }
})
</script>
<style lang='less'>
.RandomClappingMy {
  width: 100%;
  background: #f4f6f8;
  overflow: hidden;

  @font-face {
    font-family: "PingFangSC-Semibold";
    src: url("../../assets/font/PingFang-SC-Semibold.otf");
  }

  @font-face {
    font-family: "PingFangSC-Medium";
    src: url("../../assets/font/PingFang Medium_downcc.otf");
  }

  .RandomClappingMyBox {
    background: #fff;

    .RandomClappingMyBox-li {
      padding: 12px;
      border-bottom: 1px solid #f7f7f7;

      .RandomClappingMyBox-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;

        .RandomClappingMyBox-title {
          width: 70%;
          font-weight: 500;
          font-size: 17px;
          color: #333333;
          line-height: 26px;
        }

        .RandomClappingMyBox-state {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 21px;
          background: linear-gradient(0deg, #3894ff, #38c3ff);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }

        .RandomClappingMyBox-state2 {
          width: 56px;
          height: 21px;
          text-align: center;
          line-height: 21px;
          background: linear-gradient(0deg, #f6a531, #fddf2f);
          border-radius: 2px;
          font-size: 12px;
          color: #fff;
        }
      }

      .RandomClappingMyBox-time {
        font-size: 14px;
        color: #7e7e7e;
      }
    }
  }
}
</style>

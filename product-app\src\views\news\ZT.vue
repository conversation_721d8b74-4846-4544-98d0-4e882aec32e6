<template>
  <div class="ZT">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <div>
        <!-- <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" /> -->
      </div>
    </van-sticky>
    <div class="ZT_img">
      <img :src="url"
           alt="">
    </div>
    <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
        <!--数据列表-->
        <div class="ZT_list"
             v-for="item in dataList"
             :key="item.id">
          <div class="ZT_list_title">
            <!-- icon_specialsubject_hint -->
            <img :src="require('../../assets/img/icon_specialsubject_hint.png')" />
            <p class="ZT_list_title_p">{{ item.name }}</p>
            <div class="ZT_list_title_more"
                 v-if="item.specialsubjectNews && item.specialsubjectNews.length>3"
                 @click="examineAll(item.subjectId, item.id)">
              查看全部 <van-icon name="arrow" />
            </div>
          </div>
          <div class="ZT_list_type3"
               v-if="item.type == 3">
            <div class="ZT_list_type3_item"
                 v-for="it in item.specialsubjectNews"
                 :key="it.id"
                 @click="Skip(it, item.type)">
              <template v-if="it.imgObj.fullUrl">
                <div class="ZT_list_type3_item_title">
                  <p class="ZT_list_type3_item_text">{{ it.title }}</p>
                  <p class="ZT_list_type3_item_time">{{ it.createDate?dayjs(it.createDate).format('YYYY-MM-DD') : '' }}</p>
                </div>
                <div class="ZT_list_type3_item_img"
                     v-if="it.imgObj.fullUrl">
                  <img :src="it.imgObj.fullUrl"
                       alt="">
                </div>
              </template>
              <template v-else>
                <div class="ZT_list_type3_item_title2">
                  <p class="ZT_list_type3_item_text2">{{ it.title }}</p>
                  <p class="ZT_list_type3_item_time2">{{ it.createDate?dayjs(it.createDate).format('YYYY-MM-DD') : '' }}</p>
                </div>
              </template>
            </div>
          </div>
          <div class="ZT_list_type5"
               v-if="item.type == 5">
            <div class="ZT_list_type5_item"
                 v-for="it in item.specialsubjectNews"
                 :key="it.id"
                 @click="Skip(it, item.type)">
              <div class="ZT_list_type5_item_img">
                <img :src="it.imgObj.fullUrl"
                     alt="">
              </div>
              <div class="ZT_list_type5_item_title">
                <p class="ZT_list_type5_item_text">{{ it.title }}</p>
              </div>
            </div>
          </div>
          <div class="ZT_list_item"
               v-else-if="item.type == 1">
            <div class="ZT_list_item_con"
                 v-for="it in item.specialsubjectNews"
                 :key="it.id">
              <div class="ZT_list_item_con_box">
                <img :src="it.imgObj.fullUrl"
                     alt="">
                <div class="ZT_list_item_con_title">
                  {{ it.title }}
                </div>
                <div class="ZT_list_item_con_time">
                  {{ it.createDate?dayjs(it.createDate).format('YYYY-MM-DD') : '' }}
                </div>
                <div class="ZT_list_item_con_shade"
                     @click="Skip(it)">
                  <van-icon name="play-circle" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div v-for="item in dataList"
             :key="item.id"
             @click="details(item)">
          <div class="item-box">
            <div class="item-img"
                 :style="'background-image:url('+item.themeImg+')'"></div>
            <img :src="item.coverImg"
                 style="width:100%;"
                 alt=""
                 srcset="">
            <div class="item-title">{{item.title}}</div>
          </div>
        </div> -->
        <!--加载中提示 首次为骨架屏-->
        <div v-if="showSkeleton"
             class="notText">
          <van-skeleton v-for="(item,index) in 3"
                        :key="index"
                        title
                        :row="3"></van-skeleton>
        </div>
      </van-list>
    </van-pull-refresh>

  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, Image as VanImage } from 'vant'
export default {
  name: 'ZT',
  components: {
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [VanImage.name]: VanImage
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const dayjs = require('dayjs')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      id: route.query.id || '',
      title: route.query.title || '',
      url: route.query.url || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      module: 6,
      carouselList: [],
      dataList: [],
      switchs: {
        value: '',
        data: []
      }
    })
    if (data.title) {
      document.title = data.title
    }
    onMounted(() => {
      getList()
    })
    watch(() => data.switchs.value, (newName, oldName) => {
      console.log(newName, oldName)
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    })
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      getList()
    }
    const onRefresh = () => {
      data.pageNo = 1
      data.dataList = []
      data.showSkeleton = true
      data.loading = true
      data.finished = false
      getList()
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      getList()
    }
    // 列表请求
    const getList = async () => {
      var { data: list, total } = await $api.news.getSpecialsubjectColumnList({
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        subjectId: data.id,
        isOpen: 1,
        showDirectional: 2
      })
      data.dataList = list
      console.log('数据', data.dataList)
      data.showSkeleton = false
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    // 跳转
    const Skip = (item, type) => {
      console.log(item)
      if (type === 3 || type === '3' || type === '5' || type === 5) { // 列表类型
        router.push({ path: '/newsDetails', query: { type: 'listType', id: item.id } })
      }
      if (item.externalLinks != null) {
        window.location.href = item.externalLinks
      }
    }
    // 查看全部
    const examineAll = async (subjectId, id) => {
      router.push({ name: 'ZTList', query: { subjectId, id, url: data.url } })
    }
    const onClickLeft = () => history.back()
    const details = (row) => {
      console.log(row)
      router.push({ name: 'newsZTList', query: { id: row.id, title: row.title } })
    }
    return { ...toRefs(data), examineAll, search, Skip, onClickLeft, onRefresh, onLoad, details, dayjs }
  }
}
</script>

<style lang="less" scoped>
.ZT {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .ZT_list_type5 {
    display: flex;
    flex-wrap: wrap;
    margin: 10px 0 10px 6px;
    .ZT_list_type5_item {
      width: 30%;
      height: 4.8rem;
      margin: 5px;
      .ZT_list_type5_item_text {
        text-align: center;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .ZT_list_type5_item_img {
        width: 100%;
        height: 80%;
        > img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .ZT_list_type3 {
    margin: 0 15px;
    .ZT_list_type3_item {
      width: 100%;
      height: 80px;
      display: flex;
      margin: 15px 0;
      .ZT_list_type3_item_title {
        width: 70%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-left: 10px;
        box-sizing: border-box;
        .ZT_list_type3_item_text {
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .ZT_list_type3_item_time {
          font-size: 14px;
        }
      }
      .ZT_list_type3_item_title2 {
        // width: 70%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-left: 10px;
        box-sizing: border-box;
        .ZT_list_type3_item_text2 {
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .ZT_list_type3_item_time2 {
          font-size: 14px;
        }
      }
      .ZT_list_type3_item_img {
        width: 30%;
        height: 100%;
        > img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .item-box {
    width: calc(100% - 20px;);
    margin-left: 10px;
    margin-bottom: 20px;
  }
  .ZT_img {
    width: 100%;
    height: 200px;
    > img {
      width: 100%;
      height: 100%;
    }
  }
  .ZT_list {
    box-sizing: border-box;
    .ZT_list_item {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: left;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 20px;
      .ZT_list_item_con {
        height: 230px;
        width: 45%;
        position: relative;
        .ZT_list_item_con_box {
          width: 100%;
          height: 100%;
          border-radius: 20px;
          overflow: hidden;
          .ZT_list_item_con_title {
            margin: 0 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .ZT_list_item_con_time {
            font-size: 14px;
            color: #666;
            text-align: right;
            margin-top: 20px;
          }
          > img {
            width: 100%;
            height: 130px;
          }
          .ZT_list_item_con_shade {
            width: 100%;
            height: 130px;
            background: #0000003a;
            color: #fff;
            font-size: 40px;
            text-align: center;
            line-height: 130px;
            position: absolute;
            top: 0;
            left: 0;
            border-radius: 20px 20px 0 0;
          }
        }
      }
    }
    .ZT_list_title {
      width: 92%;
      height: 30px;
      display: flex;
      align-items: center;
      position: relative;
      padding: 0 10px;
      box-sizing: border-box;
      justify-content: space-between;
      margin: 15px 10px;
      .ZT_list_title_p {
        z-index: 99;
        font-size: 20px;
        margin: 0 0px 0 20px;
        box-sizing: border-box;
      }
      .ZT_list_title_more {
        font-size: 14px;
        color: #666;
      }
      > img {
        width: 50px;
        height: 20px;
        position: absolute;
        top: 5px;
        left: 10px;
      }
    }
  }
  .item-img {
    width: 100%;
    height: 170px;
    border-radius: 5px;
    background: url() no-repeat;
    background-size: 100%;
    background-position: center;
  }
  .item-title {
    font-size: 16px;
    margin: 10px 0;
    text-align: justify;
  }
}
</style>

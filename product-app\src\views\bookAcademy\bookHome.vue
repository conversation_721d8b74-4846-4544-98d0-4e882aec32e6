<template>
  <div class="home">
    <router-view />
    <van-tabbar v-model="active"
                :active-color="appTheme"
                @change="onChange">
      <van-tabbar-item :icon="active==item.id?item.selectIconUrl:item.iconUrl"
                       v-for="item in tabbarList"
                       :key="item.id"
                       :to="item.infoUrl"
                       :badge="item.pointNumber>0?item.pointNumber:''"
                       :name="item.id">{{item.name}}</van-tabbar-item>
    </van-tabbar>
  </div>
</template>
<script>
import { useRouter } from 'vue-router'
import { onMounted, reactive, toRefs, inject } from 'vue'
export default {
  name: 'home',
  setup () {
    // const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const homePageImg11 = require('../../assets/img/12.png')
    const homePageImg12 = require('../../assets/img/11.png')
    const homePageImg21 = require('../../assets/img/21.png')
    const homePageImg22 = require('../../assets/img/22.png')
    const homePageImg1 = require('../../assets/img/2.png')
    const homePageImg2 = require('../../assets/img/1.png')
    // const homePageImg31 = require('../../assets/img/31.png')
    // const homePageImg32 = require('../../assets/img/32.png')
    // const homePageImg41 = require('../../assets/img/41.png')
    // const homePageImg42 = require('../../assets/img/42.png')
    const router = useRouter()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      active: '',
      tabbarList: [
        { id: '335633278637703168', name: '推荐', iconUrl: homePageImg12, selectIconUrl: homePageImg11, infoUrl: '/recommendHome', pointNumber: 0 },
        { id: '335633485739851776', name: '书架', iconUrl: homePageImg22, selectIconUrl: homePageImg21, infoUrl: '/bookDeskHome', pointNumber: 0 },
        { id: '-1', name: '返回', iconUrl: homePageImg2, selectIconUrl: homePageImg1, infoUrl: '/module', pointNumber: 0 }
        // { id: '335633735003144192', name: '交流', iconUrl: homePageImg32, selectIconUrl: homePageImg31, infoUrl: '/msgListHome', pointNumber: 0 }
        // { id: '335633924787011584', name: '我的', iconUrl: homePageImg42, selectIconUrl: homePageImg41, infoUrl: '/myUser', pointNumber: 0 }
      ]
    })
    onMounted(() => {
      const historyIndex = sessionStorage.getItem('bookhistoryIndex') || ''
      console.log(historyIndex)
      if (historyIndex) {
        data.active = historyIndex
        data.tabbarList.forEach((element, index) => {
          if (element.id === data.active) {
            router.push({ path: data.tabbarList[index].infoUrl })
          }
        })
      } else {
        data.active = data.tabbarList[0].id
        router.push({ path: data.tabbarList[0].infoUrl })
      }
      // init()
      // appList()
    })

    const onChange = (index) => {
      if (index === '-1') {
        const backLength = window.history.length - 2
        router.go(-backLength)
        return
      }
      sessionStorage.setItem('bookhistoryIndex', index)
    }
    return { ...toRefs(data), onChange }
  }
}
</script>
<style lang="less">
.home {
  width: 100%;
  .van-tabbar {
    .van-tabbar-item__text {
      font-size: 10px;
      line-height: 12px;
    }
  }
}
</style>

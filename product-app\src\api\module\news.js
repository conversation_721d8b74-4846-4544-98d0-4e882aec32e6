import { HTTP } from '../http.js'
class news extends HTTP {
  // 获取资讯栏目(重构版）
  getNewsTree2 (params) {
    return this.request({
      url: '/newscolumn/app/list',
      data: params
    })
  }

  // 获取顶部轮播图(重构版）
  getTopList2 (params) {
    return this.request({
      url: '/newscontent/app/topList',
      data: params
    })
  }

  // 获取资讯列表
  getAppList2 (params) {
    return this.request({
      url: '/newscontent/app/batchList',
      data: params
    })
  }

  // 获取资讯详情
  getNewsDetail2 (params) {
    return this.request({
      url: '/newscontent/info/' + params.id
    })
  }

  // 获取资讯栏目
  getNewsTree (params) {
    return this.request({
      url: '/zyinfostructure/tree',
      data: params
    })
  }

  // 获取顶部轮播图
  getTopList (params) {
    return this.request({
      url: '/zyinfodetail/top/list',
      data: params
    })
  }

  // 获取公告栏详情
  getAnnouncement (params) {
    return this.request({
      url: '/oa/info/detail/info/' + params.id
    })
  }

  // 获取资讯列表
  getAppList (params) {
    return this.request({
      url: '/zyinfodetail/appList',
      data: params
    })
  }

  // 获取资讯详情
  getNewsDetail (params) {
    return this.request({
      url: '/zyinfodetail/info/' + params.id
    })
  }

  // 获取意见征集列表
  getAurveyList (params) {
    return this.request({
      url: '/survey/appList',
      data: params
    })
  }

  // 获取意见征集详情评论
  getAurveyComment (params) {
    return this.request({
      url: 'surveytownhalladvice/appList',
      data: params
    })
  }

  // 获取意见征集详情
  getAurveyDetails ({ id }) {
    return this.request({
      url: 'survey/info/' + id
    })
  }

  // 意见征集提交建言
  surveytownhalladvice (params) {
    return this.request({
      url: 'surveytownhalladvice/add',
      data: params
    })
  }

  // 获取政情快递
  getSpecialsubjectnews (params) {
    return this.request({
      url: 'specialsubjectnews/info' + params.id
    })
  }

  // 获取代表履职圈详情
  getRepresentativeDetails (params) {
    return this.request({ url: 'committeesay/info/' + params.id })
  }

  // 获取基层履职动态详情
  findWygzsWorkDynamicInfo (params) {
    return this.request({
      url: 'app/wygzsApp/findWygzsWorkDynamicInfo',
      data: params
    })
  }

  // 获取专题详情列表
  getInforelationList (params) {
    return this.request({
      url: '/inforelation/list',
      data: params
    })
  }

  // 获取专题详情
  getSpecialsubjectinfo (params) {
    return this.request({
      url: '/specialsubjectinfo/info/' + params.id,
      data: params
    })
  }

  // 获取随手拍数据
  getShowyourselfList (params) {
    return this.request({
      url: '/showyourself/app/list',
      data: params
    })
  }

  // 添加随手拍数据
  photograprAdd (params) {
    return this.request({
      url: '/showyourself/add',
      data: params
    })
  }

  // 基层代表联络站添加
  wygzsWorkDynamicAdd (params) {
    return this.request({
      url: '/wygzsWorkDynamic/add',
      data: params
    })
  }

  // 联络站站点
  wygzsStudioList (params) {
    return this.request({
      url: '/wygzsStudioApp/wygzsStudioList',
      data: params
    })
  }

  // 随手拍详情
  photograprDetails (params) {
    return this.request({
      url: '/showyourself/info/' + params.id
    })
  }

  // 删除委员说
  committeesayDels (params) {
    return this.request({
      url: 'committeesay/dels',
      data: params
    })
  }

  // 履职圈添加
  addCommittee (params) {
    return this.request({
      url: 'committeesay/add',
      data: params
    })
  }

  // 关注或取消
  attention ({ params, type }) {
    return this.request({
      url: `follow/${type}`,
      data: params
    })
  }

  // 获取专题详情栏目
  getSpecialsubjectColumnList (params) {
    return this.request({
      url: '/zySpecialsubjectColumn/list',
      data: params
    })
  }

  // 获取栏目列表 zyinfodetail/list
  getColumnList (params) {
    return this.request({
      url: 'zyinfodetail/list',
      data: params
    })
  }

  // 获取专题详情列表
  getSpecialsubjectRelateinfoList (params) {
    return this.request({
      url: '/zySpecialsubjectRelateinfo/list',
      data: params
    })
  }

  // 获取专题列表详情
  getSpecialsubjectRelateinfo (params) {
    return this.request({
      url: '/zySpecialsubjectRelateinfo/info',
      data: params
    })
  }

  // 获取专题所有列表
  getSpecialsubjectRelateinfoListAll (params) {
    return this.request({
      url: '/zySpecialsubjectRelateinfo/list?',
      data: params
    })
  }

  // 获取更多专题
  getMoreTopics (params) {
    return this.request({
      url: 'committeesay/app/list',
      data: params
    })
  }

  // 获取专题资讯详情
  getSpecialsubjectnewsInfo (params) {
    return this.request({
      url: '/specialsubjectnews/info/' + params.id
    })
  }

  // 获取专题列表
  getSpecialsubjectList (params) {
    return this.request({
      url: '/specialsubjectinfo/list',
      data: params
    })
  }

  // 排序
  zyinfodetailEditShow (params) {
    return this.request({
      url: '/zyinfodetail/editShow',
      data: params
    })
  }

  // 新版排序
  newscontentEditSort (params) {
    return this.request({
      url: '/newscontent/editSort',
      data: params
    })
  }

  // 获取角色
  roleFindRolesByUserId (params) {
    return this.request({
      url: '/role/findRolesByUserId',
      data: params
    })
  }
}
export {
  news
}

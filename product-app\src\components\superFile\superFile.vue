<template>
  <div class="superFile">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft" />
      <!-- <div>
        <van-search v-model="keyword"
                    @search="search"
                    @clear="search"
                    placeholder="请输入搜索关键词" />
      </div> -->
    </van-sticky>
    <div v-if="(iconInfo && !iconInfo.convertType)"
         class="T-flexbox-vertical flex_align_center flex_justify_content">
      <img :style="$general.loadConfigurationSize(45,'h')"
           :src="require('../../assets/img/fileicon/' + iconInfo.name)" />
      <div style="margin-top:38px;padding: 0 50px;">
        <div :style="$general.loadConfiguration(5)+'font-weight: bold;'">{{fileOther.name}}</div>
      </div>
      <div style="margin-top:15px;">
        <div :style="$general.loadConfiguration()">文件大小：{{$general.getFileSize(fileOther.size)}}</div>
      </div>
      <div style="margin-top:175px;">
        <div @click="openBtn"
             class="open_btn flex_box flex_align_center flex_justify_content"
             :style="$general.loadConfiguration()+'background:'+appTheme">
          <template v-if="fileOther.state ==0">
            接收
          </template>
          <template v-else-if="fileOther.state == 1">
            <van-circle style="margin-right:15px;"
                        :size="19"
                        v-model="fileOther.schedule"
                        :rate="fileOther.schedule"
                        stroke-width="150"></van-circle>
            下载中
          </template>
          <template v-else-if="fileOther.state == 2">
            打开
          </template>
          <template v-else>
            打开失败，请重试
          </template>
        </div>
      </div>
    </div>
    <iframe v-else
            style="width:100%;height:100vh;"
            :src="viewUrl"
            frameborder="0"></iframe>
    <!-- <van-pull-refresh v-model="refreshing"
                      @refresh="onRefresh">
      <van-list v-model:loading="loading"
                :finished="finished"
                finished-text="没有更多了"
                offset="52"
                @load="onLoad">
      </van-list>
    </van-pull-refresh> -->
    <van-overlay :show="isShowLoading">
      <div style="text-align:center;padding-top:100%;">
        <van-loading size="24px">加载中...</van-loading>
      </div>
    </van-overlay>
  </div>
</template>
<script>
import { useRoute } from 'vue-router'
import { inject, reactive, toRefs, onMounted, watch } from 'vue'
import { NavBar, Sticky, ActionSheet, Loading, Overlay } from 'vant'
import moment from 'moment'
export default {
  name: 'superFile',
  components: {
    [Loading.name]: Loading,
    [Overlay.name]: Overlay,
    [ActionSheet.name]: ActionSheet,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const route = useRoute()
    const $api = inject('$api')
    const $general = inject('$general')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      send: route.query.send || '',
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      keyword: '',
      showSkeleton: true,
      loading: false,
      finished: false,
      refreshing: false,
      isShowLoading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dataList: [],
      fileOther: {
        iconInfo: {
          name: 'icon_zip.png',
          type: 'compression',
          convertType: '19'
        },
        name: '我是一个文件.zip',
        url: 'http://**************:21621/lzt/flowAttachment/masterRepository/2021/11/08/400898353094721536.zip',
        size: '48709',
        path: '/var/mobile/Containers/Data/Application/56C66689-2A8F-4424-8C1D-8BDFB6967B98/Documents/uzfs/A6175166705094/fileCache/0358.gif_wh860.zip',
        state: 0,
        schedule: -1
      }, // 文件其它属性
      fileUrl: route.query.url || route.query.filePath, // 文件地址
      iconInfo: '', // 文件属性
      viewUrl: ''
    })
    data.fileOther.name = route.query.name
    data.fileOther.state = route.query.state
    data.fileOther.schedule = route.query.schedule
    if (data.fileUrl.indexOf('talkroup/download') === -1 && data.fileUrl.indexOf('fileinfo/download') === -1 && !data.user.userName) { // 不是聊天文件就用url判断是就用名字
      data.iconInfo = $general.getFileTypeAttr(data.fileUrl.split('.')[data.fileUrl.split('.').length - 1])
    } else {
      data.iconInfo = $general.getFileTypeAttr(data.fileOther.name.split('.')[data.fileOther.name.split('.').length - 1])
    }
    if (data.title) {
      document.title = data.title
    } else {
      data.title = route.query.name
      document.title = data.title
    }
    onMounted(() => {
      openFile()
    })
    watch(() => data.fileOther, (newName, oldName) => {

    })
    const search = () => {

    }
    const onRefresh = () => {

    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
    }
    const openBtn = () => {
      openFile()
    }
    // 列表请求
    const openFile = async () => {
      data.isShowLoading = true
      var res = await $api.general.fcscloudFile({ fileUrl: data.fileUrl })
      if (res) {
        var info = (res.data || {}).data
        if (info) {
          var datas = {
            srcRelativePath: info,
            convertType: data.iconInfo.convertType,
            isDccAsync: 1,
            isCopy: 0,
            fileUrl: data.fileUrl,
            showFooter: 0, // 是否显示页脚
            isHeaderBar: 0,
            // zoom: 2,
            time: 60000
          }
          var { data: infos } = await $api.general.fcscloudCompositeConvert(datas)
          if (infos) {
            data.viewUrl = infos.viewUrl
          } else {

          }
          data.isShowLoading = false
        }
      }
    }

    const onClickLeft = () => history.back()

    return { ...toRefs(data), moment, $general, search, onClickLeft, onRefresh, onLoad, openBtn }
  }
}
</script>

<style lang="less" scoped>
.superFile {
  width: 100%;
  background: #fff;
  .open_btn {
    min-width: 170px;
    padding: 10px 20px;
    color: #fff;
    text-align: center;
    border-radius: 10px;
  }
}
</style>

<template>
  <div class="LiaisonDataDetails">
    <van-sticky>
      <van-nav-bar v-if="isShowHead"
                   :title="title"
                   left-text=""
                   left-arrow
                   @click-left="onClickLeft"
                   @click-right="play">
        <!-- <template #right>{{playText}}
        </template> -->
      </van-nav-bar>
    </van-sticky>
    <div class="n_details_content">
      <div class="n_details_title">{{info.name}}</div>
      <div style="display: flex;align-items: center;justify-content:space-between;margin-top:10px;">
        <div style="font-size: 12px;color: #828282;">{{info.studioName}}</div>
        <div style="font-size: 12px;color: #828282;">{{info.createDate}}</div>
      </div>
      <div style="margin-top: 15px;"
           v-html="content"></div>
      <div class="house_content_bottom"
           style="margin-top: 10px;">
        <div class="flex_box flex_align_center">
          <div class="flex_placeholder"></div>
          <div class="house_content_bottom_comment flex_box flex_align_center flex_justify-content_end"
               @click.stop="replyClick(item)">
            <img :src="require('../../../assets/img/icon_comments.png')"
                 alt=""
                 style="width: 15px;">
            <span :style="'margin-left:5px;'+$general.loadConfiguration(-2)">{{commentCount}}</span>
          </div>
          <div class="house_content_bottom_like flex_box flex_align_center flex_justify-content_end"
               style="margin-left:18px;"
               @click.stop="downLike(item)">
            <img :src="require( !info.isFabulous ? '../../../assets/img/icon_likes.png': '../../../assets/img/icon_likes_on.png')"
                 alt=""
                 style="width: 15px;">
            <span :style="'margin-left:5px;'+$general.loadConfiguration(-2)+';color:'+(info.isFabulous?'#FE7530':'#333333')+';'">{{info.fabulousNumber}}</span>
          </div>
        </div>
      </div>
      <div class="likeComment_box">
        <div class="like_box"
             :style="$general.loadConfiguration(-4)">
          <van-icon name="like-o"
                    v-if="menuList != null && menuList.length" />
          <span :style="$general.loadConfiguration(-4)+'margin-bottom: 2px;' + 'line-height: 0.2rem;'"
                v-for="it,ind in menuList"
                :key="ind">{{ind>0?',':''}} {{it.name}} </span>
        </div>
        <div class="comment_box"
             @click.stop=""
             v-if="commentList.length!=0 ">
          <div v-for="items,index in commentList"
               :key="index">
            <p style="display: flex;align-items: center;">
              <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'">{{items.name}}: </span>
              <span :style="$general.loadConfiguration(-4)+'flex:1;'"
                    @click.stop="openMoreComment(items,index)"> {{items.content}} </span>
            </p>
            <p v-for="ite,ind in items.commentList"
               :key="ind"
               :style="$general.loadConfiguration(-4)">
              <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'">{{ite.name}}</span> 回复
              <span :style="$general.loadConfiguration(-4)+'color: #6e7fa3;'">{{items.name}}: </span>
              <span :style="$general.loadConfiguration(-4)"
                    @click.stop="openMoreComment(items,index)"> {{ite.content}} </span>
            </p>
          </div>
        </div>
        <div class="reply_box"
             @click.stop="">
          <div class="reply_box_item">
            <input type="text"
                   v-model="inputInfo.value"
                   :style="$general.loadConfiguration(-4)"
                   class="reply_box_inp"
                   :placeholder="inputInfo.placeholder">
            <button :class="inputInfo.value!='' ? 'reply_box_but' : 'reply_box_buts'"
                    :style="$general.loadConfiguration(-4)"
                    @click="mgcHandle()">发送</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router'
import { inject, onMounted, reactive, toRefs, computed, watch } from 'vue'
import { NavBar, Sticky, ImagePreview, Image as VanImage, Dialog, Toast } from 'vant'
import { useStore } from 'vuex'
export default {
  name: 'LiaisonDataDetails',
  components: {
    [VanImage.name]: VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky,
    [Dialog.Component.name]: Dialog.Component
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $isShowHead = inject('$isShowHead')
    const dayjs = require('dayjs')
    const $general = inject('$general')
    const store = useStore()
    const data = reactive({
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      relateType: route.query.relateType || '',
      title: route.query.title,
      user: JSON.parse(sessionStorage.getItem('user')),
      ifIike: route.query.ifIike || '',
      id: route.query.id,
      isFollow: route.query.isFollow || '',
      publishBy: route.query.publishBy || '',
      pageType: route.query.type || '',
      userName: route.query.userName || '',
      contents: route.query.content || '',
      createDate: route.query.createDate || '',
      details: {},
      type: 71,
      refreshing: false,
      show: false,
      browerCount: { show: false, value: '0', hint: '阅读：' }, // 阅读数
      shareCount: { show: false, value: '0' }, // 分享数
      // commentCount: { show: false, value: '0' }, // 评论数
      commentCount: 0,
      // fabulousNumber: route.query.fabulousCount || 0,
      dataTime: { show: false, value: '' }, // 时间
      IBSName: { show: false, value: '' }, // 资讯类型
      source: '', // 来源
      content: '', // 正文内容
      contentImgs: [], // 正文中图片集合
      picInfo: { name: '图片', data: [] }, // 图片对象
      attachInfo: { name: '附件', data: [] }, // 附件对象
      playText: '播放',
      inputData: {
        input_placeholder: '评论', // 输入框中的提示文字
        input_not: false, // 是否禁止输入
        showComment: true, // 显示评论功能
        showLike: true, // 显示点赞功能
        showAttach: true // 显示添加附件(图片)
      },
      commentData: {},
      // commentList: null,
      inputBox: null,
      commentObj: {},
      info: {},
      politicalData: {},
      twoInstitutesData: {},
      communityData: {},
      representativeData: {},
      countrysideData: {},
      numberAppData: {},
      surveyComment: [], // 意见征集评论列表
      surveyDetails: {}, // 意见征集详情
      attentionStatus: false,
      conmmentList: {},
      commentList: [],
      menuList: [],
      pingData: '',
      pingIndex: '',
      inputInfo: { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送', disabled: false }
    })
    const getShow = computed(() => {
      // 返回的是ref对象
      return store.state.speechShow
    })
    const getStatus = computed(() => {
      // 返回的是ref对象
      return store.state.speechStauts
    })
    watch(getShow, (newName, oldName) => {
      data.show = newName
      if (!data.show) {
        data.playText = '播放'
      }
    })
    watch(getStatus, (newName, oldName) => {
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '继续'
        } else {
          data.playText = '暂停'
        }
      }
    })
    const play = () => {
      if (data.playText === '播放') {
        data.playText = '暂停'
        store.commit('setSpeechShow', true)
      } else {
        store.commit('setStatus', !store.state.speechStauts)
      }
    }
    onMounted(() => {
      browseSave()
      newsInfo()
      if (store.state.speechShow) {
        if (store.state.speechStauts) {
          data.playText = '暂停'
        } else {
          data.playText = '继续'
        }
      }
    })
    const onRefresh = () => {
      newsInfo()
    }
    const browseSave = async () => {
      await $api.general.saveBrowse({
        keyId: data.id,
        type: data.type
      })
    }
    // 详情
    const newsInfo = async () => {
      getDetails()
      getFabulousList()
      getCommentList()
    }
    // 获取详情
    const getDetails = async () => {
      if (data.pageType === 'LiaisonData') {
        // resumption 履职圈
        const res = await $api.news.findWygzsWorkDynamicInfo({
          id: data.id
        })
        data.info = res.data
        const newHtml = res.data.content.replace(/<img([^>]+)>/g, function (match, p1) {
          const style = 'style="width:100%; height:100%;"'
          if (/style="/.test(p1)) {
            // 如果img标签中已经包含style属性，则替换为新的style
            return match.replace(/style="[^"]*"/, style)
          } else {
            // 如果img标签中不包含style属性，则添加新的style
            return match.replace(/<img/, '<img ' + style)
          }
        })
        console.log('newHtml==>', newHtml)
        data.content = newHtml
      }
    }
    // 获取评论列表
    const getCommentList = async () => {
      const res = await $api.general.getCommentList({
        pageNo: 1,
        pageSize: 99,
        keyId: data.id,
        type: '71',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      console.log('获取评论列表===>>', res)
      var list = res ? res.data || [] : []
      if (list && list.length !== 0) {
        list.forEach(function (_eItem, _eIndex, _eArr) {
          var item = {}
          var itemData = _eItem
          item.hasDelete = itemData.createBy === data.user.id
          item.createBy = itemData.createBy
          // id
          item.id = itemData.id || ''
          // id
          item.extend = itemData.extend || ''
          // 来源
          item.url = itemData.userHeadImg || '../../../images/icon_default_user.png'
          // 用户头像
          item.name = itemData.userName || '匿名用户'
          item.content = itemData.content
          // 评论中的图片
          var resultBatchAttach = itemData.filePathList || []
          var resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
          for (var p = 0; p < resultBatchAttachLength; p++) {
            var attachpath = resultBatchAttach[p].fullUrl || ''
            var resultBatchAttachItem = {
              url: attachpath
            }
            item.nAttach.push(resultBatchAttachItem)
          }
          item.commentList = []
          var Commentlist = itemData.children || []
          var CommentlistLength = Commentlist ? Commentlist.length : 0
          for (var j = 0; j < CommentlistLength; j++) {
            var item2 = {}
            var itemData2 = Commentlist[j]
            item2.hasDelete = itemData2.createBy === data.user.id
            item.createBy = itemData2.createBy
            // id
            item2.id = itemData2.id || ''
            // id
            item2.extend = itemData2.extend || ''
            // 来源
            item2.url = itemData2.userHeadImg || ''
            // 用户头像
            item2.name = itemData2.userName || '匿名用户'
            // 用户名
            item2.createTime = itemData2.dateDetail || ''
            item2.content = itemData.content
            // 是否审核,0待审核，1审核通过，2审核不通过
            item2.nAttach = []
            // 评论中的图片
            const resultBatchAttach = itemData2.filePathList || []
            const resultBatchAttachLength = resultBatchAttach ? resultBatchAttach.length : 0
            for (var k = 0; k < resultBatchAttachLength; k++) {
              const attachpaths = resultBatchAttach[k].fullUrl || ''
              const resultBatchAttachItem = {
                url: attachpaths
              }
              item2.nAttach.push(resultBatchAttachItem)
            }
            item.commentList.push(item2)
          }
          data.commentList.push(item)
        })
      }
    }
    // 获取点赞列表
    const getFabulousList = async () => {
      const res = await $api.general.getFabulousList({
        pageNo: 1,
        pageSize: 100,
        keyId: data.id,
        type: '71',
        areaId: data.user.areaId,
        isCheck: '1',
        isApp: '1'
      })
      if (res) {
        var list = res ? res.data || [] : []
        var dataLength = list ? list.length : 0
        if (list) {
          data.menuList = []
          for (var i = 0; i < dataLength; i++) {
            var item = {}
            var itemData = list[i]
            item.id = itemData.createBy // id
            item.name = itemData.userName // 标题
            item.url = itemData.headImg // 图标地址
            data.menuList.push(item)
          }
        }
      }
      console.log('获取点赞列表===>>', res)
    }
    // 点击评论中的评论框
    const openMoreComment = (_item, _index) => {
      data.pingData = _item || ''
      data.pingIndex = _index || ''
      if (_item) {
        data.inputInfo = { value: '', placeholder: '回复' + _item.name, name: '评论', btn: '回复' }
      } else {
        data.inputInfo = { value: '', placeholder: '说说你的看法', name: '评论', btn: '发送' }
      }
      // that.inputFocus = true
    }
    // 发送
    const mgcHandle = async () => {
      if (!data.inputInfo.value) {
        return
      }
      var url = 'comment/save'
      var params = {
        content: data.inputInfo.value,
        createBy: data.user.id,
        commentPid: data.pingData ? data.pingData.id : '',
        keyId: data.id,
        attach: '',
        extend: '1',
        type: '71',
        areaId: '370200',
        isCheck: '1'
      }
      const ret = await $api.general.fabulous({ url, params })
      if (ret) {
        data.commentList = []
        getCommentList()
        data.inputInfo.value = ''
      } else {
        Toast('请求失败。')
      }
    }

    const previewCalback = (item) => {
      var images = item.map(item => {
        return item.filePath
      })
      ImagePreview({
        images,
        closeable: true
      })
    }
    const committeesayDel = (id) => {
      console.log(id)
      Dialog.confirm({
        title: '温馨提示',
        message: '确定删除吗'
      })
        .then(async () => {
          var { errcode } = await $api.news.committeesayDels({ ids: id })
          if (errcode === 200) {
            router.push({ path: '/newsMore', query: { type: 'resumption' } })
          }
          // on confirm
        })
        .catch(() => {
          // on cancel
        })
    }
    const downLike = (_item) => {
      console.log('data.info.isFabulous==>', data.info.isFabulous)
      console.log('data.info.fabulousNumber==>', data.info.fabulousNumber)
      if (data.info.isFabulous) {
        if (data.info.fabulousNumber > 0) {
          data.info.fabulousNumber--
          data.info.ifIike = false
          data.menuList = data.menuList.filter(item => item.id !== data.user.id)
        }
      } else {
        data.info.fabulousNumber++
        data.info.ifIike = true
        data.menuList.push({ id: data.user.id, name: data.user.userName })
      }
      data.info.isFabulous = !data.info.isFabulous
      fabulousInfo(data.info.isFabulous, data.id)
    }
    // 点赞或取消点赞
    const fabulousInfo = async (_status, _id) => {
      var url = _status ? '/fabulous/save' : 'fabulous/del'
      var params = {
        keyId: _id,
        type: '71',
        areaId: data.user.areaId || '370200'
      }
      await $api.general.fabulous({ url, params })
      getDetails()
    }
    // 点关注
    const attentionEdit = () => {
      var type = ''
      if (data.isFollow === 1) {
        type = 'del'
        data.isFollow = 0
      } else {
        type = 'add'
        data.isFollow = 1
      }
      $api.news.attention({ params: { followId: data.publishBy, type: '71' }, type })
      getDetails()
    }
    const addCommentEvent = (value) => {
      data.commentList.onRefresh()
    }
    const openInputBoxEvent = (value) => {
      data.inputBox.changeType(2, value)
    }
    const freshState = (value) => {
      data.inputBox.getStats()
    }
    const annexClick = (item) => {
      var param = {
        id: item.id,
        url: item.url,
        name: item.name
      }
      router.push({ name: 'superFile', query: param })
    }

    const onClickLeft = () => history.back()
    return { ...toRefs(data), $general, openMoreComment, committeesayDel, route, dayjs, previewCalback, attentionEdit, downLike, onRefresh, onClickLeft, play, addCommentEvent, openInputBoxEvent, freshState, annexClick, mgcHandle }
  }
}
</script>
<style lang="less">
.LiaisonDataDetails {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .n_details_content {
    margin: 10px;
    .n_details_title {
      font-weight: bold;
      line-height: 1.5;
    }
  }
}
.likeComment_box {
  background: #f7f7f7;
  margin: 0 5px 10px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 5px;
  .comment_box {
    margin: 0 5px 0px;
  }
  .like_box {
    color: #6e7fa3;
    margin: 5px 5px;
  }
  .reply_box {
    background: #f7f7f7;
    margin: 5px 5px 0;
    padding: 5px 0 0 0;
    border-top: 1px solid #e8e8e8;
    height: 50px;
    .reply_box_item {
      width: 100%;
      background: #fff;
      height: 100%;
      border-radius: 5px;
      border: 1px solid #3895ff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 5px;
      .reply_box_but {
        width: 60px;
        border-radius: 5px;
        height: 80%;
        color: #fff;
        background: #3895ff;
      }
      .reply_box_buts {
        color: rgb(112, 112, 112);
        background: #bdbdbd;
        width: 60px;
        border-radius: 5px;
        height: 80%;
      }
      .reply_box_inp {
        height: 80%;
        flex: 1;
      }
    }
  }
}
.footerBox {
  position: fixed !important;
  width: 100%;
  bottom: 0;
  left: 0;
}
</style>
